---
description:
globs:
alwaysApply: false
---
# API Routes and Data Fetching

## API Structure

The application uses both App Router route handlers and Pages Router API routes:

- [app/api/](mdc:app/api/) - App Router API routes
- [pages/api/](mdc:pages/api/) - Pages Router API routes (admin only)

## Main API Endpoints

### Product-Related
- [app/api/products/by-slug/[slug]/route.ts](mdc:app/api/products/by-slug/[slug]/route.ts) - Get product by slug
- [pages/api/products/](mdc:pages/api/products/) - Admin product management

### Authentication
- [app/api/auth/](mdc:app/api/auth/) - NextAuth.js endpoints
- [pages/api/admin/login/](mdc:pages/api/admin/login/) - Admin authentication

### Database Management
- [app/api/db-init/](mdc:app/api/db-init/) - Database initialization
- [app/api/db-setup/](mdc:app/api/db-setup/) - Database setup utilities

## Data Fetching Patterns

### Server Components (RSC)
For server components, use direct database queries:

```tsx
// app/products/[slug]/page.tsx
import { query } from '@/lib/db';

export default async function ProductPage({ params }: { params: { slug: string } }) {
  // Server-side database query
  const productResult = await query(
    'SELECT * FROM products WHERE slug = $1',
    [params.slug]
  );
  
  const product = productResult.rows[0];
  
  // ...render component with data
}
```

### Client Components
For client components, fetch data from API routes:

```tsx
'use client';
import { useState, useEffect } from 'react';

export default function ProductList() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await fetch('/api/products');
        const data = await response.json();
        setProducts(data);
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchProducts();
  }, []);
  
  // ...render component with products
}
```
