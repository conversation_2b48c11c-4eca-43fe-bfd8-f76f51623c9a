---
description:
globs:
alwaysApply: false
---
# UI Components and Styling

## Component Organization

The application components are organized in two main directories:

- [app/components/](mdc:app/components/) - Shared components for the main application
- [components/](mdc:components/) - Components specific to admin functionality

## Key Components

### Layout Components
- [app/components/Header.tsx](mdc:app/components/Header.tsx) - Main site header
- [app/components/Footer.tsx](mdc:app/components/Footer.tsx) - Main site footer
- [app/components/FloatingButtons.tsx](mdc:app/components/FloatingButtons.tsx) - Floating action buttons

### Product Components
- [app/components/ProductCard.tsx](mdc:app/components/ProductCard.tsx) - Product card for listings
- [app/components/ProductGallery.tsx](mdc:app/components/ProductGallery.tsx) - Product image gallery

### Form Components
- [app/components/ContactForm.tsx](mdc:app/components/ContactForm.tsx) - Contact form
- [components/admin/ProductForm.tsx](mdc:components/admin/ProductForm.tsx) - Admin product form

## Styling

The application uses a combination of:

1. **TailwindCSS** - Core utility-based styling
2. **CSS Modules** - For component-specific styles
3. **Global CSS** - For baseline styling

### Styling Files
- [app/styles/globals.css](mdc:app/styles/globals.css) - Global styles and Tailwind imports
- [app/styles/](mdc:app/styles/) - Component and page-specific CSS

### Styling Pattern

Components typically use a combination of Tailwind classes and component-specific CSS:

```tsx
// Using Tailwind directly in components
export function Button({ children, onClick }) {
  return (
    <button 
      className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
      onClick={onClick}
    >
      {children}
    </button>
  );
}

// Using CSS modules for more complex styling
import styles from './ProductCard.module.css';

export function ProductCard({ product }) {
  return (
    <div className={styles.card}>
      <img src={product.image} alt={product.title} className={styles.image} />
      <h3 className={styles.title}>{product.title}</h3>
    </div>
  );
}
```

For consistent UI, prefer using Tailwind utility classes directly in components unless the styling is highly complex.
