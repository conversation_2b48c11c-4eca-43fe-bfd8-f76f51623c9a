---
description:
globs:
alwaysApply: false
---
# API结构

本项目的API分为两部分：App Router和Pages Router中的API路由。

## App Router API
- `/app/api/admin/` - 管理后台API
  - `/app/api/admin/login/` - 管理员登录
  - `/app/api/admin/create/` - 创建管理员
  - `/app/api/admin/setup/` - 设置管理
- `/app/api/auth/` - 认证相关API
- `/app/api/products/` - 产品相关API
  - `/app/api/products/by-slug/[slug]/` - 通过slug获取产品
- `/app/api/db-init/` - 数据库初始化
- `/app/api/db-setup/` - 数据库设置
- `/app/api/db-test/` - 数据库测试

## Pages Router API
- `/pages/api/admin/` - 管理后台API
- `/pages/api/auth/` - 认证相关API
- `/pages/api/categories/` - 分类相关API
- `/pages/api/content/` - 内容相关API
- `/pages/api/products/` - 产品相关API
  - `/pages/api/products/by-slug/` - 通过slug获取产品
- `/pages/api/users/` - 用户相关API

## API使用规范
- 创建新API时，应考虑是使用App Router还是Pages Router
- API应遵循RESTful设计原则
- 确保所有API都有适当的认证和权限控制
