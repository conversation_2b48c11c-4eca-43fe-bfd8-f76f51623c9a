---
description:
globs:
alwaysApply: false
---
# 组件组织结构

本项目将组件分为不同的类别和功能区域。

## 组件目录
- `/app/components/` - App Router使用的组件
- `/components/` - 全局共享组件
  - `/components/admin/` - 管理后台专用组件

## 常用组件位置
- 页面组件位于各自的路由目录下
- 共享组件位于 `/app/components/` 或 `/components/` 目录
- 管理界面组件位于 `/components/admin/` 目录

## 重要组件
查看 `/app/components/` 目录了解主要UI组件

## 组件开发规范
- 组件应遵循项目既有的命名和样式约定
- 新组件应放置在适当的目录结构中
- 考虑组件的可重用性和国际化需求
