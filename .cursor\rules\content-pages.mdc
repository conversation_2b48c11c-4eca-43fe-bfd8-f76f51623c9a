---
description:
globs:
alwaysApply: false
---
# Content Pages

This project includes various static content pages and a blog system.

## Static Pages

Main content pages are located at:

- [app/[lang]/pages/](mdc:app/[lang]/pages/) - Language-specific content pages
- [app/pages/](mdc:app/pages/) - Root content pages

Key content pages include:

- [app/[lang]/pages/about-us/](mdc:app/[lang]/pages/about-us/) - About us page
- [app/[lang]/pages/contact-us/](mdc:app/[lang]/pages/contact-us/) - Contact page
- [app/[lang]/pages/custom-playground-design/](mdc:app/[lang]/pages/custom-playground-design/) - Custom design services
- [app/[lang]/pages/service/](mdc:app/[lang]/pages/service/) - Service information

## Blog Structure

The blog system is organized at:

- [app/[lang]/blog/](mdc:app/[lang]/blog/) - Language-specific blog content
- [app/blog/](mdc:app/blog/) - Root blog content
- [app/blog/[slug]/](mdc:app/blog/[slug]/) - Individual blog posts
- [app/blog/category/[category]/](mdc:app/blog/category/[category]/) - Blog category pages
- [app/blog/tag/[tag]/](mdc:app/blog/tag/[tag]/) - Blog tag pages

The blog includes specific content types:

- [app/[lang]/blog/case-studies/](mdc:app/[lang]/blog/case-studies/) - Case studies
- [app/[lang]/blog/news/](mdc:app/[lang]/blog/news/) - News articles
