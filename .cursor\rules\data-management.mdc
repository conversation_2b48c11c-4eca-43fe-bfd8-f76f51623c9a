---
description:
globs:
alwaysApply: false
---
# 数据管理

本项目的数据管理涉及产品、分类、内容和用户等多个方面。

## 数据模型
- `/models/` - 数据模型定义目录

## 数据库操作
- `/app/api/db-init/` - 数据库初始化
- `/app/api/db-setup/` - 数据库设置
- `/app/api/db-test/` - 数据库测试

## 数据管理脚本
- [add-categories.js](mdc:add-categories.js) - 添加分类
- [add-featured-categories.js](mdc:add-featured-categories.js) - 添加精选分类
- [check-categories.js](mdc:check-categories.js) - 检查分类
- [check-products.js](mdc:check-products.js) - 检查产品
- [update-product-category.js](mdc:update-product-category.js) - 更新产品分类
- [update-product-fields.js](mdc:update-product-fields.js) - 更新产品字段
- [update-schema.js](mdc:update-schema.js) - 更新数据库模式

## 产品数据
产品数据分为多个分类：
- 室内游乐场 (Indoor Playground)
- 蹦床公园 (Trampoline Park)
- 按面积分类 (100-500㎡, 500-1000㎡, 1000+㎡)

## 管理后台
- `/pages/admin/` - 管理后台页面
  - `/pages/admin/categories/` - 分类管理
  - `/pages/admin/products/` - 产品管理
  - `/pages/admin/content/` - 内容管理
  - `/pages/admin/users/` - 用户管理
  - `/pages/admin/settings/` - 设置管理
