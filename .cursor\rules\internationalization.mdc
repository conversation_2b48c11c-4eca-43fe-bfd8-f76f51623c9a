---
description:
globs:
alwaysApply: false
---
# Internationalization

This project supports multiple languages with a robust internationalization structure.

## Language Implementation

The site implements localization through:

- Dynamic [lang] route parameter: [app/[lang]/](mdc:app/[lang]/) 
- Language-specific directories: [app/en/](mdc:app/en/) (English) and [app/zh/](mdc:app/zh/) (Chinese)
- Dictionary files: [app/dictionaries/](mdc:app/dictionaries/) 

## Routing Structure

The internationalization affects all main content areas:

- [app/[lang]/products/](mdc:app/[lang]/products/) - Localized product pages
- [app/[lang]/collections/](mdc:app/[lang]/collections/) - Localized collection pages
- [app/[lang]/pages/](mdc:app/[lang]/pages/) - Localized static content
- [app/[lang]/blog/](mdc:app/[lang]/blog/) - Localized blog content

## Language Detection

Language detection and routing is handled by the middleware:
- [middleware.ts](mdc:middleware.ts) - Handles language detection and routing
