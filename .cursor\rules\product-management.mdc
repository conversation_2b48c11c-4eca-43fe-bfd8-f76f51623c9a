---
description:
globs:
alwaysApply: false
---
# Product Management

This e-commerce site organizes products in both individual listings and collections.

## Product Structure

Products are organized by:

- [app/[lang]/products/](mdc:app/[lang]/products/) - Main product routes with language support
- [app/[lang]/products/[slug]/](mdc:app/[lang]/products/[slug]/) - Individual product pages
- [app/products/[slug]/](mdc:app/products/[slug]/) - Root product pages

## Collections

Products are grouped into collections:

- [app/[lang]/collections/](mdc:app/[lang]/collections/) - Collections with language support
- [app/[lang]/collections/[slug]/](mdc:app/[lang]/collections/[slug]/) - Individual collections
- [app/collections/](mdc:app/collections/) - Root collection pages

## Product Categories

Products are organized by size categories:
- [app/[lang]/products/100-500-sqm/](mdc:app/[lang]/products/100-500-sqm/) - 100-500 square meter products
- [app/[lang]/products/500-1000-sqm/](mdc:app/[lang]/products/500-1000-sqm/) - 500-1000 square meter products
- [app/[lang]/products/1000-plus-sqm/](mdc:app/[lang]/products/1000-plus-sqm/) - 1000+ square meter products

And by type:
- [app/[lang]/products/indoor-playground/](mdc:app/[lang]/products/indoor-playground/) - Indoor playground products
- [app/[lang]/products/trampoline-park/](mdc:app/[lang]/products/trampoline-park/) - Trampoline park products
