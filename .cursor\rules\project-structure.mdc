---
description:
globs:
alwaysApply: false
---
# 项目结构说明

本项目是基于Next.js的跨境电子商务网站，主要销售室内游乐场和蹦床公园设备。

## 核心目录
- `app/` - 使用Next.js App Router的主要应用代码
- `pages/` - 使用Next.js Pages Router的部分页面和API路由
- `components/` - 通用组件
- `styles/` - 样式文件
- `public/` - 静态资源
- `lib/` - 辅助库和功能
- `models/` - 数据模型定义

## 主要配置文件
- [next.config.js](mdc:next.config.js) - Next.js配置
- [tailwind.config.js](mdc:tailwind.config.js) - Tailwind CSS配置
- [package.json](mdc:package.json) - 项目依赖和脚本
