# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js构建输出
.next/
out/

# 生产构建
build/
dist/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
*.log
logs/

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
.nyc_output

# 依赖锁定文件（在容器中重新生成）
package-lock.json
yarn.lock

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git相关
.git/
.gitignore
.gitattributes

# 测试文件
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# 文档文件
README.md
CHANGELOG.md
LICENSE
*.md

# Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD配置
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# 临时文件
tmp/
temp/

# 备份文件
*.bak
*.backup

# 开发工具配置
.eslintrc*
.prettierrc*
.editorconfig
tsconfig.json
jest.config.js

# 部署脚本
deploy.sh
scripts/

# 监控和日志
monitoring/
logs/
