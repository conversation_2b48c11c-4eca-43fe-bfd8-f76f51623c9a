# 生产环境配置
NODE_ENV=production

# 数据库配置
DATABASE_URL=*************************************************/ecommerce
POSTGRES_DB=ecommerce
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password

# Redis配置
REDIS_URL=redis://redis:6379

# Next.js配置
NEXTAUTH_SECRET=your_nextauth_secret_key
NEXTAUTH_URL=https://yourdomain.com

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# 文件上传配置
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760

# API配置
API_BASE_URL=https://yourdomain.com/api

# 监控配置
ENABLE_MONITORING=true
LOG_LEVEL=info

# 安全配置
CORS_ORIGIN=https://yourdomain.com
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000
