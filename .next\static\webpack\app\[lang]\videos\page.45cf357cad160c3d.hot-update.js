"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lang]/videos/page",{

/***/ "(app-pages-browser)/./app/components/VideoGallery.tsx":
/*!*****************************************!*\
  !*** ./app/components/VideoGallery.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VideoGallery; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Maximize_Pause_Play_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Maximize,Pause,Play,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Maximize_Pause_Play_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Maximize,Pause,Play,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Maximize_Pause_Play_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Maximize,Pause,Play,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Maximize_Pause_Play_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Maximize,Pause,Play,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Maximize_Pause_Play_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Maximize,Pause,Play,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Maximize_Pause_Play_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Maximize,Pause,Play,Volume2,VolumeX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize.js\");\n/* harmony import */ var _utils_useTranslation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/useTranslation */ \"(app-pages-browser)/./app/utils/useTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst videos = [\n    {\n        id: \"10\",\n        title: \"咯咯魔法椅\",\n        title_en: \"Magic Chair\",\n        duration: \"2:15\",\n        category: \"儿童娱乐\",\n        category_en: \"Children Entertainment\",\n        aspectRatio: \"portrait\",\n        resolution: \"1080x1920\",\n        videoUrl: \"/videos/13395311668128730.mp4\"\n    },\n    {\n        id: \"11\",\n        title: \"重力玩家\",\n        title_en: \"Gravity Player\",\n        duration: \"3:24\",\n        category: \"体感运动\",\n        category_en: \"Motion Sensing\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395311687228488.mp4\"\n    },\n    {\n        id: \"12\",\n        title: \"水跃星空\",\n        title_en: \"Water Jump Starry Sky\",\n        duration: \"2:45\",\n        category: \"互动投影\",\n        category_en: \"Interactive Projection\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395311722426731.mp4\"\n    },\n    {\n        id: \"13\",\n        title: \"宇宙墙面\",\n        title_en: \"Universe Wall\",\n        duration: \"4:12\",\n        category: \"墙面互动\",\n        category_en: \"Wall Interactive\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395311740172146.mp4\"\n    },\n    {\n        id: \"14\",\n        title: \"互动砸球\",\n        title_en: \"Interactive Ball Smashing\",\n        duration: \"3:56\",\n        category: \"互动娱乐\",\n        category_en: \"Interactive Entertainment\",\n        aspectRatio: \"portrait\",\n        resolution: \"1080x1920\",\n        videoUrl: \"/videos/13395311756835607.mp4\"\n    },\n    {\n        id: \"15\",\n        title: \"互动滑梯\",\n        title_en: \"Interactive Slide\",\n        duration: \"2:18\",\n        category: \"儿童娱乐\",\n        category_en: \"Children Entertainment\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395311785464099.mp4\"\n    },\n    {\n        id: \"16\",\n        title: \"神笔绘画\",\n        title_en: \"Magic Brush Painting\",\n        duration: \"5:03\",\n        category: \"AR教育\",\n        category_en: \"AR Education\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395311798953886.mp4\"\n    },\n    {\n        id: \"17\",\n        title: \"AR智慧教室\",\n        title_en: \"AR Smart Classroom\",\n        duration: \"3:37\",\n        category: \"AR教育\",\n        category_en: \"AR Education\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395311818043592.mp4\"\n    },\n    {\n        id: \"18\",\n        title: \"互动沙池\",\n        title_en: \"Interactive Sand Pool\",\n        duration: \"4:28\",\n        category: \"儿童互动\",\n        category_en: \"Children Interactive\",\n        aspectRatio: \"portrait\",\n        resolution: \"1080x1920\",\n        videoUrl: \"/videos/13395311832319856.mp4\"\n    },\n    {\n        id: \"19\",\n        title: \"AR沙桌\",\n        title_en: \"AR Sand Table\",\n        duration: \"6:15\",\n        category: \"AR技术\",\n        category_en: \"AR Technology\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395311848006869.mp4\"\n    },\n    {\n        id: \"20\",\n        title: \"互动滑板\",\n        title_en: \"Interactive Skateboard\",\n        duration: \"2:15\",\n        category: \"体感运动\",\n        category_en: \"Motion Sensing\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395311866301039.mp4\"\n    },\n    {\n        id: \"21\",\n        title: \"益智流水墙\",\n        title_en: \"Educational Water Wall\",\n        duration: \"3:24\",\n        category: \"智能教育\",\n        category_en: \"Smart Education\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395311897107446.mp4\"\n    },\n    {\n        id: \"22\",\n        title: \"体感大师\",\n        title_en: \"Motion Sensing Master\",\n        duration: \"2:45\",\n        category: \"体感运动\",\n        category_en: \"Motion Sensing\",\n        aspectRatio: \"portrait\",\n        resolution: \"1080x1920\",\n        videoUrl: \"/videos/13395311945076997.mp4\"\n    },\n    {\n        id: \"23\",\n        title: \"体感攀岩\",\n        title_en: \"Motion Sensing Climbing\",\n        duration: \"4:12\",\n        category: \"体感运动\",\n        category_en: \"Motion Sensing\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395311986456123.mp4\"\n    },\n    {\n        id: \"24\",\n        title: \"跳跃格子\",\n        title_en: \"Jumping Grid\",\n        duration: \"3:56\",\n        category: \"儿童互动\",\n        category_en: \"Children Interactive\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395312019160663.mp4\"\n    },\n    {\n        id: \"25\",\n        title: \"运动一体机\",\n        title_en: \"Sports All-in-One\",\n        duration: \"2:18\",\n        category: \"体感运动\",\n        category_en: \"Motion Sensing\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395312071571352.mp4\"\n    },\n    {\n        id: \"26\",\n        title: \"移动射箭\",\n        title_en: \"Mobile Archery\",\n        duration: \"5:03\",\n        category: \"体感运动\",\n        category_en: \"Motion Sensing\",\n        aspectRatio: \"portrait\",\n        resolution: \"1080x1920\",\n        videoUrl: \"/videos/13395312104138908.mp4\"\n    },\n    {\n        id: \"27\",\n        title: \"实弹射击\",\n        title_en: \"Live Ammunition Shooting\",\n        duration: \"3:37\",\n        category: \"体感运动\",\n        category_en: \"Motion Sensing\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395312143443916.mp4\"\n    },\n    {\n        id: \"28\",\n        title: \"互动足球\",\n        title_en: \"Interactive Football\",\n        duration: \"4:28\",\n        category: \"体感运动\",\n        category_en: \"Motion Sensing\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395312156081604.mp4\"\n    },\n    {\n        id: \"29\",\n        title: \"魔力滚球\",\n        title_en: \"Magic Rolling Ball\",\n        duration: \"6:15\",\n        category: \"互动娱乐\",\n        category_en: \"Interactive Entertainment\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395312211986425.mp4\"\n    },\n    {\n        id: \"30\",\n        title: \"人体闯关\",\n        title_en: \"Human Adventure\",\n        duration: \"2:15\",\n        category: \"体感运动\",\n        category_en: \"Motion Sensing\",\n        aspectRatio: \"portrait\",\n        resolution: \"1080x1920\",\n        videoUrl: \"/videos/13395312244333114.mp4\"\n    },\n    {\n        id: \"31\",\n        title: \"体感蹦床\",\n        title_en: \"Motion Sensing Trampoline\",\n        duration: \"3:24\",\n        category: \"体感运动\",\n        category_en: \"Motion Sensing\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395312273652728.mp4\"\n    },\n    {\n        id: \"32\",\n        title: \"动感单车\",\n        title_en: \"Dynamic Bicycle\",\n        duration: \"2:45\",\n        category: \"体感运动\",\n        category_en: \"Motion Sensing\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395312320307968.mp4\"\n    },\n    {\n        id: \"33\",\n        title: \"模拟拳击\",\n        title_en: \"Simulated Boxing\",\n        duration: \"4:12\",\n        category: \"体感运动\",\n        category_en: \"Motion Sensing\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395312403438645.mp4\"\n    },\n    {\n        id: \"34\",\n        title: \"模拟网球\",\n        title_en: \"Simulated Tennis\",\n        duration: \"3:56\",\n        category: \"体感运动\",\n        category_en: \"Motion Sensing\",\n        aspectRatio: \"portrait\",\n        resolution: \"1080x1920\",\n        videoUrl: \"/videos/13395312421142831.mp4\"\n    },\n    {\n        id: \"35\",\n        title: \"全息宴会厅\",\n        title_en: \"Holographic Banquet Hall\",\n        duration: \"2:18\",\n        category: \"全息技术\",\n        category_en: \"Holographic Technology\",\n        aspectRatio: \"landscape\",\n        resolution: \"1920x1080\",\n        videoUrl: \"/videos/13395312446431334.mp4\"\n    }\n];\nfunction VideoGallery(param) {\n    let { className = \"\" } = param;\n    _s();\n    const { t, locale } = (0,_utils_useTranslation__WEBPACK_IMPORTED_MODULE_3__.useTranslation)();\n    const [selectedVideo, setSelectedVideo] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [volume, setVolume] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showControls, setShowControls] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(t(\"video_gallery.filter_all\", {\n        fallback: \"全部\"\n    }));\n    const [selectedFormat, setSelectedFormat] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(t(\"video_gallery.filter_all\", {\n        fallback: \"全部\"\n    }));\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const controlsTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const getLocalizedCategories = ()=>{\n        const allCategories = videos.map((v)=>locale === \"zh\" ? v.category : v.category_en);\n        return [\n            t(\"video_gallery.filter_all\", {\n                fallback: \"全部\"\n            }),\n            ...Array.from(new Set(allCategories))\n        ];\n    };\n    const getLocalizedFormats = ()=>[\n            t(\"video_gallery.filter_all\", {\n                fallback: \"全部\"\n            }),\n            t(\"video_gallery.filter_landscape\", {\n                fallback: \"横屏视频\"\n            }),\n            t(\"video_gallery.filter_portrait\", {\n                fallback: \"竖屏视频\"\n            })\n        ];\n    const filteredVideos = videos.filter((video)=>{\n        const videoCategory = locale === \"zh\" ? video.category : video.category_en;\n        const allText = t(\"video_gallery.filter_all\", {\n            fallback: \"全部\"\n        });\n        const landscapeText = t(\"video_gallery.filter_landscape\", {\n            fallback: \"横屏视频\"\n        });\n        const portraitText = t(\"video_gallery.filter_portrait\", {\n            fallback: \"竖屏视频\"\n        });\n        const categoryMatch = selectedCategory === allText || videoCategory === selectedCategory;\n        const formatMatch = selectedFormat === allText || selectedFormat === landscapeText && video.aspectRatio === \"landscape\" || selectedFormat === portraitText && video.aspectRatio === \"portrait\";\n        return categoryMatch && formatMatch;\n    }).sort((a, b)=>{\n        // 横屏视频排在前面，竖屏视频排在后面\n        if (a.aspectRatio === \"landscape\" && b.aspectRatio === \"portrait\") return -1;\n        if (a.aspectRatio === \"portrait\" && b.aspectRatio === \"landscape\") return 1;\n        return 0;\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const video = videoRef.current;\n        if (!video) return;\n        const updateTime = ()=>setCurrentTime(video.currentTime);\n        const updateDuration = ()=>setDuration(video.duration);\n        video.addEventListener(\"timeupdate\", updateTime);\n        video.addEventListener(\"loadedmetadata\", updateDuration);\n        video.addEventListener(\"ended\", ()=>setIsPlaying(false));\n        return ()=>{\n            video.removeEventListener(\"timeupdate\", updateTime);\n            video.removeEventListener(\"loadedmetadata\", updateDuration);\n            video.removeEventListener(\"ended\", ()=>setIsPlaying(false));\n        };\n    }, [\n        selectedVideo\n    ]);\n    const openVideo = (video)=>{\n        setSelectedVideo(video);\n        setIsPlaying(false);\n        setCurrentTime(0);\n    };\n    const closeVideo = ()=>{\n        setSelectedVideo(null);\n        setIsPlaying(false);\n        if (videoRef.current) {\n            videoRef.current.pause();\n        }\n    };\n    const togglePlay = ()=>{\n        const video = videoRef.current;\n        if (!video) return;\n        if (isPlaying) {\n            video.pause();\n        } else {\n            video.play();\n        }\n        setIsPlaying(!isPlaying);\n    };\n    const handleProgressChange = (value)=>{\n        const video = videoRef.current;\n        if (!video) return;\n        const newTime = value[0] / 100 * duration;\n        video.currentTime = newTime;\n        setCurrentTime(newTime);\n    };\n    const handleVolumeChange = (value)=>{\n        const video = videoRef.current;\n        if (!video) return;\n        const newVolume = value[0] / 100;\n        video.volume = newVolume;\n        setVolume(newVolume);\n        setIsMuted(newVolume === 0);\n    };\n    const toggleMute = ()=>{\n        const video = videoRef.current;\n        if (!video) return;\n        if (isMuted) {\n            video.volume = volume;\n            setIsMuted(false);\n        } else {\n            video.volume = 0;\n            setIsMuted(true);\n        }\n    };\n    const toggleFullscreen = ()=>{\n        const video = videoRef.current;\n        if (!video) return;\n        if (document.fullscreenElement) {\n            document.exitFullscreen();\n        } else {\n            video.requestFullscreen();\n        }\n    };\n    const handleMouseMove = ()=>{\n        setShowControls(true);\n        if (controlsTimeoutRef.current) {\n            clearTimeout(controlsTimeoutRef.current);\n        }\n        controlsTimeoutRef.current = setTimeout(()=>{\n            if (isPlaying) {\n                setShowControls(false);\n            }\n        }, 3000);\n    };\n    const formatTime = (time)=>{\n        const minutes = Math.floor(time / 60);\n        const seconds = Math.floor(time % 60);\n        return \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, \"0\"));\n    };\n    const progress = duration > 0 ? currentTime / duration * 100 : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-7xl mx-auto p-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                        children: t(\"video_gallery.title\", {\n                            fallback: \"产品演示视频\"\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600\",\n                        children: t(\"video_gallery.subtitle\", {\n                            fallback: \"探索我们完整的交互式技术解决方案\"\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                lineNumber: 467,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-53e079cf42f247ee\" + \" \" + \"video-grid\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"53e079cf42f247ee\",\n                        children: \".video-grid.jsx-53e079cf42f247ee{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:1.5rem;-webkit-box-align:start;-webkit-align-items:start;-moz-box-align:start;-ms-flex-align:start;align-items:start;justify-items:center}@media(max-width:1024px){.video-grid.jsx-53e079cf42f247ee{grid-template-columns:repeat(auto-fill,minmax(240px,1fr));gap:1rem}}@media(max-width:768px){.video-grid.jsx-53e079cf42f247ee{grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:.75rem}}@media(max-width:640px){.video-grid.jsx-53e079cf42f247ee{grid-template-columns:repeat(2,1fr);gap:.5rem}}\"\n                    }, void 0, false, void 0, this),\n                    filteredVideos.map((video)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            onClick: ()=>openVideo(video),\n                            className: \"jsx-53e079cf42f247ee\" + \" \" + \"relative group cursor-pointer overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gray-100 \".concat(video.aspectRatio === \"landscape\" ? \"aspect-video\" : \"aspect-[9/16]\", \" w-full max-w-xs\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                    preload: \"metadata\",\n                                    muted: true,\n                                    playsInline: true,\n                                    className: \"jsx-53e079cf42f247ee\" + \" \" + \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                        src: video.videoUrl,\n                                        type: \"video/mp4\",\n                                        className: \"jsx-53e079cf42f247ee\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-53e079cf42f247ee\" + \" \" + \"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-53e079cf42f247ee\" + \" \" + \"w-16 h-16 bg-white/90 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Maximize_Pause_Play_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-900 ml-1\",\n                                            fill: \"currentColor\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-53e079cf42f247ee\" + \" \" + \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent p-2 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-53e079cf42f247ee\" + \" \" + \"font-medium mb-1 line-clamp-2 text-xs leading-tight\",\n                                            children: video.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-53e079cf42f247ee\" + \" \" + \"flex items-center justify-between text-xs text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-53e079cf42f247ee\" + \" \" + \"text-xs\",\n                                                    children: [\n                                                        video.views,\n                                                        \" views\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-53e079cf42f247ee\" + \" \" + \"text-xs\",\n                                                    children: video.uploadDate\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, video.id, true, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                lineNumber: 479,\n                columnNumber: 7\n            }, this),\n            selectedVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative bg-black rounded-lg overflow-hidden \".concat(selectedVideo.aspectRatio === \"portrait\" ? \"max-w-2xl\" : \"max-w-5xl\", \" w-full\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"absolute top-4 right-4 z-50 text-white hover:bg-white/20 rounded-full w-10 h-10 flex items-center justify-center\",\n                            onClick: closeVideo,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Maximize_Pause_Play_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative group \".concat(selectedVideo.aspectRatio === \"landscape\" ? \"aspect-video\" : \"aspect-[9/16]\"),\n                            onMouseMove: handleMouseMove,\n                            onMouseLeave: ()=>isPlaying && setShowControls(false),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                    ref: videoRef,\n                                    className: \"w-full h-full object-cover\",\n                                    onClick: togglePlay,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                            src: selectedVideo.videoUrl || \"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4\",\n                                            type: \"video/mp4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, this),\n                                        t(\"video_gallery.unsupported_browser\", {\n                                            fallback: \"您的浏览器不支持视频播放。\"\n                                        })\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 15\n                                }, this),\n                                !isPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center bg-black/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-20 h-20 rounded-full bg-white/90 hover:bg-white text-black shadow-lg flex items-center justify-center\",\n                                        onClick: togglePlay,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Maximize_Pause_Play_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-8 h-8 ml-1\",\n                                            fill: \"currentColor\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent transition-opacity duration-300 \".concat(showControls ? \"opacity-100\" : \"opacity-0\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"0\",\n                                                max: \"100\",\n                                                value: progress,\n                                                onChange: (e)=>handleProgressChange([\n                                                        parseFloat(e.target.value)\n                                                    ]),\n                                                className: \"w-full h-1 bg-white/30 rounded-lg appearance-none cursor-pointer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between px-6 pb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"text-white hover:bg-white/20 w-10 h-10 rounded flex items-center justify-center\",\n                                                            onClick: togglePlay,\n                                                            children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Maximize_Pause_Play_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 36\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Maximize_Pause_Play_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 68\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"text-white hover:bg-white/20 w-10 h-10 rounded flex items-center justify-center\",\n                                                                    onClick: toggleMute,\n                                                                    children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Maximize_Pause_Play_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        className: \"w-5 h-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                                        lineNumber: 624,\n                                                                        columnNumber: 36\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Maximize_Pause_Play_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"w-5 h-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                                        lineNumber: 624,\n                                                                        columnNumber: 70\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                                    lineNumber: 620,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0\",\n                                                                        max: \"100\",\n                                                                        value: isMuted ? 0 : volume * 100,\n                                                                        onChange: (e)=>handleVolumeChange([\n                                                                                parseFloat(e.target.value)\n                                                                            ]),\n                                                                        className: \"w-full h-1 bg-white/30 rounded-lg appearance-none cursor-pointer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white text-sm font-medium\",\n                                                            children: [\n                                                                formatTime(currentTime),\n                                                                \" / \",\n                                                                formatTime(duration)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"hidden md:block text-white text-lg font-medium max-w-md truncate\",\n                                                            children: locale === \"zh\" ? selectedVideo.title : selectedVideo.title_en\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white text-sm bg-white/20 px-2 py-1 rounded\",\n                                                            children: selectedVideo.resolution\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-white hover:bg-white/20 w-10 h-10 rounded flex items-center justify-center\",\n                                                    onClick: toggleFullscreen,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Maximize_Pause_Play_Volume2_VolumeX_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                    lineNumber: 548,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n                lineNumber: 547,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\VideoGallery.tsx\",\n        lineNumber: 465,\n        columnNumber: 5\n    }, this);\n}\n_s(VideoGallery, \"2mZzoyZkY+ovN7qv/PjwGyCM2RY=\", false, function() {\n    return [\n        _utils_useTranslation__WEBPACK_IMPORTED_MODULE_3__.useTranslation\n    ];\n});\n_c = VideoGallery;\nvar _c;\n$RefreshReg$(_c, \"VideoGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9jb21wb25lbnRzL1ZpZGVvR2FsbGVyeS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRDtBQUNzQjtBQUNqQjtBQWN4RCxNQUFNVSxTQUFrQjtJQUN0QjtRQUNFQyxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0E7UUFDRVIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtJQUNBO1FBQ0VSLElBQUk7UUFDSkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7SUFDQTtRQUNFUixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0E7UUFDRVIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtJQUNBO1FBQ0VSLElBQUk7UUFDSkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7SUFDQTtRQUNFUixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0E7UUFDRVIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtJQUNBO1FBQ0VSLElBQUk7UUFDSkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7SUFDQTtRQUNFUixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0E7UUFDRVIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtJQUNBO1FBQ0VSLElBQUk7UUFDSkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7SUFDQTtRQUNFUixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0E7UUFDRVIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtJQUNBO1FBQ0VSLElBQUk7UUFDSkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7SUFDQTtRQUNFUixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0E7UUFDRVIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtJQUNBO1FBQ0VSLElBQUk7UUFDSkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7SUFDQTtRQUNFUixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0E7UUFDRVIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtJQUNBO1FBQ0VSLElBQUk7UUFDSkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7SUFDQTtRQUNFUixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0E7UUFDRVIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtJQUNBO1FBQ0VSLElBQUk7UUFDSkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsWUFBWTtRQUNaQyxVQUFVO0lBQ1o7SUFDQTtRQUNFUixJQUFJO1FBQ0pDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsVUFBVTtJQUNaO0lBQ0E7UUFDRVIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BDLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxZQUFZO1FBQ1pDLFVBQVU7SUFDWjtDQUNEO0FBTWMsU0FBU0MsYUFBYSxLQUFxQztRQUFyQyxFQUFFQyxZQUFZLEVBQUUsRUFBcUIsR0FBckM7O0lBQ25DLE1BQU0sRUFBRUMsQ0FBQyxFQUFFQyxNQUFNLEVBQUUsR0FBR2QscUVBQWNBO0lBQ3BDLE1BQU0sQ0FBQ2UsZUFBZUMsaUJBQWlCLEdBQUd6QiwrQ0FBUUEsQ0FBZTtJQUNqRSxNQUFNLENBQUMwQixXQUFXQyxhQUFhLEdBQUczQiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUM0QixhQUFhQyxlQUFlLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNjLFVBQVVnQixZQUFZLEdBQUc5QiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUMrQixRQUFRQyxVQUFVLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUNyQyxNQUFNLENBQUNpQyxTQUFTQyxXQUFXLEdBQUdsQywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNtQyxjQUFjQyxnQkFBZ0IsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ3FDLGtCQUFrQkMsb0JBQW9CLEdBQUd0QywrQ0FBUUEsQ0FBU3NCLEVBQUUsNEJBQTRCO1FBQUVpQixVQUFVO0lBQUs7SUFDaEgsTUFBTSxDQUFDQyxnQkFBZ0JDLGtCQUFrQixHQUFHekMsK0NBQVFBLENBQVNzQixFQUFFLDRCQUE0QjtRQUFFaUIsVUFBVTtJQUFLO0lBRTVHLE1BQU1HLFdBQVd6Qyw2Q0FBTUEsQ0FBbUI7SUFDMUMsTUFBTTBDLHFCQUFxQjFDLDZDQUFNQSxDQUF3QjtJQUV6RCxNQUFNMkMseUJBQXlCO1FBQzdCLE1BQU1DLGdCQUFnQm5DLE9BQU9vQyxHQUFHLENBQUMsQ0FBQ0MsSUFBTXhCLFdBQVcsT0FBT3dCLEVBQUVoQyxRQUFRLEdBQUdnQyxFQUFFL0IsV0FBVztRQUNwRixPQUFPO1lBQUNNLEVBQUUsNEJBQTRCO2dCQUFFaUIsVUFBVTtZQUFLO2VBQU9TLE1BQU1DLElBQUksQ0FBQyxJQUFJQyxJQUFJTDtTQUFnQjtJQUNuRztJQUVBLE1BQU1NLHNCQUFzQixJQUFNO1lBQ2hDN0IsRUFBRSw0QkFBNEI7Z0JBQUVpQixVQUFVO1lBQUs7WUFDL0NqQixFQUFFLGtDQUFrQztnQkFBRWlCLFVBQVU7WUFBTztZQUN2RGpCLEVBQUUsaUNBQWlDO2dCQUFFaUIsVUFBVTtZQUFPO1NBQ3ZEO0lBRUQsTUFBTWEsaUJBQWlCMUMsT0FBTzJDLE1BQU0sQ0FBQyxDQUFDQztRQUNwQyxNQUFNQyxnQkFBZ0JoQyxXQUFXLE9BQU8rQixNQUFNdkMsUUFBUSxHQUFHdUMsTUFBTXRDLFdBQVc7UUFDMUUsTUFBTXdDLFVBQVVsQyxFQUFFLDRCQUE0QjtZQUFFaUIsVUFBVTtRQUFLO1FBQy9ELE1BQU1rQixnQkFBZ0JuQyxFQUFFLGtDQUFrQztZQUFFaUIsVUFBVTtRQUFPO1FBQzdFLE1BQU1tQixlQUFlcEMsRUFBRSxpQ0FBaUM7WUFBRWlCLFVBQVU7UUFBTztRQUUzRSxNQUFNb0IsZ0JBQWdCdEIscUJBQXFCbUIsV0FBV0Qsa0JBQWtCbEI7UUFDeEUsTUFBTXVCLGNBQ0pwQixtQkFBbUJnQixXQUNsQmhCLG1CQUFtQmlCLGlCQUFpQkgsTUFBTXJDLFdBQVcsS0FBSyxlQUMxRHVCLG1CQUFtQmtCLGdCQUFnQkosTUFBTXJDLFdBQVcsS0FBSztRQUM1RCxPQUFPMEMsaUJBQWlCQztJQUMxQixHQUFHQyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0M7UUFDVixvQkFBb0I7UUFDcEIsSUFBSUQsRUFBRTdDLFdBQVcsS0FBSyxlQUFlOEMsRUFBRTlDLFdBQVcsS0FBSyxZQUFZLE9BQU8sQ0FBQztRQUMzRSxJQUFJNkMsRUFBRTdDLFdBQVcsS0FBSyxjQUFjOEMsRUFBRTlDLFdBQVcsS0FBSyxhQUFhLE9BQU87UUFDMUUsT0FBTztJQUNUO0lBRUFmLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTW9ELFFBQVFaLFNBQVNzQixPQUFPO1FBQzlCLElBQUksQ0FBQ1YsT0FBTztRQUVaLE1BQU1XLGFBQWEsSUFBTXBDLGVBQWV5QixNQUFNMUIsV0FBVztRQUN6RCxNQUFNc0MsaUJBQWlCLElBQU1wQyxZQUFZd0IsTUFBTXhDLFFBQVE7UUFFdkR3QyxNQUFNYSxnQkFBZ0IsQ0FBQyxjQUFjRjtRQUNyQ1gsTUFBTWEsZ0JBQWdCLENBQUMsa0JBQWtCRDtRQUN6Q1osTUFBTWEsZ0JBQWdCLENBQUMsU0FBUyxJQUFNeEMsYUFBYTtRQUVuRCxPQUFPO1lBQ0wyQixNQUFNYyxtQkFBbUIsQ0FBQyxjQUFjSDtZQUN4Q1gsTUFBTWMsbUJBQW1CLENBQUMsa0JBQWtCRjtZQUM1Q1osTUFBTWMsbUJBQW1CLENBQUMsU0FBUyxJQUFNekMsYUFBYTtRQUN4RDtJQUNGLEdBQUc7UUFBQ0g7S0FBYztJQUVsQixNQUFNNkMsWUFBWSxDQUFDZjtRQUNqQjdCLGlCQUFpQjZCO1FBQ2pCM0IsYUFBYTtRQUNiRSxlQUFlO0lBQ2pCO0lBRUEsTUFBTXlDLGFBQWE7UUFDakI3QyxpQkFBaUI7UUFDakJFLGFBQWE7UUFDYixJQUFJZSxTQUFTc0IsT0FBTyxFQUFFO1lBQ3BCdEIsU0FBU3NCLE9BQU8sQ0FBQ08sS0FBSztRQUN4QjtJQUNGO0lBRUEsTUFBTUMsYUFBYTtRQUNqQixNQUFNbEIsUUFBUVosU0FBU3NCLE9BQU87UUFDOUIsSUFBSSxDQUFDVixPQUFPO1FBRVosSUFBSTVCLFdBQVc7WUFDYjRCLE1BQU1pQixLQUFLO1FBQ2IsT0FBTztZQUNMakIsTUFBTW1CLElBQUk7UUFDWjtRQUNBOUMsYUFBYSxDQUFDRDtJQUNoQjtJQUVBLE1BQU1nRCx1QkFBdUIsQ0FBQ0M7UUFDNUIsTUFBTXJCLFFBQVFaLFNBQVNzQixPQUFPO1FBQzlCLElBQUksQ0FBQ1YsT0FBTztRQUVaLE1BQU1zQixVQUFVLEtBQU0sQ0FBQyxFQUFFLEdBQUcsTUFBTzlEO1FBQ25Dd0MsTUFBTTFCLFdBQVcsR0FBR2dEO1FBQ3BCL0MsZUFBZStDO0lBQ2pCO0lBRUEsTUFBTUMscUJBQXFCLENBQUNGO1FBQzFCLE1BQU1yQixRQUFRWixTQUFTc0IsT0FBTztRQUM5QixJQUFJLENBQUNWLE9BQU87UUFFWixNQUFNd0IsWUFBWUgsS0FBSyxDQUFDLEVBQUUsR0FBRztRQUM3QnJCLE1BQU12QixNQUFNLEdBQUcrQztRQUNmOUMsVUFBVThDO1FBQ1Y1QyxXQUFXNEMsY0FBYztJQUMzQjtJQUVBLE1BQU1DLGFBQWE7UUFDakIsTUFBTXpCLFFBQVFaLFNBQVNzQixPQUFPO1FBQzlCLElBQUksQ0FBQ1YsT0FBTztRQUVaLElBQUlyQixTQUFTO1lBQ1hxQixNQUFNdkIsTUFBTSxHQUFHQTtZQUNmRyxXQUFXO1FBQ2IsT0FBTztZQUNMb0IsTUFBTXZCLE1BQU0sR0FBRztZQUNmRyxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU04QyxtQkFBbUI7UUFDdkIsTUFBTTFCLFFBQVFaLFNBQVNzQixPQUFPO1FBQzlCLElBQUksQ0FBQ1YsT0FBTztRQUVaLElBQUkyQixTQUFTQyxpQkFBaUIsRUFBRTtZQUM5QkQsU0FBU0UsY0FBYztRQUN6QixPQUFPO1lBQ0w3QixNQUFNOEIsaUJBQWlCO1FBQ3pCO0lBQ0Y7SUFFQSxNQUFNQyxrQkFBa0I7UUFDdEJqRCxnQkFBZ0I7UUFDaEIsSUFBSU8sbUJBQW1CcUIsT0FBTyxFQUFFO1lBQzlCc0IsYUFBYTNDLG1CQUFtQnFCLE9BQU87UUFDekM7UUFDQXJCLG1CQUFtQnFCLE9BQU8sR0FBR3VCLFdBQVc7WUFDdEMsSUFBSTdELFdBQVc7Z0JBQ2JVLGdCQUFnQjtZQUNsQjtRQUNGLEdBQUc7SUFDTDtJQUVBLE1BQU1vRCxhQUFhLENBQUNDO1FBQ2xCLE1BQU1DLFVBQVVDLEtBQUtDLEtBQUssQ0FBQ0gsT0FBTztRQUNsQyxNQUFNSSxVQUFVRixLQUFLQyxLQUFLLENBQUNILE9BQU87UUFDbEMsT0FBTyxHQUFjSSxPQUFYSCxTQUFRLEtBQXVDLE9BQXBDRyxRQUFRQyxRQUFRLEdBQUdDLFFBQVEsQ0FBQyxHQUFHO0lBQ3REO0lBRUEsTUFBTUMsV0FBV2xGLFdBQVcsSUFBSSxjQUFlQSxXQUFZLE1BQU07SUFFakUscUJBQ0UsOERBQUNtRjtRQUFJNUUsV0FBVyxnQ0FBMEMsT0FBVkE7OzBCQUU5Qyw4REFBQzRFO2dCQUFJNUUsV0FBVTs7a0NBQ2IsOERBQUM2RTt3QkFBRzdFLFdBQVU7a0NBQ1hDLEVBQUUsdUJBQXVCOzRCQUFFaUIsVUFBVTt3QkFBUzs7Ozs7O2tDQUVqRCw4REFBQzREO3dCQUFFOUUsV0FBVTtrQ0FDVkMsRUFBRSwwQkFBMEI7NEJBQUVpQixVQUFVO3dCQUFtQjs7Ozs7Ozs7Ozs7OzBCQU9oRSw4REFBQzBEOzBEQUFjOzs7Ozs7b0JBNEJaN0MsZUFBZU4sR0FBRyxDQUFDLENBQUNRLHNCQUNuQiw4REFBQzJDOzRCQUtDRyxTQUFTLElBQU0vQixVQUFVZjtzRUFIZCw2SkFFVixPQURDQSxNQUFNckMsV0FBVyxLQUFLLGNBQWMsaUJBQWlCLGlCQUN0RDs7OENBR0MsOERBQUNxQztvQ0FFQytDLFNBQVE7b0NBQ1JDLEtBQUs7b0NBQ0xDLFdBQVc7OEVBSEQ7OENBS1YsNEVBQUNDO3dDQUFPQyxLQUFLbkQsTUFBTW5DLFFBQVE7d0NBQUV1RixNQUFLOzs7Ozs7Ozs7Ozs7OENBSXBDLDhEQUFDVDs4RUFBYzs4Q0FDYiw0RUFBQ0E7a0ZBQWM7a0RBQ2IsNEVBQUM5RixpSEFBSUE7NENBQUNrQixXQUFVOzRDQUE2QnNGLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS3RELDhEQUFDVjs4RUFBYzs7c0RBQ2IsOERBQUNXO3NGQUFhO3NEQUNYdEQsTUFBTTFDLEtBQUs7Ozs7OztzREFFZCw4REFBQ3FGO3NGQUFjOzs4REFDYiw4REFBQ1k7OEZBQWU7O3dEQUFXdkQsTUFBTXdELEtBQUs7d0RBQUM7Ozs7Ozs7OERBQ3ZDLDhEQUFDRDs4RkFBZTs4REFBV3ZELE1BQU15RCxVQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJCQTdCNUN6RCxNQUFNM0MsRUFBRTs7Ozs7Ozs7Ozs7WUFxQ2xCYSwrQkFDQyw4REFBQ3lFO2dCQUFJNUUsV0FBVTswQkFDYiw0RUFBQzRFO29CQUNDNUUsV0FBVyxnREFFVixPQURDRyxjQUFjUCxXQUFXLEtBQUssYUFBYSxjQUFjLGFBQzFEOztzQ0FHRCw4REFBQytGOzRCQUNDM0YsV0FBVTs0QkFDVitFLFNBQVM5QjtzQ0FFVCw0RUFBQzlELGlIQUFDQTtnQ0FBQ2EsV0FBVTs7Ozs7Ozs7Ozs7c0NBSWYsOERBQUM0RTs0QkFDQzVFLFdBQVcsa0JBRVYsT0FEQ0csY0FBY1AsV0FBVyxLQUFLLGNBQWMsaUJBQWlCOzRCQUUvRGdHLGFBQWE1Qjs0QkFDYjZCLGNBQWMsSUFBTXhGLGFBQWFVLGdCQUFnQjs7OENBRWpELDhEQUFDa0I7b0NBQ0M2RCxLQUFLekU7b0NBQ0xyQixXQUFVO29DQUNWK0UsU0FBUzVCOztzREFFVCw4REFBQ2dDOzRDQUNDQyxLQUFLakYsY0FBY0wsUUFBUSxJQUFJOzRDQUMvQnVGLE1BQUs7Ozs7Ozt3Q0FFTnBGLEVBQUUscUNBQXFDOzRDQUFFaUIsVUFBVTt3Q0FBZ0I7Ozs7Ozs7Z0NBSXJFLENBQUNiLDJCQUNBLDhEQUFDdUU7b0NBQUk1RSxXQUFVOzhDQUNiLDRFQUFDMkY7d0NBQ0MzRixXQUFVO3dDQUNWK0UsU0FBUzVCO2tEQUVULDRFQUFDckUsaUhBQUlBOzRDQUFDa0IsV0FBVTs0Q0FBZXNGLE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTTFDLDhEQUFDVjtvQ0FDQzVFLFdBQVcsMEhBQXFLLE9BQTNDYyxlQUFlLGdCQUFnQjs7c0RBR3BLLDhEQUFDOEQ7NENBQUk1RSxXQUFVO3NEQUNiLDRFQUFDK0Y7Z0RBQ0NWLE1BQUs7Z0RBQ0xXLEtBQUk7Z0RBQ0pDLEtBQUk7Z0RBQ0ozQyxPQUFPcUI7Z0RBQ1B1QixVQUFVLENBQUNDLElBQU05QyxxQkFBcUI7d0RBQUMrQyxXQUFXRCxFQUFFRSxNQUFNLENBQUMvQyxLQUFLO3FEQUFFO2dEQUNsRXRELFdBQVU7Ozs7Ozs7Ozs7O3NEQUtkLDhEQUFDNEU7NENBQUk1RSxXQUFVOzs4REFDYiw4REFBQzRFO29EQUFJNUUsV0FBVTs7c0VBQ2IsOERBQUMyRjs0REFDQzNGLFdBQVU7NERBQ1YrRSxTQUFTNUI7c0VBRVI5QywwQkFBWSw4REFBQ3RCLGlIQUFLQTtnRUFBQ2lCLFdBQVU7Ozs7O3FGQUFlLDhEQUFDbEIsaUhBQUlBO2dFQUFDa0IsV0FBVTs7Ozs7Ozs7Ozs7c0VBRy9ELDhEQUFDNEU7NERBQUk1RSxXQUFVOzs4RUFDYiw4REFBQzJGO29FQUNDM0YsV0FBVTtvRUFDVitFLFNBQVNyQjs4RUFFUjlDLHdCQUFVLDhEQUFDM0IsaUhBQU9BO3dFQUFDZSxXQUFVOzs7Ozs2RkFBZSw4REFBQ2hCLGlIQUFPQTt3RUFBQ2dCLFdBQVU7Ozs7Ozs7Ozs7OzhFQUVsRSw4REFBQzRFO29FQUFJNUUsV0FBVTs4RUFDYiw0RUFBQytGO3dFQUNDVixNQUFLO3dFQUNMVyxLQUFJO3dFQUNKQyxLQUFJO3dFQUNKM0MsT0FBTzFDLFVBQVUsSUFBSUYsU0FBUzt3RUFDOUJ3RixVQUFVLENBQUNDLElBQU0zQyxtQkFBbUI7Z0ZBQUM0QyxXQUFXRCxFQUFFRSxNQUFNLENBQUMvQyxLQUFLOzZFQUFFO3dFQUNoRXRELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQUtoQiw4REFBQzRFOzREQUFJNUUsV0FBVTs7Z0VBQ1ptRSxXQUFXNUQ7Z0VBQWE7Z0VBQUk0RCxXQUFXMUU7Ozs7Ozs7c0VBRzFDLDhEQUFDbUY7NERBQUk1RSxXQUFVO3NFQUNaRSxXQUFXLE9BQU9DLGNBQWNaLEtBQUssR0FBR1ksY0FBY1gsUUFBUTs7Ozs7O3NFQUdqRSw4REFBQ29GOzREQUFJNUUsV0FBVTtzRUFBb0RHLGNBQWNOLFVBQVU7Ozs7Ozs7Ozs7Ozs4REFHN0YsOERBQUM4RjtvREFDQzNGLFdBQVU7b0RBQ1YrRSxTQUFTcEI7OERBRVQsNEVBQUN6RSxpSEFBUUE7d0RBQUNjLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVeEM7R0EvVndCRDs7UUFDQVgsaUVBQWNBOzs7S0FEZFciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2NvbXBvbmVudHMvVmlkZW9HYWxsZXJ5LnRzeD85NTNlIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VSZWYsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBQbGF5LCBQYXVzZSwgVm9sdW1lMiwgVm9sdW1lWCwgTWF4aW1pemUsIFggfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnLi4vdXRpbHMvdXNlVHJhbnNsYXRpb24nXG5cbmludGVyZmFjZSBWaWRlbyB7XG4gIGlkOiBzdHJpbmdcbiAgdGl0bGU6IHN0cmluZ1xuICB0aXRsZV9lbjogc3RyaW5nXG4gIGR1cmF0aW9uOiBzdHJpbmdcbiAgY2F0ZWdvcnk6IHN0cmluZ1xuICBjYXRlZ29yeV9lbjogc3RyaW5nXG4gIGFzcGVjdFJhdGlvOiBcImxhbmRzY2FwZVwiIHwgXCJwb3J0cmFpdFwiXG4gIHJlc29sdXRpb246IHN0cmluZ1xuICB2aWRlb1VybDogc3RyaW5nXG59XG5cbmNvbnN0IHZpZGVvczogVmlkZW9bXSA9IFtcbiAge1xuICAgIGlkOiBcIjEwXCIsXG4gICAgdGl0bGU6IFwi5ZKv5ZKv6a2U5rOV5qSFXCIsXG4gICAgdGl0bGVfZW46IFwiTWFnaWMgQ2hhaXJcIixcbiAgICBkdXJhdGlvbjogXCIyOjE1XCIsXG4gICAgY2F0ZWdvcnk6IFwi5YS/56ul5aix5LmQXCIsXG4gICAgY2F0ZWdvcnlfZW46IFwiQ2hpbGRyZW4gRW50ZXJ0YWlubWVudFwiLFxuICAgIGFzcGVjdFJhdGlvOiBcInBvcnRyYWl0XCIsXG4gICAgcmVzb2x1dGlvbjogXCIxMDgweDE5MjBcIixcbiAgICB2aWRlb1VybDogXCIvdmlkZW9zLzEzMzk1MzExNjY4MTI4NzMwLm1wNFwiXG4gIH0sXG4gIHtcbiAgICBpZDogXCIxMVwiLFxuICAgIHRpdGxlOiBcIumHjeWKm+eOqeWutlwiLFxuICAgIHRpdGxlX2VuOiBcIkdyYXZpdHkgUGxheWVyXCIsXG4gICAgZHVyYXRpb246IFwiMzoyNFwiLFxuICAgIGNhdGVnb3J5OiBcIuS9k+aEn+i/kOWKqFwiLFxuICAgIGNhdGVnb3J5X2VuOiBcIk1vdGlvbiBTZW5zaW5nXCIsXG4gICAgYXNwZWN0UmF0aW86IFwibGFuZHNjYXBlXCIsXG4gICAgcmVzb2x1dGlvbjogXCIxOTIweDEwODBcIixcbiAgICB2aWRlb1VybDogXCIvdmlkZW9zLzEzMzk1MzExNjg3MjI4NDg4Lm1wNFwiXG4gIH0sXG4gIHtcbiAgICBpZDogXCIxMlwiLFxuICAgIHRpdGxlOiBcIuawtOi3g+aYn+epulwiLFxuICAgIHRpdGxlX2VuOiBcIldhdGVyIEp1bXAgU3RhcnJ5IFNreVwiLFxuICAgIGR1cmF0aW9uOiBcIjI6NDVcIixcbiAgICBjYXRlZ29yeTogXCLkupLliqjmipXlvbFcIixcbiAgICBjYXRlZ29yeV9lbjogXCJJbnRlcmFjdGl2ZSBQcm9qZWN0aW9uXCIsXG4gICAgYXNwZWN0UmF0aW86IFwibGFuZHNjYXBlXCIsXG4gICAgcmVzb2x1dGlvbjogXCIxOTIweDEwODBcIixcbiAgICB2aWRlb1VybDogXCIvdmlkZW9zLzEzMzk1MzExNzIyNDI2NzMxLm1wNFwiXG4gIH0sXG4gIHtcbiAgICBpZDogXCIxM1wiLFxuICAgIHRpdGxlOiBcIuWuh+WumeWimemdolwiLFxuICAgIHRpdGxlX2VuOiBcIlVuaXZlcnNlIFdhbGxcIixcbiAgICBkdXJhdGlvbjogXCI0OjEyXCIsXG4gICAgY2F0ZWdvcnk6IFwi5aKZ6Z2i5LqS5YqoXCIsXG4gICAgY2F0ZWdvcnlfZW46IFwiV2FsbCBJbnRlcmFjdGl2ZVwiLFxuICAgIGFzcGVjdFJhdGlvOiBcImxhbmRzY2FwZVwiLFxuICAgIHJlc29sdXRpb246IFwiMTkyMHgxMDgwXCIsXG4gICAgdmlkZW9Vcmw6IFwiL3ZpZGVvcy8xMzM5NTMxMTc0MDE3MjE0Ni5tcDRcIlxuICB9LFxuICB7XG4gICAgaWQ6IFwiMTRcIixcbiAgICB0aXRsZTogXCLkupLliqjnoLjnkINcIixcbiAgICB0aXRsZV9lbjogXCJJbnRlcmFjdGl2ZSBCYWxsIFNtYXNoaW5nXCIsXG4gICAgZHVyYXRpb246IFwiMzo1NlwiLFxuICAgIGNhdGVnb3J5OiBcIuS6kuWKqOWoseS5kFwiLFxuICAgIGNhdGVnb3J5X2VuOiBcIkludGVyYWN0aXZlIEVudGVydGFpbm1lbnRcIixcbiAgICBhc3BlY3RSYXRpbzogXCJwb3J0cmFpdFwiLFxuICAgIHJlc29sdXRpb246IFwiMTA4MHgxOTIwXCIsXG4gICAgdmlkZW9Vcmw6IFwiL3ZpZGVvcy8xMzM5NTMxMTc1NjgzNTYwNy5tcDRcIlxuICB9LFxuICB7XG4gICAgaWQ6IFwiMTVcIixcbiAgICB0aXRsZTogXCLkupLliqjmu5Hmoq9cIixcbiAgICB0aXRsZV9lbjogXCJJbnRlcmFjdGl2ZSBTbGlkZVwiLFxuICAgIGR1cmF0aW9uOiBcIjI6MThcIixcbiAgICBjYXRlZ29yeTogXCLlhL/nq6XlqLHkuZBcIixcbiAgICBjYXRlZ29yeV9lbjogXCJDaGlsZHJlbiBFbnRlcnRhaW5tZW50XCIsXG4gICAgYXNwZWN0UmF0aW86IFwibGFuZHNjYXBlXCIsXG4gICAgcmVzb2x1dGlvbjogXCIxOTIweDEwODBcIixcbiAgICB2aWRlb1VybDogXCIvdmlkZW9zLzEzMzk1MzExNzg1NDY0MDk5Lm1wNFwiXG4gIH0sXG4gIHtcbiAgICBpZDogXCIxNlwiLFxuICAgIHRpdGxlOiBcIuelnueslOe7mOeUu1wiLFxuICAgIHRpdGxlX2VuOiBcIk1hZ2ljIEJydXNoIFBhaW50aW5nXCIsXG4gICAgZHVyYXRpb246IFwiNTowM1wiLFxuICAgIGNhdGVnb3J5OiBcIkFS5pWZ6IKyXCIsXG4gICAgY2F0ZWdvcnlfZW46IFwiQVIgRWR1Y2F0aW9uXCIsXG4gICAgYXNwZWN0UmF0aW86IFwibGFuZHNjYXBlXCIsXG4gICAgcmVzb2x1dGlvbjogXCIxOTIweDEwODBcIixcbiAgICB2aWRlb1VybDogXCIvdmlkZW9zLzEzMzk1MzExNzk4OTUzODg2Lm1wNFwiXG4gIH0sXG4gIHtcbiAgICBpZDogXCIxN1wiLFxuICAgIHRpdGxlOiBcIkFS5pm65oWn5pWZ5a6kXCIsXG4gICAgdGl0bGVfZW46IFwiQVIgU21hcnQgQ2xhc3Nyb29tXCIsXG4gICAgZHVyYXRpb246IFwiMzozN1wiLFxuICAgIGNhdGVnb3J5OiBcIkFS5pWZ6IKyXCIsXG4gICAgY2F0ZWdvcnlfZW46IFwiQVIgRWR1Y2F0aW9uXCIsXG4gICAgYXNwZWN0UmF0aW86IFwibGFuZHNjYXBlXCIsXG4gICAgcmVzb2x1dGlvbjogXCIxOTIweDEwODBcIixcbiAgICB2aWRlb1VybDogXCIvdmlkZW9zLzEzMzk1MzExODE4MDQzNTkyLm1wNFwiXG4gIH0sXG4gIHtcbiAgICBpZDogXCIxOFwiLFxuICAgIHRpdGxlOiBcIuS6kuWKqOaymeaxoFwiLFxuICAgIHRpdGxlX2VuOiBcIkludGVyYWN0aXZlIFNhbmQgUG9vbFwiLFxuICAgIGR1cmF0aW9uOiBcIjQ6MjhcIixcbiAgICBjYXRlZ29yeTogXCLlhL/nq6XkupLliqhcIixcbiAgICBjYXRlZ29yeV9lbjogXCJDaGlsZHJlbiBJbnRlcmFjdGl2ZVwiLFxuICAgIGFzcGVjdFJhdGlvOiBcInBvcnRyYWl0XCIsXG4gICAgcmVzb2x1dGlvbjogXCIxMDgweDE5MjBcIixcbiAgICB2aWRlb1VybDogXCIvdmlkZW9zLzEzMzk1MzExODMyMzE5ODU2Lm1wNFwiXG4gIH0sXG4gIHtcbiAgICBpZDogXCIxOVwiLFxuICAgIHRpdGxlOiBcIkFS5rKZ5qGMXCIsXG4gICAgdGl0bGVfZW46IFwiQVIgU2FuZCBUYWJsZVwiLFxuICAgIGR1cmF0aW9uOiBcIjY6MTVcIixcbiAgICBjYXRlZ29yeTogXCJBUuaKgOacr1wiLFxuICAgIGNhdGVnb3J5X2VuOiBcIkFSIFRlY2hub2xvZ3lcIixcbiAgICBhc3BlY3RSYXRpbzogXCJsYW5kc2NhcGVcIixcbiAgICByZXNvbHV0aW9uOiBcIjE5MjB4MTA4MFwiLFxuICAgIHZpZGVvVXJsOiBcIi92aWRlb3MvMTMzOTUzMTE4NDgwMDY4NjkubXA0XCJcbiAgfSxcbiAge1xuICAgIGlkOiBcIjIwXCIsXG4gICAgdGl0bGU6IFwi5LqS5Yqo5ruR5p2/XCIsXG4gICAgdGl0bGVfZW46IFwiSW50ZXJhY3RpdmUgU2thdGVib2FyZFwiLFxuICAgIGR1cmF0aW9uOiBcIjI6MTVcIixcbiAgICBjYXRlZ29yeTogXCLkvZPmhJ/ov5DliqhcIixcbiAgICBjYXRlZ29yeV9lbjogXCJNb3Rpb24gU2Vuc2luZ1wiLFxuICAgIGFzcGVjdFJhdGlvOiBcImxhbmRzY2FwZVwiLFxuICAgIHJlc29sdXRpb246IFwiMTkyMHgxMDgwXCIsXG4gICAgdmlkZW9Vcmw6IFwiL3ZpZGVvcy8xMzM5NTMxMTg2NjMwMTAzOS5tcDRcIlxuICB9LFxuICB7XG4gICAgaWQ6IFwiMjFcIixcbiAgICB0aXRsZTogXCLnm4rmmbrmtYHmsLTloplcIixcbiAgICB0aXRsZV9lbjogXCJFZHVjYXRpb25hbCBXYXRlciBXYWxsXCIsXG4gICAgZHVyYXRpb246IFwiMzoyNFwiLFxuICAgIGNhdGVnb3J5OiBcIuaZuuiDveaVmeiCslwiLFxuICAgIGNhdGVnb3J5X2VuOiBcIlNtYXJ0IEVkdWNhdGlvblwiLFxuICAgIGFzcGVjdFJhdGlvOiBcImxhbmRzY2FwZVwiLFxuICAgIHJlc29sdXRpb246IFwiMTkyMHgxMDgwXCIsXG4gICAgdmlkZW9Vcmw6IFwiL3ZpZGVvcy8xMzM5NTMxMTg5NzEwNzQ0Ni5tcDRcIlxuICB9LFxuICB7XG4gICAgaWQ6IFwiMjJcIixcbiAgICB0aXRsZTogXCLkvZPmhJ/lpKfluIhcIixcbiAgICB0aXRsZV9lbjogXCJNb3Rpb24gU2Vuc2luZyBNYXN0ZXJcIixcbiAgICBkdXJhdGlvbjogXCIyOjQ1XCIsXG4gICAgY2F0ZWdvcnk6IFwi5L2T5oSf6L+Q5YqoXCIsXG4gICAgY2F0ZWdvcnlfZW46IFwiTW90aW9uIFNlbnNpbmdcIixcbiAgICBhc3BlY3RSYXRpbzogXCJwb3J0cmFpdFwiLFxuICAgIHJlc29sdXRpb246IFwiMTA4MHgxOTIwXCIsXG4gICAgdmlkZW9Vcmw6IFwiL3ZpZGVvcy8xMzM5NTMxMTk0NTA3Njk5Ny5tcDRcIlxuICB9LFxuICB7XG4gICAgaWQ6IFwiMjNcIixcbiAgICB0aXRsZTogXCLkvZPmhJ/mlIDlsqlcIixcbiAgICB0aXRsZV9lbjogXCJNb3Rpb24gU2Vuc2luZyBDbGltYmluZ1wiLFxuICAgIGR1cmF0aW9uOiBcIjQ6MTJcIixcbiAgICBjYXRlZ29yeTogXCLkvZPmhJ/ov5DliqhcIixcbiAgICBjYXRlZ29yeV9lbjogXCJNb3Rpb24gU2Vuc2luZ1wiLFxuICAgIGFzcGVjdFJhdGlvOiBcImxhbmRzY2FwZVwiLFxuICAgIHJlc29sdXRpb246IFwiMTkyMHgxMDgwXCIsXG4gICAgdmlkZW9Vcmw6IFwiL3ZpZGVvcy8xMzM5NTMxMTk4NjQ1NjEyMy5tcDRcIlxuICB9LFxuICB7XG4gICAgaWQ6IFwiMjRcIixcbiAgICB0aXRsZTogXCLot7Pot4PmoLzlrZBcIixcbiAgICB0aXRsZV9lbjogXCJKdW1waW5nIEdyaWRcIixcbiAgICBkdXJhdGlvbjogXCIzOjU2XCIsXG4gICAgY2F0ZWdvcnk6IFwi5YS/56ul5LqS5YqoXCIsXG4gICAgY2F0ZWdvcnlfZW46IFwiQ2hpbGRyZW4gSW50ZXJhY3RpdmVcIixcbiAgICBhc3BlY3RSYXRpbzogXCJsYW5kc2NhcGVcIixcbiAgICByZXNvbHV0aW9uOiBcIjE5MjB4MTA4MFwiLFxuICAgIHZpZGVvVXJsOiBcIi92aWRlb3MvMTMzOTUzMTIwMTkxNjA2NjMubXA0XCJcbiAgfSxcbiAge1xuICAgIGlkOiBcIjI1XCIsXG4gICAgdGl0bGU6IFwi6L+Q5Yqo5LiA5L2T5py6XCIsXG4gICAgdGl0bGVfZW46IFwiU3BvcnRzIEFsbC1pbi1PbmVcIixcbiAgICBkdXJhdGlvbjogXCIyOjE4XCIsXG4gICAgY2F0ZWdvcnk6IFwi5L2T5oSf6L+Q5YqoXCIsXG4gICAgY2F0ZWdvcnlfZW46IFwiTW90aW9uIFNlbnNpbmdcIixcbiAgICBhc3BlY3RSYXRpbzogXCJsYW5kc2NhcGVcIixcbiAgICByZXNvbHV0aW9uOiBcIjE5MjB4MTA4MFwiLFxuICAgIHZpZGVvVXJsOiBcIi92aWRlb3MvMTMzOTUzMTIwNzE1NzEzNTIubXA0XCJcbiAgfSxcbiAge1xuICAgIGlkOiBcIjI2XCIsXG4gICAgdGl0bGU6IFwi56e75Yqo5bCE566tXCIsXG4gICAgdGl0bGVfZW46IFwiTW9iaWxlIEFyY2hlcnlcIixcbiAgICBkdXJhdGlvbjogXCI1OjAzXCIsXG4gICAgY2F0ZWdvcnk6IFwi5L2T5oSf6L+Q5YqoXCIsXG4gICAgY2F0ZWdvcnlfZW46IFwiTW90aW9uIFNlbnNpbmdcIixcbiAgICBhc3BlY3RSYXRpbzogXCJwb3J0cmFpdFwiLFxuICAgIHJlc29sdXRpb246IFwiMTA4MHgxOTIwXCIsXG4gICAgdmlkZW9Vcmw6IFwiL3ZpZGVvcy8xMzM5NTMxMjEwNDEzODkwOC5tcDRcIlxuICB9LFxuICB7XG4gICAgaWQ6IFwiMjdcIixcbiAgICB0aXRsZTogXCLlrp7lvLnlsITlh7tcIixcbiAgICB0aXRsZV9lbjogXCJMaXZlIEFtbXVuaXRpb24gU2hvb3RpbmdcIixcbiAgICBkdXJhdGlvbjogXCIzOjM3XCIsXG4gICAgY2F0ZWdvcnk6IFwi5L2T5oSf6L+Q5YqoXCIsXG4gICAgY2F0ZWdvcnlfZW46IFwiTW90aW9uIFNlbnNpbmdcIixcbiAgICBhc3BlY3RSYXRpbzogXCJsYW5kc2NhcGVcIixcbiAgICByZXNvbHV0aW9uOiBcIjE5MjB4MTA4MFwiLFxuICAgIHZpZGVvVXJsOiBcIi92aWRlb3MvMTMzOTUzMTIxNDM0NDM5MTYubXA0XCJcbiAgfSxcbiAge1xuICAgIGlkOiBcIjI4XCIsXG4gICAgdGl0bGU6IFwi5LqS5Yqo6Laz55CDXCIsXG4gICAgdGl0bGVfZW46IFwiSW50ZXJhY3RpdmUgRm9vdGJhbGxcIixcbiAgICBkdXJhdGlvbjogXCI0OjI4XCIsXG4gICAgY2F0ZWdvcnk6IFwi5L2T5oSf6L+Q5YqoXCIsXG4gICAgY2F0ZWdvcnlfZW46IFwiTW90aW9uIFNlbnNpbmdcIixcbiAgICBhc3BlY3RSYXRpbzogXCJsYW5kc2NhcGVcIixcbiAgICByZXNvbHV0aW9uOiBcIjE5MjB4MTA4MFwiLFxuICAgIHZpZGVvVXJsOiBcIi92aWRlb3MvMTMzOTUzMTIxNTYwODE2MDQubXA0XCJcbiAgfSxcbiAge1xuICAgIGlkOiBcIjI5XCIsXG4gICAgdGl0bGU6IFwi6a2U5Yqb5rua55CDXCIsXG4gICAgdGl0bGVfZW46IFwiTWFnaWMgUm9sbGluZyBCYWxsXCIsXG4gICAgZHVyYXRpb246IFwiNjoxNVwiLFxuICAgIGNhdGVnb3J5OiBcIuS6kuWKqOWoseS5kFwiLFxuICAgIGNhdGVnb3J5X2VuOiBcIkludGVyYWN0aXZlIEVudGVydGFpbm1lbnRcIixcbiAgICBhc3BlY3RSYXRpbzogXCJsYW5kc2NhcGVcIixcbiAgICByZXNvbHV0aW9uOiBcIjE5MjB4MTA4MFwiLFxuICAgIHZpZGVvVXJsOiBcIi92aWRlb3MvMTMzOTUzMTIyMTE5ODY0MjUubXA0XCJcbiAgfSxcbiAge1xuICAgIGlkOiBcIjMwXCIsXG4gICAgdGl0bGU6IFwi5Lq65L2T6Zev5YWzXCIsXG4gICAgdGl0bGVfZW46IFwiSHVtYW4gQWR2ZW50dXJlXCIsXG4gICAgZHVyYXRpb246IFwiMjoxNVwiLFxuICAgIGNhdGVnb3J5OiBcIuS9k+aEn+i/kOWKqFwiLFxuICAgIGNhdGVnb3J5X2VuOiBcIk1vdGlvbiBTZW5zaW5nXCIsXG4gICAgYXNwZWN0UmF0aW86IFwicG9ydHJhaXRcIixcbiAgICByZXNvbHV0aW9uOiBcIjEwODB4MTkyMFwiLFxuICAgIHZpZGVvVXJsOiBcIi92aWRlb3MvMTMzOTUzMTIyNDQzMzMxMTQubXA0XCJcbiAgfSxcbiAge1xuICAgIGlkOiBcIjMxXCIsXG4gICAgdGl0bGU6IFwi5L2T5oSf6Lmm5bqKXCIsXG4gICAgdGl0bGVfZW46IFwiTW90aW9uIFNlbnNpbmcgVHJhbXBvbGluZVwiLFxuICAgIGR1cmF0aW9uOiBcIjM6MjRcIixcbiAgICBjYXRlZ29yeTogXCLkvZPmhJ/ov5DliqhcIixcbiAgICBjYXRlZ29yeV9lbjogXCJNb3Rpb24gU2Vuc2luZ1wiLFxuICAgIGFzcGVjdFJhdGlvOiBcImxhbmRzY2FwZVwiLFxuICAgIHJlc29sdXRpb246IFwiMTkyMHgxMDgwXCIsXG4gICAgdmlkZW9Vcmw6IFwiL3ZpZGVvcy8xMzM5NTMxMjI3MzY1MjcyOC5tcDRcIlxuICB9LFxuICB7XG4gICAgaWQ6IFwiMzJcIixcbiAgICB0aXRsZTogXCLliqjmhJ/ljZXovaZcIixcbiAgICB0aXRsZV9lbjogXCJEeW5hbWljIEJpY3ljbGVcIixcbiAgICBkdXJhdGlvbjogXCIyOjQ1XCIsXG4gICAgY2F0ZWdvcnk6IFwi5L2T5oSf6L+Q5YqoXCIsXG4gICAgY2F0ZWdvcnlfZW46IFwiTW90aW9uIFNlbnNpbmdcIixcbiAgICBhc3BlY3RSYXRpbzogXCJsYW5kc2NhcGVcIixcbiAgICByZXNvbHV0aW9uOiBcIjE5MjB4MTA4MFwiLFxuICAgIHZpZGVvVXJsOiBcIi92aWRlb3MvMTMzOTUzMTIzMjAzMDc5NjgubXA0XCJcbiAgfSxcbiAge1xuICAgIGlkOiBcIjMzXCIsXG4gICAgdGl0bGU6IFwi5qih5ouf5ouz5Ye7XCIsXG4gICAgdGl0bGVfZW46IFwiU2ltdWxhdGVkIEJveGluZ1wiLFxuICAgIGR1cmF0aW9uOiBcIjQ6MTJcIixcbiAgICBjYXRlZ29yeTogXCLkvZPmhJ/ov5DliqhcIixcbiAgICBjYXRlZ29yeV9lbjogXCJNb3Rpb24gU2Vuc2luZ1wiLFxuICAgIGFzcGVjdFJhdGlvOiBcImxhbmRzY2FwZVwiLFxuICAgIHJlc29sdXRpb246IFwiMTkyMHgxMDgwXCIsXG4gICAgdmlkZW9Vcmw6IFwiL3ZpZGVvcy8xMzM5NTMxMjQwMzQzODY0NS5tcDRcIlxuICB9LFxuICB7XG4gICAgaWQ6IFwiMzRcIixcbiAgICB0aXRsZTogXCLmqKHmi5/nvZHnkINcIixcbiAgICB0aXRsZV9lbjogXCJTaW11bGF0ZWQgVGVubmlzXCIsXG4gICAgZHVyYXRpb246IFwiMzo1NlwiLFxuICAgIGNhdGVnb3J5OiBcIuS9k+aEn+i/kOWKqFwiLFxuICAgIGNhdGVnb3J5X2VuOiBcIk1vdGlvbiBTZW5zaW5nXCIsXG4gICAgYXNwZWN0UmF0aW86IFwicG9ydHJhaXRcIixcbiAgICByZXNvbHV0aW9uOiBcIjEwODB4MTkyMFwiLFxuICAgIHZpZGVvVXJsOiBcIi92aWRlb3MvMTMzOTUzMTI0MjExNDI4MzEubXA0XCJcbiAgfSxcbiAge1xuICAgIGlkOiBcIjM1XCIsXG4gICAgdGl0bGU6IFwi5YWo5oGv5a605Lya5Y6FXCIsXG4gICAgdGl0bGVfZW46IFwiSG9sb2dyYXBoaWMgQmFucXVldCBIYWxsXCIsXG4gICAgZHVyYXRpb246IFwiMjoxOFwiLFxuICAgIGNhdGVnb3J5OiBcIuWFqOaBr+aKgOacr1wiLFxuICAgIGNhdGVnb3J5X2VuOiBcIkhvbG9ncmFwaGljIFRlY2hub2xvZ3lcIixcbiAgICBhc3BlY3RSYXRpbzogXCJsYW5kc2NhcGVcIixcbiAgICByZXNvbHV0aW9uOiBcIjE5MjB4MTA4MFwiLFxuICAgIHZpZGVvVXJsOiBcIi92aWRlb3MvMTMzOTUzMTI0NDY0MzEzMzQubXA0XCJcbiAgfVxuXVxuXG5pbnRlcmZhY2UgVmlkZW9HYWxsZXJ5UHJvcHMge1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVmlkZW9HYWxsZXJ5KHsgY2xhc3NOYW1lID0gXCJcIiB9OiBWaWRlb0dhbGxlcnlQcm9wcykge1xuICBjb25zdCB7IHQsIGxvY2FsZSB9ID0gdXNlVHJhbnNsYXRpb24oKVxuICBjb25zdCBbc2VsZWN0ZWRWaWRlbywgc2V0U2VsZWN0ZWRWaWRlb10gPSB1c2VTdGF0ZTxWaWRlbyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtpc1BsYXlpbmcsIHNldElzUGxheWluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2N1cnJlbnRUaW1lLCBzZXRDdXJyZW50VGltZV0gPSB1c2VTdGF0ZSgwKVxuICBjb25zdCBbZHVyYXRpb24sIHNldER1cmF0aW9uXSA9IHVzZVN0YXRlKDApXG4gIGNvbnN0IFt2b2x1bWUsIHNldFZvbHVtZV0gPSB1c2VTdGF0ZSgxKVxuICBjb25zdCBbaXNNdXRlZCwgc2V0SXNNdXRlZF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3Nob3dDb250cm9scywgc2V0U2hvd0NvbnRyb2xzXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtzZWxlY3RlZENhdGVnb3J5LCBzZXRTZWxlY3RlZENhdGVnb3J5XSA9IHVzZVN0YXRlPHN0cmluZz4odCgndmlkZW9fZ2FsbGVyeS5maWx0ZXJfYWxsJywgeyBmYWxsYmFjazogJ+WFqOmDqCcgfSkpXG4gIGNvbnN0IFtzZWxlY3RlZEZvcm1hdCwgc2V0U2VsZWN0ZWRGb3JtYXRdID0gdXNlU3RhdGU8c3RyaW5nPih0KCd2aWRlb19nYWxsZXJ5LmZpbHRlcl9hbGwnLCB7IGZhbGxiYWNrOiAn5YWo6YOoJyB9KSlcblxuICBjb25zdCB2aWRlb1JlZiA9IHVzZVJlZjxIVE1MVmlkZW9FbGVtZW50PihudWxsKVxuICBjb25zdCBjb250cm9sc1RpbWVvdXRSZWYgPSB1c2VSZWY8Tm9kZUpTLlRpbWVvdXQgfCBudWxsPihudWxsKVxuXG4gIGNvbnN0IGdldExvY2FsaXplZENhdGVnb3JpZXMgPSAoKSA9PiB7XG4gICAgY29uc3QgYWxsQ2F0ZWdvcmllcyA9IHZpZGVvcy5tYXAoKHYpID0+IGxvY2FsZSA9PT0gJ3poJyA/IHYuY2F0ZWdvcnkgOiB2LmNhdGVnb3J5X2VuKVxuICAgIHJldHVybiBbdCgndmlkZW9fZ2FsbGVyeS5maWx0ZXJfYWxsJywgeyBmYWxsYmFjazogJ+WFqOmDqCcgfSksIC4uLkFycmF5LmZyb20obmV3IFNldChhbGxDYXRlZ29yaWVzKSldXG4gIH1cblxuICBjb25zdCBnZXRMb2NhbGl6ZWRGb3JtYXRzID0gKCkgPT4gW1xuICAgIHQoJ3ZpZGVvX2dhbGxlcnkuZmlsdGVyX2FsbCcsIHsgZmFsbGJhY2s6ICflhajpg6gnIH0pLFxuICAgIHQoJ3ZpZGVvX2dhbGxlcnkuZmlsdGVyX2xhbmRzY2FwZScsIHsgZmFsbGJhY2s6ICfmqKrlsY/op4bpopEnIH0pLFxuICAgIHQoJ3ZpZGVvX2dhbGxlcnkuZmlsdGVyX3BvcnRyYWl0JywgeyBmYWxsYmFjazogJ+erluWxj+inhumikScgfSlcbiAgXVxuXG4gIGNvbnN0IGZpbHRlcmVkVmlkZW9zID0gdmlkZW9zLmZpbHRlcigodmlkZW8pID0+IHtcbiAgICBjb25zdCB2aWRlb0NhdGVnb3J5ID0gbG9jYWxlID09PSAnemgnID8gdmlkZW8uY2F0ZWdvcnkgOiB2aWRlby5jYXRlZ29yeV9lblxuICAgIGNvbnN0IGFsbFRleHQgPSB0KCd2aWRlb19nYWxsZXJ5LmZpbHRlcl9hbGwnLCB7IGZhbGxiYWNrOiAn5YWo6YOoJyB9KVxuICAgIGNvbnN0IGxhbmRzY2FwZVRleHQgPSB0KCd2aWRlb19nYWxsZXJ5LmZpbHRlcl9sYW5kc2NhcGUnLCB7IGZhbGxiYWNrOiAn5qiq5bGP6KeG6aKRJyB9KVxuICAgIGNvbnN0IHBvcnRyYWl0VGV4dCA9IHQoJ3ZpZGVvX2dhbGxlcnkuZmlsdGVyX3BvcnRyYWl0JywgeyBmYWxsYmFjazogJ+erluWxj+inhumikScgfSlcbiAgICBcbiAgICBjb25zdCBjYXRlZ29yeU1hdGNoID0gc2VsZWN0ZWRDYXRlZ29yeSA9PT0gYWxsVGV4dCB8fCB2aWRlb0NhdGVnb3J5ID09PSBzZWxlY3RlZENhdGVnb3J5XG4gICAgY29uc3QgZm9ybWF0TWF0Y2ggPVxuICAgICAgc2VsZWN0ZWRGb3JtYXQgPT09IGFsbFRleHQgfHxcbiAgICAgIChzZWxlY3RlZEZvcm1hdCA9PT0gbGFuZHNjYXBlVGV4dCAmJiB2aWRlby5hc3BlY3RSYXRpbyA9PT0gXCJsYW5kc2NhcGVcIikgfHxcbiAgICAgIChzZWxlY3RlZEZvcm1hdCA9PT0gcG9ydHJhaXRUZXh0ICYmIHZpZGVvLmFzcGVjdFJhdGlvID09PSBcInBvcnRyYWl0XCIpXG4gICAgcmV0dXJuIGNhdGVnb3J5TWF0Y2ggJiYgZm9ybWF0TWF0Y2hcbiAgfSkuc29ydCgoYSwgYikgPT4ge1xuICAgIC8vIOaoquWxj+inhumikeaOkuWcqOWJjemdou+8jOerluWxj+inhumikeaOkuWcqOWQjumdolxuICAgIGlmIChhLmFzcGVjdFJhdGlvID09PSBcImxhbmRzY2FwZVwiICYmIGIuYXNwZWN0UmF0aW8gPT09IFwicG9ydHJhaXRcIikgcmV0dXJuIC0xXG4gICAgaWYgKGEuYXNwZWN0UmF0aW8gPT09IFwicG9ydHJhaXRcIiAmJiBiLmFzcGVjdFJhdGlvID09PSBcImxhbmRzY2FwZVwiKSByZXR1cm4gMVxuICAgIHJldHVybiAwXG4gIH0pXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB2aWRlbyA9IHZpZGVvUmVmLmN1cnJlbnRcbiAgICBpZiAoIXZpZGVvKSByZXR1cm5cblxuICAgIGNvbnN0IHVwZGF0ZVRpbWUgPSAoKSA9PiBzZXRDdXJyZW50VGltZSh2aWRlby5jdXJyZW50VGltZSlcbiAgICBjb25zdCB1cGRhdGVEdXJhdGlvbiA9ICgpID0+IHNldER1cmF0aW9uKHZpZGVvLmR1cmF0aW9uKVxuXG4gICAgdmlkZW8uYWRkRXZlbnRMaXN0ZW5lcihcInRpbWV1cGRhdGVcIiwgdXBkYXRlVGltZSlcbiAgICB2aWRlby5hZGRFdmVudExpc3RlbmVyKFwibG9hZGVkbWV0YWRhdGFcIiwgdXBkYXRlRHVyYXRpb24pXG4gICAgdmlkZW8uYWRkRXZlbnRMaXN0ZW5lcihcImVuZGVkXCIsICgpID0+IHNldElzUGxheWluZyhmYWxzZSkpXG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgdmlkZW8ucmVtb3ZlRXZlbnRMaXN0ZW5lcihcInRpbWV1cGRhdGVcIiwgdXBkYXRlVGltZSlcbiAgICAgIHZpZGVvLnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJsb2FkZWRtZXRhZGF0YVwiLCB1cGRhdGVEdXJhdGlvbilcbiAgICAgIHZpZGVvLnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJlbmRlZFwiLCAoKSA9PiBzZXRJc1BsYXlpbmcoZmFsc2UpKVxuICAgIH1cbiAgfSwgW3NlbGVjdGVkVmlkZW9dKVxuXG4gIGNvbnN0IG9wZW5WaWRlbyA9ICh2aWRlbzogVmlkZW8pID0+IHtcbiAgICBzZXRTZWxlY3RlZFZpZGVvKHZpZGVvKVxuICAgIHNldElzUGxheWluZyhmYWxzZSlcbiAgICBzZXRDdXJyZW50VGltZSgwKVxuICB9XG5cbiAgY29uc3QgY2xvc2VWaWRlbyA9ICgpID0+IHtcbiAgICBzZXRTZWxlY3RlZFZpZGVvKG51bGwpXG4gICAgc2V0SXNQbGF5aW5nKGZhbHNlKVxuICAgIGlmICh2aWRlb1JlZi5jdXJyZW50KSB7XG4gICAgICB2aWRlb1JlZi5jdXJyZW50LnBhdXNlKClcbiAgICB9XG4gIH1cblxuICBjb25zdCB0b2dnbGVQbGF5ID0gKCkgPT4ge1xuICAgIGNvbnN0IHZpZGVvID0gdmlkZW9SZWYuY3VycmVudFxuICAgIGlmICghdmlkZW8pIHJldHVyblxuXG4gICAgaWYgKGlzUGxheWluZykge1xuICAgICAgdmlkZW8ucGF1c2UoKVxuICAgIH0gZWxzZSB7XG4gICAgICB2aWRlby5wbGF5KClcbiAgICB9XG4gICAgc2V0SXNQbGF5aW5nKCFpc1BsYXlpbmcpXG4gIH1cblxuICBjb25zdCBoYW5kbGVQcm9ncmVzc0NoYW5nZSA9ICh2YWx1ZTogbnVtYmVyW10pID0+IHtcbiAgICBjb25zdCB2aWRlbyA9IHZpZGVvUmVmLmN1cnJlbnRcbiAgICBpZiAoIXZpZGVvKSByZXR1cm5cblxuICAgIGNvbnN0IG5ld1RpbWUgPSAodmFsdWVbMF0gLyAxMDApICogZHVyYXRpb25cbiAgICB2aWRlby5jdXJyZW50VGltZSA9IG5ld1RpbWVcbiAgICBzZXRDdXJyZW50VGltZShuZXdUaW1lKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlVm9sdW1lQ2hhbmdlID0gKHZhbHVlOiBudW1iZXJbXSkgPT4ge1xuICAgIGNvbnN0IHZpZGVvID0gdmlkZW9SZWYuY3VycmVudFxuICAgIGlmICghdmlkZW8pIHJldHVyblxuXG4gICAgY29uc3QgbmV3Vm9sdW1lID0gdmFsdWVbMF0gLyAxMDBcbiAgICB2aWRlby52b2x1bWUgPSBuZXdWb2x1bWVcbiAgICBzZXRWb2x1bWUobmV3Vm9sdW1lKVxuICAgIHNldElzTXV0ZWQobmV3Vm9sdW1lID09PSAwKVxuICB9XG5cbiAgY29uc3QgdG9nZ2xlTXV0ZSA9ICgpID0+IHtcbiAgICBjb25zdCB2aWRlbyA9IHZpZGVvUmVmLmN1cnJlbnRcbiAgICBpZiAoIXZpZGVvKSByZXR1cm5cblxuICAgIGlmIChpc011dGVkKSB7XG4gICAgICB2aWRlby52b2x1bWUgPSB2b2x1bWVcbiAgICAgIHNldElzTXV0ZWQoZmFsc2UpXG4gICAgfSBlbHNlIHtcbiAgICAgIHZpZGVvLnZvbHVtZSA9IDBcbiAgICAgIHNldElzTXV0ZWQodHJ1ZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCB0b2dnbGVGdWxsc2NyZWVuID0gKCkgPT4ge1xuICAgIGNvbnN0IHZpZGVvID0gdmlkZW9SZWYuY3VycmVudFxuICAgIGlmICghdmlkZW8pIHJldHVyblxuXG4gICAgaWYgKGRvY3VtZW50LmZ1bGxzY3JlZW5FbGVtZW50KSB7XG4gICAgICBkb2N1bWVudC5leGl0RnVsbHNjcmVlbigpXG4gICAgfSBlbHNlIHtcbiAgICAgIHZpZGVvLnJlcXVlc3RGdWxsc2NyZWVuKClcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVNb3VzZU1vdmUgPSAoKSA9PiB7XG4gICAgc2V0U2hvd0NvbnRyb2xzKHRydWUpXG4gICAgaWYgKGNvbnRyb2xzVGltZW91dFJlZi5jdXJyZW50KSB7XG4gICAgICBjbGVhclRpbWVvdXQoY29udHJvbHNUaW1lb3V0UmVmLmN1cnJlbnQpXG4gICAgfVxuICAgIGNvbnRyb2xzVGltZW91dFJlZi5jdXJyZW50ID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBpZiAoaXNQbGF5aW5nKSB7XG4gICAgICAgIHNldFNob3dDb250cm9scyhmYWxzZSlcbiAgICAgIH1cbiAgICB9LCAzMDAwKVxuICB9XG5cbiAgY29uc3QgZm9ybWF0VGltZSA9ICh0aW1lOiBudW1iZXIpID0+IHtcbiAgICBjb25zdCBtaW51dGVzID0gTWF0aC5mbG9vcih0aW1lIC8gNjApXG4gICAgY29uc3Qgc2Vjb25kcyA9IE1hdGguZmxvb3IodGltZSAlIDYwKVxuICAgIHJldHVybiBgJHttaW51dGVzfToke3NlY29uZHMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCBcIjBcIil9YFxuICB9XG5cbiAgY29uc3QgcHJvZ3Jlc3MgPSBkdXJhdGlvbiA+IDAgPyAoY3VycmVudFRpbWUgLyBkdXJhdGlvbikgKiAxMDAgOiAwXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YHctZnVsbCBtYXgtdy03eGwgbXgtYXV0byBwLTYgJHtjbGFzc05hbWV9YH0+XG4gICAgICB7LyogSGVhZGVyICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAge3QoJ3ZpZGVvX2dhbGxlcnkudGl0bGUnLCB7IGZhbGxiYWNrOiAn5Lqn5ZOB5ryU56S66KeG6aKRJyB9KX1cbiAgICAgICAgPC9oMT5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAge3QoJ3ZpZGVvX2dhbGxlcnkuc3VidGl0bGUnLCB7IGZhbGxiYWNrOiAn5o6i57Si5oiR5Lus5a6M5pW055qE5Lqk5LqS5byP5oqA5pyv6Kej5Yaz5pa55qGIJyB9KX1cbiAgICAgICAgPC9wPlxuICAgICAgPC9kaXY+XG5cblxuXG4gICAgICB7LyogVmlkZW8gR3JpZCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidmlkZW8tZ3JpZFwiPlxuICAgICAgICA8c3R5bGUganN4PntgXG4gICAgICAgICAgLnZpZGVvLWdyaWQge1xuICAgICAgICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZmlsbCwgbWlubWF4KDI4MHB4LCAxZnIpKTtcbiAgICAgICAgICAgIGdhcDogMS41cmVtO1xuICAgICAgICAgICAgYWxpZ24taXRlbXM6IHN0YXJ0O1xuICAgICAgICAgICAganVzdGlmeS1pdGVtczogY2VudGVyO1xuICAgICAgICAgIH1cbiAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogMTAyNHB4KSB7XG4gICAgICAgICAgICAudmlkZW8tZ3JpZCB7XG4gICAgICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZmlsbCwgbWlubWF4KDI0MHB4LCAxZnIpKTtcbiAgICAgICAgICAgICAgZ2FwOiAxcmVtO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgICAgICAgIC52aWRlby1ncmlkIHtcbiAgICAgICAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maWxsLCBtaW5tYXgoMjAwcHgsIDFmcikpO1xuICAgICAgICAgICAgICBnYXA6IDAuNzVyZW07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiA2NDBweCkge1xuICAgICAgICAgICAgLnZpZGVvLWdyaWQge1xuICAgICAgICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCAxZnIpO1xuICAgICAgICAgICAgICBnYXA6IDAuNXJlbTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIGB9PC9zdHlsZT5cbiAgICAgICAge2ZpbHRlcmVkVmlkZW9zLm1hcCgodmlkZW8pID0+IChcbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBrZXk9e3ZpZGVvLmlkfVxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgcmVsYXRpdmUgZ3JvdXAgY3Vyc29yLXBvaW50ZXIgb3ZlcmZsb3ctaGlkZGVuIHJvdW5kZWQtbGcgc2hhZG93LW1kIGhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgdHJhbnNmb3JtIGhvdmVyOi10cmFuc2xhdGUteS0xIGJnLWdyYXktMTAwICR7XG4gICAgICAgICAgICAgIHZpZGVvLmFzcGVjdFJhdGlvID09PSBcImxhbmRzY2FwZVwiID8gXCJhc3BlY3QtdmlkZW9cIiA6IFwiYXNwZWN0LVs5LzE2XVwiXG4gICAgICAgICAgICB9IHctZnVsbCBtYXgtdy14c2B9XG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuVmlkZW8odmlkZW8pfVxuICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHZpZGVvXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgcHJlbG9hZD1cIm1ldGFkYXRhXCJcbiAgICAgICAgICAgICAgICBtdXRlZFxuICAgICAgICAgICAgICAgIHBsYXlzSW5saW5lXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8c291cmNlIHNyYz17dmlkZW8udmlkZW9Vcmx9IHR5cGU9XCJ2aWRlby9tcDRcIiAvPlxuICAgICAgICAgICAgICA8L3ZpZGVvPlxuXG4gICAgICAgICAgICAgIHsvKiBQbGF5IEJ1dHRvbiBPdmVybGF5ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctYmxhY2svMCBncm91cC1ob3ZlcjpiZy1ibGFjay8yMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy13aGl0ZS85MCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0zMDAgc2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgICAgICA8UGxheSBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtZ3JheS05MDAgbWwtMVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogVmlkZW8gSW5mbyBPdmVybGF5ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0wIGxlZnQtMCByaWdodC0wIGJnLWdyYWRpZW50LXRvLXQgZnJvbS1ibGFjay85MCB2aWEtYmxhY2svNTAgdG8tdHJhbnNwYXJlbnQgcC0yIHRleHQtd2hpdGUgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0zMDBcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gbWItMSBsaW5lLWNsYW1wLTIgdGV4dC14cyBsZWFkaW5nLXRpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICB7dmlkZW8udGl0bGV9XG4gICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB0ZXh0LXhzIHRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHNcIj57dmlkZW8udmlld3N9IHZpZXdzPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14c1wiPnt2aWRlby51cGxvYWREYXRlfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBWaWRlbyBNb2RhbCAqL31cbiAgICAgIHtzZWxlY3RlZFZpZGVvICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrLzgwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHotNTAgcC00XCI+XG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgcmVsYXRpdmUgYmctYmxhY2sgcm91bmRlZC1sZyBvdmVyZmxvdy1oaWRkZW4gJHtcbiAgICAgICAgICAgICAgc2VsZWN0ZWRWaWRlby5hc3BlY3RSYXRpbyA9PT0gXCJwb3J0cmFpdFwiID8gXCJtYXgtdy0yeGxcIiA6IFwibWF4LXctNXhsXCJcbiAgICAgICAgICAgIH0gdy1mdWxsYH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7LyogQ2xvc2UgQnV0dG9uICovfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtNCByaWdodC00IHotNTAgdGV4dC13aGl0ZSBob3ZlcjpiZy13aGl0ZS8yMCByb3VuZGVkLWZ1bGwgdy0xMCBoLTEwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCJcbiAgICAgICAgICAgICAgb25DbGljaz17Y2xvc2VWaWRlb31cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgey8qIFZpZGVvIFBsYXllciAqL31cbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcmVsYXRpdmUgZ3JvdXAgJHtcbiAgICAgICAgICAgICAgICBzZWxlY3RlZFZpZGVvLmFzcGVjdFJhdGlvID09PSBcImxhbmRzY2FwZVwiID8gXCJhc3BlY3QtdmlkZW9cIiA6IFwiYXNwZWN0LVs5LzE2XVwiXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICBvbk1vdXNlTW92ZT17aGFuZGxlTW91c2VNb3ZlfVxuICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eygpID0+IGlzUGxheWluZyAmJiBzZXRTaG93Q29udHJvbHMoZmFsc2UpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8dmlkZW9cbiAgICAgICAgICAgICAgICByZWY9e3ZpZGVvUmVmfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVQbGF5fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHNvdXJjZVxuICAgICAgICAgICAgICAgICAgc3JjPXtzZWxlY3RlZFZpZGVvLnZpZGVvVXJsIHx8IFwiaHR0cHM6Ly9jb21tb25kYXRhc3RvcmFnZS5nb29nbGVhcGlzLmNvbS9ndHYtdmlkZW9zLWJ1Y2tldC9zYW1wbGUvQmlnQnVja0J1bm55Lm1wNFwifVxuICAgICAgICAgICAgICAgICAgdHlwZT1cInZpZGVvL21wNFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICB7dCgndmlkZW9fZ2FsbGVyeS51bnN1cHBvcnRlZF9icm93c2VyJywgeyBmYWxsYmFjazogJ+aCqOeahOa1j+iniOWZqOS4jeaUr+aMgeinhumikeaSreaUvuOAgicgfSl9XG4gICAgICAgICAgICAgIDwvdmlkZW8+XG5cbiAgICAgICAgICAgICAgey8qIFBsYXkgQnV0dG9uIE92ZXJsYXkgKi99XG4gICAgICAgICAgICAgIHshaXNQbGF5aW5nICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctYmxhY2svMjBcIj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0yMCBoLTIwIHJvdW5kZWQtZnVsbCBiZy13aGl0ZS85MCBob3ZlcjpiZy13aGl0ZSB0ZXh0LWJsYWNrIHNoYWRvdy1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3RvZ2dsZVBsYXl9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxQbGF5IGNsYXNzTmFtZT1cInctOCBoLTggbWwtMVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgey8qIFZpZGVvIENvbnRyb2xzICovfVxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYWJzb2x1dGUgaW5zZXQteC0wIGJvdHRvbS0wIGJnLWdyYWRpZW50LXRvLXQgZnJvbS1ibGFjay84MCB2aWEtYmxhY2svNDAgdG8tdHJhbnNwYXJlbnQgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMCAke3Nob3dDb250cm9scyA/IFwib3BhY2l0eS0xMDBcIiA6IFwib3BhY2l0eS0wXCJ9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHsvKiBQcm9ncmVzcyBCYXIgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweC02IHBiLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwicmFuZ2VcIlxuICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgICAgbWF4PVwiMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Byb2dyZXNzfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVByb2dyZXNzQ2hhbmdlKFtwYXJzZUZsb2F0KGUudGFyZ2V0LnZhbHVlKV0pfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC0xIGJnLXdoaXRlLzMwIHJvdW5kZWQtbGcgYXBwZWFyYW5jZS1ub25lIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogQ29udHJvbCBCdXR0b25zICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB4LTYgcGItNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBob3ZlcjpiZy13aGl0ZS8yMCB3LTEwIGgtMTAgcm91bmRlZCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlUGxheX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtpc1BsYXlpbmcgPyA8UGF1c2UgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+IDogPFBsYXkgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+fVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBob3ZlcjpiZy13aGl0ZS8yMCB3LTEwIGgtMTAgcm91bmRlZCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVNdXRlfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtpc011dGVkID8gPFZvbHVtZVggY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+IDogPFZvbHVtZTIgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+fVxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJyYW5nZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1pbj1cIjBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBtYXg9XCIxMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aXNNdXRlZCA/IDAgOiB2b2x1bWUgKiAxMDB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlVm9sdW1lQ2hhbmdlKFtwYXJzZUZsb2F0KGUudGFyZ2V0LnZhbHVlKV0pfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC0xIGJnLXdoaXRlLzMwIHJvdW5kZWQtbGcgYXBwZWFyYW5jZS1ub25lIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFRpbWUoY3VycmVudFRpbWUpfSAvIHtmb3JtYXRUaW1lKGR1cmF0aW9uKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6YmxvY2sgdGV4dC13aGl0ZSB0ZXh0LWxnIGZvbnQtbWVkaXVtIG1heC13LW1kIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2xvY2FsZSA9PT0gJ3poJyA/IHNlbGVjdGVkVmlkZW8udGl0bGUgOiBzZWxlY3RlZFZpZGVvLnRpdGxlX2VufVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbSBiZy13aGl0ZS8yMCBweC0yIHB5LTEgcm91bmRlZFwiPntzZWxlY3RlZFZpZGVvLnJlc29sdXRpb259PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGhvdmVyOmJnLXdoaXRlLzIwIHctMTAgaC0xMCByb3VuZGVkIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlRnVsbHNjcmVlbn1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPE1heGltaXplIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKVxufSJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsIlBsYXkiLCJQYXVzZSIsIlZvbHVtZTIiLCJWb2x1bWVYIiwiTWF4aW1pemUiLCJYIiwidXNlVHJhbnNsYXRpb24iLCJ2aWRlb3MiLCJpZCIsInRpdGxlIiwidGl0bGVfZW4iLCJkdXJhdGlvbiIsImNhdGVnb3J5IiwiY2F0ZWdvcnlfZW4iLCJhc3BlY3RSYXRpbyIsInJlc29sdXRpb24iLCJ2aWRlb1VybCIsIlZpZGVvR2FsbGVyeSIsImNsYXNzTmFtZSIsInQiLCJsb2NhbGUiLCJzZWxlY3RlZFZpZGVvIiwic2V0U2VsZWN0ZWRWaWRlbyIsImlzUGxheWluZyIsInNldElzUGxheWluZyIsImN1cnJlbnRUaW1lIiwic2V0Q3VycmVudFRpbWUiLCJzZXREdXJhdGlvbiIsInZvbHVtZSIsInNldFZvbHVtZSIsImlzTXV0ZWQiLCJzZXRJc011dGVkIiwic2hvd0NvbnRyb2xzIiwic2V0U2hvd0NvbnRyb2xzIiwic2VsZWN0ZWRDYXRlZ29yeSIsInNldFNlbGVjdGVkQ2F0ZWdvcnkiLCJmYWxsYmFjayIsInNlbGVjdGVkRm9ybWF0Iiwic2V0U2VsZWN0ZWRGb3JtYXQiLCJ2aWRlb1JlZiIsImNvbnRyb2xzVGltZW91dFJlZiIsImdldExvY2FsaXplZENhdGVnb3JpZXMiLCJhbGxDYXRlZ29yaWVzIiwibWFwIiwidiIsIkFycmF5IiwiZnJvbSIsIlNldCIsImdldExvY2FsaXplZEZvcm1hdHMiLCJmaWx0ZXJlZFZpZGVvcyIsImZpbHRlciIsInZpZGVvIiwidmlkZW9DYXRlZ29yeSIsImFsbFRleHQiLCJsYW5kc2NhcGVUZXh0IiwicG9ydHJhaXRUZXh0IiwiY2F0ZWdvcnlNYXRjaCIsImZvcm1hdE1hdGNoIiwic29ydCIsImEiLCJiIiwiY3VycmVudCIsInVwZGF0ZVRpbWUiLCJ1cGRhdGVEdXJhdGlvbiIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwib3BlblZpZGVvIiwiY2xvc2VWaWRlbyIsInBhdXNlIiwidG9nZ2xlUGxheSIsInBsYXkiLCJoYW5kbGVQcm9ncmVzc0NoYW5nZSIsInZhbHVlIiwibmV3VGltZSIsImhhbmRsZVZvbHVtZUNoYW5nZSIsIm5ld1ZvbHVtZSIsInRvZ2dsZU11dGUiLCJ0b2dnbGVGdWxsc2NyZWVuIiwiZG9jdW1lbnQiLCJmdWxsc2NyZWVuRWxlbWVudCIsImV4aXRGdWxsc2NyZWVuIiwicmVxdWVzdEZ1bGxzY3JlZW4iLCJoYW5kbGVNb3VzZU1vdmUiLCJjbGVhclRpbWVvdXQiLCJzZXRUaW1lb3V0IiwiZm9ybWF0VGltZSIsInRpbWUiLCJtaW51dGVzIiwiTWF0aCIsImZsb29yIiwic2Vjb25kcyIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJwcm9ncmVzcyIsImRpdiIsImgxIiwicCIsIm9uQ2xpY2siLCJwcmVsb2FkIiwibXV0ZWQiLCJwbGF5c0lubGluZSIsInNvdXJjZSIsInNyYyIsInR5cGUiLCJmaWxsIiwiaDMiLCJzcGFuIiwidmlld3MiLCJ1cGxvYWREYXRlIiwiYnV0dG9uIiwib25Nb3VzZU1vdmUiLCJvbk1vdXNlTGVhdmUiLCJyZWYiLCJpbnB1dCIsIm1pbiIsIm1heCIsIm9uQ2hhbmdlIiwiZSIsInBhcnNlRmxvYXQiLCJ0YXJnZXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/VideoGallery.tsx\n"));

/***/ })

});