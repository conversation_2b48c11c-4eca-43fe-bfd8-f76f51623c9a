'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from "next/navigation";
import { useForm } from 'react-hook-form';
import AdminLayout from '../../../../../components/admin/Layout';
import { i18n } from '../../../../../lib/i18n-config';

interface ContentFormData {
  key: string;
  section: string;
  page: string;
  content: string;
  translations: {
    [locale: string]: string;
  };
}

export default function Page() {
  const router = useRouter();
  const params = useParams<{ id: string }>();
  const id = params.id;
  const isNew = id === 'new';

  const [loading, setLoading] = useState(!isNew);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [translations, setTranslations] = useState<{ [locale: string]: string }>({});

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<ContentFormData>({
    defaultValues: {
      translations: {},
    },
  });

  // Watch form values
  // const watchKey = watch('key', ''); // Removed unused variable
  // const watchPage = watch('page', ''); // Removed unused variable
  // const watchSection = watch('section', ''); // Removed unused variable

  useEffect(() => {
    const fetchContent = async () => {
      if (isNew) {
        setLoading(false);
        return;
      }

      try {
        const res = await fetch(`/api/content/${id}`);

        if (!res.ok) {
          throw new Error('Failed to fetch content');
        }

        const data = await res.json();

        if (data.success) {
          const content = data.data;

          // Set form values
          setValue('key', content.key);
          setValue('section', content.section);
          setValue('page', content.page);
          setValue('content', content.content);

          // Set translations
          if (content.translations) {
            setTranslations(content.translations);
          }
        } else {
          throw new Error(data.message || 'Failed to fetch content');
        }
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('An unknown error occurred while fetching the content.');
        }
        console.error('Error fetching content:', err);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchContent();
    }
  }, [id, isNew, setValue]);

  const onSubmit = async (data: ContentFormData) => {
    setSaving(true);
    setError(null);

    try {
      // Prepare data for API
      const contentData = {
        ...data,
        translations,
      };

      // Determine if creating or updating
      const url = isNew ? '/api/content' : `/api/content/${id}`;
      const method = isNew ? 'POST' : 'PUT';

      const res = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contentData),
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to save content');
      }

      const responseData = await res.json();

      if (responseData.success) {
        // Redirect to content list
        router.push('/[lang]/admin/content');
      } else {
        throw new Error(responseData.message || 'Failed to save content');
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred while saving the content.');
      }
      console.error('Error saving content:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleTranslationChange = (locale: string, value: string) => {
    setTranslations({
      ...translations,
      [locale]: value,
    });
  };

  // Common page types
  const pageOptions = [
    'home',
    'about-us',
    'products',
    'services',
    'contact-us',
    'custom-playground-design',
    'quality-control',
    'safe-standard',
    'installation-services',
  ];

  // Common section types
  const sectionOptions = [
    'hero',
    'features',
    'about',
    'services',
    'products',
    'testimonials',
    'cta',
    'footer',
  ];

  return (
    <AdminLayout title={isNew ? 'Add New Content' : 'Edit Content'}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-800">
          {isNew ? 'Add New Content' : 'Edit Content'}
        </h2>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-xl text-gray-500">Loading content...</div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-6">
          {error && <div className="bg-red-100 text-red-700 p-4 rounded-md mb-6">{error}</div>}

          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Key <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  className={`w-full px-4 py-2 border rounded-md ${
                    errors.key ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="e.g. hero.title"
                  {...register('key', { required: 'Key is required' })}
                />
                {errors.key && <p className="mt-1 text-sm text-red-500">{errors.key.message}</p>}
                <p className="mt-1 text-sm text-gray-500">
                  Unique identifier for this content (e.g. hero.title, about.description)
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Page <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  list="page-options"
                  className={`w-full px-4 py-2 border rounded-md ${
                    errors.page ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="e.g. home"
                  {...register('page', { required: 'Page is required' })}
                />
                <datalist id="page-options">
                  {pageOptions.map(option => (
                    <option key={option} value={option} />
                  ))}
                </datalist>
                {errors.page && <p className="mt-1 text-sm text-red-500">{errors.page.message}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Section <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  list="section-options"
                  className={`w-full px-4 py-2 border rounded-md ${
                    errors.section ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="e.g. hero"
                  {...register('section', { required: 'Section is required' })}
                />
                <datalist id="section-options">
                  {sectionOptions.map(option => (
                    <option key={option} value={option} />
                  ))}
                </datalist>
                {errors.section && (
                  <p className="mt-1 text-sm text-red-500">{errors.section.message}</p>
                )}
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Content (Default/English) <span className="text-red-500">*</span>
              </label>
              <textarea
                rows={5}
                className={`w-full px-4 py-2 border rounded-md ${
                  errors.content ? 'border-red-500' : 'border-gray-300'
                }`}
                {...register('content', { required: 'Content is required' })}
              ></textarea>
              {errors.content && (
                <p className="mt-1 text-sm text-red-500">{errors.content.message}</p>
              )}
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-800 mb-3">Translations</h3>

              {i18n.locales
                .filter(locale => locale !== 'en')
                .map(locale => (
                  <div key={locale} className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {locale === 'zh' ? '中文 (Chinese)' : locale.toUpperCase()}
                    </label>
                    <textarea
                      rows={5}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md"
                      value={translations[locale] || ''}
                      onChange={e => handleTranslationChange(locale, e.target.value)}
                    ></textarea>
                  </div>
                ))}
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => router.push('/[lang]/admin/content')}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={saving}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400"
              >
                {saving ? 'Saving...' : 'Save Content'}
              </button>
            </div>
          </form>
        </div>
      )}
    </AdminLayout>
  );
}
