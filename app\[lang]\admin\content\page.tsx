'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import AdminLayout from "../../../../components/admin/Layout";

interface Content {
  _id: string;
  key: string;
  section: string;
  page: string;
  content: string;
  createdAt: string;
  updatedAt: string;
}

export default function Page({ params }: { params: { lang: string } }) {
  const [content, setContent] = useState<Content[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState({
    page: '',
    section: '',
  });
  const [language, setLanguage] = useState<string>('zh'); // 默认中文

  // 初始化时从localStorage获取语言设置
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedLang = localStorage.getItem('ADMIN_LANG') || 'zh';
      setLanguage(storedLang);
    }
  }, []);

  // 翻译函数
  const t = (key: string) => {
    const translations: { [key: string]: { [key: string]: string } } = {
      content_management: { zh: '内容管理', en: 'Content Management' },
      add_new_content: { zh: '添加新内容', en: 'Add New Content' },
      all_pages: { zh: '所有页面', en: 'All Pages' },
      all_sections: { zh: '所有板块', en: 'All Sections' },
      loading_content: { zh: '加载内容中...', en: 'Loading content...' },
      no_content_found: { zh: '未找到内容', en: 'No content found' },
      add_first_content: { zh: '添加您的第一个内容', en: 'Add your first content' },
      key: { zh: '键名', en: 'Key' },
      page: { zh: '页面', en: 'Page' },
      section: { zh: '板块', en: 'Section' },
      content_preview: { zh: '内容预览', en: 'Content Preview' },
      last_updated: { zh: '最后更新', en: 'Last Updated' },
      actions: { zh: '操作', en: 'Actions' },
      edit: { zh: '编辑', en: 'Edit' },
      delete: { zh: '删除', en: 'Delete' },
      delete_confirm: {
        zh: '您确定要删除此内容吗？',
        en: 'Are you sure you want to delete this content?',
      },
      delete_error: { zh: '错误：', en: 'Error: ' },
    };

    return translations[key]?.[language] || key;
  };

  const fetchContent = async () => {
    console.log('fetchContent called');
    setLoading(true);
    setError(null);

    try {
      // Build query string
      const queryParams = new URLSearchParams();

      if (filter.page) {
        queryParams.append('page', filter.page);
      }

      if (filter.section) {
        queryParams.append('section', filter.section);
      }

      const url = `/api/content?${queryParams.toString()}`;
      console.log('Fetching from URL:', url);

      const res = await fetch(url);
      console.log('Response status:', res.status, res.statusText);

      if (!res.ok) {
        throw new Error('Failed to fetch content');
      }

      const data = await res.json();
      console.log('Content API response:', data);
      console.log('Data type:', typeof data);
      console.log('Data.success:', data.success);
      console.log('Data.data:', data.data);
      console.log('Data.data length:', data.data?.length);

      if (data.success) {
        console.log('Setting content data:', data.data);
        setContent(data.data);
        console.log('Content state after setting:', data.data);
      } else {
        throw new Error(data.message || 'Failed to fetch content');
      }
    } catch (err: unknown) {
      console.error('Error in fetchContent:', err);
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred while fetching content.');
      }
      console.error('Error fetching content:', err);
    } finally {
      setLoading(false);
      console.log('fetchContent finished, loading set to false');
    }
  };

  useEffect(() => {
    fetchContent();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filter]);

  const handleDelete = async (id: string) => {
    if (!window.confirm(t('delete_confirm'))) {
      return;
    }

    try {
      const res = await fetch(`/api/content/${id}`, {
        method: 'DELETE',
      });

      if (!res.ok) {
        throw new Error('Failed to delete content');
      }

      const data = await res.json();

      if (data.success) {
        // Refresh content list
        fetchContent();
      } else {
        throw new Error(data.message || 'Failed to delete content');
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        alert(`${t('delete_error')}${err.message}`);
      } else {
        alert(`${t('delete_error')}An unknown error occurred while deleting content.`);
      }
      console.error('Error deleting content:', err);
    }
  };

  // Get unique pages and sections for filters
  const pagesSet = new Set(content.map(item => item.page));
  const sectionsSet = new Set(content.map(item => item.section));

  // Convert to arrays for safer iteration
  const pages = Array.from(pagesSet);
  const sections = Array.from(sectionsSet);

  // Debug logging
  console.log('Render - content state:', content);
  console.log('Render - content length:', content.length);
  console.log('Render - loading:', loading);
  console.log('Render - error:', error);

  return (
    <AdminLayout title={t('content_management')}>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h2 className="text-2xl font-bold text-gray-800">{t('content_management')}</h2>
        <Link
          href={`/${params.lang}/admin/content/new`}
          className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <span className="mr-2">➕</span>
          {t('add_new_content')}
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-end gap-4 mb-4">
          <div className="flex flex-wrap gap-2">
            <select
              className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={filter.page}
              onChange={e => setFilter({ ...filter, page: e.target.value })}
            >
              <option value="">{t('all_pages')}</option>
              {pages.map(page => (
                <option key={page} value={page}>
                  {page}
                </option>
              ))}
            </select>

            <select
              className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={filter.section}
              onChange={e => setFilter({ ...filter, section: e.target.value })}
            >
              <option value="">{t('all_sections')}</option>
              {sections.map(section => (
                <option key={section} value={section}>
                  {section}
                </option>
              ))}
            </select>
          </div>
        </div>

        {error && <div className="bg-red-100 text-red-700 p-4 rounded-md mb-4">{error}</div>}

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="text-xl text-gray-500">{t('loading_content')}</div>
          </div>
        ) : content.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 text-lg">{t('no_content_found')}</p>
            <Link
              href={`/${params.lang}/admin/content/new`}
              className="mt-4 inline-block text-blue-600 hover:underline"
            >
              {t('add_first_content')}
            </Link>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {t('key')}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {t('page')}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {t('section')}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {t('content_preview')}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {t('last_updated')}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {t('actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {content.map(item => (
                  <tr key={item._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{item.key}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{item.page}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{item.section}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 truncate max-w-xs">
                        {item.content.length > 50
                          ? `${item.content.substring(0, 50)}...`
                          : item.content}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(item.updatedAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link
                        href={`/${params.lang}/admin/content/${item._id}`}
                        className="text-blue-600 hover:text-blue-900 mr-4"
                      >
                        {t('edit')}
                      </Link>
                      <button
                        onClick={() => handleDelete(item._id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        {t('delete')}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
