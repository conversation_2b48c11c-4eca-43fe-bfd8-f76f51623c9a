'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '../../../../components/admin/Layout';
import AuthGuard from '../../../../components/admin/AuthGuard';

interface EmailSettings {
  smtp_host: string;
  smtp_port: number;
  smtp_user: string;
  smtp_pass: string;
  from_email: string;
  from_name: string;
  admin_email: string;
  sendgrid_api_key: string;
}

export default function EmailSettingsPage() {
  const [settings, setSettings] = useState<EmailSettings>({
    smtp_host: 'smtp.gmail.com',
    smtp_port: 587,
    smtp_user: '',
    smtp_pass: '',
    from_email: '',
    from_name: '跨境电商网站',
    admin_email: '',
    sendgrid_api_key: '',
  });
  
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [testEmailLoading, setTestEmailLoading] = useState(false);
  const [emailjsTestLoading, setEmailjsTestLoading] = useState(false);
  const [recipientEmail, setRecipientEmail] = useState('<EMAIL>');

  // 处理表单输入
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: name === 'smtp_port' ? parseInt(value) || 587 : value
    }));
  };

  // 保存设置
  const handleSave = async () => {
    setLoading(true);
    setMessage(null);

    try {
      // 这里应该调用API保存设置到环境变量或数据库
      // 由于环境变量在运行时不能修改，这里只是演示
      console.log('保存邮件设置:', settings);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMessage({ type: 'success', text: '邮件设置已保存！请重启应用以使设置生效。' });
    } catch (error) {
      setMessage({ type: 'error', text: '保存设置失败，请重试。' });
    } finally {
      setLoading(false);
    }
  };

  // 发送测试邮件 (SMTP)
  const sendTestEmail = async () => {
    setTestEmailLoading(true);
    setMessage(null);

    try {
      const response = await fetch('/api/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          testData: {
            id: 999,
            name: '测试用户',
            email: '<EMAIL>',
            phone: '+86 123-4567-8900',
            country: '中国',
            playground_size: '100-500 sqm',
            product: '测试产品',
            message: '这是一封测试邮件，用于验证邮件通知功能是否正常工作。',
            created_at: new Date().toISOString(),
          }
        }),
      });

      const result = await response.json();

      if (result.success) {
        setMessage({ type: 'success', text: '测试邮件发送成功！请检查您的邮箱。' });
      } else {
        setMessage({ type: 'error', text: `测试邮件发送失败: ${result.message}` });
      }
    } catch (error) {
      setMessage({ type: 'error', text: '发送测试邮件时出错，请检查网络连接。' });
    } finally {
      setTestEmailLoading(false);
    }
  };

  // 发送EmailJS测试邮件 (直接使用SDK)
  const sendEmailJSTest = async () => {
    setEmailjsTestLoading(true);
    setMessage(null);

    try {
      // 动态导入EmailJS
      const emailjs = await import('@emailjs/browser');

      // 初始化EmailJS
      emailjs.init('NEGhUwls3EE6iiapf');

      // 测试数据
      const templateParams = {
        to_email: recipientEmail,
        from_name: '跨境电商网站',
        name: '测试用户',
        email: '<EMAIL>',
        phone: '+86 138-0000-0000',
        country: '中国',
        playground_size: '500-1000 sqm',
        product: 'AR互动娱乐设备',
        message: '这是一封来自管理员后台的测试邮件，用于验证EmailJS邮件发送功能是否正常工作。',
        time: new Date().toLocaleString('zh-CN')
      };

      console.log('发送EmailJS邮件，参数:', templateParams);

      // 发送邮件
      const response = await emailjs.send(
        'default_service',
        'template_contact',
        templateParams
      );

      console.log('EmailJS发送成功:', response);

      setMessage({
        type: 'success',
        text: `EmailJS邮件发送成功！已发送到 ${recipientEmail}`
      });

    } catch (error) {
      console.error('EmailJS发送失败:', error);
      setMessage({
        type: 'error',
        text: `EmailJS邮件发送失败: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setEmailjsTestLoading(false);
    }
  };

  return (
    <AuthGuard adminOnly>
      <AdminLayout title="邮件设置">
        <div className="space-y-6">
          {/* 页面标题 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">邮件通知设置</h2>
            <p className="text-gray-600">
              配置邮件服务以接收表单提交通知。支持 SMTP 和 SendGrid 两种方式。
            </p>
          </div>

          {/* 消息提示 */}
          {message && (
            <div className={`p-4 rounded-lg ${
              message.type === 'success' 
                ? 'bg-green-100 text-green-700 border border-green-200' 
                : 'bg-red-100 text-red-700 border border-red-200'
            }`}>
              {message.text}
            </div>
          )}

          {/* SMTP 设置 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">SMTP 邮件服务设置</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SMTP 服务器
                </label>
                <input
                  type="text"
                  name="smtp_host"
                  value={settings.smtp_host}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="smtp.gmail.com"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SMTP 端口
                </label>
                <input
                  type="number"
                  name="smtp_port"
                  value={settings.smtp_port}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="587"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  邮箱用户名
                </label>
                <input
                  type="email"
                  name="smtp_user"
                  value={settings.smtp_user}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  邮箱密码/应用密码
                </label>
                <input
                  type="password"
                  name="smtp_pass"
                  value={settings.smtp_pass}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="your-app-password"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  发件人邮箱
                </label>
                <input
                  type="email"
                  name="from_email"
                  value={settings.from_email}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  发件人名称
                </label>
                <input
                  type="text"
                  name="from_name"
                  value={settings.from_name}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="跨境电商网站"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  管理员邮箱 (接收通知)
                </label>
                <input
                  type="email"
                  name="admin_email"
                  value={settings.admin_email}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </div>

          {/* EmailJS 测试发送 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">📧 EmailJS 邮件发送测试</h3>
            <p className="text-gray-600 mb-4">
              使用EmailJS API直接发送测试邮件，无需SMTP配置。
            </p>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  收件邮箱地址
                </label>
                <input
                  type="email"
                  value={recipientEmail}
                  onChange={(e) => setRecipientEmail(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="请输入收件邮箱地址"
                />
              </div>

              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 className="text-sm font-semibold text-blue-900 mb-2">EmailJS 配置状态</h4>
                <div className="grid grid-cols-2 gap-2 text-xs text-blue-800">
                  <div>✅ Public Key: NEGhUwls3EE6iiapf</div>
                  <div>✅ Service ID: default_service</div>
                  <div>✅ Template ID: template_contact</div>
                  <div>✅ 免费额度: 200封/月</div>
                </div>
                <div className="mt-2 text-xs text-blue-700">
                  <p>⚠️ 注意：需要在EmailJS后台创建对应的服务和模板</p>
                </div>
              </div>

              <button
                onClick={sendEmailJSTest}
                disabled={emailjsTestLoading || !recipientEmail}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {emailjsTestLoading ? '发送中...' : '发送EmailJS测试邮件'}
              </button>
            </div>
          </div>

          {/* SendGrid 设置 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">SendGrid API 设置 (可选)</h3>
            <p className="text-gray-600 mb-4">
              如果您使用 SendGrid 服务，可以配置 API Key 作为备用邮件发送方式。
            </p>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                SendGrid API Key
              </label>
              <input
                type="password"
                name="sendgrid_api_key"
                value={settings.sendgrid_api_key}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="SG.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
              />
            </div>
          </div>

          {/* 配置说明 */}
          <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">📋 配置说明</h3>
            <div className="space-y-2 text-blue-800">
              <p><strong>Gmail 用户:</strong> 需要开启两步验证并生成应用密码</p>
              <p><strong>QQ 邮箱:</strong> 使用 smtp.qq.com，端口 587</p>
              <p><strong>163 邮箱:</strong> 使用 smtp.163.com，端口 587</p>
              <p><strong>企业邮箱:</strong> 请联系您的邮件服务提供商获取 SMTP 设置</p>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-4">
            <button
              onClick={handleSave}
              disabled={loading}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? '保存中...' : '保存设置'}
            </button>

            <button
              onClick={sendTestEmail}
              disabled={testEmailLoading || !settings.admin_email}
              className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {testEmailLoading ? '发送中...' : '发送测试邮件'}
            </button>
          </div>

          {/* 重要提示 */}
          <div className="bg-yellow-50 p-6 rounded-lg border border-yellow-200">
            <h3 className="text-lg font-semibold text-yellow-900 mb-3">⚠️ 重要提示</h3>
            <div className="space-y-2 text-yellow-800">
              <p>1. 邮件设置需要在 .env.local 文件中配置，页面保存后需要重启应用</p>
              <p>2. 建议使用应用密码而不是账户密码，以提高安全性</p>
              <p>3. 测试邮件功能可以验证当前配置是否正确</p>
              <p>4. 如果 SMTP 发送失败，系统会自动尝试 SendGrid API（如果已配置）</p>
            </div>
          </div>
        </div>
      </AdminLayout>
    </AuthGuard>
  );
}
