'use client';

import type { Metadata } from 'next';
import { SessionProvider } from 'next-auth/react';
import { useEffect } from 'react';
import '../../../styles/admin-login.css';
import Head from 'next/head';

export default function LoginLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  useEffect(() => {
    // 确保Font Awesome图标库已加载
    const existingLink = document.querySelector('link[href*="font-awesome"]');
    if (!existingLink) {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
      link.integrity = 'sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==';
      link.crossOrigin = 'anonymous';
      link.referrerPolicy = 'no-referrer';
      document.head.appendChild(link);

      // 等待字体加载完成
      link.onload = () => {
        console.log('Font Awesome loaded successfully');
      };

      link.onerror = () => {
        console.error('Failed to load Font Awesome');
        // 尝试备用CDN
        const backupLink = document.createElement('link');
        backupLink.rel = 'stylesheet';
        backupLink.href = 'https://use.fontawesome.com/releases/v6.4.0/css/all.css';
        document.head.appendChild(backupLink);
      };
    }

    // 强制重新渲染图标
    setTimeout(() => {
      const icons = document.querySelectorAll('i[class*="fa"]');
      icons.forEach(icon => {
        icon.style.fontFamily = '"Font Awesome 6 Free", "Font Awesome 6 Brands"';
        icon.style.fontWeight = '900';
      });
    }, 100);
  }, []);

  return (
    <>
      <Head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
      </Head>
      <SessionProvider>
        {children}
      </SessionProvider>
    </>
  );
}
