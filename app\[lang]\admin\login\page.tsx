'use client';

import { useState, useEffect } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter } from "next/navigation";
import { useForm } from 'react-hook-form';

type FormData = {
  identifier: string; // 可以是用户名或邮箱
  password: string;
};

export default function Page() {
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [currentYear, setCurrentYear] = useState<number>(2023);
  const [language, setLanguage] = useState<string>('zh'); // 默认中文
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>();

  // 客户端初始化时设置年份和语言，避免服务端渲染不匹配
  useEffect(() => {
    setCurrentYear(new Date().getFullYear());

    if (typeof window !== 'undefined') {
      const storedLang = localStorage.getItem('ADMIN_LANG') || 'zh';
      setLanguage(storedLang);
    }
  }, []);

  // 切换语言
  const toggleLanguage = () => {
    const newLang = language === 'zh' ? 'en' : 'zh';
    setLanguage(newLang);
    localStorage.setItem('ADMIN_LANG', newLang);
  };

  // 翻译函数
  const t = (key: string) => {
    const translations: { [key: string]: { [key: string]: string } } = {
      admin_login: { zh: '管理员登录', en: 'Admin Login' },
      username_email: { zh: '用户名 / 邮箱', en: 'Username / Email' },
      username_email_placeholder: {
        zh: '请输入您的用户名或邮箱',
        en: 'Enter your username or email',
      },
      username_required: { zh: '请输入用户名或邮箱', en: 'Username or email is required' },
      password: { zh: '密码', en: 'Password' },
      password_placeholder: { zh: '请输入您的密码', en: 'Enter your password' },
      password_required: { zh: '请输入密码', en: 'Password is required' },
      forgot_password: { zh: '忘记密码?', en: 'Forgot Password?' },
      logging_in: { zh: '登录中...', en: 'Logging in...' },
      login: { zh: '登 录', en: 'Login' },
      system_footer: { zh: '跨境电商网站管理系统', en: 'Cross-Border E-commerce Admin System' },
      login_failed: {
        zh: '登录失败，请检查用户名和密码',
        en: 'Login failed, please check username and password',
      },
      system_error: { zh: '系统错误，请稍后再试', en: 'System error, please try again later' },
    };

    return translations[key]?.[language] || key;
  };

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    setError(null);

    try {
      // 简单的硬编码验证，避免复杂的NextAuth流程
      if ((data.identifier === 'admin' || data.identifier === '<EMAIL>') &&
          data.password === 'admin123') {
        console.log('使用应急管理员账号直接登录');

        // 设置简单的会话标识
        localStorage.setItem('admin_session', JSON.stringify({
          user: {
            id: '1',
            name: 'admin',
            email: '<EMAIL>',
            role: 'super_admin'
          },
          expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24小时后过期
        }));

        // 直接跳转，不使用NextAuth
        window.location.href = '/zh/admin';
        return;
      }

      // 如果不是应急账号，尝试NextAuth登录
      const isEmail = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(data.identifier);

      const credentials = {
        ...(isEmail ? { email: data.identifier } : { username: data.identifier }),
        password: data.password,
      };

      console.log('尝试NextAuth登录:', data.identifier);

      const result = await signIn('credentials', {
        redirect: false,
        ...credentials,
      });

      console.log('NextAuth登录结果:', JSON.stringify(result));

      if (result?.error) {
        if (result.error === 'CredentialsSignin') {
          setError(t('login_failed'));
        } else {
          setError(`${t('system_error')} (${result.error})`);
        }
        console.error('登录错误:', result.error);
      } else if (!result?.ok) {
        setError(t('login_failed'));
        console.error('登录失败，无明确错误信息');
      } else {
        console.log('NextAuth登录成功，正在跳转...');
        // 使用replace避免历史记录问题
        window.location.replace('/zh/admin');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      setError(`登录过程中发生错误: ${errorMessage}`);
      console.error('登录过程错误:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="admin-login-container">
      {/* 语言切换按钮 */}
      <button
        onClick={toggleLanguage}
        className="language-toggle"
        type="button"
        translate="no"
      >
        {language === 'zh' ? '🇬🇧' : '🇨🇳'}
      </button>

      <div className="admin-login-card">
        {/* 登录头部 */}
        <div className="admin-login-header">
          <div className="admin-login-logo">
            <i className="fas fa-shield-alt"></i>
          </div>
          <h1 className="admin-login-title" translate="no">
            {t('admin_login')}
          </h1>
          <p className="admin-login-subtitle" translate="no">
            请使用管理员账号登录系统
          </p>
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="error-message animate-fadeIn" translate="no">
            <i className="fas fa-exclamation-triangle mr-2"></i>
            {error}
          </div>
        )}

        {/* 登录表单 */}
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="form-group">
            <label htmlFor="identifier" className="form-label" translate="no">
              <i className="fas fa-user mr-2"></i>
              {t('username_email')}
            </label>
            <input
              id="identifier"
              type="text"
              className={`form-input ${errors.identifier ? 'error' : ''}`}
              placeholder={t('username_email_placeholder')}
              translate="no"
              autoComplete="username"
              {...register('identifier', {
                required: t('username_required'),
              })}
            />
            {errors.identifier && (
              <p className="form-error" translate="no">
                {errors.identifier.message}
              </p>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="password" className="form-label" translate="no">
              <i className="fas fa-lock mr-2"></i>
              {t('password')}
            </label>
            <input
              id="password"
              type="password"
              className={`form-input ${errors.password ? 'error' : ''}`}
              placeholder={t('password_placeholder')}
              translate="no"
              autoComplete="current-password"
              {...register('password', { required: t('password_required') })}
            />
            {errors.password && (
              <p className="form-error" translate="no">
                {errors.password.message}
              </p>
            )}
          </div>

          <div className="form-group">
            <button
              type="submit"
              disabled={loading}
              className="btn-primary"
              translate="no"
            >
              {loading ? (
                <>
                  <i className="fas fa-spinner fa-spin"></i>
                  {t('logging_in')}
                </>
              ) : (
                <>
                  <i className="fas fa-sign-in-alt"></i>
                  {t('login')}
                </>
              )}
            </button>
          </div>
        </form>



        {/* 页脚 */}
        <div className="admin-login-footer" translate="no">
          <p>
            {t('system_footer')} &copy; {currentYear}
          </p>
        </div>
      </div>
    </div>
  );
}
