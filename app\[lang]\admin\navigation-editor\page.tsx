'use client';

import { useState, useEffect } from 'react';
import AdminLayout from "../../../../components/admin/Layout";
import Link from 'next/link';

interface SubMenuItem {
  _id: string;
  name: string;
  slug: string;
  url: string;
  order: number;
  translations: {
    zh?: { name: string };
    en?: { name: string };
  };
}

interface MainNavItem {
  _id: string;
  name: string;
  slug: string;
  order: number;
  isActive: boolean;
  subItems: SubMenuItem[];
  translations: {
    zh?: { name: string };
    en?: { name: string };
  };
}

export default function Page() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [language, setLanguage] = useState<string>('zh');

  // 导航数据
  const [mainNavItems, setMainNavItems] = useState<MainNavItem[]>([]);
  const [selectedNavItem, setSelectedNavItem] = useState<MainNavItem | null>(null);
  const [editingMainNav, setEditingMainNav] = useState<string | null>(null);
  const [editingSubNav, setEditingSubNav] = useState<string | null>(null);

  // 初始化时从localStorage获取语言设置
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedLang = localStorage.getItem('ADMIN_LANG') || 'zh';
      setLanguage(storedLang);
    }
  }, []);

  // 翻译函数
  const t = (key: string) => {
    const translations: { [key: string]: { [key: string]: string } } = {
      navigation_editor: { zh: '导航菜单编辑器', en: 'Navigation Menu Editor' },
      loading: { zh: '加载中...', en: 'Loading...' },
      main_navigation: { zh: '主导航', en: 'Main Navigation' },
      sub_navigation: { zh: '下拉菜单', en: 'Dropdown Menu' },
      name_zh: { zh: '中文名称', en: 'Chinese Name' },
      name_en: { zh: '英文名称', en: 'English Name' },
      url: { zh: '链接地址', en: 'URL' },
      order: { zh: '排序', en: 'Order' },
      actions: { zh: '操作', en: 'Actions' },
      save_all: { zh: '保存全部', en: 'Save All' },
      saving: { zh: '保存中...', en: 'Saving...' },
      save_success: { zh: '保存成功', en: 'Saved successfully' },
      add_main_nav: { zh: '添加主导航', en: 'Add Main Nav' },
      add_sub_nav: { zh: '添加子菜单', en: 'Add Sub Menu' },
      edit: { zh: '编辑', en: 'Edit' },
      delete: { zh: '删除', en: 'Delete' },
      confirm_delete: { zh: '确认删除', en: 'Confirm Delete' },
      no_items: { zh: '暂无菜单项', en: 'No menu items' },
      select_main_nav: { zh: '请选择主导航项查看子菜单', en: 'Select a main navigation item to view sub-menu' },
      cancel: { zh: '取消', en: 'Cancel' },
      save: { zh: '保存', en: 'Save' },
      add_success: { zh: '✅ 子菜单添加成功！', en: '✅ Sub-menu added successfully!' },
      order_tip: { zh: '数字越小显示越靠前', en: 'Smaller numbers appear first' },
    };

    return translations[key]?.[language] || key;
  };

  // 获取导航数据
  const fetchNavigationData = async () => {
    setLoading(true);

    try {
      const res = await fetch('/api/navigation?t=' + Date.now());

      if (!res.ok) {
        throw new Error('Failed to fetch navigation data');
      }

      const data = await res.json();

      if (data.success) {
        setMainNavItems(data.data);
        if (data.data.length > 0) {
          setSelectedNavItem(data.data[0]);
        }
      } else {
        throw new Error(data.message || 'Failed to fetch navigation data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取导航数据失败');
      console.error('Error fetching navigation data:', err);

      // 使用模拟数据
      const mockData: MainNavItem[] = [
        {
          _id: '1',
          name: '产品',
          slug: 'products',
          order: 1,
          isActive: true,
          translations: {
            zh: { name: '产品' },
            en: { name: 'Products' },
          },
          subItems: [
            {
              _id: '1-1',
              name: '室内游乐场',
              slug: 'indoor-playground',
              url: '/products/indoor-playground',
              order: 1,
              translations: {
                zh: { name: '室内游乐场' },
                en: { name: 'Indoor Playground' },
              },
            },
            {
              _id: '1-2',
              name: '蹦床公园',
              slug: 'trampoline-park',
              url: '/products/trampoline-park',
              order: 2,
              translations: {
                zh: { name: '蹦床公园' },
                en: { name: 'Trampoline Park' },
              },
            },
          ],
        },
        {
          _id: '2',
          name: '服务',
          slug: 'services',
          order: 2,
          isActive: true,
          translations: {
            zh: { name: '服务' },
            en: { name: 'Services' },
          },
          subItems: [],
        },
        {
          _id: '3',
          name: '解决方案',
          slug: 'solutions',
          order: 3,
          isActive: true,
          translations: {
            zh: { name: '解决方案' },
            en: { name: 'Solutions' },
          },
          subItems: [],
        },
      ];
      setMainNavItems(mockData);
      setSelectedNavItem(mockData[0]);
    } finally {
      setLoading(false);
    }
  };

  // 首次加载时获取导航数据
  useEffect(() => {
    fetchNavigationData();
  }, []);

  // 更新主导航项
  const updateMainNavItem = (itemId: string, field: string, value: string) => {
    setMainNavItems(items =>
      items.map(item => {
        if (item._id === itemId) {
          if (field.startsWith('translations.')) {
            const [, lang, prop] = field.split('.');
            return {
              ...item,
              translations: {
                ...item.translations,
                [lang]: {
                  ...item.translations?.[lang],
                  [prop]: value,
                },
              },
            };
          } else {
            return {
              ...item,
              [field]: field === 'order' ? parseInt(value) || 0 : value,
            };
          }
        }
        return item;
      })
    );
  };

  // 更新子菜单项
  const updateSubNavItem = (mainNavId: string, subItemId: string, field: string, value: string) => {
    setMainNavItems(items =>
      items.map(item => {
        if (item._id === mainNavId) {
          return {
            ...item,
            subItems: item.subItems.map(subItem => {
              if (subItem._id === subItemId) {
                if (field.startsWith('translations.')) {
                  const [, lang, prop] = field.split('.');
                  return {
                    ...subItem,
                    translations: {
                      ...subItem.translations,
                      [lang]: {
                        ...subItem.translations?.[lang],
                        [prop]: value,
                      },
                    },
                  };
                } else {
                  return {
                    ...subItem,
                    [field]: field === 'order' ? parseInt(value) || 0 : value,
                  };
                }
              }
              return subItem;
            }),
          };
        }
        return item;
      })
    );
  };

  // 添加新的主导航项
  const addMainNavItem = () => {
    const newItem: MainNavItem = {
      _id: `new-${Date.now()}`,
      name: '新导航',
      slug: 'new-nav',
      order: mainNavItems.length + 1,
      isActive: true,
      subItems: [],
      translations: {
        zh: { name: '新导航' },
        en: { name: 'New Nav' },
      },
    };
    setMainNavItems([...mainNavItems, newItem]);
  };

  // 添加新的子菜单项
  const addSubNavItem = (mainNavId: string) => {
    // 找到对应的主导航项
    const targetMainNav = mainNavItems.find(item => item._id === mainNavId);

    const newSubItem: SubMenuItem = {
      _id: `sub-${Date.now()}`,
      name: '新子菜单',
      slug: 'new-sub-menu',
      url: '/new-page',
      order: (targetMainNav?.subItems.length || 0) + 1,
      translations: {
        zh: { name: '新子菜单' },
        en: { name: 'New Sub Menu' },
      },
    };

    const updatedItems = mainNavItems.map(item => {
      if (item._id === mainNavId) {
        return {
          ...item,
          subItems: [...item.subItems, newSubItem],
        };
      }
      return item;
    });

    setMainNavItems(updatedItems);

    // 同时更新选中的导航项
    if (selectedNavItem && selectedNavItem._id === mainNavId) {
      const updatedSelectedItem = {
        ...selectedNavItem,
        subItems: [...selectedNavItem.subItems, newSubItem],
      };
      setSelectedNavItem(updatedSelectedItem);
    }
  };

  // 删除主导航项
  const deleteMainNavItem = (itemId: string) => {
    if (confirm(t('confirm_delete'))) {
      setMainNavItems(items => items.filter(item => item._id !== itemId));
      if (selectedNavItem?._id === itemId) {
        setSelectedNavItem(mainNavItems[0] || null);
      }
    }
  };

  // 删除子菜单项
  const deleteSubNavItem = (mainNavId: string, subItemId: string) => {
    if (confirm(t('confirm_delete'))) {
      setMainNavItems(items =>
        items.map(item => {
          if (item._id === mainNavId) {
            return {
              ...item,
              subItems: item.subItems.filter(subItem => subItem._id !== subItemId),
            };
          }
          return item;
        })
      );
    }
  };

  // 保存所有导航修改
  const saveAllChanges = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const res = await fetch('/api/navigation', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ navigationData: mainNavItems }),
      });

      if (!res.ok) {
        throw new Error('Failed to save navigation data');
      }

      const data = await res.json();

      if (data.success) {
        setSuccess(t('save_success'));
        // 刷新导航数据
        fetchNavigationData();
      } else {
        throw new Error(data.message || 'Failed to save navigation data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存失败');
      console.error('Error saving navigation data:', err);
    } finally {
      setSaving(false);
    }
  };

  return (
    <AdminLayout title={t('navigation_editor')}>
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-xl text-gray-500">{t('loading')}</div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {error && <div className="col-span-full bg-red-100 text-red-700 p-4 rounded-md mb-4">{error}</div>}

          {success && (
            <div className="col-span-full bg-green-100 text-green-700 p-4 rounded-md mb-4">{success}</div>
          )}

          {/* 主导航列表 */}
          <div className="bg-white rounded-lg shadow">
            <div className="flex justify-between items-center p-4 border-b">
              <h2 className="text-xl font-bold text-gray-800">{t('main_navigation')}</h2>
              <button
                onClick={addMainNavItem}
                className="px-3 py-1 bg-green-600 text-white rounded-md text-sm hover:bg-green-700"
              >
                {t('add_main_nav')}
              </button>
            </div>

            <div className="p-4">
              {mainNavItems.length === 0 ? (
                <div className="text-center text-gray-500 py-8">{t('no_items')}</div>
              ) : (
                <div className="space-y-2">
                  {mainNavItems.map(item => (
                    <div
                      key={item._id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedNavItem?._id === item._id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedNavItem(item)}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex-1">
                          <div className="grid grid-cols-2 gap-2 mb-2">
                            <input
                              type="text"
                              className="px-2 py-1 border border-gray-300 rounded text-sm"
                              placeholder={t('name_zh')}
                              value={item.translations?.zh?.name || ''}
                              onChange={e => updateMainNavItem(item._id, 'translations.zh.name', e.target.value)}
                              onClick={e => e.stopPropagation()}
                            />
                            <input
                              type="text"
                              className="px-2 py-1 border border-gray-300 rounded text-sm"
                              placeholder={t('name_en')}
                              value={item.translations?.en?.name || ''}
                              onChange={e => updateMainNavItem(item._id, 'translations.en.name', e.target.value)}
                              onClick={e => e.stopPropagation()}
                            />
                          </div>
                          <div className="flex gap-2">
                            <div className="flex flex-col">
                              <input
                                type="number"
                                className="w-16 px-2 py-1 border border-gray-300 rounded text-sm"
                                placeholder={t('order')}
                                value={item.order}
                                onChange={e => updateMainNavItem(item._id, 'order', e.target.value)}
                                onClick={e => e.stopPropagation()}
                              />
                              <span className="text-xs text-blue-600 mt-1">{t('order_tip')}</span>
                            </div>
                            <span className="text-xs text-gray-500 self-center">
                              {item.subItems.length} 个子菜单
                            </span>
                          </div>
                        </div>
                        <button
                          onClick={e => {
                            e.stopPropagation();
                            deleteMainNavItem(item._id);
                          }}
                          className="ml-2 px-2 py-1 text-red-600 hover:text-red-800 text-sm"
                        >
                          {t('delete')}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* 子菜单编辑 */}
          <div className="bg-white rounded-lg shadow">
            <div className="flex justify-between items-center p-4 border-b">
              <h2 className="text-xl font-bold text-gray-800">
                {selectedNavItem ? `${selectedNavItem.translations?.zh?.name || selectedNavItem.name} - ${t('sub_navigation')}` : t('sub_navigation')}
              </h2>
              {selectedNavItem && (
                <button
                  onClick={() => {
                    addSubNavItem(selectedNavItem._id);
                    alert(t('add_success'));
                  }}
                  className="px-3 py-1 bg-green-600 text-white rounded-md text-sm hover:bg-green-700"
                >
                  {t('add_sub_nav')}
                </button>
              )}
            </div>

            <div className="p-4">
              {!selectedNavItem ? (
                <div className="text-center text-gray-500 py-8">{t('select_main_nav')}</div>
              ) : (() => {
                // 从mainNavItems中获取最新的选中项数据
                const currentSelectedItem = mainNavItems.find(item => item._id === selectedNavItem._id);
                const currentSubItems = currentSelectedItem?.subItems || [];

                return currentSubItems.length === 0 ? (
                  <div className="text-center text-gray-500 py-8">{t('no_items')}</div>
                ) : (
                  <div className="space-y-3">
                    {currentSubItems.map(subItem => (
                    <div key={subItem._id} className="p-3 border border-gray-200 rounded-lg">
                      <div className="grid grid-cols-2 gap-2 mb-2">
                        <input
                          type="text"
                          className="px-2 py-1 border border-gray-300 rounded text-sm"
                          placeholder={t('name_zh')}
                          value={subItem.translations?.zh?.name || ''}
                          onChange={e => updateSubNavItem(selectedNavItem._id, subItem._id, 'translations.zh.name', e.target.value)}
                        />
                        <input
                          type="text"
                          className="px-2 py-1 border border-gray-300 rounded text-sm"
                          placeholder={t('name_en')}
                          value={subItem.translations?.en?.name || ''}
                          onChange={e => updateSubNavItem(selectedNavItem._id, subItem._id, 'translations.en.name', e.target.value)}
                        />
                      </div>
                      <div className="grid grid-cols-3 gap-2 mb-2">
                        <input
                          type="text"
                          className="px-2 py-1 border border-gray-300 rounded text-sm"
                          placeholder={t('url')}
                          value={subItem.url}
                          onChange={e => updateSubNavItem(selectedNavItem._id, subItem._id, 'url', e.target.value)}
                        />
                        <div className="relative">
                          <input
                            type="number"
                            className="px-2 py-1 border border-gray-300 rounded text-sm w-full"
                            placeholder={t('order')}
                            value={subItem.order}
                            onChange={e => updateSubNavItem(selectedNavItem._id, subItem._id, 'order', e.target.value)}
                          />
                          <div className="text-xs text-blue-600 mt-1">{t('order_tip')}</div>
                        </div>
                        <button
                          onClick={() => deleteSubNavItem(selectedNavItem._id, subItem._id)}
                          className="px-2 py-1 text-red-600 hover:text-red-800 text-sm border border-red-300 rounded"
                        >
                          {t('delete')}
                        </button>
                      </div>
                    </div>
                    ))}
                  </div>
                );
              })()}
            </div>
          </div>

          {/* 保存按钮 */}
          <div className="col-span-full flex justify-end">
            <button
              onClick={saveAllChanges}
              disabled={saving}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
            >
              {saving ? t('saving') : t('save_all')}
            </button>
          </div>
        </div>
      )}
    </AdminLayout>
  );
}


