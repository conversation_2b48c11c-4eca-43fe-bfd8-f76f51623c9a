'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams } from "next/navigation";
import { useForm } from 'react-hook-form';
import AdminLayout from '../../../../../components/admin/Layout';
import Image from 'next/image';

interface ProductFormData {
  name: string;
  slug: string;
  description: string;
  video_url: string;
  key_features: string;
  tech_specs: string;
  applications: string;
  in_stock: boolean;
  images: string[];
  detail_images: string[];
}



export default function Page({ params }: { params: { lang: string; id: string } }) {
  const router = useRouter();
  const id = params.id;
  const isNew = id === 'new';

  const [loading, setLoading] = useState(!isNew);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [images, setImages] = useState<string[]>([]);
  const [detailImages, setDetailImages] = useState<string[]>([]);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [uploadingDetailImage, setUploadingDetailImage] = useState(false);
  const [language, setLanguage] = useState<string>('zh'); // 默认中文


  // 初始化时从localStorage获取语言设置
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedLang = localStorage.getItem('ADMIN_LANG') || 'zh';
      setLanguage(storedLang);
    }
  }, []);

  // 翻译函数
  const t = useCallback(
    (key: string) => {
      const translations: { [key: string]: { [key: string]: string } } = {
        add_new_product: { zh: '添加新产品', en: 'Add New Product' },
        edit_product: { zh: '编辑产品', en: 'Edit Product' },
        loading_product: { zh: '加载产品中...', en: 'Loading product...' },

        name: { zh: '产品名称', en: 'Product Name' },
        slug: { zh: '别名', en: 'Slug' },
        description: { zh: '产品描述', en: 'Product Description' },
        video_url: { zh: '产品视频链接', en: 'Video URL' },
        key_features: { zh: '核心特点 (每行一个)', en: 'Key Features (one per line)' },
        tech_specs: { zh: '技术规格 (JSON格式)', en: 'Technical Specifications (JSON format)' },
        applications: { zh: '应用场景 (每行一个)', en: 'Applications (one per line)' },

        images: { zh: '产品图片', en: 'Product Images' },
        detail_images: { zh: '详情图片', en: 'Detail Images' },
        in_stock: { zh: '有库存', en: 'In Stock' },
        publish: { zh: '发布此产品', en: 'Publish this product' },
        cancel: { zh: '取消', en: 'Cancel' },
        saving: { zh: '保存中...', en: 'Saving...' },
        save_product: { zh: '保存产品', en: 'Save Product' },
        uploading: { zh: '上传中...', en: 'Uploading...' },
        add_image: { zh: '添加图片', en: 'Add Image' },
        upload_info: {
          zh: '上传产品图片。第一张图片将作为主图。',
          en: 'Upload product images. The first image will be used as the main image.',
        },
        video_placeholder: { zh: '例如: https://example.com/video.mp4', en: 'e.g. https://example.com/video.mp4' },
        key_features_placeholder: {
          zh: '高精度投影技术\n多点触控识别\n实时互动反馈',
          en: 'High-precision projection\nMulti-touch recognition\nReal-time interaction',
        },
        tech_specs_placeholder: {
          zh: '{"投影分辨率": "1920x1080", "识别精度": "±2mm", "响应时间": "<50ms"}',
          en: '{"Resolution": "1920x1080", "Accuracy": "±2mm", "Response Time": "<50ms"}',
        },
        applications_placeholder: {
          zh: '儿童游乐场\n科技馆\n商场娱乐区',
          en: 'Children\'s playground\nScience museum\nShopping mall entertainment',
        },
        name_required: { zh: '产品名称是必填项', en: 'Product name is required' },
        slug_required: { zh: '别名是必填项', en: 'Slug is required' },
        slug_format: {
          zh: '别名只能包含小写字母、数字和连字符',
          en: 'Slug can only contain lowercase letters, numbers, and hyphens',
        },
        description_required: { zh: '描述是必填项', en: 'Description is required' },
        product_image: { zh: '产品图片', en: 'Product image' },

      };

      return translations[key]?.[language] || key;
    },
    [language]
  );

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    getValues,
    formState: { errors },
  } = useForm<ProductFormData>({
    defaultValues: {
      name: '',
      slug: '',
      description: '',
      video_url: '',
      key_features: '',
      tech_specs: '',
      applications: '',
      in_stock: true,
      images: [],
      detail_images: [],
    },
  });

  // Watch form values
  const watchName = watch('name', '');

  // 获取正确的API基础URL
  const getApiBaseUrl = () => {
    // 使用环境变量中的API URL，或者根据当前页面URL构建
    const envApiUrl = process.env.NEXT_PUBLIC_API_URL;
    if (envApiUrl) return envApiUrl;

    // 如果没有环境变量，根据当前页面URL确定API URL
    const currentUrl = typeof window !== 'undefined' ? window.location.origin : '';
    return `${currentUrl}/api`;
  };



  useEffect(() => {
    // Generate slug from name
    if (watchName && isNew) {
      const slug = watchName
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-');

      setValue('slug', slug);
    }
  }, [watchName, isNew, setValue]);

  useEffect(() => {
    const fetchProduct = async () => {
      if (isNew) {
        setLoading(false);
        return;
      }

      try {
        const apiBaseUrl = getApiBaseUrl();
        const res = await fetch(`${apiBaseUrl}/products/${id}`);

        if (!res.ok) {
          throw new Error('Failed to fetch product');
        }

        const data = await res.json();

        if (data.success) {
          const product = data.data;

          // Set form values - 处理不同的数据格式
          setValue('name', product.name || product.title || '');
          setValue('slug', product.slug || '');
          setValue('description', product.description || '');
          setValue('video_url', product.video_url || '');

          // 处理key_features - 可能是数组或字符串
          let keyFeaturesText = '';
          if (Array.isArray(product.key_features)) {
            keyFeaturesText = product.key_features.join('\n');
          } else if (typeof product.key_features === 'string') {
            keyFeaturesText = product.key_features;
          }
          setValue('key_features', keyFeaturesText);

          // 处理tech_specs - 可能是对象或字符串
          let techSpecsText = '';
          if (typeof product.tech_specs === 'object' && product.tech_specs !== null) {
            techSpecsText = JSON.stringify(product.tech_specs, null, 2);
          } else if (typeof product.tech_specs === 'string') {
            techSpecsText = product.tech_specs;
          }
          setValue('tech_specs', techSpecsText);

          // 处理applications - 可能是数组或字符串
          let applicationsText = '';
          if (Array.isArray(product.applications)) {
            applicationsText = product.applications.join('\n');
          } else if (typeof product.applications === 'string') {
            applicationsText = product.applications;
          }
          setValue('applications', applicationsText);

          setValue('in_stock', product.in_stock !== false); // 默认为true

          // Set images - 处理不同的图片格式
          setImages(product.images || []);
          setDetailImages(product.detail_images || []);
        } else {
          throw new Error(data.message || 'Failed to fetch product');
        }
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('An unknown error occurred while fetching the product.');
        }
        console.error('Error fetching product:', err);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchProduct();
    }
  }, [id, isNew, setValue]);

  const onSubmit = async (data: ProductFormData) => {
    setSaving(true);
    setError(null);

    try {
      // Process arrays from text areas
      const key_features = data.key_features
        .split('\n')
        .map(feature => feature.trim())
        .filter(feature => feature);

      const applications = data.applications
        .split('\n')
        .map(app => app.trim())
        .filter(app => app);

      // Process tech specs JSON
      let tech_specs = {};
      if (data.tech_specs.trim()) {
        try {
          tech_specs = JSON.parse(data.tech_specs);
        } catch (e) {
          throw new Error('技术规格必须是有效的JSON格式');
        }
      }

      // Prepare data for API
      const productData = {
        ...data,
        key_features,
        applications,
        tech_specs,
        images,
        detail_images: detailImages,
      };

      // Determine if creating or updating
      const apiBaseUrl = getApiBaseUrl();
      const url = isNew ? `${apiBaseUrl}/products` : `${apiBaseUrl}/products/${id}`;
      const method = isNew ? 'POST' : 'PUT';

      // 打印完整的请求体，便于调试
      console.log('Submitting product data:', JSON.stringify(productData));

      const res = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to save product');
      }

      const responseData = await res.json();

      if (responseData.success) {
        // 打印保存后的响应，验证分类是否被保存
        console.log('Product saved successfully:', responseData.data);
        // Redirect to products list
        router.push(`/${params.lang}/admin/products`);
      } else {
        throw new Error(responseData.message || 'Failed to save product');
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred while saving the product.');
      }
      console.error('Error saving product:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setUploadingImage(true);
    setError(null);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const apiBaseUrl = getApiBaseUrl();
      const res = await fetch(`${apiBaseUrl}/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to upload image');
      }

      const data = await res.json();
      if (data.success && data.filePath) {
        setImages(prevImages => [...prevImages, data.filePath]);
      } else {
        throw new Error(data.message || 'Failed to get image path');
      }
    } catch (err: unknown) {
      console.error('Error uploading image:', err);
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred while uploading the image.');
      }
    } finally {
      setUploadingImage(false);
    }
  };

  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };

  const handleDetailImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setUploadingDetailImage(true);
    setError(null);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const apiBaseUrl = getApiBaseUrl();
      const res = await fetch(`${apiBaseUrl}/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to upload detail image');
      }

      const data = await res.json();
      if (data.success && data.filePath) {
        setDetailImages(prevImages => [...prevImages, data.filePath]);
      } else {
        throw new Error(data.message || 'Failed to get detail image path');
      }
    } catch (err: unknown) {
      console.error('Error uploading detail image:', err);
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred while uploading the detail image.');
      }
    } finally {
      setUploadingDetailImage(false);
    }
  };

  const removeDetailImage = (index: number) => {
    const newImages = [...detailImages];
    newImages.splice(index, 1);
    setDetailImages(newImages);
  };



  return (
    <AdminLayout title={isNew ? t('add_new_product') : t('edit_product')}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-800">
          {isNew ? t('add_new_product') : t('edit_product')}
        </h2>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-xl text-gray-500">{t('loading_product')}</div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {error && <div className="bg-red-100 text-red-700 p-4 mb-6">{error}</div>}

          <form onSubmit={handleSubmit(onSubmit)}>
            {/* 模仿产品详情页的布局：左侧图片，右侧信息 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-6">

              {/* 左侧：图片管理区域 */}
              <div className="product-media-editor">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">产品媒体</h3>

                {/* 主图片预览区域 */}
                <div className="main-image-preview bg-gray-100 rounded-lg overflow-hidden mb-4" style={{ height: '350px' }}>
                  {images.length > 0 || getValues('video_url') ? (
                    <div className="relative w-full h-full">
                      {getValues('video_url') ? (
                        <video
                          src={getValues('video_url')}
                          className="w-full h-full object-contain"
                          controls
                        />
                      ) : (
                        <Image
                          src={images[0]}
                          alt="主图预览"
                          layout="fill"
                          objectFit="contain"
                          className="rounded-lg"
                        />
                      )}
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full text-gray-500">
                      <div className="text-center">
                        <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <p>暂无图片或视频</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* 缩略图管理 */}
                <div className="thumbnails-editor">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">{t('images')}</h4>
                  <div className="grid grid-cols-5 gap-2 mb-4">
                    {images.map((image, index) => (
                      <div key={index} className="relative h-20 bg-gray-100 rounded-md overflow-hidden border-2 border-transparent hover:border-blue-500">
                        <Image
                          src={image}
                          alt={`图片 ${index + 1}`}
                          layout="fill"
                          objectFit="cover"
                        />
                        <button
                          type="button"
                          onClick={() => removeImage(index)}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center hover:bg-red-600 text-xs"
                        >
                          ✕
                        </button>
                      </div>
                    ))}

                    {/* 添加图片按钮 */}
                    <div className="h-20 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center hover:border-blue-500">
                      <label className="cursor-pointer text-center">
                        <svg className="w-6 h-6 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        <input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={handleImageUpload}
                          disabled={uploadingImage}
                        />
                      </label>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500">{t('upload_info')}</p>
                </div>

                {/* 详情图片管理 */}
                <div className="detail-images-editor mt-6">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">{t('detail_images')}</h4>
                  <div className="grid grid-cols-3 gap-2 mb-4">
                    {detailImages.map((image, index) => (
                      <div key={index} className="relative h-24 bg-gray-100 rounded-md overflow-hidden">
                        <Image
                          src={image}
                          alt={`详情图片 ${index + 1}`}
                          layout="fill"
                          objectFit="cover"
                        />
                        <button
                          type="button"
                          onClick={() => removeDetailImage(index)}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center hover:bg-red-600 text-xs"
                        >
                          ✕
                        </button>
                      </div>
                    ))}

                    {/* 添加详情图片按钮 */}
                    <div className="h-24 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center hover:border-blue-500">
                      <label className="cursor-pointer text-center">
                        <svg className="w-6 h-6 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        <input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={handleDetailImageUpload}
                          disabled={uploadingDetailImage}
                        />
                      </label>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500">详情图片将在产品详情页的概述选项卡中显示</p>
                </div>
              </div>

              {/* 右侧：产品信息编辑区域 */}
              <div className="product-info-editor">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">产品信息</h3>

                {/* 基本信息 */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('name')} <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      className={`w-full px-4 py-2 border rounded-md ${
                        errors.name ? 'border-red-500' : 'border-gray-300'
                      }`}
                      {...register('name', { required: t('name_required') })}
                    />
                    {errors.name && (
                      <p className="mt-1 text-sm text-red-500">{errors.name.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('slug')} <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      className={`w-full px-4 py-2 border rounded-md ${
                        errors.slug ? 'border-red-500' : 'border-gray-300'
                      }`}
                      {...register('slug', {
                        required: t('slug_required'),
                        pattern: {
                          value: /^[a-z0-9-]+$/,
                          message: t('slug_format'),
                        },
                      })}
                    />
                    {errors.slug && <p className="mt-1 text-sm text-red-500">{errors.slug.message}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('description')} <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      rows={4}
                      className={`w-full px-4 py-2 border rounded-md ${
                        errors.description ? 'border-red-500' : 'border-gray-300'
                      }`}
                      {...register('description', { required: t('description_required') })}
                    ></textarea>
                    {errors.description && (
                      <p className="mt-1 text-sm text-red-500">{errors.description.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('video_url')}
                    </label>
                    <input
                      type="url"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md"
                      placeholder={t('video_placeholder')}
                      {...register('video_url')}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('key_features')}
                    </label>
                    <textarea
                      rows={4}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md"
                      placeholder={t('key_features_placeholder')}
                      {...register('key_features')}
                    ></textarea>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('tech_specs')}
                    </label>
                    <textarea
                      rows={4}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md font-mono text-sm"
                      placeholder={t('tech_specs_placeholder')}
                      {...register('tech_specs')}
                    ></textarea>
                    <p className="mt-1 text-xs text-gray-500">请输入有效的JSON格式，例如: {`{"规格1": "值1", "规格2": "值2"}`}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('applications')}
                    </label>
                    <textarea
                      rows={3}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md"
                      placeholder={t('applications_placeholder')}
                      {...register('applications')}
                    ></textarea>
                  </div>

                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-blue-600 rounded"
                        {...register('in_stock')}
                      />
                      <span className="ml-2 text-sm text-gray-700">{t('in_stock')}</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* 底部操作按钮 */}
            <div className="border-t border-gray-200 px-6 py-4">
              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => router.push(`/${params.lang}/admin/products`)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  {t('cancel')}
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400"
                >
                  {saving ? t('saving') : t('save_product')}
                </button>
              </div>
            </div>
          </form>
        </div>
      )}
    </AdminLayout>
  );
}
