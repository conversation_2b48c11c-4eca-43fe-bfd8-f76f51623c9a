'use client';

import { useState, useEffect } from 'react';
// import { useRouter } from "next/navigation"; // router is unused
import Link from 'next/link';
import AdminLayout from "../../../../components/admin/Layout";
import AuthGuard from "../../../../components/admin/AuthGuard";

interface Product {
  _id: string;
  title: string;
  slug: string;
  type: string;
  size: string;
  isPublished: boolean;
  createdAt: string;
}

// 模拟产品数据
const MOCK_PRODUCTS: Product[] = [
  {
    _id: 'mock-1',
    title: 'KTV互动设备',
    slug: 'ktv-interactive-device',
    type: 'family_entertainment_center',
    size: '100-500 SQM',
    isPublished: true,
    createdAt: '2024-01-15T10:30:00Z'
  },
  {
    _id: 'mock-2',
    title: 'AR体感蹦床',
    slug: 'ar-motion-trampoline',
    type: 'interactive_sports',
    size: '500-1000 SQM',
    isPublished: true,
    createdAt: '2024-01-16T14:20:00Z'
  },
  {
    _id: 'mock-3',
    title: '3D电子沙盘',
    slug: '3d-electronic-sandbox',
    type: 'interactive_education',
    size: '100-500 SQM',
    isPublished: true,
    createdAt: '2024-01-17T09:15:00Z'
  },
  {
    _id: 'mock-4',
    title: '互动足球场',
    slug: 'interactive-football',
    type: 'interactive_sports',
    size: '1000+ SQM',
    isPublished: false,
    createdAt: '2024-01-18T16:45:00Z'
  },
  {
    _id: 'mock-5',
    title: '全息沙漠',
    slug: 'holographic-desert',
    type: 'holographic_display',
    size: '500-1000 SQM',
    isPublished: true,
    createdAt: '2024-01-19T11:30:00Z'
  },
  {
    _id: 'mock-6',
    title: '儿童互动沙滩',
    slug: 'kids-interactive-beach',
    type: 'kids_playground',
    size: '100-500 SQM',
    isPublished: false,
    createdAt: '2024-01-20T13:25:00Z'
  },
  {
    _id: 'mock-7',
    title: '体感攀岩墙',
    slug: 'motion-climbing-wall',
    type: 'interactive_sports',
    size: '500-1000 SQM',
    isPublished: true,
    createdAt: '2024-01-21T15:10:00Z'
  },
  {
    _id: 'mock-8',
    title: '保龄球互动系统',
    slug: 'bowling-interactive-system',
    type: 'family_entertainment_center',
    size: '1000+ SQM',
    isPublished: true,
    createdAt: '2024-01-22T08:40:00Z'
  }
];

export default function Page({ params }: { params: { lang: string } }) {
  // const router = useRouter(); // router is unused
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState({
    published: '',
  });
  const [language, setLanguage] = useState<string>('zh'); // 默认中文
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());
  
  // 新增：数据源模式切换
  const [dataMode, setDataMode] = useState<'real' | 'mock'>('real');
  const [mockProducts, setMockProducts] = useState<Product[]>(MOCK_PRODUCTS);
  const [realDataSource, setRealDataSource] = useState<string>('database'); // 追踪真实数据的来源
  
  // 新增：多选功能状态
  const [selectedProducts, setSelectedProducts] = useState<Set<string>>(new Set());
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [showBulkActions, setShowBulkActions] = useState(false);

  // 初始化时从localStorage获取语言设置和数据模式设置
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedLang = localStorage.getItem('ADMIN_LANG') || 'zh';
      setLanguage(storedLang);
      
      // 获取上次选择的数据模式
      const storedDataMode = localStorage.getItem('ADMIN_PRODUCT_DATA_MODE') as 'real' | 'mock' || 'real';
      setDataMode(storedDataMode);
    }
  }, []);

  // 保存数据模式到localStorage
  const handleDataModeChange = (mode: 'real' | 'mock') => {
    setDataMode(mode);
    setCurrentPage(1); // 重置到第一页
    if (typeof window !== 'undefined') {
      localStorage.setItem('ADMIN_PRODUCT_DATA_MODE', mode);
    }
  };

  // 翻译函数
  const t = (key: string) => {
    const translations: { [key: string]: { [key: string]: string } } = {
      products: { zh: '产品管理', en: 'Products' },
      add_new_product: { zh: '添加新产品', en: 'Add New Product' },
      search_products: { zh: '搜索产品...', en: 'Search products...' },
      search: { zh: '搜索', en: 'Search' },
      all_status: { zh: '所有状态', en: 'All Status' },
      published: { zh: '已发布', en: 'Published' },
      draft: { zh: '草稿', en: 'Draft' },

      loading_products: { zh: '加载产品中...', en: 'Loading products...' },
      no_products_found: { zh: '未找到产品', en: 'No products found' },
      add_first_product: { zh: '添加您的第一个产品', en: 'Add your first product' },
      title: { zh: '标题', en: 'Title' },
      type: { zh: '类型', en: 'Type' },
      size: { zh: '尺寸', en: 'Size' },
      status: { zh: '状态', en: 'Status' },
      created: { zh: '创建时间', en: 'Created' },
      actions: { zh: '操作', en: 'Actions' },
      edit: { zh: '编辑', en: 'Edit' },
      delete: { zh: '删除', en: 'Delete' },
      previous: { zh: '上一页', en: 'Previous' },
      next: { zh: '下一页', en: 'Next' },
      showing_page: { zh: '显示页面', en: 'Showing page' },
      of: { zh: '共', en: 'of' },
      delete_confirm: {
        zh: '您确定要删除此产品吗？',
        en: 'Are you sure you want to delete this product?',
      },
      delete_error: { zh: '错误：', en: 'Error: ' },
      
      // 新增翻译
      data_source: { zh: '数据源', en: 'Data Source' },
      real_database: { zh: '真实数据库', en: 'Real Database' },
      mock_data: { zh: '模拟数据', en: 'Mock Data' },
      real_db_desc: { zh: '真实的产品数据（来自数据库或本地文件）', en: 'Real product data (from database or local files)' },
      mock_data_desc: { zh: '用于开发测试的模拟产品数据', en: 'Mock product data for development and testing' },
      add_mock_product: { zh: '添加模拟产品', en: 'Add Mock Product' },
      
      // 多选功能翻译
      select_all: { zh: '全选', en: 'Select All' },
      deselect_all: { zh: '取消全选', en: 'Deselect All' },
      selected_items: { zh: '已选择 {count} 项', en: '{count} items selected' },
      bulk_delete: { zh: '批量删除', en: 'Bulk Delete' },
      bulk_actions: { zh: '批量操作', en: 'Bulk Actions' },
      confirm_bulk_delete: { zh: '确定要删除选中的 {count} 个产品吗？', en: 'Are you sure you want to delete {count} selected products?' },
      bulk_delete_success: { zh: '成功删除 {count} 个产品', en: 'Successfully deleted {count} products' },
      cancel_selection: { zh: '取消选择', en: 'Cancel Selection' },
    };

    return translations[key]?.[language] || key;
  };

  // 支持参数的翻译函数
  const tWithParams = (key: string, params: { [key: string]: string | number }) => {
    let text = t(key);
    Object.keys(params).forEach(param => {
      text = text.replace(`{${param}}`, params[param].toString());
    });
    return text;
  };

  // 获取当前数据源的产品列表
  const getCurrentProducts = () => {
    if (dataMode === 'mock') {
      return mockProducts;
    }
    return products;
  };

  // 过滤和搜索逻辑
  const getFilteredProducts = () => {
    let filteredProducts = getCurrentProducts();

    // 搜索过滤
    if (searchTerm) {
      filteredProducts = filteredProducts.filter(product =>
        product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.type.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // 发布状态过滤
    if (filter.published) {
      const isPublished = filter.published === 'true';
      filteredProducts = filteredProducts.filter(product => product.isPublished === isPublished);
    }

    return filteredProducts;
  };

  // 分页逻辑（不在这里设置totalPages）
  const getPaginatedProducts = () => {
    const filtered = getFilteredProducts();
    const itemsPerPage = 10;
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    
    return filtered.slice(startIndex, endIndex);
  };

  // 计算总页数的useEffect
  useEffect(() => {
    const filtered = getFilteredProducts();
    const itemsPerPage = 10;
    const totalPagesCount = Math.ceil(filtered.length / itemsPerPage);
    setTotalPages(Math.max(1, totalPagesCount)); // 确保至少有1页
  }, [searchTerm, filter, dataMode, products, mockProducts]);

  const fetchProducts = async () => {
    if (dataMode === 'mock') {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Build query string
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        limit: '50',
      });

      if (searchTerm) {
        queryParams.append('search', searchTerm);
      }

      if (filter.published) {
        queryParams.append('published', filter.published);
      }

      // 准备请求头
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      // 如果有本地会话，添加到Authorization头
      if (typeof window !== 'undefined') {
        const adminSession = localStorage.getItem('admin_session');
        if (adminSession) {
          headers['Authorization'] = `Bearer ${encodeURIComponent(adminSession)}`;
        }
      }

      // 使用管理员专用API，只获取数据库中的真实产品
      const res = await fetch(`/api/admin/products?${queryParams.toString()}`, {
        method: 'GET',
        headers,
      });

      if (!res.ok) {
        if (res.status === 403) {
          throw new Error('权限不足：需要管理员权限访问此功能');
        }
        throw new Error(`HTTP ${res.status}: Failed to fetch products`);
      }

      const data = await res.json();

      if (data.success) {
        setProducts(data.products || []);
        // 保存API返回的数据源信息
        setRealDataSource(data.source || 'database');
        console.log(`[ADMIN] 获取到 ${data.products?.length || 0} 个产品，数据源: ${data.source || 'database'}`);
      } else {
        throw new Error(data.message || 'Failed to fetch products');
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred while fetching products.');
      }
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (dataMode === 'real') {
      fetchProducts();
    } else {
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, filter, dataMode]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page
    if (dataMode === 'real') {
      fetchProducts();
    }
  };

  // 处理模拟数据删除
  const handleMockDelete = (id: string) => {
    const productToDelete = mockProducts.find(p => p._id === id);
    const productName = productToDelete?.title || '该产品';

    if (!window.confirm(`确定要删除模拟产品 "${productName}" 吗？`)) {
      return;
    }

    setMockProducts(prev => prev.filter(p => p._id !== id));
    alert(`模拟产品 "${productName}" 已删除！`);
  };

  // 添加新的模拟产品
  const handleAddMockProduct = () => {
    const newId = `mock-${Date.now()}`;
    const newProduct: Product = {
      _id: newId,
      title: `新模拟产品 ${newId.slice(-4)}`,
      slug: `new-mock-product-${newId.slice(-4)}`,
      type: 'test_product',
      size: '100-500 SQM',
      isPublished: false,
      createdAt: new Date().toISOString()
    };

    setMockProducts(prev => [newProduct, ...prev]);
    alert(`已添加新的模拟产品: ${newProduct.title}`);
  };

  const handleDelete = async (id: string) => {
    if (dataMode === 'mock') {
      handleMockDelete(id);
      return;
    }

    // 原有的真实数据库删除逻辑
    const productToDelete = products.find(p => p._id === id);
    const productName = productToDelete?.title || '该产品';

    // 更详细的确认对话框
    const confirmMessage = `确定要删除产品 "${productName}" 吗？\n\n此操作将：\n- 永久删除产品信息\n- 删除所有相关图片和特性\n- 删除所有翻译内容\n- 删除分类关联\n\n此操作不可撤销！`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    // 添加到删除状态
    setDeletingIds(prev => new Set(prev).add(id));

    try {
      console.log(`开始删除产品: ${productName} (ID: ${id})`);

      // 准备请求头
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      // 如果有本地会话，添加到Authorization头
      if (typeof window !== 'undefined') {
        const adminSession = localStorage.getItem('admin_session');
        if (adminSession) {
          headers['Authorization'] = `Bearer ${encodeURIComponent(adminSession)}`;
        }
      }

      const res = await fetch(`/api/products/${id}`, {
        method: 'DELETE',
        headers,
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || `HTTP ${res.status}: Failed to delete product`);
      }

      const data = await res.json();

      if (data.success) {
        console.log(`产品删除成功: ${productName}`);

        // 显示成功消息
        alert(`产品 "${productName}" 已成功删除！`);

        // 刷新产品列表
        await fetchProducts();

        // 如果当前页没有产品了，回到上一页
        if (products.length === 1 && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        }
      } else {
        throw new Error(data.message || 'Failed to delete product');
      }
    } catch (err: unknown) {
      console.error('删除产品时出错:', err);

      let errorMessage = '删除产品时发生未知错误';
      if (err instanceof Error) {
        errorMessage = err.message;
      }

      alert(`删除失败: ${errorMessage}\n\n请检查网络连接或联系管理员。`);
    } finally {
      // 从删除状态中移除
      setDeletingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }
  };

  // 获取分页后的产品列表
  const displayProducts = getPaginatedProducts();

  // 多选功能处理函数
  const handleSelectProduct = (productId: string) => {
    setSelectedProducts(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(productId)) {
        newSelected.delete(productId);
      } else {
        newSelected.add(productId);
      }
      setShowBulkActions(newSelected.size > 0);
      return newSelected;
    });
  };

  const handleSelectAll = () => {
    const currentProducts = displayProducts;
    console.log('[全选调试] 当前页产品数:', currentProducts.length);
    console.log('[全选调试] 当前是否全选:', isAllSelected);
    
    if (isAllSelected) {
      // 取消全选
      setSelectedProducts(new Set());
      setIsAllSelected(false);
      setShowBulkActions(false);
      console.log('[全选调试] 取消全选');
    } else {
      // 全选当前页的产品
      const allIds = new Set(currentProducts.map(p => p._id));
      setSelectedProducts(allIds);
      setIsAllSelected(true);
      setShowBulkActions(true);
      console.log('[全选调试] 全选当前页，选中ID:', Array.from(allIds));
    }
  };

  // 清除选择
  const clearSelection = () => {
    setSelectedProducts(new Set());
    setIsAllSelected(false);
    setShowBulkActions(false);
  };

  // 监听选择状态变化，自动更新全选状态
  useEffect(() => {
    const currentProducts = displayProducts;
    const allCurrentIds = currentProducts.map(p => p._id);
    const allSelected = allCurrentIds.length > 0 && allCurrentIds.every(id => selectedProducts.has(id));
    setIsAllSelected(allSelected);
  }, [selectedProducts, displayProducts]);

  // 批量删除处理
  const handleBulkDelete = async () => {
    if (selectedProducts.size === 0) return;

    const confirmMessage = tWithParams('confirm_bulk_delete', { count: selectedProducts.size });
    if (!window.confirm(confirmMessage)) {
      return;
    }

    const selectedIds = Array.from(selectedProducts);
    let successCount = 0;
    let failureCount = 0;

    setDeletingIds(prev => {
      const newSet = new Set(prev);
      selectedIds.forEach(id => newSet.add(id));
      return newSet;
    });

    try {
      // 批量删除 - 逐个删除以便错误处理
      for (const id of selectedIds) {
        try {
          if (dataMode === 'mock') {
            setMockProducts(prev => prev.filter(p => p._id !== id));
            successCount++;
          } else {
            // 准备请求头
            const headers: HeadersInit = {
              'Content-Type': 'application/json',
            };

            // 如果有本地会话，添加到Authorization头
            if (typeof window !== 'undefined') {
              const adminSession = localStorage.getItem('admin_session');
              if (adminSession) {
                headers['Authorization'] = `Bearer ${encodeURIComponent(adminSession)}`;
              }
            }

            const res = await fetch(`/api/products/${id}`, {
              method: 'DELETE',
              headers,
            });

            if (res.ok) {
              successCount++;
            } else {
              failureCount++;
              console.error(`删除产品 ${id} 失败`);
            }
          }
        } catch (error) {
          failureCount++;
          console.error(`删除产品 ${id} 时出错:`, error);
        }
      }

      // 显示结果
      if (successCount > 0) {
        alert(tWithParams('bulk_delete_success', { count: successCount }));
        
        // 刷新产品列表
        if (dataMode === 'real') {
          await fetchProducts();
        }
      }

      if (failureCount > 0) {
        alert(`${failureCount} 个产品删除失败，请检查网络连接或联系管理员。`);
      }

      // 清除选择
      clearSelection();

    } finally {
      // 清除删除状态
      setDeletingIds(prev => {
        const newSet = new Set(prev);
        selectedIds.forEach(id => newSet.delete(id));
        return newSet;
      });
    }
  };

  return (
    <AuthGuard>
      <AdminLayout title={t('products')}>
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h2 className="text-2xl font-bold text-gray-800">{t('products')}</h2>
        <div className="mt-4 sm:mt-0 flex gap-2">
          {dataMode === 'mock' && (
            <button
              onClick={handleAddMockProduct}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <span className="mr-2">➕</span>
              {t('add_mock_product')}
            </button>
          )}
          <Link
            href={`/${params.lang}/admin/products/new`}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <span className="mr-2">➕</span>
            {t('add_new_product')}
          </Link>
        </div>
      </div>

      {/* 数据源选择器 */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg shadow-md p-6 mb-6 border border-blue-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">{t('data_source')}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div 
            className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
              dataMode === 'real' 
                ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200' 
                : 'border-gray-200 bg-white hover:border-gray-300'
            }`}
            onClick={() => handleDataModeChange('real')}
          >
            <div className="flex items-center mb-2">
              <input
                type="radio"
                name="dataMode"
                value="real"
                checked={dataMode === 'real'}
                onChange={() => handleDataModeChange('real')}
                className="mr-3"
              />
              <h4 className="font-semibold text-gray-900">{t('real_database')}</h4>
              <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                生产环境
              </span>
            </div>
            <p className="text-sm text-gray-600">{t('real_db_desc')}</p>
            <p className="text-xs text-blue-600 mt-1">
              {realDataSource === 'local_json' 
                ? `来自本地JSON文件: ${products.length} 个产品` 
                : `PostgreSQL数据库: ${products.length} 个产品`
              }
            </p>
          </div>

          <div 
            className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
              dataMode === 'mock' 
                ? 'border-orange-500 bg-orange-50 ring-2 ring-orange-200' 
                : 'border-gray-200 bg-white hover:border-gray-300'
            }`}
            onClick={() => handleDataModeChange('mock')}
          >
            <div className="flex items-center mb-2">
              <input
                type="radio"
                name="dataMode"
                value="mock"
                checked={dataMode === 'mock'}
                onChange={() => handleDataModeChange('mock')}
                className="mr-3"
              />
              <h4 className="font-semibold text-gray-900">{t('mock_data')}</h4>
              <span className="ml-2 text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
                开发测试
              </span>
            </div>
            <p className="text-sm text-gray-600">{t('mock_data_desc')}</p>
            <p className="text-xs text-orange-600 mt-1">模拟产品数据: {mockProducts.length} 个</p>
          </div>
        </div>
      </div>

      {/* 批量操作工具栏 */}
      {showBulkActions && (
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-blue-800 font-medium">
                {tWithParams('selected_items', { count: selectedProducts.size })}
              </span>
              {selectedProducts.size < getFilteredProducts().length && (
                <button
                  onClick={() => {
                    // 选择所有产品（不仅是当前页）
                    const allProducts = getFilteredProducts();
                    const allIds = new Set(allProducts.map(p => p._id));
                    setSelectedProducts(allIds);
                    setShowBulkActions(true);
                  }}
                  className="text-blue-600 hover:text-blue-800 text-sm underline"
                >
                  选择所有 {getFilteredProducts().length} 个产品
                </button>
              )}
              <button
                onClick={clearSelection}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                {t('cancel_selection')}
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleBulkDelete}
                disabled={selectedProducts.size === 0}
                className="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="mr-2">🗑️</span>
                {t('bulk_delete')} ({selectedProducts.size})
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-end gap-4 mb-4">
          <div className="flex-1">
            <form onSubmit={handleSearch} className="flex">
              <input
                type="text"
                placeholder={t('search_products')}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
              />
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700"
              >
                {t('search')}
              </button>
            </form>
          </div>

          <div className="flex flex-wrap gap-2">
            <select
              className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={filter.published}
              onChange={e => setFilter({ ...filter, published: e.target.value })}
            >
              <option value="">{t('all_status')}</option>
              <option value="true">{t('published')}</option>
              <option value="false">{t('draft')}</option>
            </select>
          </div>
        </div>

        {/* 当前数据源提示 */}
        <div className={`mb-4 p-3 rounded-md ${
          dataMode === 'real' 
            ? 'bg-blue-100 text-blue-800' 
            : 'bg-orange-100 text-orange-800'
        }`}>
          <div className="flex items-center">
            <span className="mr-2">
              {dataMode === 'real' ? '🗄️' : '🧪'}
            </span>
            <span className="text-sm font-medium">
              {dataMode === 'real' 
                ? `正在显示真实数据产品 (${getFilteredProducts().length} 个) - ${realDataSource === 'local_json' ? '本地文件' : '数据库'}` 
                : `正在显示模拟数据产品 (${getFilteredProducts().length} 个)`
              }
            </span>
          </div>
        </div>

        {error && <div className="bg-red-100 text-red-700 p-4 rounded-md mb-4">{error}</div>}

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="text-xl text-gray-500">{t('loading_products')}</div>
          </div>
        ) : !displayProducts || displayProducts.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 text-lg">{t('no_products_found')}</p>
            {dataMode === 'mock' ? (
              <button
                onClick={handleAddMockProduct}
                className="mt-4 inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                {t('add_mock_product')}
              </button>
            ) : (
              <Link
                href={`/${params.lang}/admin/products/new`}
                className="mt-4 inline-block text-blue-600 hover:underline"
              >
                {t('add_first_product')}
              </Link>
            )}
          </div>
        ) : (
          <>
            {/* 选择信息提示 */}
            <div className="mb-4 flex items-center justify-between">
              <div className="text-sm text-gray-600">
                当前页显示 <span className="font-medium">{displayProducts.length}</span> 个产品，
                总共 <span className="font-medium">{getFilteredProducts().length}</span> 个产品
              </div>
              {displayProducts.length > 0 && (
                <div className="text-sm text-gray-600">
                  点击表头复选框可{isAllSelected ? '取消' : ''}全选当前页
                </div>
              )}
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      <input
                        type="checkbox"
                        checked={isAllSelected}
                        onChange={handleSelectAll}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('title')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('type')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('status')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('created')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('actions')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {displayProducts && displayProducts.map(product => (
                    <tr key={product._id} className="hover:bg-gray-50">
                      <td className="px-3 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedProducts.has(product._id)}
                          onChange={() => handleSelectProduct(product._id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-sm font-medium text-gray-900">
                            {product.title}
                            {dataMode === 'mock' && (
                              <span className="ml-2 text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
                                模拟
                              </span>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{product.type}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            product.isPublished
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {product.isPublished ? t('published') : t('draft')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(product.createdAt).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          {dataMode === 'real' ? (
                            <Link
                              href={`/${params.lang}/admin/products/${product._id}`}
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              {t('edit')}
                            </Link>
                          ) : (
                            <button
                              onClick={() => alert('模拟产品编辑功能 - 开发中')}
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              {t('edit')} (模拟)
                            </button>
                          )}
                          <button
                            onClick={() => handleDelete(product._id)}
                            disabled={deletingIds.has(product._id)}
                            className={`ml-4 ${
                              deletingIds.has(product._id)
                                ? 'text-gray-400 cursor-not-allowed'
                                : 'text-red-600 hover:text-red-900'
                            }`}
                          >
                            {deletingIds.has(product._id) ? '删除中...' : t('delete')}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-4">
                <div className="flex flex-1 justify-between sm:hidden">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                  >
                    {t('previous')}
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
                  >
                    {t('next')}
                  </button>
                </div>
                <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      {t('showing_page')} <span className="font-medium">{currentPage}</span>{' '}
                      {t('of')} <span className="font-medium">{totalPages}</span>
                    </p>
                  </div>
                  <div>
                    <nav
                      className="isolate inline-flex -space-x-px rounded-md shadow-sm"
                      aria-label="Pagination"
                    >
                      <button
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                      >
                        &laquo; {t('previous')}
                      </button>
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                            page === currentPage
                              ? 'z-10 bg-blue-600 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600'
                              : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                          }`}
                        >
                          {page}
                        </button>
                      ))}
                      <button
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50"
                      >
                        {t('next')} &raquo;
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
        </div>
      </AdminLayout>
    </AuthGuard>
  );
}