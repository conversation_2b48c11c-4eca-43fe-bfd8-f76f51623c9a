'use client';

import { useState, useCallback } from 'react';
import { useRouter } from "next/navigation";
import AdminLayout from '../../../../../components/admin/Layout';

export default function ProductUploadPage({ params }: { params: { lang: string } }) {
  const router = useRouter();
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // 翻译函数
  const t = useCallback(
    (key: string) => {
      const translations: { [key: string]: { [key: string]: string } } = {
        bulk_upload: { zh: '批量上传产品', en: 'Bulk Upload Products' },
        upload_csv: { zh: '上传CSV文件', en: 'Upload CSV File' },
        upload_json: { zh: '上传JSON文件', en: 'Upload JSON File' },
        csv_format: { zh: 'CSV格式要求', en: 'CSV Format Requirements' },
        json_format: { zh: 'JSON格式要求', en: 'JSON Format Requirements' },
        download_template: { zh: '下载模板', en: 'Download Template' },
        csv_template: { zh: 'CSV模板', en: 'CSV Template' },
        json_template: { zh: 'JSON模板', en: 'JSON Template' },
        select_file: { zh: '选择文件', en: 'Select File' },
        upload_file: { zh: '上传文件', en: 'Upload File' },
        uploading: { zh: '上传中...', en: 'Uploading...' },
        back_to_products: { zh: '返回产品列表', en: 'Back to Products' },
        csv_description: {
          zh: 'CSV文件应包含以下列：name, slug, description, video_url, key_features, tech_specs, applications, in_stock',
          en: 'CSV file should contain columns: name, slug, description, video_url, key_features, tech_specs, applications, in_stock'
        },
        json_description: {
          zh: 'JSON文件应为产品对象数组，每个对象包含产品的所有字段',
          en: 'JSON file should be an array of product objects with all required fields'
        },
        file_requirements: {
          zh: '文件要求：最大10MB，支持.csv和.json格式',
          en: 'File requirements: Maximum 10MB, supports .csv and .json formats'
        },
      };

      return translations[key]?.['zh'] || key; // 默认使用中文
    },
    []
  );

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    const allowedTypes = ['text/csv', 'application/json', '.csv', '.json'];
    const fileExtension = file.name.toLowerCase().split('.').pop();
    
    if (!allowedTypes.includes(file.type) && !['csv', 'json'].includes(fileExtension || '')) {
      setError('请选择CSV或JSON文件');
      return;
    }

    // 验证文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError('文件大小不能超过10MB');
      return;
    }

    setUploading(true);
    setError(null);
    setSuccess(null);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const res = await fetch('/api/admin/products/upload', {
        method: 'POST',
        body: formData,
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Upload failed');
      }

      const data = await res.json();
      
      if (data.success) {
        setSuccess(`成功上传 ${data.count || 0} 个产品`);
        // 3秒后跳转到产品列表
        setTimeout(() => {
          router.push(`/${params.lang}/admin/products`);
        }, 3000);
      } else {
        throw new Error(data.message || 'Upload failed');
      }
    } catch (err: unknown) {
      console.error('Error uploading file:', err);
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('上传过程中发生未知错误');
      }
    } finally {
      setUploading(false);
    }
  };

  const downloadTemplate = (type: 'csv' | 'json') => {
    const baseUrl = window.location.origin;
    const url = `${baseUrl}/templates/products-template.${type}`;
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `products-template.${type}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <AdminLayout title={t('bulk_upload')}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-800">
          {t('bulk_upload')}
        </h2>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 左侧：上传区域 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {t('upload_file')}
          </h3>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {success && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              {success}
            </div>
          )}

          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <svg className="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            
            <div className="mb-4">
              <label className="cursor-pointer">
                <span className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                  {t('select_file')}
                </span>
                <input
                  type="file"
                  accept=".csv,.json"
                  className="hidden"
                  onChange={handleFileUpload}
                  disabled={uploading}
                />
              </label>
            </div>

            <p className="text-sm text-gray-500 mb-2">
              {t('file_requirements')}
            </p>

            {uploading && (
              <div className="flex items-center justify-center mt-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-blue-600">{t('uploading')}</span>
              </div>
            )}
          </div>

          <div className="mt-6">
            <button
              onClick={() => router.push(`/${params.lang}/admin/products`)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {t('back_to_products')}
            </button>
          </div>
        </div>

        {/* 右侧：格式说明和模板下载 */}
        <div className="space-y-6">
          {/* CSV格式说明 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('csv_format')}
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              {t('csv_description')}
            </p>
            <button
              onClick={() => downloadTemplate('csv')}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              {t('download_template')} (CSV)
            </button>
          </div>

          {/* JSON格式说明 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('json_format')}
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              {t('json_description')}
            </p>
            <button
              onClick={() => downloadTemplate('json')}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {t('download_template')} (JSON)
            </button>
          </div>

          {/* 格式示例 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              格式示例
            </h3>
            <div className="bg-gray-100 rounded-md p-4 text-sm">
              <pre className="text-xs overflow-x-auto">
{`{
  "name": "AR互动蹦床",
  "slug": "ar-interactive-trampoline",
  "description": "结合AR技术的互动蹦床系统",
  "video_url": "https://example.com/video.mp4",
  "key_features": ["AR投影", "动作识别", "游戏互动"],
  "tech_specs": {"分辨率": "1920x1080"},
  "applications": ["游乐场", "健身房"],
  "in_stock": true
}`}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
