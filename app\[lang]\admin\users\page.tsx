'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import AdminLayout from '../../../../components/admin/Layout';
import AuthGuard from '../../../../components/admin/AuthGuard';

interface User {
  _id: string;
  username: string;
  email: string;
  role: 'admin' | 'editor';
  createdAt: string;
}

export default function Page({ params }: { params: { lang: string } }) {
  const router = useRouter();
  const { data: session } = useSession();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [language, setLanguage] = useState<string>('zh'); // 默认中文

  // 初始化时从localStorage获取语言设置
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedLang = localStorage.getItem('ADMIN_LANG') || 'zh';
      setLanguage(storedLang);
    }
  }, []);

  // 翻译函数
  const t = (key: string) => {
    const translations: { [key: string]: { [key: string]: string } } = {
      user_management: { zh: '用户管理', en: 'User Management' },
      add_new_user: { zh: '添加新用户', en: 'Add New User' },
      loading_users: { zh: '加载用户中...', en: 'Loading users...' },
      no_users_found: { zh: '未找到用户', en: 'No users found' },
      add_first_user: { zh: '添加您的第一个用户', en: 'Add your first user' },
      username: { zh: '用户名', en: 'Username' },
      email: { zh: '邮箱', en: 'Email' },
      role: { zh: '角色', en: 'Role' },
      created: { zh: '创建时间', en: 'Created' },
      actions: { zh: '操作', en: 'Actions' },
      edit: { zh: '编辑', en: 'Edit' },
      delete: { zh: '删除', en: 'Delete' },
      delete_own_account: { zh: '您不能删除自己的账户', en: 'You cannot delete your own account' },
      delete_confirm: {
        zh: '您确定要删除此用户吗？',
        en: 'Are you sure you want to delete this user?',
      },
      delete_error: { zh: '错误：', en: 'Error: ' },
    };

    return translations[key]?.[language] || key;
  };

  useEffect(() => {
    // Redirect if not admin
    if (session && session.user.role !== 'admin') {
      router.push(`/${params.lang}/admin`);
    }
  }, [session, router]);

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);

    try {
      const res = await fetch('/api/users');

      if (!res.ok) {
        throw new Error('Failed to fetch users');
      }

      const data = await res.json();

      if (data.success) {
        setUsers(data.data);
      } else {
        throw new Error(data.message || 'Failed to fetch users');
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred while fetching users.');
      }
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (session && session.user.role === 'admin') {
      fetchUsers();
    }
  }, [session]);

  const handleDelete = async (id: string) => {
    // Prevent deleting yourself
    const currentUser = users.find(user => user.email === session?.user.email);
    if (currentUser && currentUser._id === id) {
      alert(t('delete_own_account'));
      return;
    }

    if (!window.confirm(t('delete_confirm'))) {
      return;
    }

    try {
      const res = await fetch(`/api/users/${id}`, {
        method: 'DELETE',
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to delete user');
      }

      const data = await res.json();

      if (data.success) {
        // Refresh users list
        fetchUsers();
      } else {
        throw new Error(data.message || 'Failed to delete user');
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        alert(`${t('delete_error')}${err.message}`);
      } else {
        alert(`${t('delete_error')}An unknown error occurred while deleting the user.`);
      }
      console.error('Error deleting user:', err);
    }
  };

  // If not admin, don't render the page
  if (session && session.user.role !== 'admin') {
    return null;
  }

  return (
    <AuthGuard adminOnly>
      <AdminLayout title={t('user_management')}>
        <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-2xl font-bold text-gray-800">{t('user_management')}</h2>
          <Link
            href={`/${params.lang}/admin/users/new`}
            className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <span className="mr-2">➕</span>
            {t('add_new_user')}
          </Link>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          {error && <div className="bg-red-100 text-red-700 p-4 rounded-md mb-4">{error}</div>}

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="text-xl text-gray-500">{t('loading_users')}</div>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 text-lg">{t('no_users_found')}</p>
              <Link
                href={`/${params.lang}/admin/users/new`}
                className="mt-4 inline-block text-blue-600 hover:underline"
              >
                {t('add_first_user')}
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('username')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('email')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('role')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('created')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {t('actions')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {users.map(user => (
                    <tr key={user._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{user.username}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{user.email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            user.role === 'admin'
                              ? 'bg-purple-100 text-purple-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}
                        >
                          {user.role}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(user.createdAt).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <Link
                          href={`/${params.lang}/admin/users/${user._id}`}
                          className="text-blue-600 hover:text-blue-900 mr-4"
                        >
                          {t('edit')}
                        </Link>
                        <button
                          onClick={() => handleDelete(user._id)}
                          className="text-red-600 hover:text-red-900"
                          disabled={user.email === session?.user.email}
                        >
                          {t('delete')}
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </AdminLayout>
    </AuthGuard>
  );
}
