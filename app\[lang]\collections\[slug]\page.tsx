'use client';

import { useEffect, useState } from 'react';
import { useLanguage } from '../../../components/LanguageProvider';
import Link from 'next/link';
import Image from 'next/image';
import { generatePlaceholderSVG } from '../../../utils/imagePlaceholder';

interface Product {
  _id: string;
  name: string;
  slug: string;
  description: string;
  price: number;
  category: string;
  images: string[];
}

interface CategoryData {
  _id: string;
  name: string;
  slug: string;
  description?: string;
}

export default function CategoryPage({ params }: { params: { slug: string; lang: string } }) {
  const { t, locale } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [category, setCategory] = useState<CategoryData | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        // 获取分类信息
        const categoryResponse = await fetch(`/api/categories?slug=${params.slug}`);
        if (!categoryResponse.ok) {
          throw new Error('分类不存在');
        }
        const categoryData = await categoryResponse.json();
        if (categoryData.data.length === 0) {
          throw new Error('分类不存在');
        }
        setCategory(categoryData.data[0]);

        // 获取分类下的产品
        const productsResponse = await fetch(`/api/products?category=${params.slug}`);
        if (!productsResponse.ok) {
          throw new Error('获取产品失败');
        }
        const productsData = await productsResponse.json();
        setProducts(productsData.data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载失败');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params.slug]);

  if (loading) {
    return (
      <main className="main-content">
        <div className="container py-12">
          <div className="text-center">
            <p suppressHydrationWarning>加载中...</p>
          </div>
        </div>
      </main>
    );
  }

  if (error || !category) {
    return (
      <main className="main-content">
        <div className="container py-12">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4" suppressHydrationWarning>
              分类不存在
            </h1>
            <p className="mb-6" suppressHydrationWarning>
              {error || '请求的分类不存在或已被删除。'}
            </p>
            <Link href={`/${locale}`} className="btn-primary" suppressHydrationWarning>
              返回首页
            </Link>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="main-content">
      <section className="page-header">
        <div className="container">
          <h1 className="page-title" suppressHydrationWarning>
            {category.name}
          </h1>
          <div className="breadcrumbs">
            <Link href={`/${locale}`} suppressHydrationWarning key="home">
              {t('common.home')}
            </Link>{' '}
            &gt;
            <Link href={`/${locale}/products`} suppressHydrationWarning key="products">
              {t('common.products')}
            </Link>{' '}
            &gt;
            <span suppressHydrationWarning key="category">
              {category.name}
            </span>
          </div>
        </div>
      </section>

      <section className="category-content">
        <div className="container">
          {category.description && (
            <div className="category-description mb-10">
              <p suppressHydrationWarning>{category.description}</p>
            </div>
          )}

          {products.length > 0 ? (
            <div className="products-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {products.map(product => (
                <div key={product._id} className="product-card">
                  <Link href={`/${locale}/products/${product.slug}`}>
                    <div className="product-image">
                      <Image
                        src={product.images?.[0] || generatePlaceholderSVG(400, 300, product.name)}
                        alt={product.name}
                        width={400}
                        height={300}
                        className="w-full h-auto rounded-lg"
                      />
                    </div>
                    <div className="product-info p-4">
                      <h3 className="product-title text-lg font-semibold" suppressHydrationWarning>
                        {product.name}
                      </h3>
                      <p
                        className="product-desc text-sm line-clamp-2 mt-2"
                        suppressHydrationWarning
                      >
                        {product.description}
                      </p>
                      <div className="product-action mt-4">
                        <span className="btn-secondary inline-block" suppressHydrationWarning>
                          {t('products.moreDetails')}
                        </span>
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-products text-center py-10">
              <p className="text-lg" suppressHydrationWarning>
                该分类下暂无产品。
              </p>
            </div>
          )}
        </div>
      </section>
    </main>
  );
}
