'use client';

import { useLanguage } from '../../../components/LanguageProvider';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect } from 'react';
import { motion } from 'framer-motion';
import AdvantageShowcase from '../../../components/AdvantageShowcase';
import PageHeader from '../../../components/PageHeader';

export default function AboutUsPage() {
  const { t, locale } = useLanguage();

  // 滚动渐入效果
  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
          }
        });
      },
      { threshold: 0.1 }
    );

    document.querySelectorAll('.fade-in-section').forEach(section => {
      observer.observe(section);
    });

    return () => {
      document.querySelectorAll('.fade-in-section').forEach(section => {
        observer.unobserve(section);
      });
    };
  }, []);

  return (
    <main className="main-content about-us-page">
      {/* 页面头部 */}
      <PageHeader
        title={t('about.page_title', { fallback: 'About Us' })}
        subtitle={t('about.page_subtitle', { fallback: 'Learn more about our company and mission' })}
        bgImage="/images/company/about-header-bg.jpg"
        height="md:h-[400px] h-[300px]"
        overlayOpacity={0.6}
        useImageComponent={true}
        animationEffect="up"
        className="mb-0"
      />

      <section className="about-intro fade-in-section">
        <div className="container">
          <div className="about-grid">
            <div className="about-image">
              <div className="image-wrapper">
                <motion.div
                  className="image-decoration"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                ></motion.div>
                <Image
                  src="/images/company/about-company.jpg"
                  alt={t('about.image_alt', { fallback: 'About Junsheng Technology' })}
                  width={600}
                  height={400}
                  style={{ objectFit: 'cover', borderRadius: '8px' }}
                  className="main-image"
                />
              </div>
            </div>
            <div className="about-content">
              <div className="section-badge">
                {t('about.established', { fallback: 'Established 2008' })}
              </div>
              <h2>
                {t('about.company_name', { fallback: '广州钧盛科技有限公司' })}
              </h2>
              <div className="content-divider"></div>
              <p>
                {t('about.paragraph1', {
                  fallback: '广州钧盛科技有限公司是一家专注于互动多媒体、AR体感、全息投影数字运动等产品研发生产及应用的创意科技型企业。办公室面积1300平方米，展厅面积500平方米（全息宴会厅、裸眼空间、儿童游乐科普产品）。'
                })}
              </p>
              <p>
                {t('about.paragraph2', {
                  fallback: '主营业务：互动投影产品、数字运动系列、AR体感、场景类游戏、全息KTV巨幕、全息婚宴以及其他定制类等产品。致力于互动多媒体创意设计与制作、软件技术研发、人机智能交互的研发、以视觉算法技术、运动识别技术等为核心。'
                })}
              </p>
              <p>
                {t('about.paragraph3', {
                  fallback: '钧盛科技秉承"约盛未来，创新不止"的经营理念，为行业注入更多的新鲜元素。公司一直追求优秀的服务质量、完善的客户体验以及高水准的专业服务团队。'
                })}
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="our-mission fade-in-section">
        <div className="container">
          <h2 className="section-title">
            <span className="title-accent">
              {t('about.mission.title', { fallback: 'Our Mission' })}
            </span>
          </h2>
          <div className="mission-content">
            <p>
              {t('about.mission.paragraph1', {
                fallback:
                  'At Junsheng Technology, we are dedicated to pushing the boundaries between reality and virtual through cutting-edge holographic projection technology and innovative interactive experiences, creating stunning immersive spaces for clients worldwide.',
              })}
            </p>
            <p>
              {t('about.mission.paragraph2', {
                fallback:
                  'We believe in the power of technology to transform how people perceive and experience spaces, and through the perfect integration of visuals, audio, and interaction, we bring unlimited possibilities to commercial spaces, cultural tourism, exhibitions, and more.',
              })}
            </p>
          </div>
        </div>
        <div className="mission-background-pattern"></div>
      </section>

      <section className="team-section fade-in-section">
        <div className="container">
          <h3 className="section-subtitle">
            <span className="subtitle-accent">
              {t('about.team.title', { fallback: 'Our Professional Team' })}
            </span>
          </h3>
          <div className="team-image-container">
            <div className="team-image-frame">
              <Image
                src="/images/company/team-group.jpg"
                alt={t('about.team.title', { fallback: 'Our Professional Team' })}
                width={1000}
                height={400}
                style={{ width: '100%', height: 'auto', objectFit: 'cover' }}
                className="team-image"
              />
              <div className="image-overlay">
                <div className="overlay-content">
                  <span className="team-members">
                    {t('about.team.members', { fallback: '50+ Professionals' })}
                  </span>
                  <span className="team-expertise">
                    {t('about.team.expertise', { fallback: 'Technology, Design & Installation' })}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="company-building-section fade-in-section">
        <div className="container">
          <h3 className="section-subtitle">
            <span className="subtitle-accent">
              {t('about.headquarters', { fallback: 'Company Headquarters' })}
            </span>
          </h3>
          <div className="building-image-container">
            <div className="building-image-frame">
              <Image
                src="/images/company/company-building.jpg"
                alt={t('about.headquarters', { fallback: 'Company Headquarters' })}
                width={1000}
                height={400}
                style={{ width: '100%', height: 'auto', objectFit: 'cover' }}
                className="building-image"
              />
              <div className="location-marker">
                <i className="fas fa-map-marker-alt"></i>
                <span>{t('about.location', { fallback: 'Guangzhou, China' })}</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="core-technology fade-in-section">
        <div className="container">
          <h2 className="section-title">
            <span className="title-accent">
              {t('about.tech.title', { fallback: 'Core Technologies' })}
            </span>
          </h2>
          <div className="technology-highlights">
            <motion.div
              className="tech-item"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -12, boxShadow: '0 15px 35px rgba(26, 26, 46, 0.15)' }}
            >
              <div className="tech-icon-wrapper">
                <div className="tech-icon">
                  <i className="fas fa-landmark"></i>
                </div>
                <div className="tech-icon-bg"></div>
              </div>
              <div className="tech-content">
                <div className="tech-number">01</div>
                <h3 suppressHydrationWarning>{t('about.tech.item1.title', { fallback: '3D全息投影' })}</h3>
                <div className="tech-divider"></div>
                <p suppressHydrationWarning>{t('about.tech.item1.description', { fallback: '无需特殊眼镜即可呈现立体悬浮影像，为您创造真实的三维视觉体验' })}</p>
              </div>
            </motion.div>

            <motion.div
              className="tech-item"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              whileHover={{ y: -12, boxShadow: '0 15px 35px rgba(26, 26, 46, 0.15)' }}
            >
              <div className="tech-icon-wrapper">
                <div className="tech-icon">
                  <i className="fas fa-hand-pointer"></i>
                </div>
                <div className="tech-icon-bg"></div>
              </div>
              <div className="tech-content">
                <div className="tech-number">02</div>
                <h3 suppressHydrationWarning>{t('about.tech.item2.title', { fallback: '裸眼5D互动投影' })}</h3>
                <div className="tech-divider"></div>
                <p suppressHydrationWarning>{t('about.tech.item2.description', { fallback: '结合动作感应与全息成像，让观众无需任何设备即可与投影内容自然互动' })}</p>
              </div>
            </motion.div>

            <motion.div
              className="tech-item"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
              whileHover={{ y: -12, boxShadow: '0 15px 35px rgba(26, 26, 46, 0.15)' }}
            >
              <div className="tech-icon-wrapper">
                <div className="tech-icon">
                  <i className="fas fa-utensils"></i>
                </div>
                <div className="tech-icon-bg"></div>
              </div>
              <div className="tech-content">
                <div className="tech-number">03</div>
                <h3 suppressHydrationWarning>{t('about.tech.item3.title', { fallback: '沉浸式餐厅/宴会厅' })}</h3>
                <div className="tech-divider"></div>
                <p suppressHydrationWarning>{t('about.tech.item3.description', { fallback: '将餐饮空间转变为动态艺术画布，通过墙面和桌面投影创造独特用餐体验' })}</p>
              </div>
            </motion.div>

            <motion.div
              className="tech-item"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              viewport={{ once: true }}
              whileHover={{ y: -12, boxShadow: '0 15px 35px rgba(26, 26, 46, 0.15)' }}
            >
              <div className="tech-icon-wrapper">
                <div className="tech-icon">
                  <i className="fas fa-museum"></i>
                </div>
                <div className="tech-icon-bg"></div>
              </div>
              <div className="tech-content">
                <div className="tech-number">04</div>
                <h3 suppressHydrationWarning>{t('about.tech.item4.title', { fallback: '科技展厅' })}</h3>
                <div className="tech-divider"></div>
                <p suppressHydrationWarning>{t('about.tech.item4.description', { fallback: '为企业和文化展览打造引人入胜的互动展示空间，提升品牌传播效果' })}</p>
              </div>
            </motion.div>
          </div>

          <div className="stats-grid">
            <motion.div
              className="stat-item"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="stat-number">
                1300<span className="unit">㎡</span>
              </div>
              <div className="stat-label">
                {t('about.stats.office_area', { fallback: '办公面积' })}
              </div>
            </motion.div>
            <motion.div
              className="stat-item"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="stat-number">
                500<span className="unit">㎡</span>
              </div>
              <div className="stat-label">
                {t('about.stats.showroom_area', { fallback: '展厅面积' })}
              </div>
            </motion.div>
            <motion.div
              className="stat-item"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="stat-number">
                15<span className="plus">+</span>
              </div>
              <div className="stat-label">
                {t('about.stats.experience', { fallback: '年行业经验' })}
              </div>
            </motion.div>
            <motion.div
              className="stat-item"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="stat-number">
                100<span className="percent">%</span>
              </div>
              <div className="stat-label">
                {t('about.stats.service_quality', { fallback: '优质服务' })}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      <AdvantageShowcase />

      <section className="contact-cta">
        <div className="cta-particles"></div>
        <div className="container">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            {t('about.cta.title', {
              fallback: 'Ready to Create Your Immersive Spatial Experience?',
            })}
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            {t('about.cta.description', {
              fallback:
                'Contact our team today to discuss your custom holographic projection needs.',
            })}
          </motion.p>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <Link
              href={`/${locale}/pages/contact-us`}
              className="btn-primary"
            >
              {t('about.cta.button', { fallback: 'Contact Us Now' })}
            </Link>
          </motion.div>
        </div>
      </section>

      <style jsx={true}>{`


        .about-intro {
          padding: 6rem 0;
          position: relative;
        }

        .fade-in-section {
          opacity: 0;
          transform: translateY(20px);
          transition: all 0.8s ease;
        }

        .animate-in {
          opacity: 1;
          transform: translateY(0);
        }

        .about-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 4rem;
          align-items: center;
        }

        .image-wrapper {
          position: relative;
          z-index: 1;
        }

        .image-decoration {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 25px;
          left: 25px;
          background-color: rgba(26, 26, 46, 0.1);
          border-radius: 8px;
          z-index: -1;
        }

        .main-image {
          box-shadow: var(--box-shadow-medium);
          position: relative;
          z-index: 2;
        }

        .section-badge {
          display: inline-block;
          padding: 0.3rem 1rem;
          background-color: rgba(26, 26, 46, 0.1);
          color: var(--primary-color);
          border-radius: 20px;
          font-size: 0.9rem;
          font-weight: 600;
          margin-bottom: 1rem;
        }

        .about-content h2 {
          font-size: 2.5rem;
          margin-bottom: 1.5rem;
          font-weight: 600;
          color: var(--text-dark);
        }

        .content-divider {
          width: 60px;
          height: 3px;
          background-color: var(--primary-color);
          margin-bottom: 1.5rem;
        }

        .about-content p {
          color: var(--text-medium);
          font-size: 1.1rem;
          line-height: 1.7;
          margin-bottom: 1.5rem;
        }

        .our-mission {
          padding: 5rem 0;
          background-color: #f8f9fc;
          position: relative;
          overflow: hidden;
        }

        .section-title {
          text-align: center;
          margin-bottom: 2.5rem;
          position: relative;
          font-size: 2.5rem;
          font-weight: 600;
        }

        .title-accent {
          position: relative;
          display: inline-block;
        }

        .title-accent:after {
          content: '';
          position: absolute;
          bottom: -10px;
          left: 50%;
          transform: translateX(-50%);
          width: 60%;
          height: 3px;
          background-color: var(--primary-color);
        }

        .mission-content {
          max-width: 900px;
          margin: 0 auto;
          text-align: center;
          position: relative;
          z-index: 2;
        }

        .mission-content p {
          color: var(--text-medium);
          font-size: 1.15rem;
          line-height: 1.8;
          margin-bottom: 1.5rem;
        }

        .mission-background-pattern {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image:
            radial-gradient(circle at 90% 10%, rgba(26, 26, 46, 0.05) 0%, transparent 40%),
            radial-gradient(circle at 10% 90%, rgba(26, 26, 46, 0.03) 0%, transparent 40%);
          z-index: 1;
        }

        .team-section,
        .company-building-section {
          padding: 4rem 0;
        }

        .section-subtitle {
          text-align: center;
          margin-bottom: 2rem;
          font-size: 2rem;
          font-weight: 600;
          position: relative;
        }

        .subtitle-accent {
          position: relative;
          display: inline-block;
        }

        .subtitle-accent:after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 40px;
          height: 2px;
          background-color: var(--primary-color);
        }

        .team-image-container,
        .building-image-container {
          margin-bottom: 3rem;
          position: relative;
        }

        .team-image-frame,
        .building-image-frame {
          position: relative;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: var(--box-shadow-medium);
        }

        .team-image,
        .building-image {
          border-radius: 12px;
          transition: transform 0.8s ease;
        }

        .team-image-frame:hover .team-image,
        .building-image-frame:hover .building-image {
          transform: scale(1.03);
        }

        .image-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
          padding: 2rem;
          color: white;
          opacity: 0;
          transition: opacity 0.5s ease;
        }

        .team-image-frame:hover .image-overlay {
          opacity: 1;
        }

        .overlay-content {
          display: flex;
          flex-direction: column;
        }

        .team-members {
          font-size: 1.25rem;
          font-weight: 600;
          margin-bottom: 0.25rem;
        }

        .team-expertise {
          font-size: 0.9rem;
          opacity: 0.9;
        }

        .location-marker {
          position: absolute;
          bottom: 20px;
          right: 20px;
          background-color: var(--primary-color);
          color: white;
          padding: 0.7rem 1.2rem;
          border-radius: 30px;
          font-size: 0.9rem;
          font-weight: 500;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .core-technology {
          padding: 5rem 0;
          background-color: #f8f9fc;
          position: relative;
          overflow: hidden;
        }

        .core-technology::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-image:
            radial-gradient(circle at 5% 15%, rgba(26, 26, 46, 0.03) 0%, transparent 30%),
            radial-gradient(circle at 95% 85%, rgba(26, 26, 46, 0.03) 0%, transparent 30%);
          z-index: 1;
        }

        .core-technology .container {
          position: relative;
          z-index: 2;
        }

        .technology-highlights {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 2.5rem;
          margin-bottom: 4rem;
        }

        .tech-item {
          background: white;
          border-radius: 16px;
          padding: 2.5rem;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          box-shadow: 0 10px 30px rgba(26, 26, 46, 0.05);
          transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
          position: relative;
          overflow: hidden;
        }

        .tech-item::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 5px;
          background: linear-gradient(90deg, var(--primary-color), rgba(26, 26, 46, 0.5));
          transform: scaleX(0);
          transform-origin: left;
          transition: transform 0.4s ease;
        }

        .tech-item:hover::after {
          transform: scaleX(1);
        }

        .tech-icon-wrapper {
          position: relative;
          margin-bottom: 2rem;
        }

        .tech-icon {
          width: 80px;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(145deg, rgba(26, 26, 46, 0.9), rgba(26, 26, 46, 1));
          color: white;
          font-size: 2.5rem;
          border-radius: 16px;
          position: relative;
          z-index: 2;
          box-shadow: 0 8px 20px rgba(26, 26, 46, 0.2);
        }

        .tech-icon-bg {
          position: absolute;
          width: 80px;
          height: 80px;
          top: 12px;
          left: 12px;
          background-color: rgba(26, 26, 46, 0.1);
          border-radius: 16px;
          z-index: 1;
        }

        .tech-number {
          font-size: 1rem;
          font-weight: 700;
          color: var(--primary-color);
          margin-bottom: 0.8rem;
          background-color: rgba(26, 26, 46, 0.05);
          padding: 0.3rem 0.8rem;
          border-radius: 20px;
          display: inline-block;
        }

        .tech-content h3 {
          margin-top: 0;
          margin-bottom: 1rem;
          font-size: 1.6rem;
          font-weight: 600;
          color: var(--text-dark);
        }

        .tech-divider {
          width: 50px;
          height: 3px;
          background: linear-gradient(90deg, var(--primary-color), rgba(26, 26, 46, 0.4));
          margin-bottom: 1.5rem;
          border-radius: 3px;
        }

        .tech-content p {
          margin: 0;
          color: var(--text-medium);
          line-height: 1.7;
          font-size: 1.1rem;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 2rem;
          text-align: center;
          position: relative;
          z-index: 2;
        }

        .stat-item {
          background: white;
          border-radius: 10px;
          padding: 2.5rem 1.5rem;
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
          position: relative;
          overflow: hidden;
        }

        .stat-item:before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 5px;
          height: 60px;
          background-color: var(--primary-color);
          border-radius: 0 0 3px 0;
        }

        .stat-number {
          font-size: 3rem;
          font-weight: 700;
          color: var(--primary-color);
          margin-bottom: 0.8rem;
          line-height: 1;
        }

        .plus,
        .percent,
        .unit {
          font-size: 2rem;
          vertical-align: top;
          position: relative;
          top: 8px;
        }

        .stat-label {
          color: var(--text-medium);
          font-size: 1rem;
          font-weight: 500;
        }

        .contact-cta {
          background-color: var(--primary-color);
          color: white;
          padding: 5rem 0;
          text-align: center;
          position: relative;
          overflow: hidden;
        }

        .cta-particles {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image:
            radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.12) 0%, transparent 25%),
            radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.12) 0%, transparent 25%);
        }

        .contact-cta h2 {
          margin-bottom: 1.2rem;
          font-size: 2.5rem;
          font-weight: 600;
          position: relative;
        }

        .contact-cta p {
          margin-bottom: 2.5rem;
          max-width: 700px;
          margin-left: auto;
          margin-right: auto;
          font-size: 1.2rem;
          opacity: 0.9;
          line-height: 1.6;
        }

        /* 移除内联按钮样式，使用统一CTA样式 */

        @media (max-width: 992px) {
          .about-grid,
          .stats-grid {
            grid-template-columns: 1fr;
            gap: 3rem;
          }

          .technology-highlights {
            grid-template-columns: 1fr;
          }

          .tech-item {
            padding: 2rem;
          }

          .tech-icon {
            width: 70px;
            height: 70px;
            font-size: 2rem;
          }

          .tech-icon-bg {
            width: 70px;
            height: 70px;
            top: 10px;
            left: 10px;
          }



          .about-content h2 {
            font-size: 2rem;
          }

          .section-title,
          .contact-cta h2 {
            font-size: 2.2rem;
          }
        }

        @media (max-width: 768px) {

          .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
          }

          .tech-item {
            padding: 1.5rem;
          }

          .tech-icon {
            width: 50px;
            height: 50px;
            font-size: 1.5rem;
            margin-right: 1rem;
          }

          .tech-content h3 {
            font-size: 1.2rem;
          }

          .contact-cta h2 {
            font-size: 1.8rem;
          }

          .contact-cta p {
            font-size: 1rem;
          }

          .btn-primary {
            padding: 0.8rem 1.8rem;
            font-size: 1rem;
          }
        }

        @media (max-width: 576px) {

          .stats-grid {
            grid-template-columns: 1fr;
          }

          .about-intro,
          .our-mission,
          .core-technology {
            padding: 4rem 0;
          }

          .team-section,
          .company-building-section {
            padding: 3rem 0;
          }

          .section-title,
          .section-subtitle {
            font-size: 1.8rem;
          }

          .location-marker {
            bottom: 10px;
            right: 10px;
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
          }
        }
      `}</style>
    </main>
  );
}
