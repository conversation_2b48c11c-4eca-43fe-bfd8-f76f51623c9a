'use client';

import { useLanguage } from '../../../components/LanguageProvider';
import ContactForm from '../../../components/ContactForm';
import Globe from '../../../components/Globe';
import '../../../styles/contact-us.css';

export default function ContactPage() {
  const { t } = useLanguage();

  return (
    <>
      <section className="contact-page">
        {/* 蓝色背景头部区域 */}
        <div className="page-header">
          <div className="container">
            <h1 className="page-title">
              {t('contact.title', { fallback: '联系我们' })}
            </h1>
            <p className="page-description">
              {t('contact.description', {
                fallback:
                  '想要了解更多产品信息或者其他疑问？我们随时为您提供帮助，让我们一起创造更美好的未来。',
              })}
            </p>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="contact-content">
          <div className="container">
            <div className="contact-container">
              <div className="contact-info">
                <div className="map-decoration"></div>
                <div className="info-item">
                  <i className="fas fa-map-marker-alt"></i>
                  <div className="info-content">
                    <h3>{t('contact.office', { fallback: 'Our Office' })}</h3>
                    <p>
                      {t('contact.address', {
                        fallback: 'Guangzhou City, Guangdong Province, China',
                      })}
                    </p>
                  </div>
                </div>

                <div className="info-item">
                  <i className="fas fa-envelope"></i>
                  <div className="info-content">
                    <h3>{t('contact.email_us', { fallback: 'Email Us' })}</h3>
                    <p>{t('contact.email', { fallback: '<EMAIL>' })}</p>
                  </div>
                </div>

                <div className="info-item">
                  <i className="fas fa-phone-alt"></i>
                  <div className="info-content">
                    <h3>{t('contact.call_us', { fallback: 'Call Us' })}</h3>
                    <p>{t('contact.phone', { fallback: '+86 13800138000' })}</p>
                  </div>
                </div>

                <div className="info-item">
                  <i className="fas fa-clock"></i>
                  <div className="info-content">
                    <h3>{t('contact.working_hours', { fallback: 'Working Hours' })}</h3>
                    <p>{t('contact.hours', { fallback: 'Monday-Friday: 9:00 AM - 6:00 PM' })}</p>
                  </div>
                </div>

                <div className="info-item">
                  <i className="fas fa-globe"></i>
                  <div className="info-content">
                    <h3>{t('contact.social', { fallback: 'Follow Us' })}</h3>
                    <div className="social-links">
                      <a href="#" aria-label="Facebook">
                        <i className="fab fa-facebook"></i>
                      </a>
                      <a href="#" aria-label="Twitter">
                        <i className="fab fa-twitter"></i>
                      </a>
                      <a href="#" aria-label="LinkedIn">
                        <i className="fab fa-linkedin"></i>
                      </a>
                      <a href="#" aria-label="Instagram">
                        <i className="fab fa-instagram"></i>
                      </a>
                    </div>
                  </div>
                </div>
              </div>

              <ContactForm />
            </div>

            <div className="world-map-section">
              <h2>{t('contact.global_presence', { fallback: 'Our Global Presence' })}</h2>
              <p>
                {t('contact.global_description', {
                  fallback:
                    'We serve customers worldwide with high-quality holographic projection equipment and solutions.',
                })}
              </p>
              <div className="globe-container">
                <Globe className="interactive-globe" />
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
