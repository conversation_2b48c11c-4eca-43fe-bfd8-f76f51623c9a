"use client"

import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"
import { useState, useEffect, useMemo } from "react"
import { useLanguage } from '../../../components/LanguageProvider';
import Link from 'next/link';

export default function CustomPlaygroundDesignPage() {
  const { t, locale, isHydrated } = useLanguage();
  const [hoveredStep, setHoveredStep] = useState<number | null>(null)
  const [hoveredProject, setHoveredProject] = useState<number | null>(null)

  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    message: ''
  });
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 处理表单输入
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // 模拟提交过程
      await new Promise(resolve => setTimeout(resolve, 1000));
      setFormSubmitted(true);
      setFormData({ name: '', phone: '', message: '' });
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 检测屏幕尺寸
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const designSteps = useMemo(() => [
    {
      icon: (
        <svg className="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
          <path d="M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7zm2.85 11.1l-.85.6V16h-4v-2.3l-.85-.6C7.8 12.16 7 10.63 7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 1.63-.8 3.16-2.15 4.1z"/>
        </svg>
      ),
      title: t('custom_playground.design_steps.step1.title', { fallback: "创意构思" }),
      description: t('custom_playground.design_steps.step1.description', { fallback: "深入了解您的需求，制定创新的全息投影概念方案，确保每个细节都符合您的期望。从视觉设计到技术架构，每一个环节都经过精心规划和反复优化。" }),
      number: "01",
      features: t('custom_playground.design_steps.step1.features', { fallback: ["需求分析", "创意策划", "概念设计", "方案确认"] }),
      bgColor: "from-blue-500 to-purple-500",
      image: "/images/holographic/consultation.jpg",
    },
    {
      icon: (
        <svg className="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
          <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
        </svg>
      ),
      title: t('custom_playground.design_steps.step2.title', { fallback: "方案设计" }),
      description: t('custom_playground.design_steps.step2.description', { fallback: "专业团队进行详细设计，确保视觉效果与技术可行性完美结合，打造独一无二的体验。从视觉设计到技术架构，每一个环节都经过精心规划和反复优化。" }),
      number: "02",
      features: t('custom_playground.design_steps.step2.features', { fallback: ["视觉设计", "技术架构", "交互设计", "效果预览"] }),
      bgColor: "from-purple-500 to-pink-500",
      image: "/images/holographic/concept-development.jpg",
    },
    {
      icon: (
        <svg className="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
          <path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4zM6.7 8.8c-.7.7-1.9.7-2.6 0-.7-.7-.7-1.9 0-2.6.7-.7 1.9-.7 2.6 0 .7.7.7 1.9 0 2.6z"/>
        </svg>
      ),
      title: t('custom_playground.design_steps.step3.title', { fallback: "技术开发" }),
      description: t('custom_playground.design_steps.step3.description', { fallback: "运用先进技术进行开发制作，保证全息效果的精准呈现，每一帧都追求完美。采用最新的全息投影技术和专业设备，确保最佳的视觉效果。" }),
      number: "03",
      features: t('custom_playground.design_steps.step3.features', { fallback: ["内容制作", "系统开发", "设备调试", "效果优化"] }),
      bgColor: "from-green-500 to-blue-500",
      image: "/images/holographic/engineering-design.jpg",
    },
    {
      icon: (
        <svg className="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
          <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
        </svg>
      ),
      title: t('custom_playground.design_steps.step4.title', { fallback: "测试优化" }),
      description: t('custom_playground.design_steps.step4.description', { fallback: "严格测试每个细节，持续优化直至达到最佳展示效果，确保万无一失。通过多轮测试和调优，保证项目的稳定性和完美呈现。" }),
      number: "04",
      features: t('custom_playground.design_steps.step4.features', { fallback: ["功能测试", "性能优化", "兼容性测试", "用户体验优化"] }),
      bgColor: "from-orange-500 to-red-500",
      image: "/images/holographic/3d-visualization.jpg",
    },
    {
      icon: (
        <svg className="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
          <path d="M8 5v14l11-7z"/>
        </svg>
      ),
      title: t('custom_playground.design_steps.step5.title', { fallback: "部署实施" }),
      description: t('custom_playground.design_steps.step5.description', { fallback: "专业团队现场安装调试，确保项目顺利交付并提供全方位后续技术支持。从设备安装到系统调试，全程专业服务，确保完美交付。" }),
      number: "05",
      features: t('custom_playground.design_steps.step5.features', { fallback: ["现场安装", "系统调试", "培训交付", "售后支持"] }),
      bgColor: "from-cyan-500 to-blue-500",
      image: "/images/holographic/installation.jpg",
    },
  ], [t]);

  const projects = useMemo(() => [
    {
      title: t('custom_playground.projects.project1.title', { fallback: "全息主题餐厅" }),
      subtitle: t('custom_playground.projects.project1.subtitle', { fallback: "1,200㎡海底主题沉浸式用餐体验" }),
      description: t('custom_playground.projects.project1.description', { fallback: "大型沉浸式全息展示空间，占地1200平米，日均接待客户超过500人次。" }),
      image: "/images/holographic/holographic-restaurant.jpg",
      tags: t('custom_playground.projects.project1.tags', { fallback: ["大型商业", "沉浸式体验", "主题餐厅"] }),
      stats: t('custom_playground.projects.project1.stats', { fallback: ["1200㎡", "500+/日", "行业标杆"] }),
    },
    {
      title: t('custom_playground.projects.project2.title', { fallback: "企业全息展厅" }),
      subtitle: t('custom_playground.projects.project2.subtitle', { fallback: "800㎡企业形象展示空间" }),
      description: t('custom_playground.projects.project2.description', { fallback: "企业全息展厅全方位展示企业实力和产品优势，提升品牌影响力。" }),
      image: "/images/holographic/exhibition-hall.jpg",
      tags: t('custom_playground.projects.project2.tags', { fallback: ["企业展厅", "品牌展示", "科技创新"] }),
      stats: t('custom_playground.projects.project2.stats', { fallback: ["800㎡", "品牌提升", "科技创新"] }),
    },
    {
      title: t('custom_playground.projects.project3.title', { fallback: "博物馆全息展区" }),
      subtitle: t('custom_playground.projects.project3.subtitle', { fallback: "2,000㎡历史文化主题展示" }),
      description: t('custom_playground.projects.project3.description', { fallback: "文物全息复原展示系统，让珍贵文物以全新形式与观众见面。" }),
      image: "/images/holographic/museum.jpg",
      tags: t('custom_playground.projects.project3.tags', { fallback: ["文化遗产", "数字化保护", "博物馆项目"] }),
      stats: t('custom_playground.projects.project3.stats', { fallback: ["2000㎡", "文化部认可", "数字重生"] }),
    },
  ], [t]);

  const features = useMemo(() => [
    {
      icon: (
        <svg className="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
        </svg>
      ),
      title: t('custom_playground.features.feature1.title', { fallback: "8K超高清画质" }),
      description: t('custom_playground.features.feature1.description', { fallback: "采用最新8K超高清技术，呈现前所未有的细腻逼真全息影像" }),
    },
    {
      icon: (
        <svg className="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
          <path d="M7 2v11h3v9l7-12h-4l4-8z"/>
        </svg>
      ),
      title: t('custom_playground.features.feature2.title', { fallback: "尖端全息技术" }),
      description: t('custom_playground.features.feature2.description', { fallback: "采用最先进的裸眼3D/5D全息投影技术，无需任何设备即可观看" }),
    },
    {
      icon: (
        <svg className="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
          <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2 1l-3 4v2h3v7h2zM12.5 11.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S11 9.17 11 10s.67 1.5 1.5 1.5zM5.5 6c1.11 0 2-.89 2-2s-.89-2-2-2-2 .89-2 2 .89 2 2 2zm2 16v-7H10V9.5l-3.5-3L5 8l2.5 3H6v7h1.5z"/>
        </svg>
      ),
      title: t('custom_playground.features.feature3.title', { fallback: "智能互动系统" }),
      description: t('custom_playground.features.feature3.description', { fallback: "结合动作感应与AI技术，让观众自然地与投影内容互动" }),
    },
    {
      icon: (
        <svg className="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1Z"/>
        </svg>
      ),
      title: t('custom_playground.features.feature4.title', { fallback: "工业级稳定性" }),
      description: t('custom_playground.features.feature4.description', { fallback: "军工级硬件配置，24小时不间断稳定运行" }),
    },
    {
      icon: (
        <svg className="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      ),
      title: t('custom_playground.features.feature5.title', { fallback: "专属定制方案" }),
      description: t('custom_playground.features.feature5.description', { fallback: "根据您的独特需求量身定制专属解决方案" }),
    },
    {
      icon: (
        <svg className="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
          <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z"/>
        </svg>
      ),
      title: t('custom_playground.features.feature6.title', { fallback: "全程技术支持" }),
      description: t('custom_playground.features.feature6.description', { fallback: "提供7*24小时专业技术团队支持" }),
    },
  ], [t]);

  // 如果语言提供者还没有准备好，显示加载状态
  if (!isHydrated) {
    return (
      <main className="main-content">
        <div style={{ padding: '4rem 0', textAlign: 'center', minHeight: '50vh' }}>
          <div style={{ fontSize: '1.5rem', color: '#6b7280' }}>{t('custom_playground.loading', { fallback: '加载中...' })}</div>
        </div>
      </main>
    );
  }

  return (
    <div className="main-content">
      {/* Page Header */}
      <section className="page-header bg-gradient">
        <div className="container">
          <h1 className="page-title">
            {t('custom_playground.page_title', { fallback: '项目定制指南' })}
          </h1>
          <div className="breadcrumbs">
            <Link href={`/${locale}`}>{t('custom_playground.breadcrumb_home', { fallback: '首页' })}</Link> &gt;
            <Link href={`/${locale}/pages/custom-solutions`}>
              {t('custom_playground.breadcrumb_solutions', { fallback: '定制解决方案' })}
            </Link>{' '}
            &gt;
            <span>{t('custom_playground.breadcrumb_current', { fallback: '项目定制指南' })}</span>
          </div>
        </div>
      </section>

        {/* Design Process */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-4 text-gray-900">{t('custom_playground.design_process_title', { fallback: '我们的设计流程' })}</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t('custom_playground.design_process_subtitle', { fallback: '打造卓越全息体验的五步专业流程，从创意构思到最终实施的全程定制服务' })}
              </p>
            </div>

            <div className="space-y-8">
              {designSteps.map((step, index) => (
                <div
                  key={index}
                  className="relative group"
                  onMouseEnter={() => setHoveredStep(index)}
                  onMouseLeave={() => setHoveredStep(null)}
                >
                  {/* 背景图片容器 */}
                  <div className="relative h-[500px] rounded-3xl overflow-hidden">
                    {/* 背景图片 */}
                    <img
                      src={step.image || "/placeholder.svg"}
                      alt={step.title}
                      className="w-full h-full object-cover transition-all duration-700 group-hover:scale-105"
                    />

                    {/* 渐变遮罩 */}
                    <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent"></div>

                    {/* 悬停时的动态遮罩 */}
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${step.bgColor} opacity-0 group-hover:opacity-20 transition-opacity duration-500`}
                    ></div>

                    {/* 内容区域 */}
                    <div className="absolute inset-0 flex items-center">
                      <div className="container mx-auto px-6">
                        <div className="max-w-2xl">
                          {/* 图标和编号 */}
                          <div className="flex items-center gap-4 mb-6">
                            <div
                              className={`relative w-16 h-16 bg-white rounded-2xl flex items-center justify-center shadow-lg transition-all duration-300 border border-gray-200 ${
                                hoveredStep === index ? "scale-110 rotate-3" : ""
                              }`}
                            >
                              <div className="flex items-center justify-center w-full h-full text-gray-800">
                                {step.icon}
                              </div>
                            </div>
                            <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white text-xl font-bold border border-white/30">
                              {step.number}
                            </div>
                          </div>

                          {/* 标题 */}
                          <h3 className="text-4xl md:text-5xl font-bold mb-6 text-white transform transition-all duration-500 group-hover:translate-x-2">
                            {step.title}
                          </h3>

                          {/* 描述 */}
                          <p className="text-lg text-white/90 mb-8 leading-relaxed transform transition-all duration-500 delay-100 group-hover:translate-x-2">
                            {step.description}
                          </p>

                          {/* 功能列表 */}
                          <div className="grid grid-cols-2 gap-4 mb-8">
                            {(Array.isArray(step.features) ? step.features : []).map((feature, featureIndex) => (
                              <div
                                key={featureIndex}
                                className="flex items-center gap-3 transform transition-all duration-500 group-hover:translate-x-2"
                                style={{ transitionDelay: `${200 + featureIndex * 100}ms` }}
                              >
                                <div className="w-2 h-2 bg-white rounded-full"></div>
                                <span className="text-white/90 font-medium">{feature}</span>
                              </div>
                            ))}
                          </div>

                          {/* 按钮 */}
                          <Button
                            className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border border-white/30 px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105 transform group-hover:translate-x-2"
                            style={{ transitionDelay: "600ms" }}
                          >
                            {t('custom_playground.learn_more', { fallback: '了解详情' })}
                            <ArrowRight className="w-4 h-4 ml-2 text-white" strokeWidth={2} />
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* 右侧装饰性元素 */}
                    <div className="absolute top-8 right-8 opacity-20 group-hover:opacity-40 transition-opacity duration-500">
                      <div className="w-32 h-32 border-2 border-white rounded-full animate-pulse"></div>
                    </div>
                    <div className="absolute bottom-8 right-16 opacity-10 group-hover:opacity-30 transition-opacity duration-500 delay-200">
                      <div className="w-24 h-24 border border-white rounded-full animate-ping"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Project Cases */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-4 text-gray-900">{t('custom_playground.projects.title', { fallback: '我们的定制项目案例' })}</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">{t('custom_playground.projects.subtitle', { fallback: '查看我们为不同客户打造的成功全息投影项目案例' })}</p>
            </div>

            {/* 大图展示区域 */}
            <div className="relative max-w-6xl mx-auto">
              <div className="relative h-[600px] rounded-3xl overflow-hidden group">
                {/* 当前项目背景图 */}
                <img
                  src={projects[hoveredProject || 0].image || "/placeholder.svg"}
                  alt={projects[hoveredProject || 0].title}
                  className="w-full h-full object-cover transition-all duration-700"
                />

                {/* 渐变遮罩 */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>

                {/* 项目信息叠加 */}
                <div className="absolute bottom-0 left-0 right-0 p-12">
                  <div className="max-w-2xl">
                    <h3 className="text-4xl font-bold mb-4 text-white">{projects[hoveredProject || 0].title}</h3>
                    <p className="text-xl text-blue-200 mb-4">{projects[hoveredProject || 0].subtitle}</p>
                    <p className="text-lg text-white/90 mb-6 leading-relaxed">
                      {projects[hoveredProject || 0].description}
                    </p>

                    {/* 统计数据 */}
                    <div className="flex gap-6 mb-6">
                      {(Array.isArray(projects[hoveredProject || 0].stats) ? projects[hoveredProject || 0].stats : []).map((stat, index) => (
                        <div key={index} className="text-center">
                          <div className="text-2xl font-bold text-white">{stat}</div>
                        </div>
                      ))}
                    </div>

                    <Button className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border border-white/30 px-8 py-3 rounded-xl">
                      {t('custom_playground.view_details', { fallback: '查看详情' })}
                      <ArrowRight className="w-4 h-4 ml-2 text-white" strokeWidth={2} />
                    </Button>
                  </div>
                </div>

                {/* 项目编号指示器 */}
                <div className="absolute top-8 right-8">
                  <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white text-xl font-bold border border-white/30">
                    {String(hoveredProject || 0 + 1).padStart(2, "0")}
                  </div>
                </div>
              </div>

              {/* 导航按钮 */}
              <button
                onClick={() =>
                  setHoveredProject((prev) =>
                    prev === null ? projects.length - 1 : prev === 0 ? projects.length - 1 : prev - 1,
                  )
                }
                className="absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300 border border-white/30"
              >
                <ArrowRight className="w-6 h-6 rotate-180 text-white" strokeWidth={2} />
              </button>

              <button
                onClick={() => setHoveredProject((prev) => (prev === null ? 1 : (prev + 1) % projects.length))}
                className="absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300 border border-white/30"
              >
                <ArrowRight className="w-6 h-6 text-white" strokeWidth={2} />
              </button>
            </div>

            {/* 项目缩略图导航 */}
            <div className="flex justify-center gap-4 mt-8">
              {projects.map((project, index) => (
                <button
                  key={index}
                  onClick={() => setHoveredProject(index)}
                  className={`relative w-20 h-20 rounded-xl overflow-hidden transition-all duration-300 ${
                    (hoveredProject || 0) === index
                      ? "ring-4 ring-blue-500 scale-110"
                      : "hover:scale-105 opacity-70 hover:opacity-100"
                  }`}
                >
                  <img
                    src={project.image || "/placeholder.svg"}
                    alt={project.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black/20"></div>
                  <div className="absolute bottom-1 right-1 w-6 h-6 bg-white/80 rounded-full flex items-center justify-center text-xs font-bold text-gray-800">
                    {index + 1}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Features */}
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-4 text-gray-900">{t('custom_playground.features.title', { fallback: '全息定制特色' })}</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t('custom_playground.features.subtitle', { fallback: '我们的全息定制解决方案具备多项优势，确保为您打造专属的视觉震撼体验' })}
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <div key={index} className="relative group cursor-pointer">
                  {/* 背景卡片 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-500 transform group-hover:scale-105"></div>

                  {/* 主卡片 */}
                  <Card className="relative p-8 border-0 bg-white hover:bg-transparent transition-all duration-500 hover:-translate-y-2 group overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl">
                    {/* 装饰性背景元素 */}
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-100 to-blue-100 rounded-full -translate-y-16 translate-x-16 group-hover:scale-150 transition-transform duration-700"></div>
                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-pink-100 to-blue-100 rounded-full translate-y-12 -translate-x-12 group-hover:scale-125 transition-transform duration-700 delay-100"></div>

                    {/* 图标容器 */}
                    <div className="relative mb-6">
                      <div className="w-16 h-16 bg-white rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 relative z-10 border border-gray-200">
                        {feature.icon}
                      </div>
                      {/* 图标光晕效果 */}
                      <div className="absolute inset-0 w-16 h-16 bg-blue-500 rounded-2xl opacity-0 group-hover:opacity-20 blur-xl transition-all duration-500"></div>
                    </div>

                    {/* 文字内容 */}
                    <div className="relative z-10">
                      <h3 className="text-xl font-bold mb-4 text-gray-900 group-hover:text-white transition-colors duration-500">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed group-hover:text-white/90 transition-colors duration-500">
                        {feature.description}
                      </p>
                    </div>

                    {/* 悬停时的边框光效 */}
                    <div className="absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-blue-200/30 transition-all duration-500"></div>

                    {/* 底部装饰线 */}
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 to-blue-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 rounded-b-2xl"></div>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* 报价表单部分 */}
        <div style={{
          maxWidth: '1240px',
          margin: '120px auto 80px',
          padding: '0 20px',
          position: 'relative'
        }}>
          {/* 装饰元素 */}
          <div style={{
            position: 'absolute',
            width: '150px',
            height: '150px',
            borderRadius: '50%',
            top: '20%',
            left: '-5%',
            background: 'linear-gradient(135deg, #1a1a2e, #5050a5)',
            opacity: 0.1,
            zIndex: -1,
            animation: 'float 8s ease-in-out infinite'
          }}></div>
          <div style={{
            position: 'absolute',
            width: '150px',
            height: '150px',
            borderRadius: '50%',
            bottom: '10%',
            right: '-5%',
            background: 'linear-gradient(135deg, #5050a5, #1a1a2e)',
            opacity: 0.1,
            zIndex: -1,
            animation: 'float 10s ease-in-out infinite'
          }}></div>

          <div style={{ width: '100%', position: 'relative' }}>
            <h2 style={{
              textAlign: 'center',
              marginBottom: '40px',
              fontSize: '32px',
              fontWeight: 600,
              color: '#1a1a2e',
              position: 'relative',
              paddingBottom: '15px',
              marginTop: '30px'
            }}>
              咨询领取报价
              <div style={{
                position: 'absolute',
                bottom: 0,
                left: '50%',
                transform: 'translateX(-50%)',
                width: '80px',
                height: '3px',
                background: 'linear-gradient(90deg, rgba(26, 26, 46, 0.1), #1a1a2e, rgba(26, 26, 46, 0.1))'
              }}></div>
            </h2>

            {formSubmitted ? (
              <div style={{
                maxWidth: '800px',
                margin: '0 auto',
                backgroundColor: '#fff',
                padding: '40px',
                borderRadius: '15px',
                boxShadow: '0 10px 30px rgba(26, 26, 46, 0.08)',
                textAlign: 'center',
                border: '1px solid rgba(26, 26, 46, 0.05)'
              }}>
                <h3 style={{
                  color: '#28a745',
                  marginBottom: '20px',
                  fontSize: '26px'
                }}>提交成功！</h3>
                <p style={{
                  marginBottom: '30px',
                  color: '#555',
                  fontSize: '18px',
                  lineHeight: 1.6
                }}>感谢您的咨询，我们的团队将尽快与您联系。</p>
                <button
                  style={{
                    padding: '14px 30px',
                    backgroundColor: '#6c757d',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '16px',
                    fontWeight: 500,
                    cursor: 'pointer',
                    transition: 'all 0.3s',
                    boxShadow: '0 5px 15px rgba(108, 117, 125, 0.2)'
                  }}
                  onClick={() => setFormSubmitted(false)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#5a6268';
                    e.currentTarget.style.transform = 'translateY(-3px)';
                    e.currentTarget.style.boxShadow = '0 8px 20px rgba(108, 117, 125, 0.3)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#6c757d';
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 5px 15px rgba(108, 117, 125, 0.2)';
                  }}
                >
                  再次提交
                </button>
              </div>
            ) : (
              <form
                style={{
                  maxWidth: '800px',
                  margin: '0 auto',
                  backgroundColor: '#fff',
                  padding: '40px',
                  borderRadius: '15px',
                  boxShadow: '0 10px 30px rgba(26, 26, 46, 0.08)',
                  border: '1px solid rgba(26, 26, 46, 0.05)',
                  transition: 'transform 0.3s ease, box-shadow 0.3s ease'
                }}
                onSubmit={handleSubmit}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px)';
                  e.currentTarget.style.boxShadow = '0 15px 40px rgba(26, 26, 46, 0.12)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 10px 30px rgba(26, 26, 46, 0.08)';
                }}
              >
                <div style={{
                  display: 'flex',
                  gap: '30px',
                  marginBottom: '25px',
                  flexDirection: isMobile ? 'column' : 'row'
                }}>
                  <div style={{ flex: 1, marginBottom: 0 }}>
                    <label htmlFor="name" style={{
                      display: 'block',
                      marginBottom: '10px',
                      fontWeight: 500,
                      color: '#1a1a2e',
                      fontSize: '17px'
                    }}>
                      您的姓名 <span style={{ color: '#f53d3d', marginLeft: '4px' }}>*</span>
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      style={{
                        width: '100%',
                        padding: '16px 20px',
                        fontSize: '16px',
                        border: '1px solid rgba(26, 26, 46, 0.1)',
                        borderRadius: '8px',
                        transition: 'all 0.3s ease',
                        backgroundColor: '#f9f9fd',
                        boxSizing: 'border-box'
                      }}
                      required
                      value={formData.name}
                      onChange={handleInputChange}
                      onFocus={(e) => {
                        e.target.style.borderColor = '#1a1a2e';
                        e.target.style.boxShadow = '0 0 0 3px rgba(26, 26, 46, 0.1)';
                        e.target.style.backgroundColor = '#fff';
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = 'rgba(26, 26, 46, 0.1)';
                        e.target.style.boxShadow = 'none';
                        e.target.style.backgroundColor = '#f9f9fd';
                      }}
                    />
                  </div>
                  <div style={{ flex: 1, marginBottom: 0 }}>
                    <label htmlFor="phone" style={{
                      display: 'block',
                      marginBottom: '10px',
                      fontWeight: 500,
                      color: '#1a1a2e',
                      fontSize: '17px'
                    }}>
                      联系电话 <span style={{ color: '#f53d3d', marginLeft: '4px' }}>*</span>
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      style={{
                        width: '100%',
                        padding: '16px 20px',
                        fontSize: '16px',
                        border: '1px solid rgba(26, 26, 46, 0.1)',
                        borderRadius: '8px',
                        transition: 'all 0.3s ease',
                        backgroundColor: '#f9f9fd',
                        boxSizing: 'border-box'
                      }}
                      required
                      value={formData.phone}
                      onChange={handleInputChange}
                      onFocus={(e) => {
                        e.target.style.borderColor = '#1a1a2e';
                        e.target.style.boxShadow = '0 0 0 3px rgba(26, 26, 46, 0.1)';
                        e.target.style.backgroundColor = '#fff';
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = 'rgba(26, 26, 46, 0.1)';
                        e.target.style.boxShadow = 'none';
                        e.target.style.backgroundColor = '#f9f9fd';
                      }}
                    />
                  </div>
                </div>

                <div style={{ marginBottom: '25px' }}>
                  <label htmlFor="message" style={{
                    display: 'block',
                    marginBottom: '10px',
                    fontWeight: 500,
                    color: '#1a1a2e',
                    fontSize: '17px'
                  }}>需求备注</label>
                  <textarea
                    id="message"
                    name="message"
                    rows={4}
                    style={{
                      width: '100%',
                      padding: '16px 20px',
                      fontSize: '16px',
                      border: '1px solid rgba(26, 26, 46, 0.1)',
                      borderRadius: '8px',
                      transition: 'all 0.3s ease',
                      backgroundColor: '#f9f9fd',
                      resize: 'vertical',
                      minHeight: '120px',
                      boxSizing: 'border-box'
                    }}
                    value={formData.message}
                    onChange={handleInputChange}
                    onFocus={(e) => {
                      e.target.style.borderColor = '#1a1a2e';
                      e.target.style.boxShadow = '0 0 0 3px rgba(26, 26, 46, 0.1)';
                      e.target.style.backgroundColor = '#fff';
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = 'rgba(26, 26, 46, 0.1)';
                      e.target.style.boxShadow = 'none';
                      e.target.style.backgroundColor = '#f9f9fd';
                    }}
                  ></textarea>
                </div>

                <div style={{ marginBottom: '25px' }}>
                  <button
                    type="submit"
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '100%',
                      height: '50px',
                      padding: '0 20px',
                      backgroundColor: 'hsl(210deg 100% 44%)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '12px',
                      fontSize: '18px',
                      fontWeight: 'bold',
                      letterSpacing: '4px',
                      textTransform: 'uppercase',
                      cursor: isSubmitting ? 'not-allowed' : 'pointer',
                      transition: '31ms cubic-bezier(.5, .7, .4, 1)',
                      boxShadow: 'hsl(210deg 87% 36%) 0px 7px 0px 0px',
                      position: 'relative',
                      overflow: 'hidden',
                      opacity: isSubmitting ? 0.7 : 1
                    }}
                    disabled={isSubmitting}
                    onMouseDown={(e) => {
                      if (!isSubmitting) {
                        e.currentTarget.style.boxShadow = 'none';
                        e.currentTarget.style.transform = 'translateY(7px)';
                      }
                    }}
                    onMouseUp={(e) => {
                      if (!isSubmitting) {
                        e.currentTarget.style.boxShadow = 'hsl(210deg 87% 36%) 0px 7px 0px 0px';
                        e.currentTarget.style.transform = 'translateY(0)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!isSubmitting) {
                        e.currentTarget.style.boxShadow = 'hsl(210deg 87% 36%) 0px 7px 0px 0px';
                        e.currentTarget.style.transform = 'translateY(0)';
                      }
                    }}
                  >
                    {isSubmitting ? '提交中...' : '提交'}
                  </button>
                </div>

                <p style={{
                  textAlign: 'center',
                  color: '#666',
                  fontSize: '0.9rem',
                  marginTop: '1.5rem'
                }}>提交即视为同意我们的隐私政策</p>
              </form>
            )}
          </div>
        </div>
      </div>
  );
}
