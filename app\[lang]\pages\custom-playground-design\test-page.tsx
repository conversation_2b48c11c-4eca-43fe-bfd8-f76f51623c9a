"use client"

import { useState } from "react"
import { useLanguage } from '../../../components/LanguageProvider';
import Link from 'next/link';

export default function TestCustomPlaygroundDesignPage() {
  const { t, locale, isHydrated } = useLanguage();
  const [hoveredStep, setHoveredStep] = useState<number | null>(null)

  // 如果语言提供者还没有准备好，显示加载状态
  if (!isHydrated) {
    return (
      <main className="main-content">
        <div style={{ padding: '4rem 0', textAlign: 'center', minHeight: '50vh' }}>
          <div style={{ fontSize: '1.5rem', color: '#6b7280' }}>加载中...</div>
        </div>
      </main>
    );
  }

  return (
    <div className="main-content">
      {/* Page Header */}
      <section className="page-header bg-gradient">
        <div className="container">
          <h1 className="page-title">
            项目定制指南
          </h1>
          <div className="breadcrumbs">
            <Link href={`/${locale}`}>首页</Link> &gt;
            <Link href={`/${locale}/pages/custom-solutions`}>
              定制解决方案
            </Link>{' '}
            &gt;
            <span>项目定制指南</span>
          </div>
        </div>
      </section>

      <div className="min-h-screen bg-white">
        {/* Test Section */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-4 text-gray-900">测试页面</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                这是一个测试页面，用于验证基本功能是否正常
              </p>
            </div>

            <div className="space-y-8">
              <div className="bg-white p-8 rounded-lg shadow-lg">
                <h3 className="text-2xl font-bold mb-4">基本功能测试</h3>
                <p className="text-gray-600 mb-4">如果您能看到这个页面，说明基本功能正常。</p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-blue-50 p-6 rounded-lg">
                    <h4 className="font-bold text-blue-900 mb-2">React 状态</h4>
                    <p className="text-blue-700">悬停步骤: {hoveredStep ?? '无'}</p>
                  </div>
                  
                  <div className="bg-green-50 p-6 rounded-lg">
                    <h4 className="font-bold text-green-900 mb-2">语言设置</h4>
                    <p className="text-green-700">当前语言: {locale}</p>
                  </div>
                  
                  <div className="bg-purple-50 p-6 rounded-lg">
                    <h4 className="font-bold text-purple-900 mb-2">水合状态</h4>
                    <p className="text-purple-700">已水合: {isHydrated ? '是' : '否'}</p>
                  </div>
                </div>

                <div className="mt-6">
                  <button
                    className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors"
                    onClick={() => setHoveredStep(hoveredStep === null ? 0 : null)}
                  >
                    切换状态测试
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
