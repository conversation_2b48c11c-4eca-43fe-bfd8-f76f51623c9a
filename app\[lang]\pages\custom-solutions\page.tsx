"use client"

import { motion } from "framer-motion"
import { ArrowRight, CheckCircle, Users, Lightbulb, Settings, Rocket } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useState } from "react"
import { useLanguage } from '../../../components/LanguageProvider';

export default function CustomSolutionsPage() {
  const { t, locale } = useLanguage();
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  }

  const solutions = [
    {
      icon: Lightbulb,
      title: t('custom_solutions.design.title', { fallback: "全息项目定制" }),
      description: t('custom_solutions.design.description', { fallback: "通过我们的定制设计服务将您的创意变为现实。我们才华横溢的设计师将与您紧密合作，创建完美满足您需求的独特全息解决方案。" }),
      features: [
        t('custom_solutions.design.feature1', { fallback: "基于您需求的定制化设计" }),
        t('custom_solutions.design.feature2', { fallback: "项目的3D可视化呈现" }),
        t('custom_solutions.design.feature3', { fallback: "主题化设计选项" }),
        t('custom_solutions.design.feature4', { fallback: "空间优化解决方案" })
      ],
      href: `/${locale}/pages/custom-playground-design`,
      image: "/images/solutions/custom-design-guide.jpg"
    },
    {
      icon: Settings,
      title: t('custom_solutions.purchase.title', { fallback: "全息技术购买指南" }),
      description: t('custom_solutions.purchase.description', { fallback: "刚接触全息和沉浸式技术？我们的综合指南将引导您了解为企业选择和实施正确全息解决方案的各个方面。" }),
      features: [
        t('custom_solutions.purchase.feature1', { fallback: "逐步实施流程" }),
        t('custom_solutions.purchase.feature2', { fallback: "预算和财务规划" }),
        t('custom_solutions.purchase.feature3', { fallback: "位置和空间规划建议" }),
        t('custom_solutions.purchase.feature4', { fallback: "技术选择指导" })
      ],
      href: `/${locale}/pages/how-to-purchase-your-first-holographic-system`,
      image: "/images/solutions/purchase-guide.jpg"
    }
  ]

  const processSteps = [
    {
      number: "01",
      title: "初步咨询",
      description: "我们首先了解您的创意、需求、空间限制和预算。",
      icon: Users
    },
    {
      number: "02",
      title: "概念开发",
      description: "我们的设计团队根据您的输入和需求创建初步概念。",
      icon: Lightbulb
    },
    {
      number: "03",
      title: "设计完善",
      description: "我们根据您的反馈和偏好完善选定的概念。",
      icon: Settings
    },
    {
      number: "04",
      title: "最终设计与实施",
      description: "一旦获得批准，我们完成设计并开始实施过程。",
      icon: Rocket
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Hero Section */}
      <section
        className="relative text-white overflow-hidden"
        style={{
          backgroundImage: 'url(/images/backgrounds/product-banner.png)',
          backgroundSize: 'cover',
          backgroundPosition: 'center center'
        }}
      >
        <div className="absolute inset-0 bg-black/30" />
        <div className="relative z-10 max-w-7xl mx-auto px-6 py-20 md:py-24">
          <motion.div
            className="text-center"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight text-white"
              variants={itemVariants}
            >
              全息解决方案
            </motion.h1>
            <motion.p
              className="text-lg md:text-xl lg:text-2xl text-white max-w-3xl mx-auto mb-8 leading-relaxed"
              variants={itemVariants}
            >
              为您的独特需求量身定制全息解决方案
            </motion.p>
            <motion.div
              className="flex items-center justify-center space-x-2 text-white text-sm md:text-base"
              variants={itemVariants}
            >
              <Link href={`/${locale}`} className="hover:text-gray-200 transition-colors">
                首页
              </Link>
              <ArrowRight className="w-4 h-4" />
              <span>全息解决方案</span>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Introduction Section */}
      <section className="py-20">
        <motion.div
          className="max-w-7xl mx-auto px-6"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div variants={itemVariants}>
              <div className="relative">
                <Image
                  src="/images/solutions/custom-solution-planning.jpg"
                  alt="全息解决方案规划"
                  width={600}
                  height={400}
                  className="rounded-2xl shadow-2xl"
                />
                <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/20 to-purple-500/20 rounded-2xl" />
              </div>
            </motion.div>

            <motion.div variants={itemVariants} className="space-y-6">
              <h2 className="text-4xl font-bold text-gray-900">
                专业定制，精准匹配
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                在广州骏升，我们深知每个项目都是独一无二的。因此，我们提供定制化的全息解决方案，完美契合您的具体需求、空间限制和预算。
              </p>
              <p className="text-lg text-gray-600 leading-relaxed">
                无论您是寻求完整的定制化全息方案，还是在实施新技术方面需要指导，我们的专家团队都将全程为您提供支持。
              </p>
              <div className="flex items-center space-x-4 pt-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-gray-700">专业团队</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-gray-700">定制方案</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-gray-700">全程支持</span>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </section>

      {/* Solutions Cards */}
      <section className="py-20 bg-gray-50">
        <motion.div
          className="max-w-7xl mx-auto px-6"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.div className="text-center mb-16" variants={itemVariants}>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              我们的全息解决方案
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              从概念到实施，我们提供全方位的全息技术解决方案
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {solutions.map((solution, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="group"
                onMouseEnter={() => setHoveredCard(index)}
                onMouseLeave={() => setHoveredCard(null)}
              >
                <div className="bg-white rounded-2xl shadow-lg overflow-hidden transition-all duration-300 group-hover:shadow-2xl group-hover:-translate-y-2">
                  <div className="relative h-64 overflow-hidden">
                    <Image
                      src={solution.image}
                      alt={solution.title}
                      width={600}
                      height={400}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                    <div className="absolute top-4 left-4">
                      <div
                        className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center"
                        style={{
                          backgroundColor: 'rgba(255, 255, 255, 0.2)',
                          backdropFilter: 'blur(4px)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <solution.icon
                          className="w-6 h-6 text-white"
                          style={{
                            color: '#ffffff',
                            width: '24px',
                            height: '24px',
                            display: 'block'
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="p-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">
                      {solution.title}
                    </h3>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      {solution.description}
                    </p>

                    <div className="space-y-3 mb-8">
                      {solution.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                          <span className="text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>

                    <Link
                      href={solution.href}
                      className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:from-blue-700 hover:to-purple-700 hover:shadow-lg border-0"
                      style={{
                        background: 'linear-gradient(to right, #2563eb, #9333ea)',
                        color: '#ffffff',
                        border: 'none',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                      }}
                    >
                      <span>了解更多</span>
                      <ArrowRight className={`w-4 h-4 transition-transform duration-300 ${hoveredCard === index ? 'translate-x-1' : ''}`} />
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </section>

      {/* Process Steps */}
      <section className="py-20">
        <motion.div
          className="max-w-7xl mx-auto px-6"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.div className="text-center mb-16" variants={itemVariants}>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              我们的全息定制流程
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              从初步咨询到最终实施，我们为您提供专业的全程服务
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="text-center group"
              >
                <div className="relative mb-6">
                  <div
                    className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300"
                    style={{
                      background: 'linear-gradient(135deg, #3b82f6, #9333ea)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <step.icon
                      className="w-8 h-8 text-white"
                      style={{
                        color: '#ffffff',
                        width: '32px',
                        height: '32px',
                        display: 'block'
                      }}
                    />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-white border-2 border-blue-500 rounded-full flex items-center justify-center text-sm font-bold text-blue-500">
                    {step.number}
                  </div>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {step.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="cta-particles"></div>
        <div className="container">
          <div className="cta-content">
            <h2 suppressHydrationWarning>{t('custom_solutions.cta.title', { fallback: '准备好讨论您的全息定制解决方案？' })}</h2>
            <p suppressHydrationWarning>{t('custom_solutions.cta.description', { fallback: '今天就联系我们的团队，探索我们如何为您的需求创造完美的全息解决方案。' })}</p>
            <Link
              href={`/${locale}/pages/contact-us`}
              className="btn-primary"
              suppressHydrationWarning
            >
              {t('custom_solutions.cta.button', { fallback: '立即联系我们' })}
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
