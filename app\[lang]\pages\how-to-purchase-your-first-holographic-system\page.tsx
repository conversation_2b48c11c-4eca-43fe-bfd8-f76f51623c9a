'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { CheckCircle, Users, TrendingUp, Star, ArrowRight, MapPin, Calculator, Package, Building } from "lucide-react";
import { useLanguage } from '../../../components/LanguageProvider';

export default function HolographicGuide({ params }: { params: { lang: string } }) {
  const locale = params.lang || 'zh';
  const { t } = useLanguage();

  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    message: ''
  });
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 处理表单输入
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // 模拟提交过程
      await new Promise(resolve => setTimeout(resolve, 1000));
      setFormSubmitted(true);
      setFormData({ name: '', phone: '', message: '' });
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 检测屏幕尺寸
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const stats = [
    { number: "10+", label: t('holographic_guide.stats.experience', { fallback: "年项目经验" }), icon: Building },
    { number: "200+", label: t('holographic_guide.stats.cases', { fallback: "客户案例" }), icon: Users },
    { number: "98%", label: t('holographic_guide.stats.satisfaction', { fallback: "客户满意度" }), icon: Star },
  ];

  const planningSteps = [
    {
      title: t('holographic_guide.steps.business.title', { fallback: "Business Planning" }),
      subtitle: t('holographic_guide.steps.business.subtitle', { fallback: "商业规划" }),
      color: "bg-blue-500",
      icon: TrendingUp,
      points: [
        t('holographic_guide.steps.business.point1', { fallback: "分析您的业务模式，确定盈利模式" }),
        t('holographic_guide.steps.business.point2', { fallback: "定义目标客户群体（年龄段、人口统计）" }),
        t('holographic_guide.steps.business.point3', { fallback: "制定营销策略和推广计划" }),
        t('holographic_guide.steps.business.point4', { fallback: "建立运营管理体系" }),
      ],
      expert: t('holographic_guide.steps.business.expert', { fallback: "专业建议：基于丰富的运营经验，我们将帮助您建立可持续发展的商业模式。" }),
    },
    {
      title: t('holographic_guide.steps.location.title', { fallback: "Location Planning" }),
      subtitle: t('holographic_guide.steps.location.subtitle', { fallback: "选址规划" }),
      color: "bg-red-500",
      icon: MapPin,
      points: [
        t('holographic_guide.steps.location.point1', { fallback: "选择具有高客流量的优质地段位置" }),
        t('holographic_guide.steps.location.point2', { fallback: "确保有足够的停车位和便利设施" }),
        t('holographic_guide.steps.location.point3', { fallback: "考虑交通便利性，让家长和孩子容易到达" }),
        t('holographic_guide.steps.location.point4', { fallback: "检查当地法规、消防、卫生等要求，确保合规" }),
      ],
      expert: t('holographic_guide.steps.location.expert', { fallback: "专业建议：理想的选址将直接影响全息项目的成功，我们帮助您找到最佳位置。" }),
    },
    {
      title: t('holographic_guide.steps.budgeting.title', { fallback: "Budgeting" }),
      subtitle: t('holographic_guide.steps.budgeting.subtitle', { fallback: "预算规划" }),
      color: "bg-green-500",
      icon: Calculator,
      points: [
        t('holographic_guide.steps.budgeting.point1', { fallback: "确定总体投资预算和分阶段投入" }),
        t('holographic_guide.steps.budgeting.point2', { fallback: "设备采购预算（全息设备约占60%-70%）" }),
        t('holographic_guide.steps.budgeting.point3', { fallback: "装修装饰、安装、运输等费用预算" }),
        t('holographic_guide.steps.budgeting.point4', { fallback: "为开业前期运营、市场推广预留资金" }),
      ],
      expert: t('holographic_guide.steps.budgeting.expert', { fallback: "专业建议：合理的预算分配是项目成功的关键，我们帮您优化投资回报。" }),
    },
    {
      title: t('holographic_guide.steps.equipment.title', { fallback: "Equipment Selection" }),
      subtitle: t('holographic_guide.steps.equipment.subtitle', { fallback: "设备选择" }),
      color: "bg-sky-500",
      icon: Package,
      points: [
        t('holographic_guide.steps.equipment.point1', { fallback: "根据场地面积和客户需求选择合适的设备" }),
        t('holographic_guide.steps.equipment.point2', { fallback: "选择符合国际安全标准的优质设备" }),
        t('holographic_guide.steps.equipment.point3', { fallback: "考虑设备的趣味性和教育价值" }),
        t('holographic_guide.steps.equipment.point4', { fallback: "确保设备的维护便利性和使用寿命" }),
      ],
      expert: t('holographic_guide.steps.equipment.expert', { fallback: "专业建议：优质的设备是全息项目的核心，我们提供最适合的设备解决方案。" }),
    },
  ];

  return (
    <div className="main-content">
      {/* Page Header */}
      <section className="page-header bg-gradient">
        <div className="container">
          <h1 className="page-title" suppressHydrationWarning>
            {t('holographic_guide.page_title', { fallback: '全息投影项目购买指南' })}
          </h1>
          <div className="breadcrumbs">
            <Link href={`/${locale}`} suppressHydrationWarning>{t('common.home', { fallback: '首页' })}</Link> &gt;
            <Link href={`/${locale}/custom-solutions`} suppressHydrationWarning>
              {t('common.custom_solutions', { fallback: '定制解决方案' })}
            </Link>{' '}
            &gt;
            <span suppressHydrationWarning>{t('holographic_guide.page_title', { fallback: '全息投影项目购买指南' })}</span>
          </div>
        </div>
      </section>

      {/* 添加横幅和内容之间的间距 */}
      <div className="py-16"></div>

      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50" suppressHydrationWarning={true}>
        {/* Header Section */}
        <div className="container mx-auto px-4 py-12">
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          <div className="space-y-8">
            <div className="space-y-4">
              <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-600" suppressHydrationWarning>
                {t('holographic_guide.badge', { fallback: '专业指导' })}
              </div>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight" suppressHydrationWarning>
                {t('holographic_guide.main_title_part1', { fallback: '您成功全息投影项目的' })}
                <span className="text-blue-600" suppressHydrationWarning>{t('holographic_guide.main_title_part2', { fallback: '路线图' })}</span>
              </h1>
              <p className="text-lg text-gray-600 leading-relaxed" suppressHydrationWarning>
                {t('holographic_guide.description1', { fallback: '我们为您提供一个专业的指导路线图，让您从投资规划、选址定位到设备选择，每个环节都能得到专业指导。让您的全息投影项目从规划到运营都能获得成功保障，确保投资回报最大化和长期可持续发展。' })}
              </p>
              <p className="text-gray-600" suppressHydrationWarning>
                {t('holographic_guide.description2', { fallback: '我们的专业团队将陪伴您，从项目规划到成功运营的每一个关键环节提供全方位支持，确保您的投资获得最佳回报，实现商业目标的同时创造社会价值。' })}
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div key={index} className="text-center space-y-2">
                    <div className="flex justify-center">
                      <IconComponent className="w-8 h-8 text-blue-600" />
                    </div>
                    <div className="text-3xl font-bold text-gray-900">{stat.number}</div>
                    <div className="text-sm text-gray-600">{stat.label}</div>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="relative">
            <div className="bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl p-8 text-white shadow-2xl">
              <div className="text-center space-y-4">
                <Package className="w-16 h-16 mx-auto opacity-90" />
                <h3 className="text-2xl font-bold" suppressHydrationWarning>{t('holographic_guide.card_title', { fallback: 'Holographic Project Guide' })}</h3>
                <p className="text-blue-100" suppressHydrationWarning>{t('holographic_guide.card_subtitle', { fallback: '专业的全息投影采购指导方案' })}</p>
              </div>
            </div>
            <div className="absolute -bottom-4 -right-4 w-24 h-24 bg-blue-200 rounded-full opacity-20"></div>
            <div className="absolute -top-4 -left-4 w-16 h-16 bg-blue-300 rounded-full opacity-30"></div>
          </div>
        </div>

        {/* Step by Step Guide */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-600 mb-4" suppressHydrationWarning>
            {t('holographic_guide.process_badge', { fallback: '专业流程' })}
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-4" suppressHydrationWarning>{t('holographic_guide.steps_title', { fallback: '逐步购买指南' })}</h2>
          <p className="text-gray-600 max-w-2xl mx-auto" suppressHydrationWarning>
            {t('holographic_guide.steps_description', { fallback: '从市场调研到设备安装，我们将指导您完成每个重要步骤，确保您的投资决策明智且有效。' })}
          </p>
        </div>

        {/* Planning Steps */}
        <div className="space-y-8">
          {planningSteps.map((step, index) => {
            const IconComponent = step.icon;
            return (
              <div key={index} className="overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-lg bg-white">
                <div className="grid lg:grid-cols-2 gap-0">
                  <div className="p-8 space-y-8 bg-gradient-to-br from-white to-gray-50">
                    <div className="flex items-center gap-4 pb-4 border-b border-gray-100">
                      <div className={`${step.color} p-4 rounded-xl shadow-lg`}>
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="text-sm text-gray-500 font-medium uppercase tracking-wide">
                          {step.subtitle}
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900 mt-1">{step.title}</h3>
                      </div>
                      <div className="text-right">
                        <div className="text-3xl font-bold text-gray-300">0{index + 1}</div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      {step.points.map((point, pointIndex) => (
                        <div
                          key={pointIndex}
                          className="flex items-start gap-4 p-4 bg-white rounded-lg border border-gray-100 hover:border-gray-200 transition-colors"
                        >
                          <div className="flex-shrink-0 mt-1">
                            <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                              <CheckCircle className="w-4 h-4 text-green-600" />
                            </div>
                          </div>
                          <span className="text-gray-700 leading-relaxed">{point}</span>
                        </div>
                      ))}
                    </div>

                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl"></div>
                      <div className="relative bg-white/80 backdrop-blur-sm p-6 rounded-xl border border-blue-100 shadow-sm">
                        <div className="flex items-start gap-4">
                          <div className="flex-shrink-0">
                            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-lg">
                              <Star className="w-5 h-5 text-white" />
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                              <span suppressHydrationWarning>{t('holographic_guide.expert_advice', { fallback: '专业建议' })}</span>
                              <div className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                                EXPERT TIP
                              </div>
                            </div>
                            <p className="text-gray-600 leading-relaxed">{step.expert}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`${step.color} flex items-center justify-center p-8 text-white relative overflow-hidden`}
                  >
                    <div className="text-center z-10">
                      <IconComponent className="w-16 h-16 mx-auto mb-4 opacity-90" />
                      <h4 className="text-2xl font-bold">{step.title}</h4>
                      <p className="text-lg opacity-90 mt-2">{step.subtitle}</p>
                    </div>
                    <div className="absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -translate-y-16 translate-x-16"></div>
                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-10 rounded-full translate-y-12 -translate-x-12"></div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>


        </div>

        {/* 报价表单部分 */}
        <div style={{
          maxWidth: '1240px',
          margin: '120px auto 80px',
          padding: '0 20px',
          position: 'relative'
        }}>
          {/* 装饰元素 */}
          <div style={{
            position: 'absolute',
            width: '150px',
            height: '150px',
            borderRadius: '50%',
            top: '20%',
            left: '-5%',
            background: 'linear-gradient(135deg, #1a1a2e, #5050a5)',
            opacity: 0.1,
            zIndex: -1,
            animation: 'float 8s ease-in-out infinite'
          }}></div>
          <div style={{
            position: 'absolute',
            width: '150px',
            height: '150px',
            borderRadius: '50%',
            bottom: '10%',
            right: '-5%',
            background: 'linear-gradient(135deg, #5050a5, #1a1a2e)',
            opacity: 0.1,
            zIndex: -1,
            animation: 'float 10s ease-in-out infinite'
          }}></div>

          <div style={{ width: '100%', position: 'relative' }}>
            <h2 style={{
              textAlign: 'center',
              marginBottom: '40px',
              fontSize: '32px',
              fontWeight: 600,
              color: '#1a1a2e',
              position: 'relative',
              paddingBottom: '15px',
              marginTop: '30px'
            }} suppressHydrationWarning>
              {t('quote_form.title', { fallback: '咨询领取报价' })}
              <div style={{
                position: 'absolute',
                bottom: 0,
                left: '50%',
                transform: 'translateX(-50%)',
                width: '80px',
                height: '3px',
                background: 'linear-gradient(90deg, rgba(26, 26, 46, 0.1), #1a1a2e, rgba(26, 26, 46, 0.1))'
              }}></div>
            </h2>

            {formSubmitted ? (
              <div style={{
                maxWidth: '800px',
                margin: '0 auto',
                backgroundColor: '#fff',
                padding: '40px',
                borderRadius: '15px',
                boxShadow: '0 10px 30px rgba(26, 26, 46, 0.08)',
                textAlign: 'center',
                border: '1px solid rgba(26, 26, 46, 0.05)'
              }}>
                <h3 style={{
                  color: '#28a745',
                  marginBottom: '20px',
                  fontSize: '26px'
                }} suppressHydrationWarning>{t('quote_form.success_title', { fallback: '提交成功！' })}</h3>
                <p style={{
                  marginBottom: '30px',
                  color: '#555',
                  fontSize: '18px',
                  lineHeight: 1.6
                }} suppressHydrationWarning>{t('quote_form.success_message', { fallback: '感谢您的咨询，我们的团队将尽快与您联系。' })}</p>
                <button
                  style={{
                    padding: '14px 30px',
                    backgroundColor: '#6c757d',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '16px',
                    fontWeight: 500,
                    cursor: 'pointer',
                    transition: 'all 0.3s',
                    boxShadow: '0 5px 15px rgba(108, 117, 125, 0.2)'
                  }}
                  onClick={() => setFormSubmitted(false)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#5a6268';
                    e.currentTarget.style.transform = 'translateY(-3px)';
                    e.currentTarget.style.boxShadow = '0 8px 20px rgba(108, 117, 125, 0.3)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#6c757d';
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 5px 15px rgba(108, 117, 125, 0.2)';
                  }}
                >
                  <span suppressHydrationWarning>{t('quote_form.submit_again', { fallback: '再次提交' })}</span>
                </button>
              </div>
            ) : (
              <form
                style={{
                  maxWidth: '800px',
                  margin: '0 auto',
                  backgroundColor: '#fff',
                  padding: '40px',
                  borderRadius: '15px',
                  boxShadow: '0 10px 30px rgba(26, 26, 46, 0.08)',
                  border: '1px solid rgba(26, 26, 46, 0.05)',
                  transition: 'transform 0.3s ease, box-shadow 0.3s ease'
                }}
                onSubmit={handleSubmit}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px)';
                  e.currentTarget.style.boxShadow = '0 15px 40px rgba(26, 26, 46, 0.12)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 10px 30px rgba(26, 26, 46, 0.08)';
                }}
              >
                <div style={{
                  display: 'flex',
                  gap: '30px',
                  marginBottom: '25px',
                  flexDirection: isMobile ? 'column' : 'row'
                }}>
                  <div style={{ flex: 1, marginBottom: 0 }}>
                    <label htmlFor="name" style={{
                      display: 'block',
                      marginBottom: '10px',
                      fontWeight: 500,
                      color: '#1a1a2e',
                      fontSize: '17px'
                    }} suppressHydrationWarning>
                      {t('quote_form.name', { fallback: '您的姓名' })} <span style={{ color: '#f53d3d', marginLeft: '4px' }}>*</span>
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      style={{
                        width: '100%',
                        padding: '16px 20px',
                        fontSize: '16px',
                        border: '1px solid rgba(26, 26, 46, 0.1)',
                        borderRadius: '8px',
                        transition: 'all 0.3s ease',
                        backgroundColor: '#f9f9fd',
                        boxSizing: 'border-box'
                      }}
                      required
                      value={formData.name}
                      onChange={handleInputChange}
                      onFocus={(e) => {
                        e.target.style.borderColor = '#1a1a2e';
                        e.target.style.boxShadow = '0 0 0 3px rgba(26, 26, 46, 0.1)';
                        e.target.style.backgroundColor = '#fff';
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = 'rgba(26, 26, 46, 0.1)';
                        e.target.style.boxShadow = 'none';
                        e.target.style.backgroundColor = '#f9f9fd';
                      }}
                    />
                  </div>
                  <div style={{ flex: 1, marginBottom: 0 }}>
                    <label htmlFor="phone" style={{
                      display: 'block',
                      marginBottom: '10px',
                      fontWeight: 500,
                      color: '#1a1a2e',
                      fontSize: '17px'
                    }} suppressHydrationWarning>
                      {t('quote_form.phone', { fallback: '联系电话' })} <span style={{ color: '#f53d3d', marginLeft: '4px' }}>*</span>
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      style={{
                        width: '100%',
                        padding: '16px 20px',
                        fontSize: '16px',
                        border: '1px solid rgba(26, 26, 46, 0.1)',
                        borderRadius: '8px',
                        transition: 'all 0.3s ease',
                        backgroundColor: '#f9f9fd',
                        boxSizing: 'border-box'
                      }}
                      required
                      value={formData.phone}
                      onChange={handleInputChange}
                      onFocus={(e) => {
                        e.target.style.borderColor = '#1a1a2e';
                        e.target.style.boxShadow = '0 0 0 3px rgba(26, 26, 46, 0.1)';
                        e.target.style.backgroundColor = '#fff';
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = 'rgba(26, 26, 46, 0.1)';
                        e.target.style.boxShadow = 'none';
                        e.target.style.backgroundColor = '#f9f9fd';
                      }}
                    />
                  </div>
                </div>

                <div style={{ marginBottom: '25px' }}>
                  <label htmlFor="message" style={{
                    display: 'block',
                    marginBottom: '10px',
                    fontWeight: 500,
                    color: '#1a1a2e',
                    fontSize: '17px'
                  }} suppressHydrationWarning>{t('quote_form.message', { fallback: '需求备注' })}</label>
                  <textarea
                    id="message"
                    name="message"
                    rows={4}
                    style={{
                      width: '100%',
                      padding: '16px 20px',
                      fontSize: '16px',
                      border: '1px solid rgba(26, 26, 46, 0.1)',
                      borderRadius: '8px',
                      transition: 'all 0.3s ease',
                      backgroundColor: '#f9f9fd',
                      resize: 'vertical',
                      minHeight: '120px',
                      boxSizing: 'border-box'
                    }}
                    value={formData.message}
                    onChange={handleInputChange}
                    onFocus={(e) => {
                      e.target.style.borderColor = '#1a1a2e';
                      e.target.style.boxShadow = '0 0 0 3px rgba(26, 26, 46, 0.1)';
                      e.target.style.backgroundColor = '#fff';
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = 'rgba(26, 26, 46, 0.1)';
                      e.target.style.boxShadow = 'none';
                      e.target.style.backgroundColor = '#f9f9fd';
                    }}
                  ></textarea>
                </div>

                <div style={{ marginBottom: '25px' }}>
                  <button
                    type="submit"
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '100%',
                      height: '50px',
                      padding: '0 20px',
                      backgroundColor: 'hsl(210deg 100% 44%)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '12px',
                      fontSize: '18px',
                      fontWeight: 'bold',
                      letterSpacing: '4px',
                      textTransform: 'uppercase',
                      cursor: isSubmitting ? 'not-allowed' : 'pointer',
                      transition: '31ms cubic-bezier(.5, .7, .4, 1)',
                      boxShadow: 'hsl(210deg 87% 36%) 0px 7px 0px 0px',
                      position: 'relative',
                      overflow: 'hidden',
                      opacity: isSubmitting ? 0.7 : 1
                    }}
                    disabled={isSubmitting}
                    onMouseDown={(e) => {
                      if (!isSubmitting) {
                        e.currentTarget.style.boxShadow = 'none';
                        e.currentTarget.style.transform = 'translateY(7px)';
                      }
                    }}
                    onMouseUp={(e) => {
                      if (!isSubmitting) {
                        e.currentTarget.style.boxShadow = 'hsl(210deg 87% 36%) 0px 7px 0px 0px';
                        e.currentTarget.style.transform = 'translateY(0)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!isSubmitting) {
                        e.currentTarget.style.boxShadow = 'hsl(210deg 87% 36%) 0px 7px 0px 0px';
                        e.currentTarget.style.transform = 'translateY(0)';
                      }
                    }}
                  >
                    <span suppressHydrationWarning>{isSubmitting ? t('quote_form.submitting', { fallback: '提交中...' }) : t('quote_form.submit', { fallback: '提交' })}</span>
                  </button>
                </div>

                <p style={{
                  textAlign: 'center',
                  color: '#666',
                  fontSize: '0.9rem',
                  marginTop: '1.5rem'
                }} suppressHydrationWarning>{t('quote_form.privacy_note', { fallback: '提交即视为同意我们的隐私政策' })}</p>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}