'use client';

import { useLanguage } from '../../../components/LanguageProvider';
import Image from 'next/image';
import Link from 'next/link';
import { generatePlaceholderSVG } from '../../../utils/imagePlaceholder';

export default function QualityControlPage() {
  const { t, locale } = useLanguage();

  return (
    <div className="quality-control-page">
      <section className="page-banner">
        <div className="container">
          <h1 className="page-title">{t('dropdown.quality_control')}</h1>
          <div className="breadcrumb">
            <Link href={`/${locale}`}>{t('common.home')}</Link> /
            <Link href={`/${locale}/pages/service`}>{t('common.services')}</Link> /
            <span>{t('dropdown.quality_control')}</span>
          </div>
        </div>
      </section>

      <section className="quality-intro">
        <div className="container">
          <div className="content-grid">
            <div className="content-image">
              <Image
                src={generatePlaceholderSVG(600, 400, 'Quality Control')}
                alt="Quality Control at Infinity Playground"
                width={600}
                height={400}
              />
            </div>
            <div className="content-text">
              <h2>{t('quality_control.intro.title', { fallback: 'Our Commitment to Quality' })}</h2>
              <p>
                {t('quality_control.intro.paragraph1', {
                  fallback:
                    'At Infinity Playground Park, quality is at the heart of everything we do. We understand that playground equipment must be manufactured to the highest standards to ensure safety, durability, and enjoyment.',
                })}
              </p>
              <p>
                {t('quality_control.intro.paragraph2', {
                  fallback:
                    'Our comprehensive quality control system covers every stage of production, from raw material selection to final product inspection.',
                })}
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="quality-process">
        <div className="container">
          <h2 className="section-title">
            {t('quality_control.process.title', { fallback: 'Our Quality Control Process' })}
          </h2>

          <div className="process-grid">
            <div className="process-item">
              <div className="process-icon">
                <i className="fas fa-microscope"></i>
              </div>
              <h3>
                {t('quality_control.process.step1.title', { fallback: 'Raw Material Inspection' })}
              </h3>
              <p>
                {t('quality_control.process.step1.description', {
                  fallback:
                    'Every material that enters our factory undergoes rigorous testing to ensure it meets our quality specifications. We test for composition, strength, and durability.',
                })}
              </p>
            </div>

            <div className="process-item">
              <div className="process-icon">
                <i className="fas fa-cogs"></i>
              </div>
              <h3>
                {t('quality_control.process.step2.title', { fallback: 'Production Monitoring' })}
              </h3>
              <p>
                {t('quality_control.process.step2.description', {
                  fallback:
                    'Our quality control team monitors the production process at every stage, conducting regular checks and ensuring compliance with design specifications.',
                })}
              </p>
            </div>

            <div className="process-item">
              <div className="process-icon">
                <i className="fas fa-clipboard-check"></i>
              </div>
              <h3>
                {t('quality_control.process.step3.title', { fallback: 'Pre-Shipment Inspection' })}
              </h3>
              <p>
                {t('quality_control.process.step3.description', {
                  fallback:
                    'Before any product leaves our facility, it undergoes comprehensive testing and inspection to ensure it meets all safety and quality standards.',
                })}
              </p>
            </div>

            <div className="process-item">
              <div className="process-icon">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>{t('quality_control.process.step4.title', { fallback: 'Certification' })}</h3>
              <p>
                {t('quality_control.process.step4.description', {
                  fallback:
                    'Our products are certified by international safety standards, including ASTM, EN, CE, TÜV, and more.',
                })}
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
