'use client';

import { useLanguage } from '../../../components/LanguageProvider';
import Image from 'next/image';
import Link from 'next/link';
import { generatePlaceholderSVG } from '../../../utils/imagePlaceholder';

export default function SafeStandardPage() {
  const { t, locale } = useLanguage();

  return (
    <>
      <section className="page-header">
        <div className="container">
          <h1 className="page-title">{t('dropdown.safe_standard')}</h1>
          <div className="breadcrumbs">
            <Link href={`/${locale}`}>{t('common.home')}</Link> &gt;
            <Link href={`/${locale}/pages/service`}>{t('common.services')}</Link> &gt;
            <span>{t('dropdown.safe_standard')}</span>
          </div>
        </div>
      </section>

      <section className="safety-intro">
        <div className="container">
          <div className="content-grid">
            <div className="content-image">
              <Image
                src={generatePlaceholderSVG(600, 400, 'Safety Standards')}
                alt={t('safe_standard.intro.image_alt', {
                  fallback: 'Safety Standards at Infinity Playground',
                })}
                width={600}
                height={400}
              />
            </div>
            <div className="content-text">
              <h2>{t('safe_standard.intro.title', { fallback: 'Safety Is Our Top Priority' })}</h2>
              <p>
                {t('safe_standard.intro.paragraph1', {
                  fallback:
                    'At Infinity Playground Park, we are committed to creating fun and engaging play environments that prioritize safety above all else. We understand the trust that parents, educators, and facility owners place in us when choosing our playground equipment.',
                })}
              </p>
              <p>
                {t('safe_standard.intro.paragraph2', {
                  fallback:
                    'Our dedication to safety begins at the design stage and continues through manufacturing, installation, and beyond. Every component, material, and feature is carefully engineered to meet or exceed international safety standards.',
                })}
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="safety-standards">
        <div className="container">
          <h2 className="section-title">
            {t('safe_standard.standards.title', { fallback: 'International Safety Standards' })}
          </h2>

          <div className="standards-grid">
            <div className="standard-item">
              <div className="standard-icon">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>ASTM F1487</h3>
              <p>
                {t('safe_standard.standards.astm', {
                  fallback:
                    'Standard Consumer Safety Performance Specification for Playground Equipment for Public Use (United States)',
                })}
              </p>
            </div>

            <div className="standard-item">
              <div className="standard-icon">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>EN 1176 & EN 1177</h3>
              <p>
                {t('safe_standard.standards.en', {
                  fallback:
                    'European Standards for Playground Equipment and Impact Absorbing Playground Surfacing',
                })}
              </p>
            </div>

            <div className="standard-item">
              <div className="standard-icon">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>ISO 9001</h3>
              <p>
                {t('safe_standard.standards.iso', {
                  fallback: 'International Standard for Quality Management Systems',
                })}
              </p>
            </div>

            <div className="standard-item">
              <div className="standard-icon">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>CE Marking</h3>
              <p>
                {t('safe_standard.standards.ce', {
                  fallback:
                    'European Conformity for Health, Safety, and Environmental Protection Standards',
                })}
              </p>
            </div>

            <div className="standard-item">
              <div className="standard-icon">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>TÜV Certification</h3>
              <p>
                {t('safe_standard.standards.tuv', {
                  fallback:
                    'German Technical Inspection Association Certification for Safety and Quality',
                })}
              </p>
            </div>

            <div className="standard-item">
              <div className="standard-icon">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>CSA Z614</h3>
              <p>
                {t('safe_standard.standards.csa', {
                  fallback:
                    "Canadian Standards Association Guidelines for Children's Playspaces and Equipment",
                })}
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="safety-features">
        <div className="container">
          <h2 className="section-title">
            {t('safe_standard.features.title', { fallback: 'Safety Features in Our Equipment' })}
          </h2>

          <div className="features-content">
            <ul className="features-list">
              <li>
                <strong>
                  {t('safe_standard.features.rounded_edges.title', { fallback: 'Rounded Edges:' })}
                </strong>{' '}
                {t('safe_standard.features.rounded_edges.description', {
                  fallback: 'All components have rounded edges and corners to prevent injuries.',
                })}
              </li>
              <li>
                <strong>
                  {t('safe_standard.features.non_toxic.title', {
                    fallback: 'Non-Toxic Materials:',
                  })}
                </strong>{' '}
                {t('safe_standard.features.non_toxic.description', {
                  fallback:
                    'We use only non-toxic, lead-free materials and paints that are safe for children.',
                })}
              </li>
              <li>
                <strong>
                  {t('safe_standard.features.anti_slip.title', { fallback: 'Anti-Slip Surfaces:' })}
                </strong>{' '}
                {t('safe_standard.features.anti_slip.description', {
                  fallback: 'Platforms and steps feature anti-slip surfaces to prevent falls.',
                })}
              </li>
              <li>
                <strong>
                  {t('safe_standard.features.impact_absorbing.title', {
                    fallback: 'Impact-Absorbing Flooring:',
                  })}
                </strong>{' '}
                {t('safe_standard.features.impact_absorbing.description', {
                  fallback:
                    'Our flooring systems are designed to cushion falls and reduce injury risk.',
                })}
              </li>
              <li>
                <strong>
                  {t('safe_standard.features.secure.title', { fallback: 'Secure Connections:' })}
                </strong>{' '}
                {t('safe_standard.features.secure.description', {
                  fallback: 'All parts are securely connected with tamper-resistant hardware.',
                })}
              </li>
              <li>
                <strong>
                  {t('safe_standard.features.spacing.title', { fallback: 'Adequate Spacing:' })}
                </strong>{' '}
                {t('safe_standard.features.spacing.description', {
                  fallback: 'Equipment is spaced to prevent crowding and collisions.',
                })}
              </li>
              <li>
                <strong>
                  {t('safe_standard.features.enclosed.title', {
                    fallback: 'Enclosed Climbing Areas:',
                  })}
                </strong>{' '}
                {t('safe_standard.features.enclosed.description', {
                  fallback: 'High platforms are enclosed with barriers to prevent falls.',
                })}
              </li>
              <li>
                <strong>
                  {t('safe_standard.features.age_appropriate.title', {
                    fallback: 'Age-Appropriate Design:',
                  })}
                </strong>{' '}
                {t('safe_standard.features.age_appropriate.description', {
                  fallback: 'Equipment is designed with age-appropriate features and challenges.',
                })}
              </li>
            </ul>

            <div className="features-image">
              <Image
                src={generatePlaceholderSVG(500, 400, 'Safety Features')}
                alt={t('safe_standard.features.image_alt', {
                  fallback: 'Safety Features in Infinity Playground Equipment',
                })}
                width={500}
                height={400}
              />
            </div>
          </div>
        </div>
      </section>

      <section className="cta-section cta-particles">
        <div className="container">
          <div className="cta-content">
            <h2>准备好讨论您的全息定制解决方案？</h2>
            <p>今天就联系我们的团队，探索我们如何为您的需求创造完美的全息解决方案。</p>
            <Link
              href={`/${locale}/pages/contact-us`}
              className="btn-primary"
            >
              立即联系我们
            </Link>
          </div>
        </div>
      </section>
    </>
  );
}
