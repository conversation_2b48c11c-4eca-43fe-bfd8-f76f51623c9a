'use client';

import { useLanguage } from '../../../components/LanguageProvider';
import Image from 'next/image';
import Link from 'next/link';
import { generatePlaceholderSVG } from '../../../utils/imagePlaceholder';

export default function ServicePage() {
  const { t, locale } = useLanguage();

  return (
    <>
      <section className="page-header">
        <div className="container">
          <h1 className="page-title">{t('services.title', { fallback: 'Our Services' })}</h1>
          <div className="breadcrumbs">
            <Link href={`/${locale}`}>{t('common.home')}</Link> &gt;{' '}
            <span>{t('common.services')}</span>
          </div>
        </div>
      </section>

      <section className="services-intro">
        <div className="container">
          <div className="content-grid">
            <div className="content-image">
              <Image
                src={generatePlaceholderSVG(600, 400, 'Infinity Services')}
                alt="Infinity Playground Services"
                width={600}
                height={400}
              />
            </div>
            <div className="content-text">
              <h2>
                {t('services.intro.title', {
                  fallback: '售后体系 - 专业-质量-服务',
                })}
              </h2>
              <p>
                {t('services.intro.paragraph1', {
                  fallback:
                    '我们提供全方位的售后保障体系，从项目咨询到安装调试，再到后期维护，确保您的全息投影系统稳定运行。',
                })}
              </p>
              <p>
                {t('services.intro.paragraph2', {
                  fallback:
                    '免费提供产品规划CAD图，多种场景、多种难度，内容不断丰富更新，增强趣味性，常规产品按年免费更新。',
                })}
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="services-list">
        <div className="container">
          <h2 className="section-title">
            {t('services.offerings.title', { fallback: 'Our Service Offerings' })}
          </h2>

          <div className="services-grid">
            <div className="service-card">
              <div className="service-image">
                <Image
                  src={generatePlaceholderSVG(400, 300, 'Quality Control')}
                  alt="Quality Control Services"
                  width={400}
                  height={300}
                />
              </div>
              <div className="service-content">
                <h3>{t('services.guidance', { fallback: '全程指导' })}</h3>
                <p>
                  {t('services.offerings.guidance.description', {
                    fallback:
                      '客户只需要告知场地面积等基本信息，我们提供全程上门服务，确保项目顺利实施。',
                  })}
                </p>
                <ul className="service-features">
                  <li>
                    {t('services.offerings.guidance.feature1', {
                      fallback: '场地勘测与规划设计',
                    })}
                  </li>
                  <li>
                    {t('services.offerings.guidance.feature2', {
                      fallback: '设备送货与现场安装',
                    })}
                  </li>
                  <li>
                    {t('services.offerings.guidance.feature3', {
                      fallback: '软硬件配置与调试',
                    })}
                  </li>
                  <li>
                    {t('services.offerings.guidance.feature4', {
                      fallback: '点位图制作与优化',
                    })}
                  </li>
                </ul>
                <Link href={`/${locale}/pages/quality-control`} className="btn-secondary">
                  {t('services.learn_more', { fallback: '了解更多' })}
                </Link>
              </div>
            </div>

            <div className="service-card">
              <div className="service-image">
                <Image
                  src={generatePlaceholderSVG(400, 300, 'Safety Standards')}
                  alt="Safety Standards Services"
                  width={400}
                  height={300}
                />
              </div>
              <div className="service-content">
                <h3>{t('services.delivery_process', { fallback: '发货流程' })}</h3>
                <p>
                  {t('services.offerings.delivery_process.description', {
                    fallback:
                      '发货前电子化设备至少需测试48小时以上，防止出现产品质量问题，确保设备稳定可靠。',
                  })}
                </p>
                <ul className="service-features">
                  <li>
                    {t('services.offerings.delivery_process.feature1', {
                      fallback: '48小时以上设备测试',
                    })}
                  </li>
                  <li>
                    {t('services.offerings.delivery_process.feature2', {
                      fallback: '电子化设备全面检测',
                    })}
                  </li>
                  <li>
                    {t('services.offerings.delivery_process.feature3', {
                      fallback: '质量问题预防机制',
                    })}
                  </li>
                  <li>
                    {t('services.offerings.delivery_process.feature4', {
                      fallback: '发货前最终确认',
                    })}
                  </li>
                </ul>
                <Link href={`/${locale}/pages/safe-standard`} className="btn-secondary">
                  {t('services.learn_more', { fallback: '了解更多' })}
                </Link>
              </div>
            </div>

            <div className="service-card">
              <div className="service-image">
                <Image
                  src={generatePlaceholderSVG(400, 300, 'Marketing Support')}
                  alt="Marketing Support Services"
                  width={400}
                  height={300}
                />
              </div>
              <div className="service-content">
                <h3>{t('services.after_sales', { fallback: '售后流程' })}</h3>
                <p>
                  {t('services.offerings.after_sales.description', {
                    fallback:
                      '工程技术1对1二十四小时服务，建立售后微信群，第一时间远程协助并解决问题。',
                  })}
                </p>
                <ul className="service-features">
                  <li>
                    {t('services.offerings.after_sales.feature1', {
                      fallback: '工程技术1对1专属服务',
                    })}
                  </li>
                  <li>
                    {t('services.offerings.after_sales.feature2', {
                      fallback: '24小时技术支持热线',
                    })}
                  </li>
                  <li>
                    {t('services.offerings.after_sales.feature3', {
                      fallback: '售后微信群实时响应',
                    })}
                  </li>
                  <li>
                    {t('services.offerings.after_sales.feature4', {
                      fallback: '远程协助快速解决',
                    })}
                  </li>
                </ul>
                <Link href={`/${locale}/pages/marketing-support`} className="btn-secondary">
                  {t('services.learn_more', { fallback: '了解更多' })}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
