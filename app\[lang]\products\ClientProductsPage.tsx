'use client';

import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import { sliderPlaceholders } from '../../utils/imagePlaceholder';

// Import CSS files
import '../../styles/banner-fix.css'; // 导入覆盖样式

// 定义产品接口 - 修改为PostgreSQL数据库中的结构
interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  price: number;
  category: string;
  image_url: string;
  is_featured: boolean;
  in_stock: boolean;
  created_at: string;
  updated_at: string;
  categories?: string | string[];
}



interface ClientProductsPageProps {
  lang: string;
  initialDict: Record<string, unknown>;
}

export default function ClientProductsPage({ lang, initialDict }: ClientProductsPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [dict, setDict] = useState<Record<string, unknown>>(initialDict);
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [isHydrated, setIsHydrated] = useState(false);

  // Mark when component is hydrated on client
  useEffect(() => {
    setIsHydrated(true);
  }, []);



  // 加载产品
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // 获取所有产品（传递语言参数）
        console.log(`[前端] 请求产品数据，语言: ${lang}`);
        const productsRes = await fetch(`/api/products?lang=${lang}`);
        const productsData = await productsRes.json();
        const productsList = productsData.products || [];
        console.log(`[前端] 收到产品数据，数量: ${productsList.length}, 第一个产品: ${productsList[0]?.name}`);
        setProducts(productsList);
        setFilteredProducts(productsList);

        setLoading(false);
      } catch (error) {
        console.error('Failed to load data:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, [lang]); // 添加 lang 作为依赖项





  // 处理产品点击 - 保持在产品列表页面
  const handleProductClick = (slug: string) => {
    console.log(`点击产品: ${slug}, 保持在产品列表页面`);
    // 不进行导航，保持在当前页面
  };

  // Use a consistent title that doesn't change between server and client renders
  const getTitle = () => {
    // During initial server render or before hydration, return a static placeholder
    if (!isHydrated) {
      return <span suppressHydrationWarning></span>;
    }

    // After hydration on client side, show the actual title from dictionary
    return (dict.products as Record<string, string> | undefined)?.title || 'Products';
  };

  return (
    <>
      <section className="page-banner product-list-page">
        {/* 使用img标签替代背景图片 */}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            zIndex: 0,
            overflow: 'hidden',
          }}
        >
          <img
            src="/images/backgrounds/product-banner.png"
            alt={lang === 'zh' ? '产品横幅背景' : 'Product Banner Background'}
            suppressHydrationWarning
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              objectPosition: 'center center',
            }}
            loading="eager"
            fetchPriority="high"
          />
        </div>
        {/* 添加半透明叠加层，增强文字可见性 */}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background:
              'linear-gradient(to bottom, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.2) 50%, rgba(0,0,0,0.4) 100%)',
            zIndex: 1,
          }}
        ></div>
        <div className="container" style={{ position: 'relative', zIndex: 2, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
          <h1 className="page-title" suppressHydrationWarning>
            {getTitle()}
          </h1>
          <div className="breadcrumb">
            <Link href={`/${lang}`}>
              <span suppressHydrationWarning>
                {isHydrated &&
                  ((dict.common as Record<string, string> | undefined)?.home || 'Home')}
              </span>
            </Link>
            <span className="separator">/</span>
            <span suppressHydrationWarning>
              {isHydrated &&
                ((dict.common as Record<string, string> | undefined)?.products || 'Products')}
            </span>
          </div>
        </div>
      </section>

      <section className="products-page product-list-content">
        <div className="container product-list-container">
          <div className="page-header">
            <p className="page-description" suppressHydrationWarning>
              {isHydrated &&
                ((dict.products as Record<string, string> | undefined)?.description ||
                  'Explore our customizable indoor playground solutions designed for various spaces and themes. Each solution can be tailored to your specific requirements.')}
            </p>
          </div>



          {loading ? (
            <div key="loading-section" className="text-center py-8">
              <p className="text-gray-500 text-lg" suppressHydrationWarning>
                {isHydrated &&
                  ((dict.common as Record<string, string> | undefined)?.loading || 'Loading...')}
              </p>
            </div>
          ) : filteredProducts.length === 0 ? (
            <div key="empty-section" className="text-center py-8">
              <p className="text-gray-500 text-lg" suppressHydrationWarning>
                {isHydrated &&
                  ((dict.common as Record<string, string> | undefined)?.no_products ||
                    'No products found in this category')}
              </p>
            </div>
          ) : (
            <div key="products-grid-section" className="products-grid">
              {filteredProducts.map((product, index) => (
                <div
                  key={`product-${product.id || index}`}
                  className="product-card"
                  onClick={() => handleProductClick(product.slug)}
                  style={{
                    cursor: 'pointer',
                    transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                    position: 'relative',
                    zIndex: 1
                  }}
                >
                  {/* 使用div替代Link，并添加onClick事件 */}
                  <div className="product-card-content">
                    <div className="product-image" style={{ position: 'relative' }}>
                      {product.image_url ? (
                        <div
                          className="product-image-container"
                          style={{ width: '100%', height: '100%', position: 'relative' }}
                        >
                          <img
                            key={`img-${product.id || index}`}
                            src={product.image_url}
                            alt={product.name}
                            style={{
                              objectFit: 'cover',
                              width: '100%',
                              height: '100%',
                              position: 'absolute',
                              top: 0,
                              left: 0,
                            }}
                          />
                        </div>
                      ) : (
                        <div
                          className="product-image-container"
                          style={{ width: '100%', height: '100%', position: 'relative' }}
                        >
                          <img
                            key={`placeholder-${product.id || index}`}
                            src={sliderPlaceholders[index % sliderPlaceholders.length]}
                            alt={product.name || 'Product placeholder image'}
                            style={{
                              objectFit: 'cover',
                              width: '100%',
                              height: '100%',
                              position: 'absolute',
                              top: 0,
                              left: 0,
                            }}
                          />
                        </div>
                      )}
                      <div className="product-overlay">
                        <span className="view-details-btn">
                          {isHydrated &&
                            ((dict.common as Record<string, string> | undefined)?.view_details ||
                              'View Details')}
                        </span>
                      </div>
                    </div>
                    <h3 className="product-title">{product.name}</h3>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>
    </>
  );
}
