'use client';

import { getDictionary } from '../../../utils/i18n';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { Play, Star, Monitor, Users, Volume2, Cpu } from "lucide-react";
// 移除复杂的导入

// 产品骨架屏组件
function ProductSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="animate-pulse">
        {/* 面包屑骨架 */}
        <div className="flex items-center space-x-2 mb-8">
          <div className="h-4 w-16 bg-gray-200 rounded"></div>
          <div className="h-4 w-4 bg-gray-200 rounded"></div>
          <div className="h-4 w-20 bg-gray-200 rounded"></div>
          <div className="h-4 w-4 bg-gray-200 rounded"></div>
          <div className="h-4 w-24 bg-gray-200 rounded"></div>
        </div>

        {/* 产品信息骨架 */}
        <div className="px-4 md:px-8 lg:px-12 py-8 md:py-12 lg:py-16">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-start">
              {/* 左侧图片骨架 */}
              <div className="space-y-4 animate-pulse">
                {/* 主图骨架 */}
                <div className="w-full bg-gray-200 rounded-2xl product-image-1920x1080"></div>

                {/* 缩略图骨架 */}
                <div className="grid grid-cols-4 gap-3">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="bg-gray-200 rounded-xl aspect-video"></div>
                  ))}
                </div>
              </div>

              {/* 右侧信息骨架 */}
              <div className="space-y-8 lg:pl-8 animate-pulse">
                {/* 标题和标签骨架 */}
                <div className="space-y-4">
                  <div className="h-12 w-4/5 bg-gray-200 rounded"></div>
                  <div className="h-8 w-24 bg-gray-200 rounded-full"></div>
                </div>

                {/* 描述骨架 */}
                <div className="space-y-3">
                  <div className="h-4 w-full bg-gray-200 rounded"></div>
                  <div className="h-4 w-full bg-gray-200 rounded"></div>
                  <div className="h-4 w-3/4 bg-gray-200 rounded"></div>
                </div>

                {/* 特点骨架 */}
                <div className="space-y-4">
                  <div className="h-6 w-20 bg-gray-200 rounded"></div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {[...Array(4)].map((_, i) => (
                      <div key={i} className="h-12 bg-gray-200 rounded-xl"></div>
                    ))}
                  </div>
                </div>

                {/* 按钮骨架 */}
                <div className="space-y-4 pt-4">
                  <div className="h-14 w-48 bg-gray-200 rounded-2xl"></div>
                  <div className="flex gap-4">
                    <div className="h-4 w-20 bg-gray-200 rounded"></div>
                    <div className="h-4 w-16 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}



// 定义Dictionary接口
interface Dictionary {
  common?: {
    home?: string;
    products?: string;
    product_not_found?: string;
    [key: string]: string | undefined;
  };
  [key: string]: any;
}



// 产品详情页面组件
export default function ProductPage({ params }: { params: { slug: string, lang: string } }) {
  const { slug, lang } = params;

  // 状态
  const [dict, setDict] = useState<Dictionary>({});
  const [product, setProduct] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // 获取数据 - 使用国际化API
  useEffect(() => {
    // 定义一个异步函数来获取数据
    const fetchData = async () => {
      console.log(`[ProductDetail] Starting to load product data: ${slug}, language: ${lang}`);

      try {
        setLoading(true);
        setError(null);

        // 获取字典
        const dictionary = await getDictionary(lang);
        setDict(dictionary);

        // 使用国际化API获取产品详情
        const response = await fetch(`/api/products/by-slug/${slug}?lang=${lang}`);
        if (response.ok) {
          const productData = await response.json();
          if (productData.product) {
            console.log(`[ProductDetail] Successfully retrieved product data:`, productData.product.name);
            setProduct(productData.product);
            setLoading(false);
            return;
          }
        }

        // 如果API失败，回退到直接读取JSON文件
        console.log(`[ProductDetail] API failed, falling back to JSON file`);
        const jsonResponse = await fetch('/mock-products.json');
        if (jsonResponse.ok) {
          const products = await jsonResponse.json();
          const foundProduct = products.find((p: any) => p.slug === slug);

          if (foundProduct) {
            // 根据语言选择相应的字段
            const localizedProduct = {
              ...foundProduct,
              name: lang === 'en' ? (foundProduct.name_en || foundProduct.name) : foundProduct.name,
              description: lang === 'en' ? (foundProduct.description_en || foundProduct.description) : foundProduct.description,
              category: lang === 'en' ? (foundProduct.category_en || foundProduct.category) : foundProduct.category,
              features: lang === 'en' ? (foundProduct.features_en || foundProduct.features) : foundProduct.features,
            };

            console.log(`[ProductDetail] Retrieved product data from JSON file:`, localizedProduct.name);
            setProduct(localizedProduct);
            setLoading(false);
            return;
          }
        }

        // 如果没有找到产品，抛出错误
        throw new Error(lang === 'zh' ? '产品不存在' : 'Product not found');

      } catch (error) {
        console.error(`[ProductDetail] Loading failed:`, error);
        setError(error instanceof Error ? error.message : (lang === 'zh' ? '加载失败' : 'Loading failed'));
        setLoading(false);
      }
    };

    fetchData();
  }, [lang, slug]);

  // 渲染加载状态
  if (loading) {
    return (
      <>
        <div className="w-full" style={{ marginTop: "0", display: "block", lineHeight: 0 }}>
          <div className="bg-gray-200 w-full" style={{ height: "300px" }}></div>
        </div>
        <div className="product-detail-container product-detail-fix">
          <ProductSkeleton />
        </div>
      </>
    );
  }

  // 渲染错误状态
  if (error || !product) {
    return (
      <>
        <div className="w-full" style={{ marginTop: "0", display: "block", lineHeight: 0 }}>
          <div className="bg-gray-100 w-full" style={{ height: "100px" }}></div>
        </div>
        <div className="product-detail-container product-detail-fix">
          <div className="container mx-auto px-4 py-12 text-center">
            <div className="mb-6 text-red-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold mb-4 text-gray-800">
              {dict.common?.product_not_found || (lang === 'zh' ? '产品不存在' : 'Product Not Found')}
            </h1>
            <p className="text-gray-600 mb-8">
              {error || (lang === 'zh' ? '无法找到请求的产品' : 'The requested product could not be found')}
            </p>
            <Link
              href={`/${lang}/products`}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {dict.common?.products || (lang === 'zh' ? '浏览产品' : 'Browse Products')}
            </Link>
          </div>
        </div>
      </>
    );
  }

  // 获取产品图片和视频的组合 - 视频优先放在第一个
  const getProductImages = (product: any) => {
    const media = [];
    
    // 先添加视频（放在第一个位置）
    if (product.videos && product.videos.length > 0) {
      product.videos.forEach((video: string) => {
        media.push({
          type: 'video',
          src: video,
          thumbnail: product.images?.[0] || '/images/products/video-thumbnail.jpg'
        });
      });
    }
    
    // 然后添加图片 - 确保明确标记为image类型
    if (product.images && product.images.length > 0) {
      product.images.forEach((image: string) => {
        media.push({
          type: 'image',
          src: image,
          thumbnail: image
        });
      });
    }
    
    // 如果没有任何媒体，返回默认占位符
    if (media.length === 0) {
      return [{
        type: 'image',
        src: '/images/products/placeholder.jpg',
        thumbnail: '/images/products/placeholder.jpg'
      }];
    }
    
    return media;
  };

  // 获取产品规格信息
  const getProductSpecifications = (product: any, lang: string) => {
    // 根据产品类型返回不同的规格
    if (product.slug === 'ktv' || product.category?.includes('KTV') || product.name?.includes('KTV')) {
      return [
        {
          icon: Monitor,
          title: lang === 'zh' ? "投影技术" : "Projection Technology",
          desc: lang === 'zh' ? "4K全息投影系统" : "4K Holographic Projection System"
        },
        {
          icon: Users,
          title: lang === 'zh' ? "容纳人数" : "Capacity",
          desc: lang === 'zh' ? "最多20人同时体验" : "Up to 20 people simultaneously"
        },
        {
          icon: Volume2,
          title: lang === 'zh' ? "音响配置" : "Audio System",
          desc: lang === 'zh' ? "7.1环绕立体声" : "7.1 Surround Sound"
        },
        {
          icon: Cpu,
          title: lang === 'zh' ? "处理器" : "Processor",
          desc: lang === 'zh' ? "实时渲染引擎" : "Real-time Rendering Engine"
        },
      ];
    } else if (product.category?.includes('蹦床') || product.name?.includes('蹦床')) {
      return [
        {
          icon: Monitor,
          title: lang === 'zh' ? "AR技术" : "AR Technology",
          desc: lang === 'zh' ? "增强现实互动系统" : "Augmented Reality Interactive System"
        },
        {
          icon: Users,
          title: lang === 'zh' ? "适用年龄" : "Age Range",
          desc: lang === 'zh' ? "3-15岁儿童" : "Children aged 3-15"
        },
        {
          icon: Volume2,
          title: lang === 'zh' ? "安全等级" : "Safety Level",
          desc: lang === 'zh' ? "欧盟CE认证" : "EU CE Certified"
        },
        {
          icon: Cpu,
          title: lang === 'zh' ? "传感器" : "Sensors",
          desc: lang === 'zh' ? "高精度动作捕捉" : "High-precision Motion Capture"
        },
      ];
    } else if (product.category?.includes('沙盘') || product.name?.includes('沙盘')) {
      return [
        {
          icon: Monitor,
          title: lang === 'zh' ? "投影技术" : "Projection Technology",
          desc: lang === 'zh' ? "3D立体投影" : "3D Stereoscopic Projection"
        },
        {
          icon: Users,
          title: lang === 'zh' ? "互动方式" : "Interaction Method",
          desc: lang === 'zh' ? "手势识别控制" : "Gesture Recognition Control"
        },
        {
          icon: Volume2,
          title: lang === 'zh' ? "教育内容" : "Educational Content",
          desc: lang === 'zh' ? "多学科课程包" : "Multi-disciplinary Course Package"
        },
        {
          icon: Cpu,
          title: lang === 'zh' ? "系统配置" : "System Configuration",
          desc: lang === 'zh' ? "智能学习算法" : "Intelligent Learning Algorithm"
        },
      ];
    } else {
      return [
        {
          icon: Monitor,
          title: lang === 'zh' ? "显示技术" : "Display Technology",
          desc: lang === 'zh' ? "高清数字显示" : "High-definition Digital Display"
        },
        {
          icon: Users,
          title: lang === 'zh' ? "用户体验" : "User Experience",
          desc: lang === 'zh' ? "多人互动支持" : "Multi-user Interactive Support"
        },
        {
          icon: Volume2,
          title: lang === 'zh' ? "音效系统" : "Audio System",
          desc: lang === 'zh' ? "立体声音响" : "Stereo Sound System"
        },
        {
          icon: Cpu,
          title: lang === 'zh' ? "控制系统" : "Control System",
          desc: lang === 'zh' ? "智能化管理" : "Intelligent Management"
        },
      ];
    }
  };

  const specifications = getProductSpecifications(product, lang);
  const mediaItems = getProductImages(product);
  // 过滤掉视频，只保留图片
  const imageItems = mediaItems.filter((mediaItem: any) => mediaItem?.type !== 'video');
  const thumbnails = imageItems;

  // 正常渲染产品详情 - 现代化设计
  return (
    <div className="relative product-detail-page" style={{ marginTop: 0, paddingTop: 0 }}>
      {/* 顶部横幅背景图片 */}
      <div className="w-full absolute top-0 left-0 right-0" style={{ height: "450px", marginTop: "0", zIndex: 0 }}>
        <img
          src="/images/products/product-banner.png"
          alt="Product Banner"
          className="w-full h-full object-cover"
          loading="eager"
          fetchPriority="high"
        />

        {/* 面包屑导航覆盖在背景图片上 */}
        <div className="absolute inset-0 flex items-center justify-center" style={{ zIndex: 2 }}>
          <div className="container mx-auto px-4">
            <div className="product-detail-navigation-overlay">
              <div className="flex justify-center">
                <div className="breadcrumbs-overlay animate-slide-in-left">
                  <Link href={`/${lang}`}>
                    {dict.common?.home || (lang === 'zh' ? '首页' : 'Home')}
                  </Link>
                  <span className="separator">/</span>
                  <Link href={`/${lang}/products`}>
                    {dict.common?.products || (lang === 'zh' ? '产品' : 'Products')}
                  </Link>
                  <span className="separator">/</span>
                  <span className="current">{product.title || product.name}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="product-detail-container bg-white relative transform scale-110 origin-top" style={{ marginTop: "450px", zIndex: 1 }}>

        {/* Header Section */}
        <div className="bg-gray-50 border-b">
          <div className="container mx-auto px-8 py-12">
            <div className="grid lg:grid-cols-2 gap-8 items-start">
              {/* Left - Images */}
              <div className="space-y-5">
                {/* Main Image */}
                <div className="relative aspect-[16/9] bg-gray-100 rounded-lg overflow-hidden">
                  {mediaItems[selectedImageIndex]?.type === 'video' ? (
                    // 显示视频缩略图 - 整个区域可点击
                    <div 
                      className="relative w-full h-full cursor-pointer group"
                      onClick={() => {
                        const videoUrl = mediaItems[selectedImageIndex]?.src;
                        if (videoUrl) {
                          // 创建模态框播放视频
                          const modal = document.createElement('div');
                          modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
                          modal.innerHTML = `
                            <div class="relative max-w-4xl w-full mx-4">
                              <button class="absolute -top-10 right-0 text-white text-2xl hover:text-gray-300" onclick="this.parentElement.parentElement.remove()">
                                ✕
                              </button>
                              <video controls autoplay class="w-full h-auto rounded-lg">
                                <source src="${videoUrl}" type="video/mp4">
                                Your browser does not support the video tag.
                              </video>
                            </div>
                          `;
                          document.body.appendChild(modal);
                          
                          // 点击背景关闭模态框
                          modal.addEventListener('click', (e) => {
                            if (e.target === modal) {
                              modal.remove();
                            }
                          });
                        }
                      }}
                    >
                      <img
                        src={imageItems[selectedImageIndex]?.thumbnail || imageItems[selectedImageIndex]?.src || product.images?.[0] || "/images/products/placeholder.jpg"}
                        alt={product.title || product.name}
                        className="w-full h-full object-cover"
                      />
                      {/* 视频播放图标覆盖层 */}
                      <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center group-hover:bg-opacity-40 transition-all">
                        <div className="bg-white bg-opacity-90 rounded-full p-4 group-hover:scale-110 transition-transform">
                          <Play className="w-8 h-8 text-gray-900" />
                        </div>
                        <div className="absolute bottom-4 left-4 bg-black bg-opacity-60 text-white px-3 py-1 rounded-lg text-sm">
                          {dict.common?.watch_demo || (lang === 'zh' ? '观看演示' : 'Watch Demo')}
                        </div>
                      </div>
                    </div>
                  ) : (
                    // 显示普通图片
                    <>
                      <img
                        src={mediaItems[selectedImageIndex]?.src || product.images?.[0] || "/images/products/placeholder.jpg"}
                        alt={product.title || product.name}
                        className="w-full h-full object-cover"
                      />
                      

                    </>
                  )}
                </div>

                {/* Thumbnails - 只显示图片缩略图 */}
                {thumbnails && thumbnails.length > 1 && (
                  <div className="flex gap-2 overflow-x-auto pb-2 -mx-2 px-2">
                    {thumbnails.map((mediaItem: any, index: number) => (
                      <button
                        key={`${mediaItem?.type}-${index}-${mediaItem?.src}`}
                        onClick={() => setSelectedImageIndex(index)}
                        className={`flex-shrink-0 w-24 h-20 rounded border-2 overflow-hidden transition-colors relative ${
                          selectedImageIndex === index ? "border-gray-900" : "border-gray-200 hover:border-gray-400"
                        }`}
                      >
                        <img
                          src={mediaItem?.thumbnail || mediaItem?.src || "/images/products/placeholder.jpg"}
                          alt={lang === 'zh' ? `视图 ${index + 1}` : `View ${index + 1}`}
                          className="object-cover w-full h-full"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Right - Product Info */}
              <div className="space-y-8">
                <div className="space-y-6">
                  <div className="flex items-center gap-3">
                    <span className="bg-gray-900 text-white px-3 py-1 rounded text-sm">
                      {dict.common?.professional || (lang === 'zh' ? '专业级' : 'Professional')}
                    </span>
                    <div className="flex items-center gap-1">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-gray-900 text-gray-900" />
                      ))}
                      <span className="text-sm text-gray-600 ml-2">4.9 (128 {dict.common?.reviews || (lang === 'zh' ? '评价' : 'reviews')})</span>
                    </div>
                  </div>

                  <div>
                    <h1 className="text-4xl lg:text-5xl font-light text-gray-900 mb-4 leading-tight">
                      {product.title || product.name}
                    </h1>
                    <p className="text-xl text-gray-600 leading-relaxed">
                      {product.description || (lang === 'zh' ? '专业级互动设备，采用先进技术为用户提供沉浸式体验解决方案。' : 'Professional interactive equipment using advanced technology to provide immersive experience solutions.')}
                    </p>
                  </div>

                  {/* Specifications */}
                  <div className="grid grid-cols-2 gap-6">
                    {specifications.map((spec, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center gap-2">
                          <spec.icon className="w-5 h-5 text-gray-700" />
                          <span className="font-medium text-gray-900">{spec.title}</span>
                        </div>
                        <p className="text-gray-600 text-sm">{spec.desc}</p>
                      </div>
                    ))}
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-4">
                    <div className="flex gap-3">
                      <Link
                        href={`/${lang}/pages/contact-us`}
                        className="flex-1 bg-gray-900 hover:bg-gray-800 text-white h-12 px-6 rounded-lg flex items-center justify-center transition-colors"
                      >
                        {dict.common?.get_quote || (lang === 'zh' ? '获取报价' : 'Get Quote')}
                      </Link>
                    </div>

                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        {dict.common?.in_stock || (lang === 'zh' ? '现货供应' : 'In Stock')}
                      </span>
                      <span>•</span>
                      <span>{dict.common?.professional_installation || (lang === 'zh' ? '专业安装' : 'Professional Installation')}</span>
                      <span>•</span>
                      <span>{dict.common?.three_year_warranty || (lang === 'zh' ? '质保3年' : '3-Year Warranty')}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Full Screen Gallery */}
        <div className="bg-white">
          <div className="text-center py-16">
            <h2 className="text-4xl lg:text-5xl font-light text-gray-900 mb-6">
              {dict.common?.product_gallery || (lang === 'zh' ? '产品展示' : 'Product Gallery')}
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              {lang === 'zh'
                ? `专业级${product.category || '互动设备'}在不同应用场景中的实际效果展示`
                : `Professional ${product.category || 'interactive equipment'} showcased in various application scenarios`}
            </p>
          </div>

          <div className="space-y-0">
            {imageItems.filter(Boolean).map((mediaItem: any, index: number) => (
              <div key={index} className="relative w-full h-screen bg-gray-100">
                {/* 只显示图片 */}
                <img
                  src={mediaItem?.src || "/images/products/placeholder.jpg"}
                  alt={`${product.title || product.name} - ${dict.common?.application_scenario || (lang === 'zh' ? '应用场景' : 'Application scenario')} ${index + 1}`}
                  className="w-full h-full object-cover"
                />
                
                {/* 图片描述覆盖层 */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-8">
                  <div className="container mx-auto">
                    <p className="text-white text-lg font-medium">
                      {product.title || product.name} - {dict.common?.application_scenario || (lang === 'zh' ? '应用场景' : 'Application Scenario')} {index + 1}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
