import { getDictionary } from '@/dictionaries/dictionaries';
import { Metadata } from 'next';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { Dictionary } from '@/types/dictionary';

// 产品骨架屏组件
function ProductSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="animate-pulse">
        {/* 面包屑骨架 */}
        <div className="flex items-center space-x-2 mb-8">
          <div className="h-4 w-16 bg-gray-200 rounded"></div>
          <div className="h-4 w-4 bg-gray-200 rounded"></div>
          <div className="h-4 w-20 bg-gray-200 rounded"></div>
          <div className="h-4 w-4 bg-gray-200 rounded"></div>
          <div className="h-4 w-24 bg-gray-200 rounded"></div>
        </div>

        {/* 产品信息骨架 */}
        <div className="flex flex-col md:flex-row gap-8">
          {/* 左侧图片骨架 */}
          <div className="w-full md:w-1/2">
            <div className="aspect-video bg-gray-200 rounded-lg mb-4"></div>
            <div className="grid grid-cols-4 gap-2">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="aspect-square bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>

          {/* 右侧信息骨架 */}
          <div className="w-full md:w-1/2">
            <div className="h-8 w-3/4 bg-gray-200 rounded mb-4"></div>
            <div className="h-6 w-1/4 bg-gray-200 rounded mb-6"></div>
            <div className="space-y-2 mb-6">
              <div className="h-4 w-full bg-gray-200 rounded"></div>
              <div className="h-4 w-full bg-gray-200 rounded"></div>
              <div className="h-4 w-3/4 bg-gray-200 rounded"></div>
            </div>
            <div className="h-12 w-1/3 bg-gray-200 rounded-full"></div>
          </div>
        </div>
      </div>
    </div>
  );
}

// 缩略图组件
function ThumbnailGallery() {
  // 模拟缩略图数据
  const thumbnails = [1, 2, 3, 4];

  return (
    <div className="flex flex-col space-y-3 w-full">
      {thumbnails.map((index) => (
        <div
          key={index}
          className="w-full h-16 border border-gray-200 bg-gray-50 flex items-center justify-center cursor-pointer hover:border-blue-400 transition-all hover:shadow-md overflow-hidden group"
        >
          <div className="relative w-full h-full">
            <div className="absolute inset-0 bg-gradient-to-b from-gray-100 to-gray-200 flex items-center justify-center">
              <div className="text-xs text-gray-500 font-medium group-hover:text-blue-500 transition-colors">
                缩略图 {index}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

// 大图展示组件
function LargeProductImage({ index }: { index: number }) {
  return (
    <div className="w-full aspect-video bg-black flex items-center justify-center text-white mb-8">
      <div className="text-xl">产品大图片 {index}</div>
    </div>
  );
}

// 维护页面组件 - 美化UI
function MaintenanceMessage({ lang, dict }: { lang: string, dict: Dictionary }) {
  return (
    <div className="container mx-auto px-4">
      {/* 产品预览区域 - 按照原型图布局 */}
      <div className="border border-gray-300 mb-8">
        <div className="flex flex-col md:flex-row">
          {/* 左侧缩略图列表 */}
          <div className="w-full md:w-1/4 p-4 border-r border-gray-300">
            <ThumbnailGallery />
          </div>

          {/* 右侧主图预览 */}
          <div className="w-full md:w-3/4 p-6 flex items-center justify-center bg-white">
            <div className="text-center max-w-lg py-8">
              <div className="mb-6 text-blue-500">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-24 w-24 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>

              <h1 className="text-2xl font-bold mb-4 text-gray-800">
                {lang === 'zh' ? '产品详情页面正在维护中' : 'Product Details Under Maintenance'}
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* 介绍区域 */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6 mb-8">
        <div className="flex-1">
          <h2 className="text-lg font-medium text-gray-800">介绍:</h2>
          <p className="text-gray-600 mt-3">
            {lang === 'zh'
              ? '我们正在升级产品详情页面，以提供更好的用户体验。请稍后再来查看，或联系我们了解更多产品信息。'
              : 'We are currently upgrading our product detail pages to provide a better user experience. Please check back later or contact us for more information about our products.'}
          </p>
        </div>

        <Link
          href={`/${lang}/pages/contact`}
          className="px-8 py-4 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-all whitespace-nowrap"
        >
          {lang === 'zh' ? '联系询价' : 'Contact for Inquiry'}
        </Link>
      </div>

      {/* 产品大图区域 */}
      <div className="mb-8">
        {[1, 2, 3, 4].map((index) => (
          <LargeProductImage key={index} index={index} />
        ))}
      </div>

      {/* 返回按钮 */}
      <div className="flex justify-center mb-8">
        <Link
          href={`/${lang}/products`}
          className="px-6 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
        >
          {dict.common?.back_to_products || (lang === 'zh' ? '返回产品列表' : 'Back to Products')}
        </Link>
      </div>
    </div>
  );
}

// 主产品页面组件的修改，采用现代风格设计
export default function ProductPage({ params }: { params: { slug: string, lang: string } }) {
  const { slug, lang } = params;

  // 状态
  const [dict, setDict] = useState<Dictionary>({});
  const [loading, setLoading] = useState(true);

  // 获取数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // 获取字典
        const dictionary = await getDictionary(lang);
        setDict(dictionary);

        setLoading(false);
      } catch (error) {
        console.error('加载数据失败:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, [lang]);

  if (loading) {
    return <ProductSkeleton />;
  }

  // 显示美化后的维护信息
  return (
    <div className="bg-white py-6">
      <div className="container mx-auto px-4">
        {/* 面包屑导航 - 美化UI */}
        <div className="flex items-center text-sm text-gray-500 mb-6">
          <Link href={`/${lang}`} className="hover:text-blue-600 transition-colors">
            {dict.common?.home || (lang === 'zh' ? '首页' : 'Home')}
          </Link>
          <span className="mx-2">/</span>
          <Link href={`/${lang}/products`} className="hover:text-blue-600 transition-colors">
            {dict.common?.products || (lang === 'zh' ? '产品' : 'Products')}
          </Link>
          <span className="mx-2">/</span>
          <span className="text-gray-700">
            {lang === 'zh' ? '产品详情' : 'Product Details'}
          </span>
        </div>

        {/* 新布局的维护信息 */}
        <MaintenanceMessage lang={lang} dict={dict} />
      </div>
    </div>
  );
}
