'use client';

// 产品列表页面
import { useState, useEffect, useRef } from 'react';
import { getDictionary } from '../../utils/i18n';
import { Dictionary } from '../../types/dictionary';
import { usePathname } from 'next/navigation';
import ProductGrid from '@/app/components/ProductGrid';
import QuoteForm from '@/app/components/QuoteForm';
import PageHeader from '@/app/components/PageHeader';
import ProductSectionCarousel from '@/app/components/ProductSectionCarousel';
import ProductSearch from '@/app/components/ProductSearch';
import Link from 'next/link';
import Head from 'next/head';

// 默认字典值 - 添加必要的键以避免未定义错误
const defaultDictionary: Dictionary = {
  products: {
    title: 'Products',
    all_products_title: 'All Products',
    all_products_subtitle: 'Browse our complete product series',
    description: 'We provide various innovative product solutions',
    filter: {
      all: 'All'
    }
  },
  common: {
    home: 'Home',
    products: 'Products',
    loading: 'Loading...',
    view_details: 'View Details'
  }
};

// 产品列表页面组件
export default function ProductsPage({ params }: { params: { lang: string } }) {
  // 状态和钩子
  const [dictionary, setDictionary] = useState<Dictionary>(defaultDictionary);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const pathname = usePathname();

  // 引用元素用于动画
  const separatorRef = useRef<HTMLDivElement>(null);
  const carouselRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);
  const quoteRef = useRef<HTMLDivElement>(null);

  // 滚动动画观察器
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, observerOptions);

    // 观察所有需要动画的元素
    const elementsToObserve = [
      separatorRef.current,
      carouselRef.current,
      gridRef.current,
      quoteRef.current
    ].filter(Boolean);

    elementsToObserve.forEach((el) => {
      if (el) observer.observe(el);
    });

    return () => observer.disconnect();
  }, [isLoading]);

  // 页面加载动画
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  // 加载词典
  useEffect(() => {
    let isMounted = true; // 防止组件卸载后更新状态

    const loadDictionary = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 获取字典，添加时间戳防止缓存问题
        const dict = await getDictionary(params.lang) as Dictionary;

        if (isMounted) {
          // 确保合并默认字典，以避免未定义的键
          setDictionary({
            ...defaultDictionary,
            ...dict,
            products: {
              ...defaultDictionary.products,
              ...(dict.products || {})
            },
            common: {
              ...defaultDictionary.common,
              ...(dict.common || {})
            }
          });
        }
      } catch (error) {
        console.error('Failed to load dictionary:', error);
        if (isMounted) {
          setError(params.lang === 'zh' ? '加载数据失败，使用默认内容' : 'Failed to load data, using default content');
          // 保留默认字典
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    loadDictionary();

    // 清理函数
    return () => {
      isMounted = false;
    };
  }, [params.lang]);

  // 正在加载时显示骨架屏
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 mt-20">
        <div className="animate-pulse">
          <div className="h-10 bg-gray-200 rounded w-3/4 mx-auto mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-gray-200 rounded-lg h-80"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // 显示错误信息（如果有）
  if (error) {
    console.warn('Using default dictionary due to:', error);
  }

  // 确保字典数据已定义
  const productsDict = dictionary.products || defaultDictionary.products || {
    all_products_title: params.lang === 'zh' ? '产品列表' : 'Product List',
    all_products_subtitle: params.lang === 'zh' ? '浏览我们的产品系列' : 'Browse our product series'
  };

  // 确保common数据已定义
  const commonDict = dictionary.common || defaultDictionary.common || {
    home: params.lang === 'zh' ? '首页' : 'Home',
    products: params.lang === 'zh' ? '产品' : 'Products',
  };

  return (
    <>
      <style jsx>{`
        .product-list-page {
          opacity: ${isVisible ? 1 : 0};
          transform: translateY(${isVisible ? '0' : '20px'});
          transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .animate-separator {
          opacity: 0;
          transform: scaleX(0);
          transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .animate-separator.animate-in {
          opacity: 1;
          transform: scaleX(1);
        }

        .animate-carousel {
          opacity: 0;
          transform: translateY(50px);
          transition: all 1.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .animate-carousel.animate-in {
          opacity: 1;
          transform: translateY(0);
        }

        .animate-grid {
          opacity: 0;
          transform: translateY(40px);
          transition: all 1s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
        }

        .animate-grid.animate-in {
          opacity: 1;
          transform: translateY(0);
        }

        .animate-quote {
          opacity: 0;
          transform: translateY(30px) scale(0.95);
          transition: all 1s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
        }

        .animate-quote.animate-in {
          opacity: 1;
          transform: translateY(0) scale(1);
        }

        .floating-particles {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
          overflow: hidden;
        }

        .particle {
          position: absolute;
          width: 4px;
          height: 4px;
          background: linear-gradient(45deg, #3b82f6, #8b5cf6);
          border-radius: 50%;
          animation: float 6s ease-in-out infinite;
          opacity: 0.6;
        }

        .particle:nth-child(1) { left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { left: 20%; animation-delay: 1s; }
        .particle:nth-child(3) { left: 30%; animation-delay: 2s; }
        .particle:nth-child(4) { left: 40%; animation-delay: 3s; }
        .particle:nth-child(5) { left: 50%; animation-delay: 4s; }
        .particle:nth-child(6) { left: 60%; animation-delay: 5s; }
        .particle:nth-child(7) { left: 70%; animation-delay: 0.5s; }
        .particle:nth-child(8) { left: 80%; animation-delay: 1.5s; }
        .particle:nth-child(9) { left: 90%; animation-delay: 2.5s; }

        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          25% { transform: translateY(-20px) rotate(90deg); }
          50% { transform: translateY(-40px) rotate(180deg); }
          75% { transform: translateY(-20px) rotate(270deg); }
        }

        .pulse-glow {
          animation: pulseGlow 3s ease-in-out infinite;
        }

        @keyframes pulseGlow {
          0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
          50% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.6); }
        }
      `}</style>

      <div className="product-list-page w-full overflow-hidden relative" style={{ marginTop: 0, paddingTop: 0 }}>
        {/* 浮动粒子背景 */}
        <div className="floating-particles">
          {[...Array(9)].map((_, i) => (
            <div key={i} className="particle" />
          ))}
        </div>

        {/* 使用增强的PageHeader组件 */}
        <PageHeader
          title={productsDict.all_products_title || (params.lang === 'zh' ? '产品列表' : 'Product List')}
          subtitle={productsDict.all_products_subtitle || (params.lang === 'zh' ? '浏览我们的产品系列' : 'Browse our product series')}
          bgImage="/images/products/product-banner.png"
          height="md:h-[520px] h-[420px]"
          overlayOpacity={0.6}
          useImageComponent={true}
          animationEffect="up"
          className="mb-12 mt-0 pt-0"
        />

        {/* 视觉分隔线 */}
        <div className="w-full flex justify-center">
          <div
            ref={separatorRef}
            className="animate-separator w-2/3 h-1 bg-gradient-to-r from-transparent via-blue-300 to-transparent rounded-full mb-10 mt-6 pulse-glow"
          ></div>
        </div>

        {/* 产品系列轮播展示 */}
        <div ref={carouselRef} className="animate-carousel">
          <ProductSectionCarousel />
        </div>

        {/* 产品搜索区域 */}
        <div className="w-full py-12 px-6 bg-gradient-to-br from-slate-50 via-blue-50/40 to-indigo-50/40 relative border-t border-b border-gray-100/50">
          {/* 背景装饰 */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-100/30 via-transparent to-indigo-100/30"></div>
          <div className="absolute top-0 left-1/3 w-72 h-72 bg-blue-200/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-1/3 w-72 h-72 bg-indigo-200/20 rounded-full blur-3xl"></div>

          <div className="relative z-10 max-w-6xl mx-auto">
            {/* 搜索标题 */}
            <div className="text-center mb-8">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-3">
                {dictionary.products?.search_title || (params.lang === 'zh' ? '找到您需要的产品' : 'Find the Product You Need')}
              </h2>
              <p className="text-gray-600 text-base md:text-lg max-w-2xl mx-auto">
                {dictionary.products?.search_subtitle || (params.lang === 'zh' ? '搜索我们的产品库，发现最适合您需求的解决方案' : 'Search our product catalog to discover the perfect solution for your needs')}
              </p>
            </div>

            <div className="flex flex-col lg:flex-row items-center gap-6 max-w-4xl mx-auto">
              {/* 搜索框容器 */}
              <div className="flex-1 w-full">
                <ProductSearch
                  placeholder={dictionary.products?.search_placeholder || (params.lang === 'zh' ? '搜索产品名称、型号或关键词...' : 'Search product name, model or keywords...')}
                  onSearch={(query) => {
                    console.log('Search:', query);
                    setSearchQuery(query);
                  }}
                />
              </div>
              
              {/* 视频展示入口按钮 */}
              <div className="flex-shrink-0">
                <Link
                  href={`/${params.lang}/videos`}
                  className="relative inline-flex items-center justify-center px-6 py-3 text-white font-semibold bg-blue-600 hover:bg-blue-700 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-105"
                  style={{
                    backgroundColor: '#2563eb',
                    color: '#ffffff',
                    border: '2px solid #1d4ed8',
                    minWidth: '180px'
                  }}
                >
                  {/* 播放图标 */}
                  <svg 
                    className="w-5 h-5 mr-2" 
                    fill="currentColor" 
                    viewBox="0 0 24 24"
                    style={{ color: '#ffffff' }}
                  >
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                  
                  {/* 文字 */}
                  <span style={{ color: '#ffffff' }}>
                    {params.lang === 'zh' ? '观看产品视频' : 'Watch Product Videos'}
                  </span>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* 产品网格区域 - 使用更大的容器宽度 */}
        <div ref={gridRef} className="animate-grid max-w-7xl mx-auto px-6 py-12 relative z-10">
          <ProductGrid searchQuery={searchQuery} />
        </div>

        {/* 使用唯一的id避免组件重复 */}
        <section className="w-full px-4 pb-16 mt-16">
          <div ref={quoteRef} className="animate-quote container mx-auto max-w-4xl">
            <QuoteForm className="products-page-quote-form" />
          </div>
        </section>
      </div>
    </>
  );
}
