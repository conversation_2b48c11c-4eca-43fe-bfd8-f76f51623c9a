'use client';

import { usePathname } from 'next/navigation';
import VideoGallery from '@/app/components/VideoGallery';
import PageHeader from '@/app/components/PageHeader';
import { useTranslation } from '@/app/utils/useTranslation';

interface VideoPageProps {
  params: { lang: string }
}

export default function VideoPage({ params }: VideoPageProps) {
  const { t } = useTranslation();
  
  const breadcrumbs = [
    { text: t('common.home', { fallback: '首页' }), href: `/${params.lang}` },
    { text: t('video_gallery.page_title', { fallback: '视频展示' }), href: `/${params.lang}/videos` }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page Header */}
      <PageHeader
        title={t('video_gallery.page_title', { fallback: '产品演示视频' })}
        subtitle={t('video_gallery.page_subtitle', { fallback: '通过视频深入了解我们的技术产品和解决方案' })}
        breadcrumbs={breadcrumbs}
        bgImage="/images/products/product-banner.png"
        height="md:h-[400px] h-[300px]"
        overlayOpacity={0.6}
        useImageComponent={true}
        animationEffect="up"
      />

      {/* Video Gallery */}
      <div className="py-16">
        <VideoGallery />
      </div>
    </div>
  );
}