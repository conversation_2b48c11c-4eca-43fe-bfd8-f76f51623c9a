import { NextRequest, NextResponse } from 'next/server';
// import { getServerSession } from 'next-auth/next';
// import { authOptions } from '../../auth/[...nextauth]/route';
// import { query } from '../../../lib/db';

export async function GET(request: NextRequest) {
  try {
    // 暂时禁用数据库检查，返回模拟数据
    console.log('[DATABASE-CHECK] 数据库功能已禁用，返回模拟数据');

    // 从JSON文件读取产品数据
    const mockProducts = await fetch(new URL('/mock-products.json', request.url))
      .then(res => res.json())
      .catch(() => []);

    return NextResponse.json({
      success: true,
      message: '数据库功能已禁用，使用JSON文件数据',
      data: {
        products: {
          total: mockProducts.length,
          items: mockProducts.slice(0, 10), // 只返回前10个
          source: 'JSO<PERSON>文件'
        },
        users: {
          total: 1,
          items: [
            {
              id: 1,
              username: 'admin',
              email: '<EMAIL>',
              role: 'admin',
              created_at: new Date().toISOString()
            }
          ]
        },
        tables: ['products (JSON)', 'users (模拟)'],
        timestamp: new Date().toISOString(),
        note: '当前使用JSON文件存储，数据库功能已禁用'
      }
    });

  } catch (error: unknown) {
    console.error('[DATABASE-CHECK] 检查时发生错误:', error);
    const message = error instanceof Error ? error.message : 'Unknown server error';
    return NextResponse.json({
      success: false,
      message: `Check failed: ${message}`,
      data: {
        products: { total: 0, items: [] },
        users: { total: 0, items: [] },
        tables: [],
        timestamp: new Date().toISOString()
      }
    }, { status: 500 });
  }
}
