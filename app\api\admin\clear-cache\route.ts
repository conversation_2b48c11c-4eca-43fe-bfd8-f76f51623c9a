import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route.js';

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      );
    }

    // 这里可以添加实际的缓存清理逻辑
    // 例如：清理 Redis 缓存、重新验证静态页面等

    // 模拟缓存清理过程
    await new Promise(resolve => setTimeout(resolve, 1000));

    return NextResponse.json(
      { message: '缓存清理成功' },
      { status: 200 }
    );
  } catch (error) {
    console.error('清理缓存时出错:', error);
    return NextResponse.json(
      { error: '缓存清理失败' },
      { status: 500 }
    );
  }
}
