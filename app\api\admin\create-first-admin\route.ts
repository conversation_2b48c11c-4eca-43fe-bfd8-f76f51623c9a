import { NextRequest, NextResponse } from 'next/server';
import sql from '@/lib/db';
import bcrypt from 'bcryptjs';

export async function POST(request: NextRequest) {
  try {
    // 检查是否已有管理员用户
    const adminUsers = await sql`
      SELECT id FROM users WHERE role IN ('admin', 'super_admin')
    `;

    if (adminUsers.length > 0) {
      return NextResponse.json(
        { success: false, message: 'Admin user already exists' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { username, email, password } = body;

    // 验证必填字段
    if (!username || !email || !password) {
      return NextResponse.json(
        { success: false, message: 'Username, email, and password are required' },
        { status: 400 }
      );
    }

    // 加密密码
    const salt = await bcrypt.genSalt(10);
    const passwordHash = await bcrypt.hash(password, salt);

    // 创建第一个超级管理员
    const result = await sql`
      INSERT INTO users (username, email, password_hash, role)
      VALUES (${username}, ${email}, ${passwordHash}, 'super_admin')
      RETURNING id, username, email, role, created_at
    `;

    if (result.length > 0) {
      return NextResponse.json({
        success: true,
        message: 'First admin user created successfully',
        data: result[0]
      }, { status: 201 });
    } else {
      return NextResponse.json({
        success: false,
        message: 'Failed to create admin user'
      }, { status: 400 });
    }
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // 检查是否已有管理员用户
    const adminUsers = await sql`
      SELECT id FROM users WHERE role IN ('admin', 'super_admin')
    `;

    const totalUsers = await sql`
      SELECT COUNT(*) as count FROM users
    `;

    return NextResponse.json({
      success: true,
      hasAdmin: adminUsers.length > 0,
      adminCount: adminUsers.length,
      totalUsers: totalUsers[0]?.count || 0
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}
