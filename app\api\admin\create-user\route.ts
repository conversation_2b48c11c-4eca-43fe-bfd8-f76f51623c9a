import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db.js';
import bcrypt from 'bcryptjs';

export async function POST(request: NextRequest) {
  try {
    console.log('[CREATE-ADMIN] 开始创建管理员用户...');
    
    // 检查是否已存在管理员用户
    const existingAdmin = await query('SELECT * FROM users WHERE username = $1', ['admin']);
    
    if (existingAdmin.rows.length > 0) {
      console.log('[CREATE-ADMIN] 管理员用户已存在');
      return NextResponse.json({
        success: true,
        message: '管理员用户已存在',
        user: {
          username: existingAdmin.rows[0].username,
          email: existingAdmin.rows[0].email,
          role: existingAdmin.rows[0].role,
          created_at: existingAdmin.rows[0].created_at
        }
      });
    }
    
    // 创建密码哈希
    const password = 'admin123';
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);
    
    console.log('[CREATE-ADMIN] 正在创建管理员用户...');
    
    // 插入管理员用户
    const result = await query(
      `INSERT INTO users (username, email, password_hash, name, role, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
       RETURNING id, username, email, role, created_at`,
      ['admin', '<EMAIL>', passwordHash, 'Administrator', 'admin']
    );
    
    if (result.rows.length > 0) {
      const user = result.rows[0];
      console.log('[CREATE-ADMIN] ✅ 管理员用户创建成功!');
      console.log('[CREATE-ADMIN] 用户ID:', user.id);
      console.log('[CREATE-ADMIN] 用户名:', user.username);
      console.log('[CREATE-ADMIN] 邮箱:', user.email);
      console.log('[CREATE-ADMIN] 角色:', user.role);
      
      return NextResponse.json({
        success: true,
        message: '管理员用户创建成功',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          created_at: user.created_at
        },
        credentials: {
          username: 'admin',
          password: 'admin123'
        }
      });
    } else {
      throw new Error('创建用户失败');
    }
    
  } catch (error: unknown) {
    console.error('[CREATE-ADMIN] 创建管理员用户时出错:', error);
    
    // 如果是表不存在的错误，先创建表
    if (error instanceof Error && error.message.includes('relation "users" does not exist')) {
      console.log('[CREATE-ADMIN] 用户表不存在，正在创建...');
      
      try {
        await query(`
          CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            name VARCHAR(100),
            role VARCHAR(20) DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        `);
        
        console.log('[CREATE-ADMIN] ✅ 用户表创建成功，重新尝试创建管理员用户...');
        
        // 重新调用自己
        return POST(request);
        
      } catch (createTableError) {
        console.error('[CREATE-ADMIN] 创建用户表失败:', createTableError);
        return NextResponse.json({
          success: false,
          message: `创建用户表失败: ${createTableError instanceof Error ? createTableError.message : String(createTableError)}`
        }, { status: 500 });
      }
    }
    
    const message = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({
      success: false,
      message: `创建管理员用户失败: ${message}`
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    console.log('[CREATE-ADMIN] 检查管理员用户状态...');
    
    // 检查用户表是否存在
    const tableCheck = await query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
      );
    `);
    
    if (!tableCheck.rows[0].exists) {
      return NextResponse.json({
        success: false,
        message: '用户表不存在',
        tableExists: false
      });
    }
    
    // 检查管理员用户
    const adminCheck = await query('SELECT username, email, role, created_at FROM users WHERE role = $1', ['admin']);
    
    return NextResponse.json({
      success: true,
      tableExists: true,
      adminExists: adminCheck.rows.length > 0,
      adminUsers: adminCheck.rows,
      totalUsers: await query('SELECT COUNT(*) as count FROM users').then(r => r.rows[0].count)
    });
    
  } catch (error: unknown) {
    console.error('[CREATE-ADMIN] 检查管理员用户状态时出错:', error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({
      success: false,
      message: `检查失败: ${message}`
    }, { status: 500 });
  }
}
