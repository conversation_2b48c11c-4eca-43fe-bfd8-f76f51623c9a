import { NextRequest, NextResponse } from 'next/server';
import { createAdminUser  } from '@/lib/db-admin';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证请求体
    if (!body.username || !body.email || !body.password) {
      return NextResponse.json(
        { success: false, message: 'Username, email and password are required' },
        { status: 400 }
      );
    }

    // 设置管理员用户数据
    const adminData = {
      username: body.username,
      email: body.email,
      password: body.password,
      is_super_admin: body.is_super_admin || false,
    };

    // 创建管理员用户
    const result = await createAdminUser(adminData);

    if (result.success) {
      return NextResponse.json(result, { status: 201 });
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}
