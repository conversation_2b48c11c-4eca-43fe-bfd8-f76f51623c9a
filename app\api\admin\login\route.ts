import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminLogin  } from '@/lib/db-admin';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证请求体
    if (!body.username || !body.password) {
      return NextResponse.json(
        { success: false, message: 'Username and password are required' },
        { status: 400 }
      );
    }

    // 验证登录
    const result = await verifyAdminLogin(body.username, body.password);

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: 401 });
    }
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}
