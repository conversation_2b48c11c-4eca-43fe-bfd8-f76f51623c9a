import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]/route';
import { query } from '@/lib/db.js';
import fs from 'fs';
import path from 'path';

// 添加从前端 mock 数据文件读取产品数据的函数
async function getMockProducts() {
  try {
    // 优先使用前端的 mock 数据文件
    const mockDataPath = path.join(process.cwd(), 'public', 'mock-products.json');
    if (fs.existsSync(mockDataPath)) {
      const data = fs.readFileSync(mockDataPath, 'utf8');
      const products = JSON.parse(data);
      console.log(`[ADMIN-PRODUCTS] 从前端 mock 数据文件读取到 ${products.length} 个产品`);
      return products;
    }

    // 备用：使用 app/data/products.json
    const dataPath = path.join(process.cwd(), 'app', 'data', 'products.json');
    if (fs.existsSync(dataPath)) {
      const data = fs.readFileSync(dataPath, 'utf8');
      const products = JSON.parse(data);
      console.log(`[ADMIN-PRODUCTS] 从备用数据文件读取到 ${products.length} 个产品`);
      return products;
    }

    console.log('[ADMIN-PRODUCTS] 未找到任何产品数据文件');
    return [];
  } catch (error) {
    console.error('[ADMIN-PRODUCTS] 读取产品数据错误:', error);
    return [];
  }
}

// 验证管理员权限的辅助函数
async function verifyAdminPermission(request: NextRequest) {
  // 1. 首先检查 NextAuth 会话
  const session = await getServerSession(authOptions);
  if (session?.user?.role && ['admin', 'super_admin'].includes(session.user.role)) {
    console.log('[AUTH] NextAuth 管理员验证通过:', session.user.name);
    return { success: true, user: session.user, source: 'nextauth' };
  }

  // 2. 检查本地会话 (从请求头中获取)
  const authorization = request.headers.get('authorization');
  if (authorization && authorization.startsWith('Bearer ')) {
    const token = authorization.substring(7);
    try {
      const sessionData = JSON.parse(decodeURIComponent(token));
      if (sessionData.user?.role && ['admin', 'super_admin'].includes(sessionData.user.role)) {
        // 检查会话是否过期
        if (new Date(sessionData.expires) > new Date()) {
          console.log('[AUTH] 本地会话管理员验证通过:', sessionData.user.name);
          return { success: true, user: sessionData.user, source: 'local' };
        }
      }
    } catch (error) {
      console.warn('[AUTH] 解析本地会话失败:', error);
    }
  }

  // 3. 检查 Cookie 中的本地会话
  const cookies = request.headers.get('cookie');
  if (cookies) {
    const adminSessionMatch = cookies.match(/admin_session=([^;]+)/);
    if (adminSessionMatch) {
      try {
        const sessionData = JSON.parse(decodeURIComponent(adminSessionMatch[1]));
        if (sessionData.user?.role && ['admin', 'super_admin'].includes(sessionData.user.role)) {
          if (new Date(sessionData.expires) > new Date()) {
            console.log('[AUTH] Cookie本地会话管理员验证通过:', sessionData.user.name);
            return { success: true, user: sessionData.user, source: 'cookie' };
          }
        }
      } catch (error) {
        console.warn('[AUTH] 解析Cookie会话失败:', error);
      }
    }
  }

  console.log('[AUTH] 管理员权限验证失败');
  return { success: false, user: null, source: null };
}

export async function GET(request: NextRequest) {
  try {
    // 检查管理员权限
    const authResult = await verifyAdminPermission(request);
    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized: Admin access required'
      }, { status: 403 });
    }

    console.log(`[ADMIN-PRODUCTS] 管理员权限验证通过 (${authResult.source}):`, authResult.user?.name);

    console.log('[ADMIN-PRODUCTS] 开始获取产品数据...');

    // 获取查询参数
    const url = new URL(request.url);
    const searchParams = url.searchParams;
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '50'; // 增加默认限制以显示更多产品
    const search = searchParams.get('search') || '';
    const published = searchParams.get('published') || '';

    const pageNumber = parseInt(page, 10);
    const limitNumber = parseInt(limit, 10);
    const offset = (pageNumber - 1) * limitNumber;

    // 优先使用前端 mock 数据
    console.log('[ADMIN-PRODUCTS] 尝试从前端 mock 数据获取产品...');
    const mockProducts = await getMockProducts();

    if (mockProducts && mockProducts.length > 0) {
      console.log(`[ADMIN-PRODUCTS] 成功获取到 ${mockProducts.length} 个 mock 产品`);

      // 转换 mock 产品数据为管理员界面格式
      let processedProducts = mockProducts.map((product, index) => ({
        id: product.id?.toString() || (index + 1).toString(),
        _id: product.id?.toString() || (index + 1).toString(),
        name: product.name || product.name_en || `产品 ${index + 1}`,
        title: product.name || product.name_en || `产品 ${index + 1}`,
        slug: product.slug || `product-${index + 1}`,
        description: product.description || product.description_en || '',
        type: product.type || 'interactive_equipment',
        size: product.size || '',
        style: product.style || product.style_en || '',
        image_url: product.images && product.images.length > 0 ? product.images[0] : '',
        images: product.images || [],
        features: product.features || product.features_en || [],
        isPublished: product.in_stock !== false,
        is_published: product.in_stock !== false,
        isFeatured: product.is_featured || false,
        is_featured: product.is_featured || false,
        price: product.price || 0,
        category: product.category || product.category_en || '',
        createdAt: product.created_at || new Date().toISOString(),
        created_at: product.created_at || new Date().toISOString(),
        updatedAt: product.updated_at || new Date().toISOString(),
        updated_at: product.updated_at || new Date().toISOString(),
      }));

      // 应用搜索过滤
      if (search) {
        processedProducts = processedProducts.filter(product =>
          product.name.toLowerCase().includes(search.toLowerCase()) ||
          product.description.toLowerCase().includes(search.toLowerCase())
        );
      }

      // 应用发布状态过滤
      if (published !== '') {
        const isPublishedFilter = published === 'true';
        processedProducts = processedProducts.filter(product => product.isPublished === isPublishedFilter);
      }

      // 计算分页
      const total = processedProducts.length;
      const startIndex = (pageNumber - 1) * limitNumber;
      const endIndex = startIndex + limitNumber;
      const paginatedProducts = processedProducts.slice(startIndex, endIndex);

      console.log(`[ADMIN-PRODUCTS] 返回 ${paginatedProducts.length} 个产品（共 ${total} 个），数据源: mock`);

      return NextResponse.json({
        success: true,
        products: paginatedProducts,
        pagination: {
          total: total,
          page: pageNumber,
          limit: limitNumber,
          pages: Math.ceil(total / limitNumber),
        },
        source: 'mock',
        timestamp: new Date().toISOString()
      });
    }

    // 如果没有 mock 数据，回退到数据库查询
    console.log('[ADMIN-PRODUCTS] 未找到 mock 数据，回退到数据库查询...');

    // 构建查询条件
    const whereConditions = [];
    const queryParams: (string | number | boolean | null)[] = [];
    let paramIndex = 1;

    // 搜索条件
    if (search) {
      whereConditions.push(`(name ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    // 发布状态条件
    if (published !== '') {
      whereConditions.push(`in_stock = $${paramIndex}`);
      queryParams.push(published === 'true');
      paramIndex++;
    }

    // 构建SQL查询 - 只查询实际存在的列
    let sql = `
      SELECT
        id,
        name,
        slug,
        description,
        type,
        size,
        style,
        image_url,
        images,
        features,
        in_stock,
        is_featured,
        created_at,
        updated_at
      FROM products
    `;

    if (whereConditions.length > 0) {
      sql += ' WHERE ' + whereConditions.join(' AND ');
    }

    // 添加排序和分页
    sql += ` ORDER BY is_featured DESC, id DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(limitNumber, offset);

    console.log('[ADMIN-PRODUCTS] 执行查询:', sql);
    console.log('[ADMIN-PRODUCTS] 查询参数:', queryParams);

    // 执行查询
    const result = await query(sql, queryParams);

    // 计算总数
    let countSql = 'SELECT COUNT(*) as total FROM products';
    if (whereConditions.length > 0) {
      countSql += ' WHERE ' + whereConditions.join(' AND ');
    }

    const countParams = queryParams.slice(0, paramIndex - 1) as (string | number | boolean | null)[];
    const countResult = await query(countSql, countParams);
    const total = parseInt(countResult.rows[0].total);

    // 转换为管理员界面格式
    const products = result.rows.map(row => {
      // 安全地解析JSON字段
      let images = [];
      let features = [];

      try {
        if (row.images) {
          if (typeof row.images === 'string') {
            images = JSON.parse(row.images);
          } else if (Array.isArray(row.images)) {
            images = row.images;
          }
        }
        if (!Array.isArray(images)) {
          images = row.image_url ? [row.image_url] : [];
        }
      } catch (e) {
        console.warn(`[ADMIN-PRODUCTS] 解析images字段失败:`, e.message);
        images = row.image_url ? [row.image_url] : [];
      }

      try {
        if (row.features) {
          if (typeof row.features === 'string') {
            features = JSON.parse(row.features);
          } else if (Array.isArray(row.features)) {
            features = row.features;
          }
        }
        if (!Array.isArray(features)) {
          features = [];
        }
      } catch (e) {
        console.warn(`[ADMIN-PRODUCTS] 解析features字段失败:`, e.message);
        features = [];
      }

      return {
        id: row.id.toString(),
        _id: row.id.toString(), // 兼容性字段
        name: row.name,
        title: row.name, // 兼容性字段
        slug: row.slug,
        description: row.description,
        type: row.type || '',
        size: row.size || '',
        style: row.style || '',
        image_url: row.image_url,
        images: images,
        features: features,
        isPublished: row.in_stock || false,
        is_published: row.in_stock || false, // 兼容性字段
        isFeatured: row.is_featured || false,
        is_featured: row.is_featured || false, // 兼容性字段
        price: row.price || 0,
        createdAt: row.created_at,
        created_at: row.created_at, // 兼容性字段
        updatedAt: row.updated_at,
        updated_at: row.updated_at, // 兼容性字段
      };
    });

    console.log(`[ADMIN-PRODUCTS] 最终返回 ${products.length} 个产品，数据源: database`);

    return NextResponse.json({
      success: true,
      products: products,
      pagination: {
        total: total,
        page: pageNumber,
        limit: limitNumber,
        pages: Math.ceil(total / limitNumber),
      },
      source: 'database',
      timestamp: new Date().toISOString()
    });

  } catch (error: unknown) {
    console.error('[ADMIN-PRODUCTS] 获取产品时发生错误:', error);
    const message = error instanceof Error ? error.message : 'Unknown server error';
    return NextResponse.json({
      success: false,
      message: `Failed to fetch products: ${message}`,
      products: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 12,
        pages: 0,
      }
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // 检查管理员权限
    const authResult = await verifyAdminPermission(request);
    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized: Admin access required'
      }, { status: 403 });
    }

    console.log(`[ADMIN-PRODUCTS] POST 管理员权限验证通过 (${authResult.source}):`, authResult.user?.name);

    // 获取请求体
    const data = await request.json();
    const {
      name,
      slug,
      description,
      size,
      style,
      type,
      features = [],
      images = [],
      isPublished = false,
      isFeatured = false,
      price = 0,
    } = data;

    // 验证必填字段
    if (!name || !slug || !description) {
      return NextResponse.json({
        success: false,
        message: 'Name, slug and description are required',
      }, { status: 400 });
    }

    console.log('[ADMIN-PRODUCTS] 创建新产品:', { name, slug });

    // 检查slug是否已存在
    const existingProduct = await query('SELECT id FROM products WHERE slug = $1', [slug]);

    if (existingProduct.rows.length > 0) {
      return NextResponse.json({
        success: false,
        message: `Product with slug "${slug}" already exists`,
      }, { status: 409 });
    }

    // 插入新产品
    const result = await query(
      `INSERT INTO products
        (name, slug, description, size, style, type, features, images, in_stock, is_featured, price)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING id`,
      [
        name,
        slug,
        description,
        size || null,
        style || null,
        type || null,
        JSON.stringify(features),
        JSON.stringify(images),
        isPublished,
        isFeatured,
        price,
      ]
    );

    console.log('[ADMIN-PRODUCTS] 产品创建成功, ID:', result.rows[0].id);

    return NextResponse.json({
      success: true,
      message: 'Product created successfully',
      productId: result.rows[0].id,
    });

  } catch (error: unknown) {
    console.error('[ADMIN-PRODUCTS] 创建产品时发生错误:', error);
    const message = error instanceof Error ? error.message : 'Unknown server error';
    return NextResponse.json({
      success: false,
      message: `Failed to create product: ${message}`,
    }, { status: 500 });
  }
}
