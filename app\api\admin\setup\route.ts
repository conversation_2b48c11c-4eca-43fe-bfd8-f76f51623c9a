import { NextResponse } from 'next/server';
import { query } from '@/lib/db.js';
import bcrypt from 'bcryptjs';

export async function GET() {
  try {
    console.log('开始创建用户表...');

    // 首先测试数据库连接
    try {
      await query('SELECT 1', []);
      console.log('数据库连接成功');
    } catch (connectionError) {
      console.error('数据库连接失败:', connectionError);
      return NextResponse.json({
        success: false,
        message: 'Database connection failed',
        error: String(connectionError)
      }, { status: 500 });
    }

    // 创建用户表
    const result = await query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(255) NOT NULL UNIQUE,
        email VARCHAR(255) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'editor', 'admin', 'super_admin')),
        is_active BOOLEAN DEFAULT true,
        last_login TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `, []);

    console.log('用户表创建成功');

    // 检查表是否存在
    const tableCheck = await query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public' AND table_name = 'users'
    `, []);

    // 检查是否已经有管理员用户
    const existingAdmin = await query(`
      SELECT id, username, email FROM users WHERE role = 'admin' LIMIT 1
    `, []);

    let adminUser = null;

    if (existingAdmin.rows.length === 0) {
      // 创建默认管理员用户
      console.log('创建默认管理员用户...');

      const adminData = {
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123', // 默认密码
        role: 'admin'
      };

      // 加密密码
      const saltRounds = 10;
      const passwordHash = await bcrypt.hash(adminData.password, saltRounds);

      // 插入管理员用户
      const newAdminResult = await query(
        `INSERT INTO users (username, email, password_hash, role, created_at, updated_at)
         VALUES ($1, $2, $3, $4, NOW(), NOW())
         RETURNING id, username, email, role`,
        [adminData.username, adminData.email, passwordHash, adminData.role]
      );

      adminUser = {
        ...newAdminResult.rows[0],
        defaultPassword: adminData.password,
        isNew: true
      };

      console.log('管理员用户创建成功:', adminUser.username);
    } else {
      adminUser = {
        ...existingAdmin.rows[0],
        isNew: false
      };
      console.log('管理员用户已存在:', adminUser.username);
    }

    return NextResponse.json({
      success: true,
      message: 'Setup completed successfully',
      data: {
        tableExists: tableCheck.rows.length > 0,
        adminUser: adminUser,
        loginUrl: '/zh/admin/login',
        note: adminUser.isNew ? 'Please change the default password after first login' : 'Admin user already exists'
      }
    }, { status: 200 });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}
