import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route.js';
import { getAllUsers, createUser } from '@/lib/db-admin';

export async function GET(request: NextRequest) {
  try {
    // 检查用户是否已认证且有管理员权限
    const session = await getServerSession(authOptions);
    
    if (!session || !['admin', 'super_admin'].includes(session.user?.role || '')) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const result = await getAllUsers();
    
    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: 500 });
    }
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // 检查用户是否已认证且有管理员权限
    const session = await getServerSession(authOptions);
    
    if (!session || !['admin', 'super_admin'].includes(session.user?.role || '')) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { username, email, password, role } = body;

    // 验证必填字段
    if (!username || !email || !password) {
      return NextResponse.json(
        { success: false, message: 'Username, email, and password are required' },
        { status: 400 }
      );
    }

    // 验证角色权限
    const validRoles = ['user', 'editor', 'admin', 'super_admin'];
    if (role && !validRoles.includes(role)) {
      return NextResponse.json(
        { success: false, message: 'Invalid role' },
        { status: 400 }
      );
    }

    // 只有超级管理员可以创建管理员账户
    if (role === 'admin' || role === 'super_admin') {
      if (session.user?.role !== 'super_admin') {
        return NextResponse.json(
          { success: false, message: 'Only super admin can create admin accounts' },
          { status: 403 }
        );
      }
    }

    const result = await createUser({
      username,
      email,
      password,
      role: role || 'user'
    });
    
    if (result.success) {
      return NextResponse.json(result, { status: 201 });
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}
