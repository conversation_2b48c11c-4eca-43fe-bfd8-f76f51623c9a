import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const error = searchParams.get('error');

  let errorMessage = '认证错误';
  let statusCode = 401;

  switch (error) {
    case 'CredentialsSignin':
      errorMessage = '用户名或密码错误';
      break;
    case 'SessionRequired':
      errorMessage = '需要登录后才能访问';
      statusCode = 403;
      break;
    case 'Configuration':
      errorMessage = '认证系统配置错误';
      statusCode = 500;
      break;
    case 'AccessDenied':
      errorMessage = '无权访问此资源';
      statusCode = 403;
      break;
    case 'Verification':
      errorMessage = '验证失败';
      break;
    case 'Default':
    default:
      errorMessage = '发生未知认证错误';
      break;
  }

  return NextResponse.json(
    {
      success: false,
      error: error || 'unknown',
      message: errorMessage,
    },
    { status: statusCode }
  );
}
