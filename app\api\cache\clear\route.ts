import { NextRequest, NextResponse } from 'next/server';
import { clearAllCaches, clearMemoryCache, clearClientCache, clearNextCache } from '@/lib/cache-cleaner';
import { memoryCache } from '@/lib/cache';
import { pool } from '@/lib/db';

/**
 * 清除缓存API端点
 * 
 * 使用示例:
 * GET /api/cache/clear - 清除所有缓存
 * GET /api/cache/clear?type=memory - 只清除内存缓存
 * GET /api/cache/clear?type=client - 只清除客户端缓存
 * GET /api/cache/clear?type=next - 只清除Next.js数据缓存
 */
export async function GET(request: Request) {
  try {
    // 获取缓存类型参数
    const url = new URL(request.url);
    const cacheType = url.searchParams.get('type');
    
    let result;
    
    // 根据类型清除不同的缓存
    switch (cacheType) {
      case 'memory':
        result = clearMemoryCache();
        break;
      case 'client':
        result = clearClientCache();
        break;
      case 'next':
        result = await clearNextCache();
        break;
      default:
        // 默认清除所有缓存
        result = await clearAllCaches();
    }
    
    // 清除内存缓存
    if (memoryCache && typeof memoryCache.clear === 'function') {
      memoryCache.clear();
      
      // 重点清除产品相关缓存
      const productCacheKeys = memoryCache.keys ? memoryCache.keys().filter(key => key.includes('product')) : [];
      if (productCacheKeys.length > 0) {
        console.log('[API] 清除产品相关缓存:', productCacheKeys.length, '项');
        productCacheKeys.forEach(key => memoryCache.delete(key));
      }
    }

    // 尝试清除webpack模块缓存（仅在开发环境有效）
    if (process.env.NODE_ENV === 'development') {
      // 尝试清除字典模块的缓存
      let clearedModules = 0;
      Object.keys(require.cache).forEach(key => {
        if (
          key.includes('dictionaries') || 
          key.includes('dictionary') || 
          key.includes('i18n') ||
          key.includes('webpack') && key.includes('json') ||
          key.includes('products') ||
          key.includes('data')
        ) {
          delete require.cache[key];
          clearedModules++;
        }
      });

      // 记录清除的缓存
      console.log(`[API] 已清除${clearedModules}个模块缓存`);
    }
    
    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      ...result
    });
  } catch (error) {
    console.error('[API] 清除缓存时出错:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * 清除服务器缓存和重置数据库连接池的API
 */
export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'clear-cache';
    
    // 记录操作
    console.log(`[清理操作] ${action} 被请求`);
    
    const results = {
      cache: false,
      database: false,
      timestamp: new Date().toISOString()
    };
    
    // 清除缓存
    if (action === 'clear-cache' || action === 'clear-all') {
      if (memoryCache) {
        try {
          memoryCache.clear();
          
          // 获取清理后的缓存状态
          results.cache = true;
          console.log('[清理操作] 内存缓存已清除');
        } catch (error) {
          console.error('[清理操作] 清除内存缓存错误:', error);
        }
      }
    }
    
    // 重置数据库连接池
    if (action === 'reset-db' || action === 'clear-all') {
      try {
        if (pool) {
          // 等待所有查询完成，然后结束连接池
          await pool.end();
          console.log('[清理操作] 数据库连接池已关闭');
          
          // 创建新的连接池
          // 在生产环境中，这可能并不总是可行，可能需要重启应用
          // 在当前实现中，新的连接将通过模块导入时的初始化代码创建
          results.database = true;
        }
      } catch (error) {
        console.error('[清理操作] 重置数据库连接池错误:', error);
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `清理操作 ${action} 已完成`,
      results
    });
    
  } catch (error) {
    console.error('[清理操作] 错误:', error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
} 