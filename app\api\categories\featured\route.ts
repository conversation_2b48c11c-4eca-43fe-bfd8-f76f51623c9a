import { NextRequest, NextResponse } from 'next/server';

// 使用require以避免类型检查问题
const db = require('../../../../lib/db.js');

// 模拟的特色分类，当数据库连接失败时使用
const mockFeaturedCategories = [
  {
    _id: '3',
    name: '互动投影系列',
    slug: 'interactive-equipment',
    description: '互动投影游乐设备',
    isActive: true,
    type: 'product_type',
    order: 1,
    featuredOrder: 1,
    featuredType: 'product',
    translations: {
      zh: { name: '互动投影系列' },
      en: { name: 'Interactive Equipment' },
    },
  },
];

export async function GET(request: NextRequest) {
  try {
    // 获取查询参数，允许按导航类型进行过滤
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    // 构建查询条件
    let sql = 'SELECT * FROM categories WHERE is_featured = true';
    const params: string[] = [];

    // 如果指定了导航类型，添加过滤条件
    if (type) {
      sql += ' AND featured_type = $1';
      params.push(type);
    }

    // 添加排序
    sql += ' ORDER BY featured_order ASC, order_num ASC';

    // 尝试执行查询
    const featuredResult = await db.query(sql, params);

    // 转换响应格式以兼容前端
    const featuredCategories = featuredResult.rows.map((row: any) => ({
      _id: row.id.toString(),
      name: row.name,
      slug: row.slug,
      description: row.description || '',
      isActive: row.is_active,
      type: row.type || 'product_type',
      order: row.order_num || 0,
      featuredOrder: row.featured_order || 0,
      featuredType: row.featured_type || 'product',
      translations: row.translations || {
        zh: { name: row.name },
        en: { name: row.name_en || row.name },
      },
    }));

    return NextResponse.json({
      success: true,
      data: featuredCategories,
      count: featuredCategories.length,
    });
  } catch (error: unknown) {
    console.error('Error fetching featured categories:', error);

    // 数据库连接失败时使用模拟数据
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    let filteredMock = mockFeaturedCategories;

    if (type) {
      filteredMock = mockFeaturedCategories.filter(cat => cat.featuredType === type);
    }

    console.log('Using mock data due to database connection failure');

    return NextResponse.json({
      success: true,
      data: filteredMock,
      count: filteredMock.length,
      isMock: true,
      message:
        'Using mock data due to database connection issues. Please check your database connection.',
    });
  }
}
