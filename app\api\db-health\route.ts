import { NextResponse } from 'next/server';
import db from '@/lib/db.js';

const { getHealthStatus } = db;

/**
 * 数据库健康状态检查API
 * 
 * 使用示例:
 * GET /api/db-health
 * 
 * 返回数据库连接池的健康状态信息
 */
export async function GET() {
  try {
    const healthStatus = await getHealthStatus();
    
    return NextResponse.json({
      success: true,
      ...healthStatus,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV
    });
  } catch (error) {
    console.error('[API] 获取数据库健康状态失败:', error);
    
    return NextResponse.json({
      success: false,
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * HEAD请求处理，用于简单检查数据库连接是否可用
 * 返回200表示健康，503表示不健康
 */
export async function HEAD() {
  try {
    const healthStatus = await getHealthStatus();
    
    if (healthStatus.status === 'healthy') {
      return new Response(null, { status: 200 });
    } else {
      return new Response(null, { status: 503 });
    }
  } catch (error) {
    return new Response(null, { status: 503 });
  }
} 