import { NextResponse } from 'next/server';
import { initializeDatabase } from '@/lib/db.js';

export async function GET() {
  try {
    await initializeDatabase();
    return NextResponse.json({
      success: true,
      message: '数据库初始化成功',
    });
  } catch (error: unknown) {
    console.error('数据库初始化失败:', error);
    let errorMessage = 'An unknown error occurred during database initialization.';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    return NextResponse.json(
      {
        success: false,
        message: '数据库初始化失败',
        error: errorMessage,
      },
      { status: 500 }
    );
  }
}
