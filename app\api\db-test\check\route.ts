import { NextRequest, NextResponse } from 'next/server';

// 检查数据库连接的简单端点
export async function GET(request: NextRequest) {
  // 此函数从pages/api路由自动转换，可能需要手动调整处理其他HTTP方法
  const { method } = request;

  if (method !== 'GET') {
    return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
  }

  // 获取环境变量
  const databaseUrl = process.env.DATABASE_URL;

  return NextResponse.json({
    environment: process.env.NODE_ENV,
    database: {
      url: databaseUrl ? databaseUrl.replace(/:([^:@]+)@/, ':***@') : 'Not set', // 隐藏密码部分
      type: 'PostgreSQL (Neon)',
    },
    nextauth: {
      url: process.env.NEXTAUTH_URL || 'Not set',
      secret: process.env.NEXTAUTH_SECRET ? '***' : 'Not set', // 不显示实际密钥
    },
    env_vars: Object.keys(process.env).filter(
      key => key.startsWith('DATABASE_') || key.startsWith('NEXTAUTH_') || key.startsWith('POSTGRES_')
    ),
  });
}
