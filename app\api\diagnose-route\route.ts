import { NextRequest, NextResponse } from 'next/server';
import { existsSync, readFileSync } from 'fs';
import path from 'path';

export async function GET(request: NextRequest) {
  try {
    // 1. 检查客户端发送的请求信息
    const userAgent = request.headers.get('user-agent') || 'Unknown';
    const requestUrl = request.url;
    const referer = request.headers.get('referer') || 'None';
    
    // 2. 检查产品详情页文件是否存在
    const productPagePath = path.join(process.cwd(), 'app', '[lang]', 'products', '[slug]', 'page.tsx');
    const productPageExists = existsSync(productPagePath);
    
    let productPageContent = '';
    if (productPageExists) {
      try {
        productPageContent = readFileSync(productPagePath, 'utf8').slice(0, 500) + '... (truncated)';
      } catch (err: any) {
        productPageContent = `Error reading file: ${err.message}`;
      }
    }
    
    // 3. 分析请求URL结构
    const url = new URL(requestUrl);
    const pathSegments = url.pathname.split('/').filter(Boolean);
    
    // 4. 测试导航链接
    const navigationTest = {
      productListLink: `${url.origin}/zh/products`,
      productDetailLinkTest: `${url.origin}/zh/products/ar-trampoline`,
    };
    
    // 5. 返回诊断信息
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      requestInfo: {
        url: requestUrl,
        userAgent,
        referer,
        pathSegments,
      },
      fileSystem: {
        productPageExists,
        productPagePreview: productPageContent,
      },
      navigationTest,
      nextjsInfo: {
        nodeEnv: process.env.NODE_ENV,
        nextVersion: process.env.NEXT_RUNTIME || 'Unknown',
      }
    });
  } catch (error: any) {
    return NextResponse.json({
      error: error.message || 'Unknown error',
      stack: error.stack || ''
    }, { status: 500 });
  }
} 