import { NextRequest, NextResponse } from 'next/server';
const { memoryCache } = require('@/lib/cache');

export async function POST(request: NextRequest) {
  try {
    console.log('[API] 开始修复产品路由...');

    // 清除内存缓存
    if (memoryCache && typeof memoryCache.clear === 'function') {
      memoryCache.clear();
      console.log('[API] 已清除内存缓存');
    }

    // 检查数据库连接
    let dbStatus = 'unknown';
    try {
      // 动态导入数据库模块
      const db = require('@/lib/db.js');

      // 尝试执行简单查询来测试连接
      if (typeof db.query === 'function') {
        const result = await db.query('SELECT 1 as test', [], { retries: 0, mockOnFailure: false });
        if (result && result.rows && result.rows[0] && result.rows[0].test === 1) {
          dbStatus = 'connected';
        } else {
          dbStatus = 'error-query';
        }
      } else {
        dbStatus = 'error-no-query';
      }
    } catch (dbError: any) {
      console.error('[API] 数据库连接检查失败:', dbError);
      dbStatus = `error-${dbError.code || 'unknown'}`;
    }

    // 修复模拟数据
    let mockStatus = 'unknown';
    try {
      const db = require('@/lib/db.js');

      if (db.mockData && Array.isArray(db.mockData.products)) {
        // 确保所有产品都有必要的字段
        db.mockData.products = db.mockData.products.map((product: any) => {
          return {
            id: product.id || Math.floor(Math.random() * 1000),
            name: product.name || '未命名产品',
            slug: product.slug || `product-${Math.floor(Math.random() * 1000)}`,
            description: product.description || '产品描述暂时不可用',
            category: product.category || '未分类',
            image_url: product.image_url || null,
            is_featured: typeof product.is_featured === 'boolean' ? product.is_featured : false,
            is_published: typeof product.is_published === 'boolean' ? product.is_published : true,
            created_at: product.created_at || new Date().toISOString(),
            updated_at: product.updated_at || new Date().toISOString()
          };
        });

        console.log(`[API] 已修复${db.mockData.products.length}个模拟产品数据`);
        mockStatus = 'fixed';
      } else {
        mockStatus = 'error-no-mockdata';
      }
    } catch (mockError: any) {
      console.error('[API] 修复模拟数据失败:', mockError);
      mockStatus = `error-${mockError.message || 'unknown'}`;
    }

    // 返回修复结果
    return NextResponse.json({
      success: true,
      message: '产品路由修复完成',
      fixes: {
        cache: 'cleared',
        database: dbStatus,
        mockData: mockStatus
      },
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    console.error('[API] 修复产品路由时出错:', error);

    return NextResponse.json({
      success: false,
      error: error.message || '修复产品路由时发生未知错误',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}