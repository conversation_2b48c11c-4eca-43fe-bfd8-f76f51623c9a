import { NextRequest, NextResponse } from 'next/server';
import { query } from '../../../../lib/db';

// 获取单个表单提交
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    const result = await query(`
      SELECT 
        id, name, email, phone, country, playground_size, product, message,
        status, is_read, admin_notes, created_at, updated_at
      FROM form_submissions 
      WHERE id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Form submission not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Error fetching form submission:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch form submission' },
      { status: 500 }
    );
  }
}

// 更新表单提交状态
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const body = await request.json();
    const { status, is_read, admin_notes } = body;

    // 构建更新字段
    let updateFields = [];
    let queryParams = [];
    let paramIndex = 1;

    if (status !== undefined) {
      updateFields.push(`status = $${paramIndex}`);
      queryParams.push(status);
      paramIndex++;
    }

    if (is_read !== undefined) {
      updateFields.push(`is_read = $${paramIndex}`);
      queryParams.push(is_read);
      paramIndex++;
    }

    if (admin_notes !== undefined) {
      updateFields.push(`admin_notes = $${paramIndex}`);
      queryParams.push(admin_notes);
      paramIndex++;
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, message: 'No fields to update' },
        { status: 400 }
      );
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
    queryParams.push(id);

    const result = await query(`
      UPDATE form_submissions 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING id, status, is_read, admin_notes, updated_at
    `, queryParams);

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Form submission not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Form submission updated successfully',
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Error updating form submission:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update form submission' },
      { status: 500 }
    );
  }
}

// 删除表单提交
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    const result = await query(`
      DELETE FROM form_submissions 
      WHERE id = $1
      RETURNING id
    `, [id]);

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Form submission not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Form submission deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting form submission:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete form submission' },
      { status: 500 }
    );
  }
}
