import { NextRequest, NextResponse } from 'next/server';
import { getHealthStatus, testConnection } from '@/lib/db.js';

export async function GET(request: NextRequest) {
  try {
    console.log('[Health Check] 开始数据库健康检查...');
    
    // 获取基本健康状态
    const healthStatus = await getHealthStatus();
    
    // 执行连接测试
    const connectionTest = await testConnection();
    
    // 获取当前时间戳
    const timestamp = new Date().toISOString();
    
    // 构建响应数据
    const response = {
      status: healthStatus.status === 'healthy' && connectionTest ? 'healthy' : 'unhealthy',
      timestamp,
      database: {
        connection: connectionTest ? 'connected' : 'disconnected',
        healthStatus: healthStatus.status,
        lastError: healthStatus.lastError || null,
        timeSinceLastError: healthStatus.timeSinceLastError || null,
      },
      checks: {
        connectionTest: connectionTest ? 'passed' : 'failed',
        healthCheck: healthStatus.status === 'healthy' ? 'passed' : 'failed',
      }
    };
    
    console.log('[Health Check] 数据库健康检查完成:', response.status);
    
    // 根据健康状态返回适当的HTTP状态码
    const httpStatus = response.status === 'healthy' ? 200 : 503;
    
    return NextResponse.json(response, { status: httpStatus });
    
  } catch (error: unknown) {
    console.error('[Health Check] 健康检查失败:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: errorMessage,
      database: {
        connection: 'error',
        healthStatus: 'unhealthy',
      },
      checks: {
        connectionTest: 'failed',
        healthCheck: 'failed',
      }
    }, { status: 503 });
  }
}

// 支持POST请求进行强制健康检查
export async function POST(request: NextRequest) {
  try {
    console.log('[Health Check] 执行强制数据库健康检查...');
    
    // 强制执行连接测试
    const connectionTest = await testConnection();
    
    // 获取详细健康状态
    const healthStatus = await getHealthStatus();
    
    const timestamp = new Date().toISOString();
    
    const response = {
      status: connectionTest ? 'healthy' : 'unhealthy',
      timestamp,
      forced: true,
      database: {
        connection: connectionTest ? 'connected' : 'disconnected',
        healthStatus: healthStatus.status,
        lastError: healthStatus.lastError || null,
        timeSinceLastError: healthStatus.timeSinceLastError || null,
      },
      checks: {
        connectionTest: connectionTest ? 'passed' : 'failed',
        healthCheck: healthStatus.status === 'healthy' ? 'passed' : 'failed',
      }
    };
    
    console.log('[Health Check] 强制健康检查完成:', response.status);
    
    const httpStatus = response.status === 'healthy' ? 200 : 503;
    
    return NextResponse.json(response, { status: httpStatus });
    
  } catch (error: unknown) {
    console.error('[Health Check] 强制健康检查失败:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      forced: true,
      error: errorMessage,
      database: {
        connection: 'error',
        healthStatus: 'unhealthy',
      },
      checks: {
        connectionTest: 'failed',
        healthCheck: 'failed',
      }
    }, { status: 503 });
  }
}
