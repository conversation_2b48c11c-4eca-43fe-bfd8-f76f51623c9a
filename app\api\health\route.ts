import { NextRequest, NextResponse } from 'next/server';
import { getHealthStatus, slowQueries, dbStats } from '@/lib/db';
import { memoryCache } from '@/lib/cache';
import os from 'os';

/**
 * 健康检查和性能诊断API
 * 提供系统性能指标、数据库状态和缓存统计
 */
export async function GET(request: NextRequest) {
  try {
    // 限制只有认证用户或本地请求访问此API
    
    // 获取系统信息
    const systemInfo = {
      platform: os.platform(),
      arch: os.arch(),
      cpus: os.cpus().length,
      freemem: os.freemem(),
      totalmem: os.totalmem(),
      uptime: os.uptime(),
      loadavg: os.loadavg(),
    };
    
    // 获取数据库健康状态
    const dbHealth = await getHealthStatus();
    
    // 获取数据库性能统计
    const dbPerformance = {
      ...dbStats,
      // 只显示最近的5条慢查询
      recentSlowQueries: slowQueries.slice(-5).map(q => ({
        duration: q.duration,
        timestamp: q.timestamp,
        query: q.query.substring(0, 100) + (q.query.length > 100 ? '...' : '')
      }))
    };
    
    // 获取缓存统计
    const cacheStats = memoryCache.getStats ? memoryCache.getStats() : {
      size: memoryCache.size(),
      keys: memoryCache.keys().length
    };
    
    // 组合所有数据
    const healthData = {
      status: dbHealth.status === 'healthy' ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      system: systemInfo,
      database: {
        health: dbHealth,
        performance: dbPerformance
      },
      cache: cacheStats
    };
    
    return NextResponse.json(healthData);
  } catch (error) {
    console.error('Health check error:', error);
    return NextResponse.json({
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// 数据库健康检查
async function checkDatabase() {
  try {
    // 这里可以添加实际的数据库连接检查
    // 目前使用模拟数据，所以总是返回健康状态
    return {
      status: 'healthy',
      responseTime: Math.random() * 50 + 10, // 模拟响应时间
      message: 'Database connection successful'
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      message: error instanceof Error ? error.message : 'Database connection failed'
    };
  }
}

// Redis健康检查
async function checkRedis() {
  try {
    // 这里可以添加实际的Redis连接检查
    return {
      status: 'healthy',
      responseTime: Math.random() * 20 + 5, // 模拟响应时间
      message: 'Redis connection successful'
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      message: error instanceof Error ? error.message : 'Redis connection failed'
    };
  }
}

// 文件系统健康检查
async function checkFilesystem() {
  try {
    // 检查关键目录是否可访问
    const fs = require('fs').promises;
    await fs.access('./public');
    
    return {
      status: 'healthy',
      message: 'Filesystem accessible'
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'Filesystem access failed'
    };
  }
}
