import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// 导航数据接口
interface SubMenuItem {
  _id: string;
  name: string;
  slug: string;
  url: string;
  order: number;
  translations: {
    zh?: { name: string };
    en?: { name: string };
  };
}

interface MainNavItem {
  _id: string;
  name: string;
  slug: string;
  order: number;
  isActive: boolean;
  subItems: SubMenuItem[];
  translations: {
    zh?: { name: string };
    en?: { name: string };
  };
}

// 数据文件路径
const DATA_FILE_PATH = path.join(process.cwd(), 'data', 'navigation.json');

// 确保数据目录存在
function ensureDataDirectory() {
  const dataDir = path.dirname(DATA_FILE_PATH);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// 从文件读取导航数据
function loadNavigationData(): MainNavItem[] {
  try {
    ensureDataDirectory();

    if (fs.existsSync(DATA_FILE_PATH)) {
      const fileContent = fs.readFileSync(DATA_FILE_PATH, 'utf-8');
      return JSON.parse(fileContent);
    }
  } catch (error) {
    console.error('Error loading navigation data from file:', error);
  }

  // 如果文件不存在或读取失败，返回默认数据
  return getDefaultNavigationData();
}

// 保存导航数据到文件
function saveNavigationData(data: MainNavItem[]): void {
  try {
    ensureDataDirectory();
    fs.writeFileSync(DATA_FILE_PATH, JSON.stringify(data, null, 2), 'utf-8');
    console.log('Navigation data saved to file successfully');
  } catch (error) {
    console.error('Error saving navigation data to file:', error);
    throw error;
  }
}

// 获取默认导航数据
function getDefaultNavigationData(): MainNavItem[] {
  return [
    {
      _id: '1',
      name: '产品',
      slug: 'products',
      order: 1,
      isActive: true,
      translations: {
        zh: { name: '产品' },
        en: { name: 'Products' },
      },
      subItems: [
        {
          _id: '1-1',
          name: '室内游乐场',
          slug: 'indoor-playground',
          url: '/products/indoor-playground',
          order: 1,
          translations: {
            zh: { name: '室内游乐场' },
            en: { name: 'Indoor Playground' },
          },
        },
        {
          _id: '1-2',
          name: '蹦床公园',
          slug: 'trampoline-park',
          url: '/products/trampoline-park',
          order: 2,
          translations: {
            zh: { name: '蹦床公园' },
            en: { name: 'Trampoline Park' },
          },
        },
        {
          _id: '1-3',
          name: '互动设备',
          slug: 'interactive-equipment',
          url: '/products/interactive-equipment',
          order: 3,
          translations: {
            zh: { name: '互动设备' },
            en: { name: 'Interactive Equipment' },
          },
        },
        {
          _id: '1-4',
          name: '互动投影系列',
          slug: 'interactive-projection-series',
          url: '/sections/interactive-projection-series',
          order: 4,
          translations: {
            zh: { name: '互动投影系列' },
            en: { name: 'Interactive Projection Series' },
          },
        },
      ],
    },
    {
      _id: '2',
      name: '服务',
      slug: 'services',
      order: 2,
      isActive: false, // 设置为不活跃，前端不显示
      translations: {
        zh: { name: '服务' },
        en: { name: 'Services' },
      },
      subItems: [
        {
          _id: '2-1',
          name: '设计咨询',
          slug: 'design-consultation',
          url: '/services/design-consultation',
          order: 1,
          translations: {
            zh: { name: '设计咨询' },
            en: { name: 'Design Consultation' },
          },
        },
        {
          _id: '2-2',
          name: '安装服务',
          slug: 'installation-service',
          url: '/services/installation-service',
          order: 2,
          translations: {
            zh: { name: '安装服务' },
            en: { name: 'Installation Service' },
          },
        },
        {
          _id: '2-3',
          name: '维护保养',
          slug: 'maintenance',
          url: '/services/maintenance',
          order: 3,
          translations: {
            zh: { name: '维护保养' },
            en: { name: 'Maintenance' },
          },
        },
      ],
    },
    {
      _id: '3',
      name: '解决方案',
      slug: 'solutions',
      order: 3,
      isActive: true,
      translations: {
        zh: { name: '解决方案' },
        en: { name: 'Solutions' },
      },
      subItems: [
        {
          _id: '3-1',
          name: '项目定制指南',
          slug: 'project-customization-guide',
          url: '/pages/custom-solutions',
          order: 1,
          translations: {
            zh: { name: '项目定制指南' },
            en: { name: 'Project Customization Guide' },
          },
        },
        {
          _id: '3-2',
          name: '全息项目购买指南',
          slug: 'holographic-project-guide',
          url: '/pages/how-to-purchase-your-first-holographic-system',
          order: 2,
          translations: {
            zh: { name: '全息项目购买指南' },
            en: { name: 'Holographic Project Purchase Guide' },
          },
        },
      ],
    },
  ];
}

// GET - 获取导航数据
export async function GET(request: NextRequest) {
  try {
    // 从文件加载导航数据
    const navigationData = loadNavigationData();
    const sortedData = navigationData.sort((a, b) => a.order - b.order);

    // 对每个主导航的子项也进行排序
    sortedData.forEach(item => {
      item.subItems.sort((a, b) => a.order - b.order);
    });

    return NextResponse.json({
      success: true,
      data: sortedData,
      count: sortedData.length,
    });
  } catch (error: unknown) {
    console.error('Error fetching navigation data:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch navigation data',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// PUT - 更新导航数据
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { navigationData } = body;

    if (!navigationData || !Array.isArray(navigationData)) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid navigation data format',
        },
        { status: 400 }
      );
    }

    // 验证数据结构
    for (const item of navigationData) {
      if (!item._id || !item.name || !item.slug) {
        return NextResponse.json(
          {
            success: false,
            message: 'Invalid navigation item structure',
          },
          { status: 400 }
        );
      }
    }

    // 保存数据到文件
    saveNavigationData(navigationData);

    console.log('Navigation data updated successfully');

    return NextResponse.json({
      success: true,
      message: 'Navigation data updated successfully',
      data: navigationData,
    });
  } catch (error: unknown) {
    console.error('Error updating navigation data:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to update navigation data',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST - 添加新的导航项
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, parentId, itemData } = body;

    // 从文件加载当前数据
    const navigationData = loadNavigationData();

    if (type === 'main') {
      // 添加主导航项
      const newMainItem: MainNavItem = {
        _id: `main-${Date.now()}`,
        name: itemData.name || '新导航',
        slug: itemData.slug || 'new-nav',
        order: navigationData.length + 1,
        isActive: true,
        subItems: [],
        translations: itemData.translations || {
          zh: { name: itemData.name || '新导航' },
          en: { name: itemData.name || 'New Nav' },
        },
      };

      navigationData.push(newMainItem);
    } else if (type === 'sub' && parentId) {
      // 添加子导航项
      const parentIndex = navigationData.findIndex(item => item._id === parentId);
      if (parentIndex === -1) {
        return NextResponse.json(
          {
            success: false,
            message: 'Parent navigation item not found',
          },
          { status: 404 }
        );
      }

      const newSubItem: SubMenuItem = {
        _id: `sub-${Date.now()}`,
        name: itemData.name || '新子菜单',
        slug: itemData.slug || 'new-sub-menu',
        url: itemData.url || '/new-page',
        order: navigationData[parentIndex].subItems.length + 1,
        translations: itemData.translations || {
          zh: { name: itemData.name || '新子菜单' },
          en: { name: itemData.name || 'New Sub Menu' },
        },
      };

      navigationData[parentIndex].subItems.push(newSubItem);
    } else {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid request parameters',
        },
        { status: 400 }
      );
    }

    // 保存更新后的数据
    saveNavigationData(navigationData);

    return NextResponse.json({
      success: true,
      message: 'Navigation item added successfully',
      data: navigationData,
    });
  } catch (error: unknown) {
    console.error('Error adding navigation item:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to add navigation item',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE - 删除导航项
export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const itemId = searchParams.get('id');
    const parentId = searchParams.get('parentId');

    if (!itemId) {
      return NextResponse.json(
        {
          success: false,
          message: 'Item ID is required',
        },
        { status: 400 }
      );
    }

    // 从文件加载当前数据
    let navigationData = loadNavigationData();

    if (parentId) {
      // 删除子导航项
      const parentIndex = navigationData.findIndex(item => item._id === parentId);
      if (parentIndex === -1) {
        return NextResponse.json(
          {
            success: false,
            message: 'Parent navigation item not found',
          },
          { status: 404 }
        );
      }

      navigationData[parentIndex].subItems = navigationData[parentIndex].subItems.filter(
        subItem => subItem._id !== itemId
      );
    } else {
      // 删除主导航项
      navigationData = navigationData.filter(item => item._id !== itemId);
    }

    // 保存更新后的数据
    saveNavigationData(navigationData);

    return NextResponse.json({
      success: true,
      message: 'Navigation item deleted successfully',
      data: navigationData,
    });
  } catch (error: unknown) {
    console.error('Error deleting navigation item:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to delete navigation item',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
