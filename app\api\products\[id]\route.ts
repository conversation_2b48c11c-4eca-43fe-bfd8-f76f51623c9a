import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route.js';
import { query } from '@/lib/db.js';
import { memoryCache } from '@/lib/cache';

// 验证管理员权限的辅助函数
async function verifyAdminPermission(request: NextRequest) {
  // 1. 首先检查 NextAuth 会话
  const session = await getServerSession(authOptions);
  if (session?.user?.role && ['admin', 'super_admin'].includes(session.user.role)) {
    console.log('[AUTH] NextAuth 管理员验证通过:', session.user.name);
    return { success: true, user: session.user, source: 'nextauth' };
  }

  // 2. 检查本地会话 (从请求头中获取)
  const authorization = request.headers.get('authorization');
  if (authorization && authorization.startsWith('Bearer ')) {
    const token = authorization.substring(7);
    try {
      const sessionData = JSON.parse(decodeURIComponent(token));
      if (sessionData.user?.role && ['admin', 'super_admin'].includes(sessionData.user.role)) {
        // 检查会话是否过期
        if (new Date(sessionData.expires) > new Date()) {
          console.log('[AUTH] 本地会话管理员验证通过:', sessionData.user.name);
          return { success: true, user: sessionData.user, source: 'local' };
        }
      }
    } catch (error) {
      console.warn('[AUTH] 解析本地会话失败:', error);
    }
  }

  // 3. 检查 Cookie 中的本地会话
  const cookies = request.headers.get('cookie');
  if (cookies) {
    const adminSessionMatch = cookies.match(/admin_session=([^;]+)/);
    if (adminSessionMatch) {
      try {
        const sessionData = JSON.parse(decodeURIComponent(adminSessionMatch[1]));
        if (sessionData.user?.role && ['admin', 'super_admin'].includes(sessionData.user.role)) {
          if (new Date(sessionData.expires) > new Date()) {
            console.log('[AUTH] Cookie本地会话管理员验证通过:', sessionData.user.name);
            return { success: true, user: sessionData.user, source: 'cookie' };
          }
        }
      } catch (error) {
        console.warn('[AUTH] 解析Cookie会话失败:', error);
      }
    }
  }

  console.log('[AUTH] 管理员权限验证失败');
  return { success: false, user: null, source: null };
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  const { id } = params;

  if (!id) {
    return NextResponse.json({ success: false, message: 'Invalid product ID' });
  }

  try {
    // 尝试从缓存获取产品
    const cacheKey = `product-id-${id}`;
    const cachedProduct = memoryCache.get(cacheKey);

    if (cachedProduct) {
      console.log(`[API] Serving cached product for ID: ${id}`);
      return NextResponse.json(cachedProduct);
    }

    console.log(`[API] Fetching product from database for ID: ${id}`);
    // 查询产品详情
    const result = await query('SELECT * FROM products WHERE id = $1', [id]);

    if (result.rows.length === 0) {
      return NextResponse.json({ success: false, message: 'Product not found' });
    }

    const product = result.rows[0];

        // 处理可能为字符串格式的JSON字段
        let productCategories = [];
        if (product.categories) {
          try {
            if (typeof product.categories === 'string') {
              productCategories = JSON.parse(product.categories);
            } else {
              productCategories = product.categories;
            }
          } catch (e: unknown) {
            console.error('Error parsing categories:', e);
            // 如果解析失败，保持空数组
          }
        }

        let productFeatures = [];
        if (product.features) {
          try {
            if (typeof product.features === 'string') {
              productFeatures = JSON.parse(product.features);
            } else {
              productFeatures = product.features;
            }
          } catch (e: unknown) {
            console.error('Error parsing features:', e);
            // 如果解析失败，保持空数组
          }
        }

        // 解析images字段
        let productImages = [];
        if (product.images) {
          try {
            productImages = JSON.parse(product.images);
          } catch (e) {
            console.error('Error parsing images:', e);
          }
        }
        // 如果没有images数组但有image_url，使用image_url
        if (productImages.length === 0 && product.image_url) {
          productImages = [product.image_url];
        }

        // 解析detail_images字段
        let productDetailImages = [];
        if (product.detail_images) {
          try {
            productDetailImages = JSON.parse(product.detail_images);
          } catch (e) {
            console.error('Error parsing detail_images:', e);
          }
        }

        // 解析key_features字段
        let productKeyFeatures = [];
        if (product.key_features) {
          try {
            productKeyFeatures = JSON.parse(product.key_features);
          } catch (e) {
            console.error('Error parsing key_features:', e);
          }
        }

        // 解析applications字段
        let productApplications = [];
        if (product.applications) {
          try {
            productApplications = JSON.parse(product.applications);
          } catch (e) {
            console.error('Error parsing applications:', e);
          }
        }

        // 解析tech_specs字段
        let productTechSpecs = {};
        if (product.tech_specs) {
          try {
            productTechSpecs = JSON.parse(product.tech_specs);
          } catch (e) {
            console.error('Error parsing tech_specs:', e);
          }
        }

        // 转换为前端格式，匹配新的数据结构
        const formattedProduct = {
          id: product.id.toString(),
          name: product.name,
          slug: product.slug,
          description: product.description,
          video_url: product.video_url || '',
          images: productImages,
          detail_images: productDetailImages,
          key_features: productKeyFeatures,
          tech_specs: productTechSpecs,
          applications: productApplications,
          in_stock: product.in_stock !== false,
          created_at: product.created_at,
          updated_at: product.updated_at,
        };

        const responseData = {
          success: true,
          data: formattedProduct,
        };

        // 将结果存入缓存
        memoryCache.set(cacheKey, responseData);

    return NextResponse.json(responseData);
  } catch (error: unknown) {
    console.error('Error fetching product:', error);
    const message = error instanceof Error ? error.message : 'Server error';
    return NextResponse.json({ success: false, message: message });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params;

  if (!id) {
    return NextResponse.json({ success: false, message: 'Invalid product ID' });
  }

  try {
    // 检查管理员权限
    const authResult = await verifyAdminPermission(request);
    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        message: 'Forbidden: Admin access required'
      }, { status: 403 });
    }

    console.log(`[PUT] 管理员权限验证通过 (${authResult.source}):`, authResult.user?.name);

    const {
      name,
      slug,
      description,
      video_url = '',
      key_features = [],
      tech_specs = {},
      applications = [],
      images = [],
      detail_images = [],
      in_stock = true,
    } = await request.json();

    // 检查产品是否存在
    const checkResult = await query('SELECT id FROM products WHERE id = $1', [id]);

    if (checkResult.rows.length === 0) {
      return NextResponse.json({ success: false, message: 'Product not found' });
    }

    // 更新产品 - 匹配新的数据结构
    const result = await query(
      `UPDATE products SET
       name = $1,
       slug = $2,
       description = $3,
       video_url = $4,
       key_features = $5,
       tech_specs = $6,
       applications = $7,
       images = $8,
       detail_images = $9,
       in_stock = $10,
       updated_at = CURRENT_TIMESTAMP
       WHERE id = $11
       RETURNING *`,
      [
        name,
        slug,
        description,
        video_url,
        JSON.stringify(key_features),
        JSON.stringify(tech_specs),
        JSON.stringify(applications),
        JSON.stringify(images),
        JSON.stringify(detail_images),
        in_stock,
        id,
      ]
    );

    const updatedProduct = result.rows[0];

    // 解析更新后的产品数据
    let updatedImages = [];
    if (updatedProduct.images) {
      try {
        updatedImages = JSON.parse(updatedProduct.images);
      } catch (e) {
        console.error('Error parsing updated images:', e);
      }
    }

    let updatedDetailImages = [];
    if (updatedProduct.detail_images) {
      try {
        updatedDetailImages = JSON.parse(updatedProduct.detail_images);
      } catch (e) {
        console.error('Error parsing updated detail_images:', e);
      }
    }

    let updatedKeyFeatures = [];
    if (updatedProduct.key_features) {
      try {
        updatedKeyFeatures = JSON.parse(updatedProduct.key_features);
      } catch (e) {
        console.error('Error parsing updated key_features:', e);
      }
    }

    let updatedApplications = [];
    if (updatedProduct.applications) {
      try {
        updatedApplications = JSON.parse(updatedProduct.applications);
      } catch (e) {
        console.error('Error parsing updated applications:', e);
      }
    }

    let updatedTechSpecs = {};
    if (updatedProduct.tech_specs) {
      try {
        updatedTechSpecs = JSON.parse(updatedProduct.tech_specs);
      } catch (e) {
        console.error('Error parsing updated tech_specs:', e);
      }
    }

    // 转换为前端格式
    const formattedProduct = {
      id: updatedProduct.id.toString(),
      name: updatedProduct.name,
      slug: updatedProduct.slug,
      description: updatedProduct.description,
      video_url: updatedProduct.video_url || '',
      images: updatedImages,
      detail_images: updatedDetailImages,
      key_features: updatedKeyFeatures,
      tech_specs: updatedTechSpecs,
      applications: updatedApplications,
      in_stock: updatedProduct.in_stock !== false,
      created_at: updatedProduct.created_at,
      updated_at: updatedProduct.updated_at,
    };

    // 清除该产品的相关缓存
    memoryCache.delete(`product-id-${id}`);
    memoryCache.delete(`product-slug-${slug}`);
    // 清除产品列表缓存，因为产品数据已更改
    const productListCacheKeys = memoryCache.keys().filter(key => key.startsWith('products-'));
    productListCacheKeys.forEach(key => memoryCache.delete(key));

    return NextResponse.json({
      success: true,
      data: formattedProduct,
    });
  } catch (error: unknown) {
    console.error('Error updating product:', error);
    const message = error instanceof Error ? error.message : 'Server error';
    return NextResponse.json({ success: false, message: message });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params;

  console.log(`[DELETE] 开始删除产品请求, ID: ${id}`);

  if (!id) {
    console.log('[DELETE] 错误: 无效的产品ID');
    return NextResponse.json({ success: false, message: 'Invalid product ID' });
  }

  try {
    // 检查管理员权限
    const authResult = await verifyAdminPermission(request);
    if (!authResult.success) {
      console.log('[DELETE] 错误: 用户无权限删除产品');
      return NextResponse.json({
        success: false,
        message: 'Forbidden: Admin access required'
      }, { status: 403 });
    }

    console.log(`[DELETE] 管理员权限验证通过 (${authResult.source}):`, authResult.user?.name);

    // 首先获取产品完整信息，用于日志记录和缓存清除
    const productInfo = await query(
      'SELECT id, slug, name FROM products WHERE id = $1',
      [id]
    );

    if (productInfo.rows.length === 0) {
      console.log(`[DELETE] 错误: 产品不存在, ID: ${id}`);
      return NextResponse.json({ success: false, message: 'Product not found' });
    }

    const product = productInfo.rows[0];
    const productName = product.name || '未命名产品';
    console.log(`[DELETE] 找到产品: ${productName} (slug: ${product.slug})`);

    // 开始删除操作 - 由于设置了 ON DELETE CASCADE，相关数据会自动删除
    console.log(`[DELETE] 开始删除产品及其相关数据...`);
    const result = await query('DELETE FROM products WHERE id = $1 RETURNING id', [id]);

    if (result.rows.length === 0) {
      console.log(`[DELETE] 错误: 删除操作失败, 产品可能已被删除`);
      return NextResponse.json({ success: false, message: 'Product deletion failed' });
    }

    console.log(`[DELETE] 产品删除成功: ${productName}`);
    console.log(`[DELETE] 相关数据已通过CASCADE自动删除: 特性、图片、分类关联、翻译等`);

    // 清除该产品的相关缓存
    console.log(`[DELETE] 开始清除缓存...`);
    memoryCache.delete(`product-id-${id}`);
    if (product.slug) {
      memoryCache.delete(`product-slug-${product.slug}`);
    }

    // 清除产品列表缓存，因为产品列表已更改
    const productListCacheKeys = memoryCache.keys().filter(key => key.startsWith('products-'));
    productListCacheKeys.forEach(key => memoryCache.delete(key));
    console.log(`[DELETE] 缓存清除完成, 清除了 ${productListCacheKeys.length + 2} 个缓存项`);

    return NextResponse.json({
      success: true,
      message: `Product "${productName}" and all related data deleted successfully`,
      deletedProduct: {
        id: product.id,
        name: productName,
        slug: product.slug
      }
    });
  } catch (error: unknown) {
    console.error('[DELETE] 删除产品时发生错误:', error);
    const message = error instanceof Error ? error.message : 'Unknown server error';
    return NextResponse.json({
      success: false,
      message: `Failed to delete product: ${message}`
    });
  }
}
