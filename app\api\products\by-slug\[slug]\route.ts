import { NextResponse } from 'next/server';
import db from '@/lib/db.js';
import { memoryCache } from '@/lib/cache';
import fs from 'fs';
import path from 'path';

const { query, mockData, isConnectionHealthy } = db;

// 产品接口定义
interface Product {
  id?: number | string;
  name: string;
  name_en?: string;
  slug: string;
  description?: string;
  description_en?: string;
  image_url?: string;
  images?: string[] | string;
  categories?: any;
  features?: any;
  features_en?: any;
  type?: string;
  size?: string;
  style?: string;
  is_published?: boolean;
  is_featured?: boolean;
  category?: string;
  category_en?: string;
  created_at?: string;
  updated_at?: string;
}

// 查询选项接口
interface QueryOptions {
  retries?: number;
  mockOnFailure?: boolean;
  mockResult?: any;
}

// 根据语言本地化产品数据
function localizeProduct(product: Product, lang: string): Product {
  if (lang === 'en') {
    return {
      ...product,
      name: product.name_en || product.name,
      description: product.description_en || product.description,
      category: product.category_en || product.category,
      features: product.features_en || product.features,
    };
  }

  // 默认返回中文版本
  return {
    ...product,
    name: product.name,
    description: product.description,
    category: product.category,
    features: product.features,
  };
}

/**
 * 通过slug获取产品详情
 * @param request - 请求对象
 * @param context - 上下文对象，包含动态路由参数
 * @returns 产品详情或404 Not Found响应
 */
export async function GET(request: Request, { params }: { params: { slug: string } }) {
  const { slug } = params;
  const { searchParams } = new URL(request.url);
  const lang = searchParams.get('lang') || 'zh';

  console.log(`[API] 请求产品详情: ${slug}, 语言: ${lang}`);

  if (!slug) {
    console.log(`[API] 错误: 缺少slug参数`);
    return NextResponse.json({ success: false, error: 'Slug parameter is required' }, { status: 400 });
  }

  try {
    // 跳过缓存，确保每次都从数据库获取最新数据
    if (request.url.includes('nocache=true')) {
      console.log(`[API] 跳过缓存获取产品: ${slug}`);
    } else {
      // 尝试从缓存获取产品
      const cacheKey = `product-slug-${slug}`;
      const cachedProduct = memoryCache.get(cacheKey);

      if (cachedProduct) {
        console.log(`[API] 使用缓存数据: ${slug}`);
        return NextResponse.json(cachedProduct);
      }
    }

    console.log(`[API] 从数据库获取产品: ${slug}`);

    // 检查连接池健康状态
    const useDirectMock = !isConnectionHealthy();
    if (useDirectMock) {
      console.log(`[API] 数据库连接不健康，使用模拟数据`);
      const mockResponse = getProductMockResponse(slug);
      return mockResponse;
    }

    // 从数据库查询产品，设置查询选项
    const queryOptions: QueryOptions = {
      retries: 1,
      mockOnFailure: true
    };

    console.log(`[API] 执行数据库查询: ${slug}`);
    const result = await query(
      'SELECT * FROM products WHERE slug = $1',
      [slug],
      queryOptions as any
    );

    if (!result || !result.rows || result.rows.length === 0) {
      console.log(`[API] 数据库中未找到产品，尝试使用JSON文件: ${slug}`);

      // 尝试从JSON文件获取产品数据
      try {
        const filePath = path.join(process.cwd(), 'public', 'mock-products.json');
        const fileContents = fs.readFileSync(filePath, 'utf8');
        const products: Product[] = JSON.parse(fileContents);
        const jsonProduct = products.find((p: Product) => p.slug === slug);

        if (jsonProduct) {
          console.log(`[API] 从JSON文件找到产品: ${slug}`);
          const localizedProduct = localizeProduct(jsonProduct, lang);
          return NextResponse.json({
            product: localizedProduct,
            lang: lang,
            source: 'json',
            timestamp: new Date().toISOString()
          });
        }
      } catch (jsonError) {
        console.error(`[API] 读取JSON文件失败: ${slug}`, jsonError);
      }

      // 当数据库和JSON文件都没有找到产品时，查找模拟数据中是否有匹配
      const mockProduct = mockData.products.find((p: Product) => p.slug === slug);
      if (mockProduct) {
        console.log(`[API] 找到匹配的模拟产品: ${slug}`);
        const localizedProduct = localizeProduct(mockProduct, lang);
        return NextResponse.json({
          product: localizedProduct,
          lang: lang,
          source: 'mock',
          timestamp: new Date().toISOString()
        });
      }

      console.log(`[API] 没有找到产品: ${slug}`);
      return NextResponse.json({
        error: lang === 'zh' ? '产品不存在' : 'Product not found',
        product: null
      }, { status: 404 });
    }

    console.log(`[API] 在数据库中找到产品: ${slug}`);
    const product = result.rows[0] as unknown as Product;

    // 处理数据库产品的JSON字段
    const processedProduct = {
      ...product,
      // 处理图片数组
      images: product.images ? (typeof product.images === 'string' ? JSON.parse(product.images) : product.images) : [],
      // 处理特性数组
      features: product.features ? (typeof product.features === 'string' ? JSON.parse(product.features) : product.features) : [],
      // 处理分类数组
      categories: product.categories ? (typeof product.categories === 'string' ? JSON.parse(product.categories) : product.categories) : []
    };

    const localizedProduct = localizeProduct(processedProduct, lang);
    return NextResponse.json({
      product: localizedProduct,
      lang: lang,
      source: 'database',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error(`[API] 获取产品出错: ${slug}`, error);

    // 出错时尝试使用模拟数据
    try {
      console.log(`[API] 出错后尝试使用模拟数据: ${slug}`);
      return getProductMockResponse(slug);
    } catch (mockError) {
      console.error(`[API] 使用模拟数据也失败: ${slug}`, mockError);

      // 如果模拟数据也失败，返回通用错误响应
      return NextResponse.json({
        success: false,
        error: '获取产品数据时出错',
        details: error instanceof Error ? error.message : '未知错误'
      }, { status: 500 });
    }
  }
}

/**
 * 获取格式化的产品响应
 * @param product - 原始产品数据
 * @param isMock - 是否为模拟数据
 * @returns 格式化的产品响应
 */
function getFormattedProductResponse(product: Product, isMock = false) {
  try {
    console.log(`[API] 格式化产品数据: ${product.slug}`);

    // 处理可能为字符串格式的JSON字段
    let productCategories = [];
    if (product.categories) {
      try {
        if (typeof product.categories === 'string') {
          productCategories = JSON.parse(product.categories);
        } else {
          productCategories = product.categories;
        }
      } catch (e) {
        console.error(`[API] 解析产品分类出错: ${product.slug}`, e);
      }
    }

    let productFeatures = [];
    if (product.features) {
      try {
        if (typeof product.features === 'string') {
          productFeatures = JSON.parse(product.features);
        } else {
          productFeatures = product.features;
        }
      } catch (e) {
        console.error(`[API] 解析产品特性出错: ${product.slug}`, e);
      }
    }

    // 处理产品图片数组
    let productImages = [];
    if (product.images) {
      try {
        if (typeof product.images === 'string') {
          productImages = JSON.parse(product.images);
        } else {
          productImages = product.images;
        }
      } catch (e) {
        console.error(`[API] 解析产品图片出错: ${product.slug}`, e);
      }
    }

    // 如果没有图片数组但有主图，则使用主图
    if (!productImages.length && product.image_url) {
      productImages = [product.image_url];
    }

    // 转换为前端格式
    const formattedProduct = {
      _id: (product.id || '0').toString(),
      title: product.name,
      slug: product.slug,
      description: product.description || '产品描述暂时不可用',
      type: product.type || '',
      size: product.size || '',
      style: product.style || '',
      image_url: product.image_url || (productImages.length > 0 ? productImages[0] : ''),  // 添加image_url字段以兼容旧版代码
      images: productImages,
      features: productFeatures,
      categories: productCategories,
      category: product.category || '',  // 添加category字段以兼容旧版代码
      isPublished: product.is_published || false,
      createdAt: product.created_at || new Date().toISOString(),
      updatedAt: product.updated_at || new Date().toISOString(),
    };

    const responseData = {
      success: true,
      data: formattedProduct,
      isMock: isMock,
      message: isMock ? '显示模拟数据' : undefined
    };

    // 缓存结果
    if (!isMock) {
      const cacheKey = `product-slug-${product.slug}`;
      memoryCache.set(cacheKey, responseData);
      console.log(`[API] 产品数据已缓存: ${product.slug}`);
    }

    return NextResponse.json(responseData);
  } catch (error) {
    console.error(`[API] 格式化产品数据出错: ${product.slug}`, error);

    // 出错时返回简化的产品数据
    const simpleProduct = {
      _id: (product.id || '0').toString(),
      title: product.name,
      slug: product.slug,
      description: product.description || '产品描述暂时不可用',
      image_url: product.image_url || '',
      category: product.category || ''
    };

    return NextResponse.json({
      success: true,
      data: simpleProduct,
      isMock: true,
      message: '数据格式化出错，显示简化数据'
    });
  }
}

/**
 * 获取模拟产品响应
 * @param slug - 产品Slug
 * @returns 模拟产品响应
 */
function getProductMockResponse(slug: string) {
  console.log(`[API] 准备模拟数据: ${slug}`);

  // 首先查找预定义的模拟数据
  const mockProduct = mockData.products.find((p: Product) => p.slug === slug);

  if (mockProduct) {
    console.log(`[API] 使用预定义模拟数据: ${slug}`);
    return getFormattedProductResponse(mockProduct, true);
  }

  // 如果没有找到预定义数据，创建一个通用模拟产品
  console.log(`[API] 创建通用模拟数据: ${slug}`);

  try {
    const genericMockProduct: Product = {
      id: '0',
      name: slug.split('-').map((word: string) => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
      slug: slug,
      description: '此产品信息暂时不可用，我们正在尽快恢复数据库连接。',
      category: '未分类',
      image_url: '/images/products/placeholder.jpg',
      is_featured: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    return getFormattedProductResponse(genericMockProduct, true);
  } catch (error) {
    console.error(`[API] 创建通用模拟数据出错: ${slug}`, error);

    // 如果创建模拟数据也失败，返回一个最简单的产品数据
    return NextResponse.json({
      success: true,
      data: {
        _id: '0',
        title: slug.replace(/-/g, ' '),
        slug: slug,
        description: '产品数据暂时不可用。',
        image_url: '/images/products/placeholder.jpg',
      },
      isMock: true,
      message: '显示简化的模拟数据'
    });
  }
}
