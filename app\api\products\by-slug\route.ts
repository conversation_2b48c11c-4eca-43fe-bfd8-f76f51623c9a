import { NextResponse } from 'next/server';
import { query } from '@/lib/db.js';
import { memoryCache } from '@/lib/cache';

/**
 * 获取所有产品的slug列表
 * @returns 包含所有产品slug的JSON响应
 */
export async function GET(request: Request) {
  try {
    // 首先尝试从缓存获取产品slug列表
    const cacheKey = 'product-slugs-list';
    const cachedSlugs = memoryCache.get(cacheKey);

    if (cachedSlugs) {
      console.log('[API] Serving cached product slugs list');
      return NextResponse.json(cachedSlugs);
    }

    console.log('[API] Fetching all product slugs from database');

    // 查询所有产品的slug
    const result = await query(
      'SELECT id, slug, name, category FROM products ORDER BY created_at DESC',
      []
    );

    const slugs = result.rows.map(product => ({
      id: product.id,
      slug: product.slug,
      name: product.name,
      category: product.category,
    }));

    console.log(`[API] Successfully retrieved ${slugs.length} product slugs`);

    // 将结果存入缓存，默认有效期
    memoryCache.set(cacheKey, slugs);

    // 返回产品数据
    return NextResponse.json(slugs);
  } catch (error) {
    console.error('[API] Error fetching product slugs:', error);
    return NextResponse.json({ error: 'Failed to fetch product slugs' }, { status: 500 });
  }
}
