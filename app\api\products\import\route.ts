import { NextRequest, NextResponse } from 'next/server';
// import { getServerSession } from 'next-auth/next'; // Unused import
// import { authOptions } from '../auth/[...nextauth]/route'; // Unused import dbConnect from '@/lib/mongodb.js'; // Renamed import
import { pool } from '@/lib/db';
import { batchQuery } from '@/lib/db';
import { memoryCache } from '@/lib/cache';
import crypto from 'crypto';
// Note: Using PostgreSQL instead of MongoDB, Product model not needed for raw SQL operations

// 安全导入令牌，生产环境应从环境变量读取
// const IMPORT_TOKEN = 'products-import-2024'; // Unused variable

/**
 * 产品批量导入API
 * 支持高效的批量产品导入，使用事务保证数据一致性
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求数据
    let data = await request.json();
    const products = Array.isArray(data) ? data : data.products;
    
    if (!Array.isArray(products) || products.length === 0) {
      return NextResponse.json({
        success: false,
        message: '没有提供有效的产品数据'
      }, { status: 400 });
    }

    // 准备批量插入查询
    const queries = [];
    const results = { 
      inserted: 0, 
      updated: 0, 
      failed: 0,
      errors: []
    };
    
    // 生成导入批次ID，用于跟踪和回滚
    const importBatchId = crypto.randomUUID();

    // 为每个产品生成插入或更新查询
    for (const product of products) {
      // 确保必填字段存在
      if (!product.name || !product.slug) {
        results.failed++;
        results.errors.push({
          product: product.name || 'Unknown',
          error: '缺少必填字段 (name 或 slug)'
        });
        continue;
      }

      try {
        // 检查产品是否已存在
        const checkQuery = {
          name: `check_${product.slug}`,
          text: 'SELECT id FROM products WHERE slug = $1',
          params: [product.slug]
        };
        
        // 如果产品存在则更新，否则插入
        const upsertQuery = {
          name: `upsert_${product.slug}`,
          text: `
            INSERT INTO products(
              name, slug, description, image_url, images, category, categories,
              type, size, style, features, is_featured, is_published, in_stock,
              translations, created_at, updated_at
            )
            VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, NOW(), NOW())
            ON CONFLICT (slug)
            DO UPDATE SET
              name = $1,
              description = $3,
              image_url = $4,
              images = $5,
              category = $6,
              categories = $7,
              type = $8,
              size = $9,
              style = $10,
              features = $11, 
              is_featured = $12,
              is_published = $13,
              in_stock = $14,
              translations = $15,
              updated_at = NOW()
            RETURNING id, slug
          `,
          params: [
            product.name,
            product.slug,
            product.description || '',
            product.image_url || '',
            product.images ? JSON.stringify(product.images) : JSON.stringify([]),
            product.category || '',
            product.categories ? JSON.stringify(product.categories) : JSON.stringify([]),
            product.type || '',
            product.size || '',
            product.style || '',
            product.features ? JSON.stringify(product.features) : JSON.stringify([]),
            product.is_featured === true,
            product.is_published === true,
            product.in_stock === true,
            product.translations ? JSON.stringify(product.translations) : null
          ]
        };
        
        queries.push(checkQuery);
        queries.push(upsertQuery);
      } catch (err) {
        results.failed++;
        results.errors.push({
          product: product.name || product.slug || 'Unknown',
          error: err instanceof Error ? err.message : '处理产品时发生未知错误'
        });
      }
    }

    // 如果有有效的查询，执行批量操作
    if (queries.length > 0) {
      try {
        const batchResults = await batchQuery(queries);
        
        // 分析结果
        for (let i = 0; i < batchResults.length; i += 2) {
          const checkResult = batchResults[i];
          const upsertResult = batchResults[i + 1];
          
          if (upsertResult && upsertResult.result.rows.length > 0) {
            // 检查是插入还是更新
            if (checkResult.result.rows.length > 0) {
              results.updated++;
            } else {
              results.inserted++;
            }
          }
        }
        
        // 清除产品相关缓存
        if (memoryCache) {
          const cacheKeys = memoryCache.keys().filter(key => key.startsWith('products-'));
          cacheKeys.forEach(key => memoryCache.delete(key));
          console.log(`清除了 ${cacheKeys.length} 个产品相关缓存项`);
        }
        
      } catch (batchError) {
        console.error('批量导入产品失败:', batchError);
        return NextResponse.json({
          success: false,
          message: '批量导入产品失败',
          error: batchError instanceof Error ? batchError.message : '批量处理时发生未知错误',
          results
        }, { status: 500 });
      }
    }

    return NextResponse.json({
      success: results.inserted > 0 || results.updated > 0,
      message: `导入完成: ${results.inserted} 个产品插入, ${results.updated} 个产品更新, ${results.failed} 个失败`,
      results,
      importBatchId
    });
    
  } catch (error) {
    console.error('产品导入失败:', error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : '导入产品时发生未知错误'
    }, { status: 500 });
  }
}
