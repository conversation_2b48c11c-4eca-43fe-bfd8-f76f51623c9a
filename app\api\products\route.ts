import { NextRequest, NextResponse } from 'next/server';
// import { getServerSession } from 'next-auth'; // Potentially unused if session is removed
// import { authOptions } from '@/app/api/auth/[...nextauth]/route.js'; // Potentially unused if session is removed
import { query } from '@/lib/db.js';
import { memoryCache } from '@/lib/cache';
import fs from 'fs';
import path from 'path';

// 添加从本地JSON文件读取产品数据的函数
async function getLocalProducts(lang = 'zh') {
  try {
    // 首先尝试从 public/mock-products.json 读取
    const mockDataPath = path.join(process.cwd(), 'public', 'mock-products.json');
    if (fs.existsSync(mockDataPath)) {
      const data = fs.readFileSync(mockDataPath, 'utf8');
      const products = JSON.parse(data);
      console.log(`[API] 从 mock-products.json 读取到 ${products.length} 个产品`);
      return products.map(product => ({
        id: product.id.toString(),
        _id: product.id.toString(),
        name: lang === 'en' && product.name_en ? product.name_en : product.name,
        title: lang === 'en' && product.name_en ? product.name_en : product.name,
        slug: product.slug,
        description: lang === 'en' && product.description_en ? product.description_en : product.description,
        category: lang === 'en' && product.category_en ? product.category_en : product.category,
        style: lang === 'en' && product.style_en ? product.style_en : product.style,
        features: lang === 'en' && product.features_en ? product.features_en : product.features,
        type: product.type || '',
        size: product.size || '',
        image_url: product.images && product.images.length > 0 ? product.images[0] : '/images/placeholder.jpg',
        images: product.images || [],
        isPublished: product.in_stock || false,
        is_published: product.in_stock || false,
        price: product.price || 0,
        is_featured: product.is_featured || false,
        in_stock: product.in_stock || false,
        createdAt: product.created_at,
        updatedAt: product.updated_at,
      }));
    }

    // 备用：尝试从 data/mock-products.json 读取
    const dataPath = path.join(process.cwd(), 'data', 'mock-products.json');
    if (fs.existsSync(dataPath)) {
      const data = fs.readFileSync(dataPath, 'utf8');
      const products = JSON.parse(data);
      console.log(`[API] 从 data/mock-products.json 读取到 ${products.length} 个产品`);
      return products.map(product => ({
        id: product.id.toString(),
        _id: product.id.toString(),
        name: lang === 'en' && product.name_en ? product.name_en : product.name,
        title: lang === 'en' && product.name_en ? product.name_en : product.name,
        slug: product.slug,
        description: lang === 'en' && product.description_en ? product.description_en : product.description,
        category: lang === 'en' && product.category_en ? product.category_en : product.category,
        style: lang === 'en' && product.style_en ? product.style_en : product.style,
        features: lang === 'en' && product.features_en ? product.features_en : product.features,
        type: product.type || '',
        size: product.size || '',
        image_url: product.images && product.images.length > 0 ? product.images[0] : '/images/placeholder.jpg',
        images: product.images || [],
        isPublished: product.in_stock || false,
        is_published: product.in_stock || false,
        price: product.price || 0,
        is_featured: product.is_featured || false,
        in_stock: product.in_stock || false,
        createdAt: product.created_at,
        updatedAt: product.updated_at,
      }));
    }

    // 最后备用：尝试从 app/data/products.json 读取
    const appDataPath = path.join(process.cwd(), 'app', 'data', 'products.json');
    if (fs.existsSync(appDataPath)) {
      const data = fs.readFileSync(appDataPath, 'utf8');
      const products = JSON.parse(data);
      console.log(`[API] 从 app/data/products.json 读取到 ${products.length} 个产品`);
      return products;
    }

    return [];
  } catch (error) {
    console.error('读取本地产品数据错误:', error);
    return [];
  }
}

export async function GET(request: NextRequest) {
  // 获取请求方法
  const { method } = request;

  if (method !== 'GET') {
    return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
  }

  // 处理GET请求
  try {
    // 构建查询参数
    const url = new URL(request.url);
    const searchParams = url.searchParams;
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '12';
    const search = searchParams.get('search') || '';
    const lang = searchParams.get('lang') || 'zh'; // 添加语言参数
    const published = searchParams.get('published') || '';

    console.log(`[API] 产品列表请求 - 语言: ${lang}, 页面: ${page}, 限制: ${limit}, 搜索: ${search}`);

    const pageNumber = parseInt(page, 10);
    const limitNumber = parseInt(limit, 10);
    const offset = (pageNumber - 1) * limitNumber;

    // 生成缓存键（包含语言参数）
    const cacheKey = `products-${page}-${limit}-${search}-${published}-${lang}`;

    // 使用改进的缓存查询功能
    try {
      // 从数据库获取产品总数和数据
      const { batchQuery, cachedQuery, query } = require('@/lib/db.js');

      // 尝试使用批量查询提高性能
      const queries = [
        { 
          name: 'productsCount', 
          text: `
            SELECT COUNT(*) as total 
            FROM products 
            WHERE ($1 = '' OR (name ILIKE $2 OR description ILIKE $2))
            AND ($3 = '' OR is_published = $4)
          `, 
          params: [search, `%${search}%`, published, published === 'true'] 
        },
        { 
          name: 'products', 
          text: `
            SELECT * FROM products 
            WHERE ($1 = '' OR (name ILIKE $2 OR description ILIKE $2))
            AND ($3 = '' OR is_published = $4)
            ORDER BY created_at DESC 
            LIMIT $5 OFFSET $6
          `, 
          params: [search, `%${search}%`, published, published === 'true', limitNumber, offset] 
        }
      ];

      // 使用带缓存的批量查询
      let data;
      let products = [];
      let total = 0;
      
      try {
        // 首先尝试从缓存获取结果
        data = memoryCache.get(cacheKey);
        
        if (!data) {
          // 如果没有缓存，使用批量查询
          const results = await batchQuery(queries);
          
          total = parseInt(results.find(r => r.name === 'productsCount')?.result.rows[0]?.total || '0', 10);
          products = results.find(r => r.name === 'products')?.result.rows || [];
          
          // 转换产品数据格式
          products = products.map(product => ({
            id: product.id.toString(),
            _id: product.id.toString(),
            name: product.name,
            title: product.name,
            slug: product.slug,
            description: product.description || '',
            image_url: product.image_url || '',
            type: product.type || '',
            size: product.size || '',
            style: product.style || '',
            price: product.price || 0,
            is_featured: product.is_featured || false,
            isPublished: product.is_published || false,
            is_published: product.is_published || false,
            createdAt: product.created_at || new Date().toISOString(),
            created_at: product.created_at || new Date().toISOString(),
            updatedAt: product.updated_at || new Date().toISOString(),
            updated_at: product.updated_at || new Date().toISOString(),
          }));
          
          // 缓存结果 - 10分钟过期
          memoryCache.set(cacheKey, { products, total }, 600000);
          console.log(`[API] 产品数据已从数据库获取并缓存`);
        } else {
          console.log(`[API] 返回缓存的产品数据`);
          products = data.products;
          total = data.total;
        }
      } catch (dbError) {
        console.error('[API] 数据库查询失败:', dbError);

        // 如果数据库查询失败，尝试从本地文件获取（传递语言参数）
        const localProducts = await getLocalProducts(lang);
        if (localProducts && localProducts.length > 0) {
          products = localProducts;
          total = localProducts.length;

          // 应用搜索筛选
          if (search) {
            products = products.filter(p =>
              p.name.toLowerCase().includes(search.toLowerCase()) ||
              (p.description && p.description.toLowerCase().includes(search.toLowerCase()))
            );
          }

          // 应用发布状态筛选
          if (published !== '') {
            const isPublished = published === 'true';
            products = products.filter(p => p.isPublished === isPublished || p.is_published === isPublished);
          }

          // 应用分页
          total = products.length;
          products = products.slice(offset, offset + limitNumber);
        }
      }

      const response = NextResponse.json({
        products,
        total,
        page: pageNumber,
        limit: limitNumber,
        results: products.length,
      });

      // 添加缓存控制头，确保语言切换时不使用缓存
      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      response.headers.set('Pragma', 'no-cache');
      response.headers.set('Expires', '0');

      return response;
    } catch (error) {
      console.error('获取产品数据失败:', error);
      throw error;
    }
  } catch (error) {
    console.error('处理产品请求失败:', error);
    return NextResponse.json({ error: 'Failed to process request' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // 获取请求体
    const data = await request.json();
    const {
      title,
      slug,
      description,
      size,
      style,
      type,
      features = [],

      images = [],
      isPublished = false,
      translations = {},
    } = data;

    // 验证必填字段
    if (!title || !slug || !description) {
      return NextResponse.json({
        success: false,
        message: 'Title, slug and description are required',
      }, { status: 400 });
    }

    // 检查slug是否已存在
    const existingProduct = await query('SELECT id FROM products WHERE slug = $1', [slug]);

    if (existingProduct.rows.length > 0) {
      return NextResponse.json({
        success: false,
        message: `Product with slug "${slug}" already exists`,
      }, { status: 409 });
    }

    // 插入新产品
    const result = await query(
      `INSERT INTO products
        (name, slug, description, size, style, type, features, images, in_stock, price)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING id`,
      [
        title,
        slug,
        description,
        size || null,
        style || null,
        type || null,
        JSON.stringify(features),
        JSON.stringify(images),
        isPublished,
        0, // 默认价格为0
      ]
    );

    // 清除所有产品列表缓存
    const cacheKeys = memoryCache.keys().filter(key => key.startsWith('products-'));
    cacheKeys.forEach(key => memoryCache.delete(key));

    return NextResponse.json({
      success: true,
      message: 'Product created successfully',
      productId: result.rows[0].id,
    });
  } catch (error: unknown) {
    console.error('Error creating product:', error);
    const message = error instanceof Error ? error.message : 'Error creating product';
    return NextResponse.json({
      success: false,
      message: message,
    }, { status: 500 });
  }
}
