import { NextRequest, NextResponse } from 'next/server';
import { sendNotificationEmail } from '../../../lib/sendgrid-email';

export async function POST(request: NextRequest) {
  try {
    console.log('开始专业邮件服务测试...');
    
    // 测试数据
    const testData = {
      id: 2025,
      name: "王总",
      email: "<EMAIL>",
      phone: "+86 138-0000-8888",
      country: "中国",
      playground_size: "1000+ sqm",
      product: "全息互动投影系统",
      message: "您好，我们是一家大型连锁儿童乐园企业，在全国有50多家门店。我们对贵公司的全息互动投影系统非常感兴趣，希望能够了解：1. 详细的技术参数和功能介绍 2. 批量采购的价格方案 3. 安装和维护服务 4. 成功案例展示。我们计划在今年内完成首批10家门店的设备采购和安装。",
      created_at: new Date().toISOString()
    };

    // 检查可用的邮件服务
    const availableServices = [];
    if (process.env.SENDGRID_API_KEY) {
      availableServices.push('SendGrid');
    }
    if (process.env.RESEND_API_KEY) {
      availableServices.push('Resend');
    }
    if (process.env.SMTP_USER && process.env.SMTP_PASS) {
      availableServices.push('SMTP');
    }

    console.log('可用的邮件服务:', availableServices);

    if (availableServices.length === 0) {
      return NextResponse.json({
        success: false,
        message: '没有配置任何邮件服务',
        suggestion: '请配置以下任一服务：SendGrid API密钥、Resend API密钥或SMTP设置',
        services: {
          sendgrid: 'https://sendgrid.com - 免费100封/月',
          resend: 'https://resend.com - 免费3000封/月',
          smtp: '传统邮箱SMTP服务'
        }
      }, { status: 400 });
    }

    try {
      // 尝试使用专业邮件服务发送
      const result = await sendNotificationEmail(testData);
      
      return NextResponse.json({
        success: true,
        message: '专业邮件服务发送成功',
        service: result.service,
        messageId: result.messageId,
        recipient: process.env.ADMIN_EMAIL || '<EMAIL>',
        availableServices: availableServices
      });

    } catch (emailError) {
      console.error('专业邮件服务发送失败:', emailError);
      
      // 记录到控制台作为备用
      console.log('='.repeat(60));
      console.log('专业邮件服务发送失败，记录到控制台:');
      console.log(`收件人: ${process.env.ADMIN_EMAIL || '<EMAIL>'}`);
      console.log(`发件人: ${process.env.FROM_EMAIL || '<EMAIL>'}`);
      console.log('邮件内容:');
      console.log(`新的表单提交通知

客户信息:
- 姓名: ${testData.name}
- 邮箱: ${testData.email}
- 电话: ${testData.phone}
- 国家: ${testData.country}
- 场地大小: ${testData.playground_size}
- 感兴趣的产品: ${testData.product}

客户留言:
${testData.message}

提交时间: ${new Date(testData.created_at).toLocaleString('zh-CN')}
提交ID: #${testData.id}`);
      console.log('='.repeat(60));

      return NextResponse.json({
        success: false,
        message: '专业邮件服务发送失败，但已记录到控制台',
        error: emailError instanceof Error ? emailError.message : 'Unknown error',
        fallback: 'logged_to_console',
        availableServices: availableServices,
        suggestion: '请检查API密钥配置或网络连接'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('专业邮件测试API错误:', error);
    return NextResponse.json({
      success: false,
      message: '专业邮件测试API发生错误',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const availableServices = [];
  if (process.env.SENDGRID_API_KEY) {
    availableServices.push('SendGrid');
  }
  if (process.env.RESEND_API_KEY) {
    availableServices.push('Resend');
  }
  if (process.env.SMTP_USER && process.env.SMTP_PASS) {
    availableServices.push('SMTP');
  }

  return NextResponse.json({
    message: '专业邮件服务测试API',
    endpoint: '/api/professional-email-test',
    method: 'POST',
    availableServices: availableServices,
    recommendations: {
      sendgrid: {
        name: 'SendGrid',
        url: 'https://sendgrid.com',
        freeLimit: '100封/月',
        setup: '注册账号 → 获取API密钥 → 设置SENDGRID_API_KEY环境变量'
      },
      resend: {
        name: 'Resend',
        url: 'https://resend.com',
        freeLimit: '3000封/月',
        setup: '注册账号 → 获取API密钥 → 设置RESEND_API_KEY环境变量'
      },
      emailjs: {
        name: 'EmailJS',
        url: 'https://www.emailjs.com',
        freeLimit: '200封/月',
        setup: '前端直接发送，无需后端配置'
      }
    }
  });
}
