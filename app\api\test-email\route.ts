import { NextRequest, NextResponse } from 'next/server';
import { sendFormNotification } from '../../../lib/email';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { testData } = body;

    // 验证测试数据
    if (!testData) {
      return NextResponse.json(
        { success: false, message: 'Test data is required' },
        { status: 400 }
      );
    }

    // 发送测试邮件
    const emailSent = await sendFormNotification(testData);

    if (emailSent) {
      return NextResponse.json({
        success: true,
        message: 'Test email sent successfully'
      });
    } else {
      return NextResponse.json(
        { success: false, message: 'Failed to send test email. Please check your email configuration.' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error sending test email:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to send test email',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
