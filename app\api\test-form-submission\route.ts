import { NextRequest, NextResponse } from 'next/server';
import { query } from '../../../lib/db';

export async function POST(request: NextRequest) {
  try {
    console.log('Testing form submission...');
    
    // 首先尝试创建表（如果不存在）
    try {
      await query(`
        CREATE TABLE IF NOT EXISTS form_submissions (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          email VARCHAR(255),
          phone VARCHAR(50),
          country VARCHAR(100),
          playground_size VARCHAR(100),
          product VARCHAR(255),
          message TEXT NOT NULL,
          status VARCHAR(50) DEFAULT 'new',
          is_read BOOLEAN DEFAULT FALSE,
          admin_notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('Table created or already exists');
    } catch (tableError) {
      console.error('Error creating table:', tableError);
    }

    // 插入测试数据
    const testData = {
      name: 'Test User',
      email: '<EMAIL>',
      phone: '+86 123-4567-8900',
      country: 'China',
      playground_size: '100-500 sqm',
      product: 'AR Trampoline',
      message: 'This is a test message from the form submission system.'
    };

    const result = await query(`
      INSERT INTO form_submissions (name, email, phone, country, playground_size, product, message)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id, created_at
    `, [
      testData.name,
      testData.email,
      testData.phone,
      testData.country,
      testData.playground_size,
      testData.product,
      testData.message
    ]);

    console.log('Test data inserted:', result.rows[0]);

    return NextResponse.json({
      success: true,
      message: 'Test form submission created successfully',
      data: {
        id: result.rows[0].id,
        created_at: result.rows[0].created_at,
        testData
      }
    });
  } catch (error) {
    console.error('Error in test form submission:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to create test form submission',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // 获取所有表单提交
    const result = await query(`
      SELECT 
        id, name, email, phone, country, playground_size, product, message,
        status, is_read, admin_notes, created_at, updated_at
      FROM form_submissions 
      ORDER BY created_at DESC
      LIMIT 10
    `);

    return NextResponse.json({
      success: true,
      message: 'Form submissions retrieved successfully',
      data: result.rows,
      count: result.rows.length
    });
  } catch (error) {
    console.error('Error fetching form submissions:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to fetch form submissions',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
