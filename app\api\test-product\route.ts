import { NextRequest, NextResponse } from 'next/server';

// 使用require以避免类型检查问题
const db = require('../../../lib/db.js');

export async function GET(request: NextRequest) {
  try {
    // 测试直接使用数据库查询
    const slug = request.nextUrl.searchParams.get('slug') || 'ar-trampoline';
    
    // 返回数据库连接状态
    const isDbHealthy = typeof db.isConnectionHealthy === 'function' ? db.isConnectionHealthy() : false;
    
    // 尝试从数据库获取产品
    let dbProduct = null;
    let dbError = null;
    
    try {
      const result = await db.query(
        'SELECT * FROM products WHERE slug = $1 LIMIT 1',
        [slug]
      );
      
      if (result && result.rows && result.rows.length > 0) {
        dbProduct = result.rows[0];
      }
    } catch (error: any) {
      dbError = {
        message: error.message || 'Unknown error',
        code: error.code || 'UNKNOWN',
        stack: error.stack || ''
      };
    }
    
    // 测试从API获取产品
    let apiResponse = null;
    let apiData = null;
    let apiError = null;
    
    try {
      const res = await fetch(`${request.nextUrl.origin}/api/products/by-slug/${slug}`);
      apiResponse = {
        status: res.status,
        statusText: res.statusText,
        headers: Object.fromEntries(res.headers.entries())
      };
      
      if (res.ok) {
        apiData = await res.json();
      }
    } catch (error: any) {
      apiError = {
        message: error.message || 'Unknown error',
        stack: error.stack || ''
      };
    }
    
    // 获取模拟数据
    const mockProducts = db.mockData && db.mockData.products 
      ? db.mockData.products.map((p: any) => ({ slug: p.slug, name: p.name }))
      : [];
    
    // 返回所有诊断信息
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      database: {
        isHealthy: isDbHealthy,
        product: dbProduct,
        error: dbError
      },
      api: {
        response: apiResponse,
        data: apiData,
        error: apiError
      },
      mockData: {
        products: mockProducts
      },
      slug
    });
  } catch (error: any) {
    return NextResponse.json({
      error: error.message || 'Unknown error',
      stack: error.stack || ''
    }, { status: 500 });
  }
} 