import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route.js';
import { S3Client, PutObjectCommand, ObjectCannedACL } from '@aws-sdk/client-s3';
import { writeFile, mkdir } from 'fs/promises';
import { join, extname } from 'path';
import { v4 as uuidv4 } from 'uuid';

// Disable the default body parser to handle form data
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';
export const bodyParser = false;

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
});

/**
 * 处理文件上传 - App Router版本
 * 这个函数使用新的Web标准API而不是formidable
 */
export async function POST(request: NextRequest) {
  try {
    // 认证检查
    // const session = await getServerSession(authOptions);
    // if (!session) {
    //   return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 401 });
    // }

    // 确保请求是multipart/form-data
    const contentType = request.headers.get('content-type') || '';
    if (!contentType.includes('multipart/form-data')) {
      return NextResponse.json({
        success: false,
        message: 'Content type must be multipart/form-data'
      }, { status: 400 });
    }

    // 获取表单数据
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({
        success: false,
        message: 'No file uploaded'
      }, { status: 400 });
    }

    // 生成唯一文件名
    const uniqueFilename = `${uuidv4()}${extname(file.name)}`;

    // 确定存储类型
    if (process.env.STORAGE_TYPE === 'local') {
      // 本地存储
      const uploadDir = join(process.cwd(), 'public', 'uploads');

      try {
        // 确保目录存在
        await mkdir(uploadDir, { recursive: true });

        // 读取文件数据
        const fileBuffer = Buffer.from(await file.arrayBuffer());

        // 写入文件
        const filePath = join(uploadDir, uniqueFilename);
        await writeFile(filePath, fileBuffer);

        // 返回文件URL
        const fileUrl = `/uploads/${uniqueFilename}`;
        return NextResponse.json({
          success: true,
          url: fileUrl,
          filename: uniqueFilename,
        });
      } catch (error) {
        console.error('Error saving file locally:', error);
        return NextResponse.json({
          success: false,
          message: 'Error saving file'
        }, { status: 500 });
      }
    } else {
      // S3存储
      try {
        // 读取文件数据
        const fileBuffer = Buffer.from(await file.arrayBuffer());

        // 上传到S3
        const params = {
          Bucket: process.env.AWS_S3_BUCKET || 'your-bucket-name',
          Key: `uploads/${uniqueFilename}`,
          Body: fileBuffer,
          ContentType: file.type,
          ACL: ObjectCannedACL.public_read,
        };

        await s3Client.send(new PutObjectCommand(params));

        // 返回文件URL
        const fileUrl = `https://${process.env.AWS_S3_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/uploads/${uniqueFilename}`;
        return NextResponse.json({
          success: true,
          url: fileUrl,
          filename: uniqueFilename,
        });
      } catch (error) {
        console.error('Error uploading to S3:', error);
        return NextResponse.json({
          success: false,
          message: 'Error uploading file to S3'
        }, { status: 500 });
      }
    }
  } catch (error: unknown) {
    console.error('Error in file upload handler:', error);
    const message = error instanceof Error ? error.message : 'Server error during upload process';
    return NextResponse.json({ success: false, message }, { status: 500 });
  }
}
