'use client';

import { usePathname } from 'next/navigation';
import ClientHeader from './ClientHeader';
import Footer from './Footer';
import StagewiseToolbar from './StagewiseToolbar';
import QuoteFormConditional from './QuoteFormConditional';
import FloatingButtons from './FloatingButtons';

export default function ConditionalLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  // 检查是否是管理员页面（包括登录页面和仪表板等所有管理员页面）
  const isAdminPage = pathname?.includes('/admin');

  // 如果是管理员页面，只渲染children，不显示主网站的导航和底部
  if (isAdminPage) {
    return <>{children}</>;
  }

  // 正常页面显示完整布局
  return (
    <>
      <ClientHeader />
      <StagewiseToolbar />
      <main className="main-content" suppressHydrationWarning>
        {children}
      </main>
      <QuoteFormConditional />
      <Footer />
      <FloatingButtons />
    </>
  );
}
