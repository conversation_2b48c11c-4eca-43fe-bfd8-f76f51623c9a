'use client';

import React from 'react';
import NextImage, { ImageProps as NextImageProps } from 'next/image';

/**
 * 自定义Image组件，用于解决Next.js Image组件中的fetchPriority警告
 *
 * 这个组件包装了Next.js的Image组件，并在传递props时过滤掉可能导致警告的属性
 */
export type ImageProps = NextImageProps & {
  priority?: boolean;
};

const CustomImage: React.FC<ImageProps> = ({ priority, ...props }) => {
  // 根据priority属性设置loading属性，而不是直接传递priority
  const loadingProp = priority ? 'eager' : props.loading || 'lazy';

  // 确保返回的组件不包含priority属性
  return <NextImage {...props} loading={loadingProp} />;
};

export default CustomImage;
