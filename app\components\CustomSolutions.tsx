'use client';

import Link from 'next/link';
import CustomImage from './CustomImage';
// import { solutionPlaceholders } from '../utils/imagePlaceholder'
import { useLanguage } from './LanguageProvider';
import { useEffect, useState } from 'react';
import styles from '../styles/custom-solutions.module.css';

export default function CustomSolutions() {
  const { t, locale } = useLanguage();
  const [hasLoaded, setHasLoaded] = useState(false);

  useEffect(() => {
    setHasLoaded(true);
  }, []);

  // 加载字体图标
  useEffect(() => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css';
    document.head.appendChild(link);

    return () => {
      document.head.removeChild(link);
    };
  }, []);

  // 预加载首个产品图片
  useEffect(() => {
    const preloadImg = new window.Image();
    preloadImg.src = '/images/products/3d-electronic-sandbox-main.jpg';
  }, []);

  const solutions = [
    {
      url: `/${locale}/products`,
      image: '/images/products/3d-electronic-sandbox-main.jpg',
      title: t('solutions.product1'),
      sqm: t('solutions.badge1'),
    },
    {
      url: `/${locale}/products`,
      image: '/images/products/ar-trampoline-pro-main.jpg',
      title: t('solutions.product2'),
      sqm: t('solutions.badge2'),
    },
    {
      url: `/${locale}/products`,
      image: '/images/products/ar-education-system-main.jpg',
      title: t('solutions.product3'),
      sqm: t('solutions.badge3'),
    },
    {
      url: `/${locale}/products`,
      image: '/images/products/holographic-screen-system-main.jpg',
      title: t('solutions.product4'),
      sqm: t('solutions.badge4'),
    },
    {
      url: `/${locale}/products`,
      image: '/images/products/children-interactive-beach-main.jpg',
      title: t('solutions.product5'),
      sqm: t('solutions.badge5'),
    },
    {
      url: `/${locale}/products`,
      image: '/images/products/holographic-stage-system-main.jpg',
      title: t('solutions.product6'),
      sqm: t('solutions.badge6'),
    },
    {
      url: `/${locale}/products`,
      image: '/images/products/bowling-interactive-system-main.jpg',
      title: t('solutions.product7'),
      sqm: t('solutions.badge7'),
    },
    {
      url: `/${locale}/products`,
      image: '/images/products/banquet-hall-system-main.jpg',
      title: t('solutions.product8'),
      sqm: t('solutions.badge8'),
    },
  ];

  return (
    <section className={styles.customSolutions}>
      <div className={styles.container}>
        <h2 className={styles.sectionTitle} suppressHydrationWarning>
          {t('solutions.title')}
        </h2>
        <div className={styles.solutionsGrid}>
          {solutions.map((solution, index) => (
            <div key={index} className={styles.solutionCard}>
              <Link href={solution.url} className={styles.solutionLink}>
                <div className={styles.cardBadge}>{solution.sqm}</div>
                <CustomImage
                  src={solution.image}
                  alt={solution.title}
                  width={600}
                  height={400}
                  className={styles.cardImg}
                  loading={index < 4 ? 'eager' : 'lazy'}
                  sizes="(max-width: 576px) 100vw, (max-width: 1100px) 50vw, (max-width: 1600px) 33vw, 25vw"
                />
                <div className={styles.cardGradient}></div>
                <h3 className={styles.cardTitle} suppressHydrationWarning>
                  {solution.title}
                </h3>
              </Link>
            </div>
          ))}
        </div>
        <div className={styles.ctaButton}>
          <Link
            href={`/${locale}/pages/custom-playground-design`}
            className="btn-primary"
            suppressHydrationWarning
          >
            {t('solutions.cta')}
          </Link>
        </div>
      </div>
    </section>
  );
}
