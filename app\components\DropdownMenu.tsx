'use client';

import { ReactNode, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import Link from 'next/link';

interface MenuItem {
  _id: string;
  name: string;
  slug: string;
  url?: string; // 添加可选的URL字段
  [key: string]: any;
}

interface DropdownMenuProps {
  items: MenuItem[];
  navItem: HTMLElement | null;
  isActive: boolean;
  dropdownName: string;
  onMouseEnter: (name: string) => void;
  onMouseLeave: () => void;
  locale: string;
  basePath: string;
  extraItems?: ReactNode;
  closeMenu?: () => void;
}

/**
 * 通用下拉菜单组件，用于渲染导航栏的下拉菜单
 *
 * @param props 下拉菜单组件的属性
 * @returns 下拉菜单Portal组件
 */
export default function DropdownMenu({
  items,
  navItem,
  isActive,
  dropdownName,
  onMouseEnter,
  onMouseLeave,
  locale,
  basePath,
  extraItems,
  closeMenu,
}: DropdownMenuProps) {
  // 添加客户端渲染标志
  const [mounted, setMounted] = useState(false);

  // 客户端挂载后设置标志
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // 确保只在客户端渲染
  if (!mounted) return null;

  // 如果菜单项不存在或菜单不处于激活状态，则不渲染
  if (!navItem || !isActive) return null;

  // 获取导航项的位置和尺寸
  const rect = navItem.getBoundingClientRect();

  // 获取窗口尺寸
  const windowWidth = window.innerWidth;

  // 计算下拉菜单位置 - 确保不会超出视口右侧边缘
  let left = rect.left;
  const minWidth = 240;

  if (left + minWidth > windowWidth) {
    // 如果下拉菜单会超出右侧边缘，则右对齐
    left = Math.max(0, rect.right - minWidth);
  }

  // 使用Portal渲染下拉菜单
  return createPortal(
    <div
      className="dropdown-portal"
      style={{
        position: 'fixed',
        top: `${rect.bottom}px`,
        left: `${left}px`,
        minWidth: `${minWidth}px`,
        zIndex: 100000,
        backgroundColor: 'white',
        borderRadius: '4px',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
        padding: '10px 0',
        animation: 'fadeInMenu 0.3s ease forwards',
      }}
      onMouseEnter={() => onMouseEnter(dropdownName)}
      onMouseLeave={onMouseLeave}
    >
      <ul style={{ listStyle: 'none', margin: 0, padding: 0 }}>
        {items.map(item => {
          // 使用item.url如果存在，否则使用传统的basePath/slug格式
          const href = item.url ? `/${locale}${item.url}` : `/${locale}/${basePath}/${item.slug}`;

          // 获取翻译后的名称，优先使用translations，回退到name
          const displayName = item.translations?.[locale]?.name || item.name;

          return (
            <li key={item._id} style={{ padding: '5px 0' }}>
              <Link
                href={href}
                style={{
                  display: 'block',
                  padding: '8px 20px',
                  color: '#333',
                  textDecoration: 'none',
                  fontSize: '14px',
                  transition: 'background-color 0.2s ease, color 0.2s ease',
                }}
                onMouseEnter={e => {
                  e.currentTarget.style.backgroundColor = '#f5f5f5';
                  e.currentTarget.style.color = '#0a59f7';
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = '#333';
                }}
                onClick={closeMenu}
              >
                {displayName}
              </Link>
            </li>
          );
        })}
        {extraItems}
      </ul>
    </div>,
    document.body
  );
}
