'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useLanguage } from './LanguageProvider';

export default function ExperienceCategories() {
  const { t, locale } = useLanguage();
  const [activeCategory, setActiveCategory] = useState('interactive');

  const experienceCategories = [
    {
      id: 'visual',
      icon: '🔳',
      name: t('experiences.visual', { fallback: '震撼立体视觉体验' }),
      image: '/images/products/3d-sandbox-1.jpg',
      url: `/${locale}/products/visual-experience`,
    },
    {
      id: 'immersive',
      icon: '🎮',
      name: t('experiences.immersive', { fallback: '沉浸式人机互动体验' }),
      image: '/images/products/ar-1.jpg',
      url: `/${locale}/products/immersive-experience`,
    },
    {
      id: 'interactive',
      icon: '🔺',
      name: t('experiences.interactive', { fallback: '创新公共空间互动体验' }),
      image: '/images/products/interactive-football-1.jpg',
      url: `/${locale}/products/interactive-experience`,
    },
    {
      id: 'dining',
      icon: '🍽️',
      name: t('experiences.dining', { fallback: '数字化餐饮体验革新' }),
      image: '/images/products/hologram-dining-1.jpg',
      url: `/${locale}/products/digital-dining-experience`,
    },
    {
      id: 'banquet',
      icon: '🏛️',
      name: t('experiences.banquet', { fallback: '高端宴会空间视听解决方案' }),
      image: '/images/products/hologram-stage-1.jpg',
      url: `/${locale}/products/banquet-space-solution`,
    },
    {
      id: 'enterprise',
      icon: '🏢',
      name: t('experiences.enterprise', { fallback: '企业展示空间一站式服务' }),
      image: '/images/products/ktv-1.jpg',
      url: `/${locale}/products/enterprise-display-service`,
    },
  ];

  // 获取当前活动类别数据
  const activeExperience =
    experienceCategories.find(cat => cat.id === activeCategory) || experienceCategories[2];

  return (
    <section className="experience-categories">
      <div className="container">
        <h2 className="section-title" suppressHydrationWarning>
          {t('experiences.title', { fallback: '沉浸式体验解决方案' })}
        </h2>

        <div className="category-buttons">
          {experienceCategories.map(category => (
            <button
              key={category.id}
              className={`category-button ${activeCategory === category.id ? 'active' : ''}`}
              onClick={() => setActiveCategory(category.id)}
            >
              <span className="category-icon">{category.icon}</span>
              <span className="category-name" suppressHydrationWarning>
                {category.name}
              </span>
            </button>
          ))}
        </div>

        <div className="experience-showcase">
          <div className="experience-image">
            <Image
              src={activeExperience.image}
              alt={activeExperience.name}
              fill
              style={{ objectFit: 'cover' }}
            />
          </div>

          <div className="experience-details">
            <div className="experience-title-container">
              <div className="innovation-tag" suppressHydrationWarning>
                <span className="tag-icon">{activeExperience.icon}</span>
                <span className="tag-text" suppressHydrationWarning>
                  {activeExperience.name}
                </span>
              </div>

              <h3 className="experience-title" suppressHydrationWarning>
                {activeCategory === 'interactive'
                  ? t('experiences.interactive.title', { fallback: '户外地面互动投影' })
                  : t(`experiences.${activeCategory}.title`, {
                      fallback: `${activeExperience.name}解决方案`,
                    })}
              </h3>
            </div>

            <p className="experience-description" suppressHydrationWarning>
              {activeCategory === 'interactive'
                ? t('experiences.interactive.description', {
                    fallback:
                      '专为户外环境设计的高亮度地面互动投影系统，结合防水防尘技术和智能互动系统，为广场、公园和商业街区创造引人入胜的互动体验。',
                  })
                : t(`experiences.${activeCategory}.description`, {
                    fallback:
                      '采用前沿技术打造的沉浸式体验解决方案，为客户提供卓越的视觉和互动效果，适用于各类商业和公共空间。',
                  })}
            </p>

            <div className="feature-badges">
              <div className="feature-badge">
                <span className="feature-icon">🛡️</span>
                <span className="feature-text" suppressHydrationWarning>
                  {t('experiences.feature.waterproof', { fallback: 'IP65防水防尘' })}
                </span>
              </div>

              <div className="feature-badge">
                <span className="feature-icon">💡</span>
                <span className="feature-text" suppressHydrationWarning>
                  {t('experiences.feature.brightness', { fallback: '8000流明高亮度' })}
                </span>
              </div>

              <div className="feature-badge">
                <span className="feature-icon">🌦️</span>
                <span className="feature-text" suppressHydrationWarning>
                  {t('experiences.feature.allweather', { fallback: '全天候运行设计' })}
                </span>
              </div>
            </div>

            <Link href={activeExperience.url} className="learn-more-link">
              {t('experiences.learnMore', { fallback: '了解更多' })} →
            </Link>
          </div>
        </div>
      </div>

      <style jsx>{`
        .experience-categories {
          padding: 80px 0;
          background-color: #f8f9fa;
        }

        .container {
          max-width: 1240px;
          margin: 0 auto;
          padding: 0 20px;
        }

        .section-title {
          text-align: center;
          margin-bottom: 2.5rem;
          font-size: 2.2rem;
          font-weight: 700;
          color: #1a1a2e;
          position: relative;
          padding-bottom: 1rem;
        }

        .section-title:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 80px;
          height: 3px;
          background: linear-gradient(90deg, #1a1a2e, #4040bf);
          border-radius: 3px;
        }

        .category-buttons {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          gap: 15px;
          margin-bottom: 40px;
        }

        .category-button {
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 12px 20px;
          border-radius: 8px;
          border: 1px solid #e2e8f0;
          background-color: white;
          color: #1a1a2e;
          font-size: 1rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .category-button:hover {
          background-color: #f1f5f9;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .category-button.active {
          background-color: #1a1a2e;
          color: white;
          border-color: #1a1a2e;
          box-shadow: 0 4px 15px rgba(26, 26, 46, 0.2);
        }

        .category-icon {
          font-size: 1.2rem;
        }

        .experience-showcase {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 40px;
          margin-top: 40px;
          align-items: center;
        }

        .experience-image {
          position: relative;
          overflow: hidden;
          border-radius: 12px;
          box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
          height: 450px;
        }

        .experience-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s ease;
        }

        .experience-image:hover img {
          transform: scale(1.05);
        }

        .experience-title-container {
          margin-bottom: 15px;
        }

        .innovation-tag {
          display: inline-flex;
          align-items: center;
          gap: 8px;
          padding: 6px 12px;
          border-radius: 5px;
          background-color: #e8f0fe;
          color: #1a73e8;
          font-size: 0.85rem;
          font-weight: 500;
          margin-bottom: 15px;
        }

        .tag-icon {
          font-size: 1.1rem;
        }

        .experience-title {
          font-size: 2rem;
          font-weight: 600;
          color: #1a1a2e;
          margin-bottom: 15px;
          line-height: 1.3;
        }

        .experience-description {
          color: #4a5568;
          font-size: 1.1rem;
          line-height: 1.7;
          margin-bottom: 25px;
        }

        .feature-badges {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 30px;
        }

        .feature-badge {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 15px;
          border-radius: 8px;
          background-color: #f1f5f9;
          font-size: 0.95rem;
        }

        .feature-icon {
          font-size: 1.2rem;
        }

        .learn-more-link {
          display: inline-block;
          padding: 12px 25px;
          background-color: #1a1a2e;
          color: white;
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;
          text-decoration: none;
        }

        .learn-more-link:hover {
          background-color: #2a2a4e;
          transform: translateY(-2px);
          box-shadow: 0 8px 15px rgba(26, 26, 46, 0.15);
        }

        @media (max-width: 991px) {
          .experience-showcase {
            grid-template-columns: 1fr;
            gap: 30px;
          }

          .experience-image {
            height: 350px;
          }

          .category-buttons {
            gap: 10px;
          }

          .category-button {
            padding: 10px 15px;
            font-size: 0.9rem;
          }
        }

        @media (max-width: 768px) {
          .section-title {
            font-size: 1.8rem;
          }

          .experience-title {
            font-size: 1.8rem;
          }

          .experience-description {
            font-size: 1rem;
          }
        }

        @media (max-width: 576px) {
          .category-buttons {
            gap: 8px;
          }

          .category-button {
            padding: 8px 12px;
            font-size: 0.8rem;
          }

          .experience-image {
            height: 250px;
          }

          .experience-title {
            font-size: 1.5rem;
          }
        }
      `}</style>
    </section>
  );
}
