'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import CustomImage from './CustomImage';
import { useLanguage } from './LanguageProvider';

interface FactoryData {
  image: string;
  alt: string;
}

export default function FactoryShowcase() {
  const { t, locale } = useLanguage();
  const [factorySlides, setFactorySlides] = useState<FactoryData[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovering, setIsHovering] = useState(false);
  const autoplayTimer = useRef<NodeJS.Timeout | null>(null);
  const sectionRef = useRef<HTMLDivElement>(null);
  const [showInfoCard, setShowInfoCard] = useState(false);

  // 初始化幻灯片数据
  useEffect(() => {
    const slides: FactoryData[] = [
      {
        image: '/images/company/factory/20230223110639353.jpg',
        alt: t('factory.slides.slide1'),
      },
      {
        image: '/images/company/factory/R-C.jpg',
        alt: t('factory.slides.slide2'),
      },
      {
        image: '/images/company/factory/R-C-1.jpg',
        alt: t('factory.slides.slide3'),
      },
      {
        image: '/images/company/factory/eb701ccc-5b10-4d17-8331-531f17ebd883.jpg',
        alt: t('factory.slides.slide4'),
      },
    ];

    setFactorySlides(slides);
  }, [t]);

  // 加载图标字体
  useEffect(() => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css';
    document.head.appendChild(link);

    return () => {
      document.head.removeChild(link);
    };
  }, []);

  // 添加滚动动画
  useEffect(() => {
    const handleScroll = () => {
      const section = sectionRef.current;
      if (!section) return;

      const sectionTop = section.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;

      if (sectionTop < windowHeight * 0.75) {
        section.classList.add('visible');
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // 初始检查

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // 自动播放
  useEffect(() => {
    if (true && !isHovering) {
      autoplayTimer.current = setInterval(() => {
        setCurrentIndex(prev => (prev + 1) % factorySlides.length);
      }, 5000);

      return () => {
        if (autoplayTimer.current) clearInterval(autoplayTimer.current);
      };
    }
  }, [factorySlides.length, isHovering]);

  // 导航到下一张幻灯片
  const nextSlide = useCallback(() => {
    setCurrentIndex(prev => (prev + 1) % factorySlides.length);
  }, [factorySlides.length]);

  // 导航到上一张幻灯片
  const prevSlide = useCallback(() => {
    setCurrentIndex(prev => (prev - 1 + factorySlides.length) % factorySlides.length);
  }, [factorySlides.length]);

  // 导航到特定幻灯片
  const goToSlide = useCallback((index: number) => {
    setCurrentIndex(index);
  }, []);

  const handleMouseEnter = () => {
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
  };

  // 切换信息卡片显示状态
  const toggleInfoCard = useCallback(() => {
    setShowInfoCard(prev => !prev);
  }, []);

  // 工厂统计数据
  const factoryStats = [
    {
      number: 12000,
      text: t('factory.stats.area'),
      icon: 'fa-building-circle-check',
    },
    {
      number: 150,
      text: t('factory.stats.staff'),
      icon: 'fa-users-gear',
    },
    {
      number: 50,
      text: t('factory.stats.capacity'),
      icon: 'fa-diagram-project',
    },
    {
      number: 80,
      text: t('factory.stats.countries'),
      icon: 'fa-earth-asia',
    },
  ];

  // 工厂认证数据
  const certifications = [
    {
      id: 'iso',
      name: 'ISO 9001',
      icon: 'fa-certificate',
      description: t('factory.certifications.iso'),
    },
    {
      id: 'ce',
      name: 'CE',
      icon: 'fa-circle-check',
      description: t('factory.certifications.ce'),
    },
    {
      id: 'ul',
      name: 'UL',
      icon: 'fa-shield-halved',
      description: t('factory.certifications.ul'),
    },
    {
      id: 'rohs',
      name: 'RoHS',
      icon: 'fa-seedling',
      description: t('factory.certifications.rohs'),
    },
  ];

  // 在组件可见后短暂显示信息卡片作为提示
  useEffect(() => {
    let timer: NodeJS.Timeout;

    const handleVisibilityChange = () => {
      const section = sectionRef.current;
      if (section && section.classList.contains('visible') && !showInfoCard) {
        // 短暂显示信息卡片提示
        timer = setTimeout(() => {
          setShowInfoCard(true);

          // 然后自动隐藏
          setTimeout(() => {
            setShowInfoCard(false);
          }, 3000);
        }, 1000);
      }
    };

    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.attributeName === 'class') {
          handleVisibilityChange();
        }
      });
    });

    if (sectionRef.current) {
      observer.observe(sectionRef.current, { attributes: true });
    }

    return () => {
      observer.disconnect();
      clearTimeout(timer);
    };
  }, [showInfoCard]);

  return (
    <div className="factory-container" ref={sectionRef}>
      <div className="section-header">
        <h2 className="section-title">
          <span className="thin-text">{t('factory.title_prefix')}</span>
          <strong>{t('factory.title')}</strong>
        </h2>

        <div className="section-subtitle">
          {t('factory.description')}
        </div>

        <div className="elegant-divider">
          <span className="divider-dot"></span>
        </div>
      </div>

      <div className="factory-content-grid">
        <div
          className="factory-slider-container"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <div className="slider-label">
            <i className="fas fa-industry-windows"></i>
            <span>{t('factory.slider_label')}</span>
          </div>

          <button
            className={`info-toggle-btn ${showInfoCard ? 'active' : ''}`}
            onClick={toggleInfoCard}
            aria-label={
              showInfoCard
                ? t('factory.close_btn_aria')
                : t('factory.info_btn_aria')
            }
          >
            <i className={`fas ${showInfoCard ? 'fa-times' : 'fa-info'}`}></i>
            <span className="btn-text">
              {t('factory.info_btn')}
            </span>
            <span className="info-pulse"></span>
          </button>

          <div className={`info-card ${showInfoCard ? 'visible' : ''}`}>
            <div className="info-card-header">
              <i className="fas fa-certificate header-icon"></i>
              <h3>{t('factory.info_card.header')}</h3>
            </div>

            <p className="info-card-desc">
              {t('factory.info_card.description')}
            </p>

            <div className="info-stats">
              {factoryStats.map((stat, index) => (
                <div key={index} className="info-stat-item">
                  <i className={`fas ${stat.icon}`}></i>
                  <div className="stat-number">
                    {stat.number.toLocaleString()}
                    <span>+</span>
                  </div>
                  <div className="stat-text">{stat.text}</div>
                </div>
              ))}
            </div>

            <div className="info-cert-title">
              {t('factory.info_card.cert_title')}
            </div>

            <div className="info-certs">
              {certifications.map(cert => (
                <div key={cert.id} className="info-cert-item">
                  <i className={`fas ${cert.icon}`}></i>
                  <span className="cert-name">{cert.name}</span>
                  <span className="cert-desc">{cert.description}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="factory-slides">
            {factorySlides.map((slide, index) => (
              <div
                key={index}
                className={`factory-slide ${currentIndex === index ? 'active' : ''}`}
              >
                <CustomImage
                  src={slide.image}
                  alt={slide.alt}
                  fill
                  className="object-cover transition-transform duration-700 ease-in-out hover:scale-110"
                  loading={index === 0 ? 'eager' : 'lazy'}
                />
                <div className="slide-overlay"></div>
                <div className="slide-caption">
                  <span>{slide.alt}</span>
                </div>
              </div>
            ))}
          </div>

          <div className="factory-slide-controls">
            <div className="factory-slider-dots">
              {factorySlides.map((_, index) => (
                <span
                  key={index}
                  className={`factory-dot ${currentIndex === index ? 'active' : ''}`}
                  onClick={() => goToSlide(index)}
                  aria-label={t('factory.slide_dot_aria', {
                    index: index + 1,
                  })}
                ></span>
              ))}
            </div>

            <div className="factory-slider-arrows">
              <button
                className="factory-arrow prev"
                onClick={prevSlide}
                aria-label={t('factory.prev_slide_aria')}
              >
                <i className="fas fa-arrow-left"></i>
              </button>
              <button
                className="factory-arrow next"
                onClick={nextSlide}
                aria-label={t('factory.next_slide_aria')}
              >
                <i className="fas fa-arrow-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .factory-container {
          max-width: 100%;
          margin: 0 auto;
          padding: 80px 0 0 0;
          opacity: 0;
          transform: translateY(30px);
          transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .factory-container.visible {
          opacity: 1;
          transform: translateY(0);
        }

        .section-header {
          text-align: center;
          margin-bottom: 50px;
          max-width: 1200px;
          margin-left: auto;
          margin-right: auto;
          padding: 0 20px;
        }

        .section-title {
          font-size: 42px;
          margin-bottom: 24px;
          line-height: 1.2;
          color: #1a1a2e;
        }

        .thin-text {
          font-weight: 200;
        }

        .section-title strong {
          font-weight: 600;
          position: relative;
        }

        .section-subtitle {
          font-size: 18px;
          color: #555;
          max-width: 720px;
          margin: 0 auto;
          line-height: 1.6;
        }

        .elegant-divider {
          position: relative;
          width: 100px;
          height: 3px;
          background: linear-gradient(90deg, rgba(26, 26, 46, 0), #1a1a2e, rgba(26, 26, 46, 0));
          margin: 0 auto;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .divider-dot {
          position: absolute;
          width: 10px;
          height: 10px;
          background-color: #1a1a2e;
          border-radius: 50%;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          box-shadow: 0 0 0 3px rgba(26, 26, 46, 0.1);
        }

        .factory-content-grid {
          display: grid;
          grid-template-columns: 1fr;
          width: 100%;
        }

        .factory-slider-container {
          position: relative;
          overflow: hidden;
          height: 800px;
          border: none;
          border-radius: 0;
          box-shadow: none;
        }

        .slider-label {
          position: absolute;
          top: 20px;
          left: 20px;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 12px 24px;
          border-radius: 4px;
          font-size: 1rem;
          font-weight: 500;
          z-index: 10;
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .slider-label:hover {
          background: rgba(26, 26, 46, 0.95);
          transform: translateY(-2px);
          box-shadow: 0 12px 25px rgba(0, 0, 0, 0.25);
        }

        .slider-label i {
          font-size: 1rem;
          transition: transform 0.3s ease;
        }

        .slider-label:hover i {
          transform: rotate(-10deg);
        }

        .info-toggle-btn {
          position: absolute;
          top: 20px;
          right: 20px;
          background: rgba(26, 26, 46, 0.85);
          color: white;
          padding: 10px 18px;
          border-radius: 30px;
          font-size: 0.95rem;
          font-weight: 500;
          z-index: 15;
          display: flex;
          align-items: center;
          gap: 10px;
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
          backdrop-filter: blur(8px);
          border: 1px solid rgba(255, 255, 255, 0.15);
          transition: all 0.3s ease;
          cursor: pointer;
          animation: attention-pulse 2s infinite;
        }

        @keyframes attention-pulse {
          0% {
            transform: scale(1);
          }
          5% {
            transform: scale(1.08);
          }
          10% {
            transform: scale(1);
          }
          15% {
            transform: scale(1.05);
          }
          20% {
            transform: scale(1);
          }
          100% {
            transform: scale(1);
          }
        }

        .info-toggle-btn:hover {
          background: rgba(26, 26, 46, 0.95);
          transform: translateY(-2px);
          box-shadow: 0 12px 25px rgba(0, 0, 0, 0.25);
          animation: none;
        }

        .info-toggle-btn.active {
          background: rgba(220, 20, 60, 0.85);
          animation: none;
        }

        .info-toggle-btn i {
          font-size: 1rem;
          transition: transform 0.3s ease;
        }

        .info-toggle-btn .btn-text {
          display: inline-block;
          font-size: 0.85rem;
          white-space: nowrap;
        }

        .info-toggle-btn:hover i {
          transform: rotate(10deg);
        }

        .info-pulse {
          position: absolute;
          top: -4px;
          right: -4px;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: #ff3860;
          animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
          0% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(255, 56, 96, 0.7);
          }

          70% {
            transform: scale(1);
            box-shadow: 0 0 0 10px rgba(255, 56, 96, 0);
          }

          100% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(255, 56, 96, 0);
          }
        }

        .info-card {
          position: absolute;
          top: 70px;
          right: 20px;
          width: 450px;
          max-width: calc(100% - 40px);
          background: rgba(255, 255, 255, 0.95);
          border-radius: 12px;
          box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
          z-index: 14;
          padding: 20px;
          transform: translateY(-20px);
          opacity: 0;
          pointer-events: none;
          transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
          max-height: calc(100% - 150px);
          overflow-y: auto;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .info-card.visible {
          transform: translateY(0);
          opacity: 1;
          pointer-events: all;
        }

        .info-card-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 15px;
          padding-bottom: 10px;
          border-bottom: 2px solid rgba(26, 26, 46, 0.1);
        }

        .info-card-header h3 {
          font-size: 1.3rem;
          font-weight: 600;
          color: #1a1a2e;
          margin: 0;
        }

        .header-icon {
          color: #1a1a2e;
          font-size: 1.5rem;
          background: linear-gradient(135deg, #1a1a2e, #2c2c6c);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .info-card-desc {
          font-size: 0.95rem;
          line-height: 1.6;
          color: #555;
          margin-bottom: 20px;
        }

        .info-stats {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 15px;
          margin-bottom: 20px;
        }

        .info-stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          padding: 15px 10px;
          border-radius: 8px;
          background: rgba(26, 26, 46, 0.04);
          transition: all 0.3s ease;
        }

        .info-stat-item:hover {
          transform: translateY(-5px);
          background: rgba(26, 26, 46, 0.08);
          box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
        }

        .info-stat-item i {
          font-size: 1.8rem;
          color: #1a1a2e;
          margin-bottom: 5px;
        }

        .stat-number {
          font-weight: 700;
          font-size: 1.8rem;
          color: #1a1a2e;
          line-height: 1;
          display: flex;
          align-items: baseline;
        }

        .stat-number span {
          font-size: 1rem;
          margin-left: 2px;
          font-weight: 400;
          color: #555;
        }

        .stat-text {
          font-size: 0.9rem;
          color: #555;
          margin-top: 3px;
        }

        .info-cert-title {
          font-size: 1.1rem;
          font-weight: 600;
          color: #1a1a2e;
          margin-bottom: 15px;
          padding-bottom: 5px;
          border-bottom: 1px solid rgba(26, 26, 46, 0.1);
        }

        .info-certs {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 10px;
        }

        .info-cert-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          padding: 15px 10px;
          border-radius: 8px;
          background: rgba(26, 26, 46, 0.04);
          transition: all 0.3s ease;
        }

        .info-cert-item:hover {
          background: rgba(26, 26, 46, 0.08);
          transform: translateY(-3px);
          box-shadow: 0 8px 15px rgba(0, 0, 0, 0.05);
        }

        .info-cert-item i {
          font-size: 1.8rem;
          color: #1a1a2e;
          margin-bottom: 8px;
        }

        .cert-name {
          font-weight: 600;
          font-size: 1rem;
          color: #1a1a2e;
          margin-bottom: 3px;
          display: block;
        }

        .cert-desc {
          font-size: 0.8rem;
          color: #555;
          display: block;
        }

        .factory-slides {
          position: relative;
          width: 100%;
          height: 100%;
        }

        .factory-slide {
          position: absolute !important;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          opacity: 0 !important;
          visibility: hidden !important;
          transition: opacity 0.8s ease, visibility 0.8s ease;
        }

        .factory-slide.active {
          opacity: 1 !important;
          visibility: visible !important;
          z-index: 1;
        }

        .slide-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.5));
          z-index: 1;
        }

        .slide-caption {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          color: white;
          padding: 25px 80px 25px 25px;
          font-size: 1.2rem;
          font-weight: 300;
          z-index: 2;
          transform: translateY(20px);
          opacity: 0 !important;
          visibility: hidden !important;
          transition: all 0.8s ease 0.3s;
        }

        .factory-slide.active .slide-caption {
          transform: translateY(0);
          opacity: 1 !important;
          visibility: visible !important;
        }

        .factory-slide-controls {
          position: absolute;
          bottom: 55px;
          left: 0;
          right: 0;
          z-index: 10;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 25px;
        }

        .factory-slider-dots {
          display: flex;
          gap: 12px;
        }

        .factory-dot {
          width: 35px;
          height: 3px;
          background-color: rgba(255, 255, 255, 0.3);
          cursor: pointer;
          transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .factory-dot.active {
          width: 50px;
          background-color: white;
        }

        .factory-slider-arrows {
          display: flex;
          gap: 15px;
        }

        .factory-arrow {
          width: 56px;
          height: 56px;
          border-radius: 50%;
          background: rgba(26, 26, 46, 0.6);
          backdrop-filter: blur(15px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
          font-size: 1rem;
          position: relative;
          overflow: hidden;
          margin: 0 15px;
        }

        .factory-arrow:before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: radial-gradient(
            circle at center,
            rgba(255, 255, 255, 0.3) 0%,
            rgba(255, 255, 255, 0) 70%
          );
          opacity: 0;
          transition: opacity 0.4s ease;
        }

        .factory-arrow:hover {
          background: rgba(26, 26, 46, 0.8);
          transform: scale(1.1);
          border-color: rgba(255, 255, 255, 0.4);
        }

        .factory-arrow:hover:before {
          opacity: 1;
        }

        .factory-arrow i {
          transition: transform 0.3s ease;
        }

        .factory-arrow.prev:hover i {
          transform: translateX(-3px);
        }

        .factory-arrow.next:hover i {
          transform: translateX(3px);
        }

        @media (max-width: 768px) {
          .section-title {
            font-size: 2.8rem;
          }

          .factory-slider-container {
            height: 600px;
          }
        }

        @media (max-width: 576px) {
          .section-title {
            font-size: 2.2rem;
          }

          .factory-slider-container {
            height: 500px;
          }
        }
      `}</style>
    </div>
  );
}
