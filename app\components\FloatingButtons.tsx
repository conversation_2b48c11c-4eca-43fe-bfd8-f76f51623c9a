'use client';

import { useState, useEffect } from 'react';
import { useLanguage } from './LanguageProvider';
import HydrationErrorBoundary from './HydrationErrorBoundary';

export default function FloatingButtons() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeTooltip, setActiveTooltip] = useState<number | null>(null);
  const { locale, t } = useLanguage();

  // 监听滚动显示"回到顶部"按钮
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  // 加载图标字体
  useEffect(() => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css';
    document.head.appendChild(link);

    return () => {
      document.head.removeChild(link);
    };
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  const handleTooltipShow = (index: number) => {
    setActiveTooltip(index);
  };

  const handleTooltipHide = () => {
    setActiveTooltip(null);
  };

  // 使用HydrationErrorBoundary组件完全解决水合错误
  return (
    <div className="floating-buttons">
      {/* WhatsApp按钮 */}
      <a
        href={`https://wa.me/message/H5N6NTWXKVYNA1`}
        target="_blank"
        rel="noopener noreferrer"
        className="floating-btn whatsapp-btn"
        aria-label="Contact us on WhatsApp"
        onMouseEnter={() => handleTooltipShow(0)}
        onMouseLeave={handleTooltipHide}
        onFocus={() => handleTooltipShow(0)}
        onBlur={handleTooltipHide}
      >
        <i className="fab fa-whatsapp"></i>
        <span className={`btn-tooltip ${activeTooltip === 0 ? 'active' : ''}`}>
          <HydrationErrorBoundary>
            <span className="tooltip-text">{t('common.whatsapp')}</span>
          </HydrationErrorBoundary>
        </span>
        <span className="btn-bg"></span>
      </a>

      {/* 邮件按钮 */}
      <a
        href="mailto:<EMAIL>"
        className="floating-btn email-btn"
        aria-label="Email us"
        onMouseEnter={() => handleTooltipShow(1)}
        onMouseLeave={handleTooltipHide}
        onFocus={() => handleTooltipShow(1)}
        onBlur={handleTooltipHide}
      >
        <i className="fas fa-envelope"></i>
        <span className={`btn-tooltip ${activeTooltip === 1 ? 'active' : ''}`}>
          <HydrationErrorBoundary>
            <span className="tooltip-text">{t('common.email')}</span>
          </HydrationErrorBoundary>
        </span>
        <span className="btn-bg"></span>
      </a>

      {/* 返回顶部按钮 */}
      <button
        className={`floating-btn top-btn ${isVisible ? 'visible' : ''}`}
        onClick={scrollToTop}
        aria-label="Back to top"
        onMouseEnter={() => handleTooltipShow(2)}
        onMouseLeave={handleTooltipHide}
        onFocus={() => handleTooltipShow(2)}
        onBlur={handleTooltipHide}
      >
        <i className="fas fa-arrow-up"></i>
        <span className={`btn-tooltip ${activeTooltip === 2 ? 'active' : ''}`}>
          <HydrationErrorBoundary>
            <span className="tooltip-text">{t('common.top')}</span>
          </HydrationErrorBoundary>
        </span>
        <span className="btn-bg"></span>
      </button>

      <style jsx>{`
        .floating-buttons {
          position: fixed;
          right: 30px;
          bottom: 30px;
          display: flex;
          flex-direction: column;
          gap: 15px;
          z-index: 999;
        }

        .floating-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 55px;
          height: 55px;
          border-radius: 50%;
          color: white;
          border: none;
          cursor: pointer;
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
          transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
          opacity: 0.95;
          position: relative;
          overflow: hidden;
          text-decoration: none;
          transform: translateZ(0);
        }

        .floating-btn:hover {
          transform: translateY(-6px);
          opacity: 1;
          box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .floating-btn i {
          font-size: 1.3rem;
          position: relative;
          z-index: 2;
          transition: transform 0.3s ease;
        }

        .floating-btn:hover i {
          transform: scale(1.1);
        }

        .btn-bg {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 0.2) 0%,
            rgba(255, 255, 255, 0) 60%
          );
          opacity: 0;
          transition: opacity 0.3s ease;
          z-index: 1;
        }

        .floating-btn:hover .btn-bg {
          opacity: 1;
        }

        .whatsapp-btn {
          background: linear-gradient(135deg, #25d366, #128c7e);
          animation: slideIn 0.5s cubic-bezier(0.165, 0.84, 0.44, 1) 0.1s both;
        }

        .email-btn {
          background: linear-gradient(135deg, #4285f4, #0f62fe);
          animation: slideIn 0.5s cubic-bezier(0.165, 0.84, 0.44, 1) 0.2s both;
        }

        .top-btn {
          background: linear-gradient(135deg, #1a1a2e, #2a2a5e);
          opacity: 0;
          transform: scale(0.8) translateY(20px);
          pointer-events: none;
          transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .top-btn.visible {
          opacity: 0.95;
          transform: scale(1) translateY(0);
          pointer-events: auto;
        }

        .top-btn.visible:hover {
          transform: translateY(-6px);
        }

        .btn-tooltip {
          position: absolute;
          right: 70px;
          background-color: #1a1a2e;
          color: white;
          padding: 0;
          border-radius: 8px;
          font-size: 0.9rem;
          white-space: nowrap;
          pointer-events: none;
          opacity: 0;
          transform: translateX(10px);
          transition: all 0.3s ease;
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          max-width: 0;
        }

        .tooltip-text {
          padding: 8px 16px;
          display: block;
        }

        .btn-tooltip.active {
          opacity: 1;
          transform: translateX(0);
          max-width: 200px;
        }

        .btn-tooltip:after {
          content: '';
          position: absolute;
          right: -8px;
          top: 50%;
          transform: translateY(-50%);
          border-width: 8px 0 8px 8px;
          border-style: solid;
          border-color: transparent transparent transparent #1a1a2e;
        }

        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateX(20px);
          }
          to {
            opacity: 0.95;
            transform: translateX(0);
          }
        }

        @keyframes pulse {
          0% {
            box-shadow: 0 0 0 0 rgba(26, 26, 46, 0.4);
          }
          70% {
            box-shadow: 0 0 0 10px rgba(26, 26, 46, 0);
          }
          100% {
            box-shadow: 0 0 0 0 rgba(26, 26, 46, 0);
          }
        }

        @media (max-width: 768px) {
          .floating-buttons {
            right: 20px;
            bottom: 20px;
            gap: 12px;
          }

          .floating-btn {
            width: 50px;
            height: 50px;
          }
        }

        @media (max-width: 576px) {
          .floating-buttons {
            right: 15px;
            bottom: 15px;
            gap: 10px;
          }

          .floating-btn {
            width: 45px;
            height: 45px;
          }

          .floating-btn i {
            font-size: 1.1rem;
          }

          .btn-tooltip {
            display: none;
          }
        }
      `}</style>
    </div>
  );
}
