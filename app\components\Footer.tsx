"use client"

import { motion } from "framer-motion"
import { Facebook, Twitter, Instagram, Linkedin, Mail, Phone, MapPin } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useState } from "react"
import { useTranslation } from '../utils/useTranslation';

export default function Footer() {
  const { t, locale } = useTranslation();
  const [hoveredSection, setHoveredSection] = useState<string | null>(null)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  }

  const linkVariants = {
    rest: { x: 0 },
    hover: { x: 5, transition: { duration: 0.2 } },
  }

  const sections = [
    {
      title: t('footer.services'),
      links: [
        { text: t('footer.holographic_purchase_guide'), href: `/${locale}/pages/how-to-purchase-your-first-holographic-system` },
        { text: t('footer.custom_design_guide'), href: `/${locale}/pages/custom-playground-design` },
        { text: t('footer.quality_control'), href: `/${locale}/pages/quality-control` },
        { text: t('footer.safe_standard'), href: `/${locale}/pages/safe-standard` },
        { text: t('footer.marketing_support'), href: `/${locale}/pages/marketing-support` }
      ],
    },
    {
      title: t('footer.solutions'),
      links: [
        { text: t('footer.holographic'), href: `/${locale}/collections/holographic-projection` },
        { text: t('footer.custom_solutions'), href: `/${locale}/pages/custom-playground-design` },
        { text: t('footer.interactive'), href: `/${locale}/collections/interactive-projection` }
      ],
    },
    {
      title: t('footer.blog'),
      links: [
        { text: t('footer.blog_posts'), href: `/${locale}/blog` },
        { text: t('footer.news'), href: `/${locale}/blog/news` },
        { text: t('footer.case_studies'), href: `/${locale}/blog/case-studies` }
      ],
    },
  ]

  const socialIcons = [
    { icon: Facebook, href: "#", label: "Facebook" },
    { icon: Twitter, href: "#", label: "Twitter" },
    { icon: Instagram, href: "#", label: "Instagram" },
    { icon: Linkedin, href: "#", label: "LinkedIn" },
  ]

  return (
    <footer className="bg-white py-12 px-6">
      <motion.div
        className="max-w-7xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
          {/* Dynamic Sections */}
          {sections.map((section, index) => (
            <motion.div key={section.title} variants={itemVariants}>
              <motion.h3 className="text-lg font-semibold text-gray-900 mb-6" whileHover={{ scale: 1.05 }}>
                {section.title}
              </motion.h3>
              <ul className="space-y-4">
                {section.links.map((link) => (
                  <motion.li key={link.href} variants={linkVariants} initial="rest" whileHover="hover">
                    <Link href={link.href} className="text-gray-600 hover:text-gray-900 transition-colors">
                      {link.text}
                    </Link>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          ))}

          {/* Contact Us Section */}
          <motion.div variants={itemVariants}>
            <motion.h3 className="text-lg font-semibold text-gray-900 mb-6" whileHover={{ scale: 1.05 }}>
              {t('footer.contact_us')}
            </motion.h3>
            <div className="space-y-4">
              <motion.div
                className="flex items-start gap-3"
                whileHover={{ x: 5 }}
                transition={{ duration: 0.2 }}
              >
                <MapPin className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600">{t('contact.address')}</span>
              </motion.div>
              <motion.div
                className="flex items-center gap-3"
                whileHover={{ x: 5 }}
                transition={{ duration: 0.2 }}
              >
                <Mail className="w-5 h-5 text-blue-500 flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="text-gray-600 hover:text-gray-900 transition-colors">
                  <EMAIL>
                </a>
              </motion.div>
              <motion.div
                className="flex items-center gap-3"
                whileHover={{ x: 5 }}
                transition={{ duration: 0.2 }}
              >
                <Phone className="w-5 h-5 text-blue-500 flex-shrink-0" />
                <a href="tel:+8615989399197" className="text-gray-600 hover:text-gray-900 transition-colors">
                  +86 15989399197
                </a>
              </motion.div>
            </div>
          </motion.div>
        </div>

        {/* Company Info & Logo Section */}
        <motion.div
          variants={itemVariants}
          className="mt-12 pt-8 border-t border-gray-200"
        >
          <div className="flex flex-col lg:flex-row items-center justify-center gap-8">
            <motion.div className="flex-shrink-0" whileHover={{ scale: 1.05 }} transition={{ duration: 0.3 }}>
              <Image
                src="/images/junsheng-logo-1.png"
                alt="Junsheng Logo"
                width={300}
                height={120}
                priority
                className="object-contain max-w-[300px]"
                style={{ width: 'auto', height: 'auto' }}
              />
            </motion.div>
            <motion.p
              className="text-gray-600 text-sm leading-relaxed max-w-2xl"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.8 }}
            >
              {t('footer.company_description')}
            </motion.p>
          </div>
        </motion.div>

        {/* Divider */}
        <motion.div
          className="my-8 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
        />

        {/* Bottom Section */}
        <motion.div
          className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0"
          variants={itemVariants}
        >
          <motion.p
            className="text-gray-500 text-sm"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.7 }}
          >
            © 2025 Guangzhou Junsheng Technology Co., Ltd. {t('footer.rights_reserved')}
          </motion.p>

          <motion.div
            className="flex space-x-4"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            {socialIcons.map((social, index) => (
              <motion.a
                key={social.label}
                href={social.href}
                className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-gray-600 hover:bg-blue-500 hover:text-white transition-all duration-300"
                whileHover={{
                  scale: 1.1,
                  rotate: 5,
                  boxShadow: "0 0 20px rgba(59, 130, 246, 0.3)",
                }}
                whileTap={{ scale: 0.95 }}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.9 + index * 0.1 }}
              >
                <social.icon className="w-5 h-5" />
              </motion.a>
            ))}
          </motion.div>
        </motion.div>
      </motion.div>
    </footer>
  );
}
