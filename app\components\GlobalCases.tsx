'use client';

import { useState, useEffect, useRef, useMemo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useLanguage } from './LanguageProvider';

interface CaseStudy {
  id: string;
  title: string;
  location: string;
  image: string;
  category: string;
  url: string;
  date?: string;
}

export default function GlobalCases() {
  const { locale, t } = useLanguage();
  const [activeFilter, setActiveFilter] = useState('all');
  const [filteredCases, setFilteredCases] = useState<CaseStudy[]>([]);
  const [animateCards, setAnimateCards] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);

  // 轮播相关状态
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const carouselRef = useRef<HTMLDivElement>(null);

  // 大图轮播数据
  const carouselImages = useMemo(() => [
    {
      src: '/images/cases/0052.jpg',
      title: t('cases.carousel.slide1.title', { fallback: '全息投影展览案例' }),
      description: t('cases.carousel.slide1.description', { fallback: '沉浸式全息投影技术展示' })
    },
    {
      src: '/images/cases/0053.jpg',
      title: t('cases.carousel.slide2.title', { fallback: '互动投影体验' }),
      description: t('cases.carousel.slide2.description', { fallback: '创新互动投影解决方案' })
    },
    {
      src: '/images/cases/0054.jpg',
      title: t('cases.carousel.slide3.title', { fallback: '数字餐厅案例' }),
      description: t('cases.carousel.slide3.description', { fallback: '全息投影餐厅体验' })
    },
    {
      src: '/images/cases/0055.jpg',
      title: t('cases.carousel.slide4.title', { fallback: '企业展厅案例' }),
      description: t('cases.carousel.slide4.description', { fallback: '企业级全息展示方案' })
    },
    {
      src: '/images/cases/0056.jpg',
      title: t('cases.carousel.slide5.title', { fallback: '酒店空间案例' }),
      description: t('cases.carousel.slide5.description', { fallback: '酒店全息投影装饰' })
    },
    {
      src: '/images/cases/0057.jpg',
      title: t('cases.carousel.slide6.title', { fallback: '零售空间案例' }),
      description: t('cases.carousel.slide6.description', { fallback: '零售环境全息应用' })
    },
    {
      src: '/images/cases/0058.jpg',
      title: t('cases.carousel.slide7.title', { fallback: '文化展示案例' }),
      description: t('cases.carousel.slide7.description', { fallback: '文化场馆全息投影' })
    },
    {
      src: '/images/cases/0059.jpg',
      title: t('cases.carousel.slide8.title', { fallback: '科技馆案例' }),
      description: t('cases.carousel.slide8.description', { fallback: '科技展示全息方案' })
    },
    {
      src: '/images/cases/0060.jpg',
      title: t('cases.carousel.slide9.title', { fallback: '教育培训案例' }),
      description: t('cases.carousel.slide9.description', { fallback: '教育行业全息应用方案' })
    },
    {
      src: '/images/cases/0061.jpg',
      title: t('cases.carousel.slide10.title', { fallback: '医疗健康案例' }),
      description: t('cases.carousel.slide10.description', { fallback: '医疗领域全息投影技术' })
    },
    {
      src: '/images/cases/0062.jpg',
      title: t('cases.carousel.slide11.title', { fallback: '娱乐休闲案例' }),
      description: t('cases.carousel.slide11.description', { fallback: '娱乐场所全息体验设计' })
    },
    {
      src: '/images/cases/0063.jpg',
      title: t('cases.carousel.slide12.title', { fallback: '商业展示案例' }),
      description: t('cases.carousel.slide12.description', { fallback: '商业空间全息展示方案' })
    },
    {
      src: '/images/cases/0064.jpg',
      title: t('cases.carousel.slide13.title', { fallback: '文旅景区案例' }),
      description: t('cases.carousel.slide13.description', { fallback: '旅游景区全息投影应用' })
    },
    {
      src: '/images/cases/0065.jpg',
      title: t('cases.carousel.slide14.title', { fallback: '智慧城市案例' }),
      description: t('cases.carousel.slide14.description', { fallback: '城市规划全息展示系统' })
    },
    {
      src: '/images/cases/0066.jpg',
      title: t('cases.carousel.slide15.title', { fallback: '工业制造案例' }),
      description: t('cases.carousel.slide15.description', { fallback: '工业领域全息技术应用' })
    },
    {
      src: '/images/cases/0067.jpg',
      title: t('cases.carousel.slide16.title', { fallback: '艺术创作案例' }),
      description: t('cases.carousel.slide16.description', { fallback: '艺术展览全息投影创新' })
    }
  ], [t]);

  // 案例数据
  const caseStudies: CaseStudy[] = useMemo(
    () => [
      {
        id: 'case1',
        title: t('cases.case1.title', { fallback: '黄山真趣小镇光影乐园' }),
        location: t('cases.case1.location', { fallback: '中国，安徽' }),
        image: '/images/solutions/case1.jpg',
        category: 'exhibition',
        url: `/${locale}/products`,
        date: 'January, 2022',
      },
      {
        id: 'case2',
        title: t('cases.case2.title', { fallback: '江上园光影艺术展' }),
        location: t('cases.case2.location', { fallback: '中国，福州' }),
        image: '/images/solutions/case2.jpg',
        category: 'interactive',
        url: `/${locale}/products`,
        date: 'November, 2019',
      },
      {
        id: 'case3',
        title: t('cases.case3.title', { fallback: '惠州华贸天地"格光之城"' }),
        location: t('cases.case3.location', { fallback: '中国，惠州' }),
        image: '/images/solutions/case3.jpg',
        category: 'restaurant',
        url: `/${locale}/products`,
        date: 'November, 2019',
      },
      {
        id: 'case4',
        title: t('cases.case4.title', { fallback: '鸡西革命烈士纪念馆' }),
        location: t('cases.case4.location', { fallback: '中国，鸡西' }),
        image: '/images/solutions/case4.jpg',
        category: 'corporate',
        url: `/${locale}/products`,
        date: 'February, 2024',
      },
      {
        id: 'case5',
        title: t('cases.case5.title', { fallback: '嬉水米花堂儿童光影农艺主题乐园' }),
        location: t('cases.case5.location', { fallback: '中国，嬉水' }),
        image: '/images/solutions/case5.jpg',
        category: 'hotel',
        url: `/${locale}/products`,
        date: 'February, 2024',
      },
      {
        id: 'case6',
        title: t('cases.case6.title', { fallback: '黑龙江省科技馆' }),
        location: t('cases.case6.location', { fallback: '中国，哈尔滨' }),
        image: '/images/solutions/case6.jpg',
        category: 'retail',
        url: `/${locale}/products`,
        date: 'November, 2019',
      },
    ],
    [t, locale]
  );

  // 过滤类别
  const categories = [
    { id: 'all', name: t('cases.filter.all', { fallback: '全部' }) },
    { id: 'exhibition', name: t('cases.filter.exhibition', { fallback: '展览' }) },
    { id: 'interactive', name: t('cases.filter.interactive', { fallback: '互动投影' }) },
    { id: 'restaurant', name: t('cases.filter.restaurant', { fallback: '餐厅' }) },
    { id: 'corporate', name: t('cases.filter.corporate', { fallback: '企业' }) },
    { id: 'hotel', name: t('cases.filter.hotel', { fallback: '酒店' }) },
    { id: 'retail', name: t('cases.filter.retail', { fallback: '零售' }) },
  ];

  // 添加滚动动画
  useEffect(() => {
    const handleScroll = () => {
      const section = sectionRef.current;
      if (!section) return;

      const sectionTop = section.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;

      if (sectionTop < windowHeight * 0.75) {
        section.classList.add('visible');
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // 初始检查

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // 添加获取分类名称的函数
  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category ? category.name : '';
  };

  // 过滤案例
  useEffect(() => {
    setAnimateCards(false);

    setTimeout(() => {
      if (activeFilter === 'all') {
        setFilteredCases(caseStudies);
      } else {
        setFilteredCases(caseStudies.filter(c => c.category === activeFilter));
      }

      setTimeout(() => {
        setAnimateCards(true);
      }, 50);
    }, 300);
  }, [activeFilter, caseStudies]);

  // 初始化显示全部案例
  useEffect(() => {
    setFilteredCases(caseStudies);

    setTimeout(() => {
      setAnimateCards(true);
    }, 500);
  }, [caseStudies]);

  // 轮播自动播放
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % carouselImages.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, carouselImages.length]);

  // 轮播控制函数
  const nextSlide = () => {
    setCurrentSlide(prev => (prev + 1) % carouselImages.length);
  };

  const prevSlide = () => {
    setCurrentSlide(prev => (prev - 1 + carouselImages.length) % carouselImages.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  return (
    <div className="cases-container" ref={sectionRef}>
      <div className="cases-header">
        <h2 className="section-title">
          <span className="thin-text" suppressHydrationWarning>{t('cases.title_prefix', { fallback: '经典' })} </span>
          <strong suppressHydrationWarning>{t('cases.title_main', { fallback: '案例' })}</strong>
        </h2>

        <p className="section-description" suppressHydrationWarning>
          {t('cases.description', {
            fallback: '我们为全球客户提供创新的全息投影解决方案，满足各行业的独特需求',
          })}
        </p>

        <div className="elegant-divider">
          <span className="divider-dot"></span>
        </div>
      </div>

      {/* 大图轮播展示 */}
      <div className="cases-carousel" ref={carouselRef}>
        <div className="carousel-container">
          <div
            className="carousel-track"
            style={{ transform: `translateX(-${currentSlide * 100}%)` }}
          >
            {carouselImages.map((image, index) => (
              <div key={index} className="carousel-slide">
                <div className="carousel-image">
                  <Image
                    src={image.src}
                    alt={image.title}
                    fill
                    style={{ objectFit: 'cover' }}
                    priority={index === 0}
                    sizes="100vw"
                  />
                  <div className="carousel-overlay">
                    <div className="carousel-content">
                      <h3 suppressHydrationWarning>{image.title}</h3>
                      <p suppressHydrationWarning>{image.description}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 轮播控制按钮 */}
          <button
            className="carousel-btn carousel-btn-prev"
            onClick={prevSlide}
            onMouseEnter={() => setIsAutoPlaying(false)}
            onMouseLeave={() => setIsAutoPlaying(true)}
          >
            <i className="fas fa-chevron-left"></i>
          </button>
          <button
            className="carousel-btn carousel-btn-next"
            onClick={nextSlide}
            onMouseEnter={() => setIsAutoPlaying(false)}
            onMouseLeave={() => setIsAutoPlaying(true)}
          >
            <i className="fas fa-chevron-right"></i>
          </button>

          {/* 轮播指示器 */}
          <div className="carousel-indicators">
            {carouselImages.map((_, index) => (
              <button
                key={index}
                className={`carousel-indicator ${index === currentSlide ? 'active' : ''}`}
                onClick={() => goToSlide(index)}
                onMouseEnter={() => setIsAutoPlaying(false)}
                onMouseLeave={() => setIsAutoPlaying(true)}
              />
            ))}
          </div>
        </div>
      </div>

      <div className="filter-tabs">
        {categories.map(category => (
          <button
            key={category.id}
            className={`filter-btn ${activeFilter === category.id ? 'active' : ''}`}
            onClick={() => setActiveFilter(category.id)}
          >
            {category.name}
            {activeFilter === category.id && <span className="active-indicator"></span>}
          </button>
        ))}
      </div>

      <div className="cases-grid">
        {filteredCases.map((caseItem, index) => (
          <Link
            href={caseItem.url}
            key={caseItem.id}
            className={`case-card ${animateCards ? 'animate' : ''}`}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className="case-image">
              <Image
                src={caseItem.image}
                alt={caseItem.title}
                fill
                style={{ objectFit: 'cover' }}
                loading="lazy"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
              <div className="image-overlay">
                <span className="view-case-btn">
                  {t('cases.view_case_btn', { fallback: '查看案例' })}
                </span>
              </div>
              <div className="case-badge">{getCategoryName(caseItem.category)}</div>
              <div className="case-date-location">
                <div className="case-location">
                  <i className="fas fa-map-marker-alt"></i> {caseItem.location}
                </div>
                {caseItem.date && <div className="case-date">{caseItem.date}</div>}
              </div>
            </div>
            <div className="case-info">
              <h3 className="case-title">{caseItem.title}</h3>
              <div className="case-footer">
                <div className="case-line"></div>
                <div className="case-action">
                  <span className="view-more">
                    {t('cases.view_details_btn', { fallback: '查看详情' })}
                  </span>
                  <i className="fas fa-arrow-right"></i>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      <div className="cases-footer">
        <Link href={`/${locale}/products`} className="view-all-btn">
          <span>{t('cases.view_all_btn', { fallback: '查看全部案例' })}</span>
          <i className="fas fa-long-arrow-alt-right"></i>
        </Link>
      </div>

      <style jsx>{`
        .cases-container {
          max-width: 100%;
          margin: 0 auto;
          padding: 100px 30px;
          opacity: 0;
          transform: translateY(30px);
          transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .cases-container.visible {
          opacity: 1;
          transform: translateY(0);
        }

        .cases-header {
          text-align: center;
          margin-bottom: 60px;
          max-width: 1200px;
          margin-left: auto;
          margin-right: auto;
        }

        .section-title {
          font-size: 42px;
          margin-bottom: 24px;
          line-height: 1.2;
          color: #1a1a2e;
        }

        .thin-text {
          font-weight: 200;
        }

        .section-title strong {
          font-weight: 500;
          position: relative;
        }

        .section-description {
          max-width: 700px;
          margin: 0 auto 30px;
          font-size: 1.1rem;
          line-height: 1.7;
          color: #555;
          font-weight: 300;
        }

        .elegant-divider {
          position: relative;
          width: 80px;
          height: 2px;
          background: linear-gradient(90deg, rgba(26, 26, 46, 0.1), #1a1a2e, rgba(26, 26, 46, 0.1));
          margin: 0 auto;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .divider-dot {
          position: absolute;
          width: 8px;
          height: 8px;
          background-color: #1a1a2e;
          border-radius: 50%;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }

        /* 轮播样式 */
        .cases-carousel {
          margin-bottom: 80px;
          max-width: 1400px;
          margin-left: auto;
          margin-right: auto;
          position: relative;
          border-radius: 20px;
          overflow: hidden;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .carousel-container {
          position: relative;
          width: 100%;
          height: 600px;
          overflow: hidden;
        }

        .carousel-track {
          display: flex;
          width: 100%;
          height: 100%;
          transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .carousel-slide {
          min-width: 100%;
          height: 100%;
          position: relative;
        }

        .carousel-image {
          width: 100%;
          height: 100%;
          position: relative;
        }

        .carousel-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(
            to top,
            rgba(0, 0, 0, 0.8) 0%,
            rgba(0, 0, 0, 0.4) 50%,
            transparent 100%
          );
          padding: 60px 80px;
          z-index: 2;
        }

        .carousel-content h3 {
          font-size: 2.5rem;
          font-weight: 600;
          color: white;
          margin-bottom: 15px;
          text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .carousel-content p {
          font-size: 1.2rem;
          color: rgba(255, 255, 255, 0.9);
          line-height: 1.6;
          max-width: 600px;
          text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
        }

        .carousel-btn {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 60px;
          height: 60px;
          background: rgba(255, 255, 255, 0.15);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          color: white;
          font-size: 1.2rem;
          cursor: pointer;
          transition: all 0.3s ease;
          z-index: 3;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .carousel-btn:hover {
          background: rgba(255, 255, 255, 0.25);
          transform: translateY(-50%) scale(1.1);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .carousel-btn-prev {
          left: 30px;
        }

        .carousel-btn-next {
          right: 30px;
        }

        .carousel-indicators {
          position: absolute;
          bottom: 30px;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          gap: 12px;
          z-index: 3;
        }

        .carousel-indicator {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.4);
          border: none;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .carousel-indicator.active {
          background: white;
          transform: scale(1.2);
          box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
        }

        .carousel-indicator:hover {
          background: rgba(255, 255, 255, 0.7);
          transform: scale(1.1);
        }

        .filter-tabs {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          margin-bottom: 60px;
          gap: 15px;
        }

        .filter-btn {
          padding: 12px 28px;
          background: transparent;
          border: 1px solid rgba(26, 26, 46, 0.08);
          border-radius: 8px;
          cursor: pointer;
          font-size: 0.95rem;
          color: #555;
          transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
          font-weight: 400;
          position: relative;
          overflow: hidden;
        }

        .filter-btn:hover {
          background: rgba(26, 26, 46, 0.02);
          color: #1a1a2e;
          border-color: rgba(26, 26, 46, 0.15);
          transform: translateY(-2px);
        }

        .filter-btn.active {
          background: #1a1a2e;
          color: white;
          border-color: #1a1a2e;
          box-shadow: 0 8px 25px rgba(26, 26, 46, 0.15);
          font-weight: 500;
          transform: translateY(-3px);
        }

        .active-indicator {
          position: absolute;
          bottom: 0;
          left: 50%;
          width: 6px;
          height: 6px;
          background-color: white;
          border-radius: 50%;
          transform: translateX(-50%) translateY(15px);
          opacity: 0;
          transition: all 0.3s ease;
        }

        .filter-btn.active .active-indicator {
          opacity: 1;
          transform: translateX(-50%) translateY(12px);
        }

        .cases-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 25px;
          margin-bottom: 70px;
        }

        .case-card {
          text-decoration: none;
          color: inherit;
          overflow: hidden;
          border-radius: 12px;
          background: white;
          transform: translateY(40px);
          opacity: 0;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
          border: 1px solid rgba(26, 26, 46, 0.05);
          transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .case-card.animate {
          transform: translateY(0);
          opacity: 1;
        }

        .case-card:hover {
          transform: translateY(-10px);
          box-shadow: 0 20px 40px rgba(26, 26, 46, 0.1);
          border-color: rgba(26, 26, 46, 0.1);
        }

        .case-image {
          height: 380px;
          overflow: hidden;
          position: relative;
        }

        .case-badge {
          position: absolute;
          top: 15px;
          left: 15px;
          padding: 6px 14px;
          background: rgba(26, 26, 46, 0.85);
          color: white;
          font-size: 0.75rem;
          font-weight: 500;
          border-radius: 30px;
          z-index: 2;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          backdrop-filter: blur(5px);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .case-date-location {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          padding: 15px;
          background: linear-gradient(to top, rgba(26, 26, 46, 0.7), transparent);
          color: white;
          z-index: 2;
        }

        .case-location {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 0.9rem;
          margin-bottom: 4px;
        }

        .case-location i {
          font-size: 0.8rem;
        }

        .case-date {
          font-size: 0.85rem;
          opacity: 0.8;
        }

        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            to bottom,
            rgba(26, 26, 46, 0.2) 0%,
            rgba(26, 26, 46, 0.7) 100%
          );
          z-index: 1;
          opacity: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .case-card:hover .image-overlay {
          opacity: 1;
        }

        .view-case-btn {
          display: inline-block;
          padding: 12px 24px;
          background: rgba(255, 255, 255, 0.15);
          backdrop-filter: blur(10px);
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 30px;
          font-size: 0.9rem;
          font-weight: 500;
          transition: all 0.3s ease;
          transform: translateY(20px);
          opacity: 0;
          transition-delay: 0.1s;
        }

        .case-card:hover .view-case-btn {
          transform: translateY(0);
          opacity: 1;
        }

        .case-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.7s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .case-card:hover .case-image img {
          transform: scale(1.05);
        }

        .case-info {
          padding: 25px 30px;
          background: white;
          position: relative;
        }

        .case-title {
          font-size: 1.4rem;
          font-weight: 500;
          margin-bottom: 20px;
          line-height: 1.4;
          color: #1a1a2e;
          transition: color 0.3s ease;
        }

        .case-card:hover .case-title {
          color: #1a1a2e;
        }

        .case-footer {
          display: flex;
          flex-direction: column;
        }

        .case-line {
          width: 0;
          height: 1px;
          background: linear-gradient(90deg, #1a1a2e, rgba(26, 26, 46, 0.3));
          margin-bottom: 15px;
          transition: width 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .case-card:hover .case-line {
          width: 60px;
        }

        .case-action {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .view-more {
          font-size: 0.95rem;
          color: #1a1a2e;
          font-weight: 500;
          transition: all 0.3s ease;
        }

        .case-action i {
          font-size: 0.9rem;
          color: #1a1a2e;
          transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .case-card:hover .case-action i {
          transform: translateX(8px);
        }

        .cases-footer {
          text-align: center;
          margin-top: 60px;
        }

        .view-all-btn {
          display: inline-flex;
          align-items: center;
          gap: 15px;
          padding: 18px 48px;
          background: linear-gradient(135deg, #1a1a2e, #2a2a5e);
          color: white;
          text-decoration: none;
          font-size: 1rem;
          font-weight: 500;
          transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
          letter-spacing: 1px;
          border-radius: 8px;
          border: none;
          box-shadow: 0 10px 30px rgba(26, 26, 46, 0.2);
          position: relative;
          overflow: hidden;
        }

        .view-all-btn::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.7s;
        }

        .view-all-btn:hover::before {
          left: 100%;
        }

        .view-all-btn:hover {
          transform: translateY(-5px);
          box-shadow: 0 15px 35px rgba(26, 26, 46, 0.25);
        }

        .view-all-btn i {
          font-size: 1.1rem;
          transition: transform 0.3s ease;
        }

        .view-all-btn:hover i {
          transform: translateX(5px);
        }

        @media (max-width: 1600px) {
          .cases-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
          }

          .case-image {
            height: 350px;
          }
        }

        @media (max-width: 1100px) {
          .cases-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
          }
        }

        @media (max-width: 768px) {
          .section-title {
            font-size: 2.5rem;
          }

          .filter-tabs {
            flex-wrap: nowrap;
            overflow-x: auto;
            padding-bottom: 15px;
            justify-content: flex-start;
          }

          .filter-btn {
            padding: 10px 20px;
            font-size: 0.9rem;
            white-space: nowrap;
          }

          .case-image {
            height: 320px;
          }

          .cases-container {
            padding: 80px 15px;
          }

          /* 轮播响应式 */
          .carousel-container {
            height: 400px;
          }

          .carousel-overlay {
            padding: 40px 30px;
          }

          .carousel-content h3 {
            font-size: 1.8rem;
          }

          .carousel-content p {
            font-size: 1rem;
          }

          .carousel-btn {
            width: 50px;
            height: 50px;
            font-size: 1rem;
          }

          .carousel-btn-prev {
            left: 15px;
          }

          .carousel-btn-next {
            right: 15px;
          }

          .carousel-indicators {
            bottom: 20px;
          }

          .carousel-indicator {
            width: 10px;
            height: 10px;
          }
        }

        @media (max-width: 576px) {
          .cases-grid {
            grid-template-columns: 1fr;
            gap: 30px;
          }

          .section-title {
            font-size: 2.2rem;
          }

          .case-image {
            height: 280px;
          }

          /* 小屏幕轮播样式 */
          .carousel-container {
            height: 350px;
          }

          .carousel-overlay {
            padding: 30px 20px;
          }

          .carousel-content h3 {
            font-size: 1.5rem;
          }

          .carousel-content p {
            font-size: 0.9rem;
          }

          .carousel-btn {
            width: 45px;
            height: 45px;
            font-size: 0.9rem;
          }

          .carousel-btn-prev {
            left: 10px;
          }

          .carousel-btn-next {
            right: 10px;
          }

          .carousel-indicators {
            bottom: 15px;
            gap: 8px;
          }

          .carousel-indicator {
            width: 8px;
            height: 8px;
          }
        }
      `}</style>
    </div>
  );
}
