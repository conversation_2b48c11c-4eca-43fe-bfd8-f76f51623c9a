'use client';

import { useEffect, useRef, useState } from 'react';
import createGlobe from 'cobe';

interface GlobeProps {
  className?: string;
}

export default function Globe({ className = '' }: GlobeProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [pointerInteracting, setPointerInteracting] = useState<number | null>(null);
  const [pointerInteractionMovement, setPointerInteractionMovement] = useState(0);
  const [width, setWidth] = useState(0);
  
  let phi = 0;
  let x = 0;

  const onResize = () => {
    if (canvasRef.current) {
      setWidth(canvasRef.current.offsetWidth);
    }
  };

  const onRender = (state: any) => {
    if (!pointerInteracting) {
      phi += 0.005;
    }
    state.phi = phi + x;
    state.width = width * 2;
    state.height = width * 2;
  };

  useEffect(() => {
    if (!canvasRef.current) return;

    window.addEventListener('resize', onResize);
    onResize();

    const globe = createGlobe(canvasRef.current, {
      devicePixelRatio: 2,
      width: width,
      height: width,
      phi: 0,
      theta: 0.3,
      dark: 1,
      diffuse: 0.4,
      mapSamples: 16000,
      mapBrightness: 1.2,
      baseColor: [0.1, 0.1, 0.2], // 深蓝色基调
      markerColor: [0, 1, 1], // 青色标记点，符合全息投影主题
      glowColor: [0.2, 0.6, 1], // 蓝色光晕
      markers: [
        // 北美洲
        { location: [40.7128, -74.006], size: 0.08 }, // 纽约
        { location: [34.0522, -118.2437], size: 0.06 }, // 洛杉矶
        
        // 南美洲
        { location: [-23.5505, -46.6333], size: 0.05 }, // 圣保罗
        { location: [19.4326, -99.1332], size: 0.04 }, // 墨西哥城
        
        // 欧洲
        { location: [51.5074, -0.1278], size: 0.06 }, // 伦敦
        { location: [48.8566, 2.3522], size: 0.05 }, // 巴黎
        
        // 非洲
        { location: [30.0444, 31.2357], size: 0.05 }, // 开罗
        { location: [-26.2041, 28.0473], size: 0.04 }, // 约翰内斯堡
        
        // 亚洲
        { location: [39.9042, 116.4074], size: 0.08 }, // 北京
        { location: [35.6762, 139.6503], size: 0.06 }, // 东京
        { location: [19.076, 72.8777], size: 0.05 }, // 孟买
        { location: [1.3521, 103.8198], size: 0.04 }, // 新加坡
        
        // 大洋洲
        { location: [-33.8688, 151.2093], size: 0.05 }, // 悉尼
      ],
      onRender: onRender
    });

    return () => {
      window.removeEventListener('resize', onResize);
      globe.destroy();
    };
  }, [width]);

  const handlePointerDown = (e: React.PointerEvent) => {
    setPointerInteracting(e.clientX - pointerInteractionMovement);
    if (canvasRef.current) {
      canvasRef.current.style.cursor = 'grabbing';
    }
  };

  const handlePointerUp = () => {
    setPointerInteracting(null);
    if (canvasRef.current) {
      canvasRef.current.style.cursor = 'grab';
    }
  };

  const handlePointerOut = () => {
    setPointerInteracting(null);
    if (canvasRef.current) {
      canvasRef.current.style.cursor = 'grab';
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (pointerInteracting !== null) {
      const delta = e.clientX - pointerInteracting;
      setPointerInteractionMovement(delta);
      x = delta / 200;
    }
  };

  return (
    <div className={`relative mx-auto aspect-square w-full max-w-[600px] ${className}`}>
      <canvas
        ref={canvasRef}
        className="h-full w-full cursor-grab [contain:layout_paint_size]"
        onPointerDown={handlePointerDown}
        onPointerUp={handlePointerUp}
        onPointerOut={handlePointerOut}
        onMouseMove={handleMouseMove}
      />
    </div>
  );
}
