'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useLanguage } from './LanguageProvider';
import { i18n } from '../utils/i18n';
import { createPortal } from 'react-dom';
import DropdownMenu from './DropdownMenu';

// 是否为开发环境
const isDev = process.env.NODE_ENV === 'development';

// 添加导航接口
interface SubMenuItem {
  _id: string;
  name: string;
  slug: string;
  url: string;
  order: number;
  translations: {
    zh?: { name: string };
    en?: { name: string };
  };
}

interface MainNavItem {
  _id: string;
  name: string;
  slug: string;
  order: number;
  isActive: boolean;
  subItems: SubMenuItem[];
  translations: {
    zh?: { name: string };
    en?: { name: string };
  };
}

// 保留分类接口用于兼容性
interface FeaturedCategory {
  _id: string;
  name: string;
  slug: string;
  description: string;
  isActive: boolean;
  type: string;
  order: number;
  featuredType: string; // 添加导航类型字段
}

/**
 * 网站顶部导航组件
 * 实现了响应式导航栏、下拉菜单和移动端菜单
 *
 * @returns 导航栏组件
 */
export default function Header() {
  const { locale, changeLanguage, t, isHydrated } = useLanguage();
  const [mobileMenuActive, setMobileMenuActive] = useState(false);
  const [isSticky, setIsSticky] = useState(false);
  const [lastScrollTop, setLastScrollTop] = useState(0);
  const [headerVisible, setHeaderVisible] = useState(true);
  const [languageDropdownOpen, setLanguageDropdownOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  // 添加导航数据状态
  const [navigationData, setNavigationData] = useState<MainNavItem[]>([]);
  const [productCategories, setProductCategories] = useState<SubMenuItem[]>([]);
  const [serviceCategories, setServiceCategories] = useState<SubMenuItem[]>([]);
  const [solutionCategories, setSolutionCategories] = useState<SubMenuItem[]>([]);

  // 移动菜单子菜单状态
  const [activeSubmenus, setActiveSubmenus] = useState<{ [key: string]: boolean }>({});

  // 添加下拉菜单显示状态 - 默认为null
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  // 添加导航项引用
  const productsRef = useRef<HTMLLIElement>(null);
  const servicesRef = useRef<HTMLLIElement>(null);
  const solutionsRef = useRef<HTMLLIElement>(null);

  // 添加防抖定时器引用
  const dropdownTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 添加语言选择器的引用
  const languageSelectorRef = useRef<HTMLDivElement>(null);

  /**
   * 处理导航项鼠标进入事件
   * 清除任何现有的隐藏定时器并显示对应的下拉菜单
   *
   * @param dropdownName 要显示的下拉菜单名称
   */
  const handleMouseEnter = (dropdownName: string) => {
    // 清除之前的定时器
    if (dropdownTimerRef.current) {
      clearTimeout(dropdownTimerRef.current);
      dropdownTimerRef.current = null;
    }

    // 立即设置活动下拉菜单
    setActiveDropdown(dropdownName);
  };

  /**
   * 处理导航项鼠标离开事件
   * 设置定时器延迟隐藏下拉菜单，防止菜单闪烁
   */
  const handleMouseLeave = () => {
    // 添加延时以防止菜单闪烁
    dropdownTimerRef.current = setTimeout(() => {
      setActiveDropdown(null);
    }, 150);
  };

  // 在组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (dropdownTimerRef.current) {
        clearTimeout(dropdownTimerRef.current);
      }
    };
  }, []);

  // 确保组件已挂载，避免水合错误
  useEffect(() => {
    setMounted(true);
    // 确保初始状态下下拉菜单是关闭的 - 修复白色区块问题
    setActiveDropdown(null);
  }, []);

  // 额外添加一个延迟初始化的useEffect，确保下拉菜单不会在初始加载时显示
  useEffect(() => {
    // 页面加载后，立即将activeDropdown设为null
    if (mounted) {
      setActiveDropdown(null);
    }
  }, [mounted]);

  // 获取导航数据
  const fetchNavigationData = useCallback(async (forceRefresh = false) => {
    if (!mounted) return;

    try {
      // 添加缓存逻辑，避免频繁请求
      const cacheKey = 'navigation_data_cache';
      const cacheTimeKey = 'navigation_data_cache_time';
      const cacheExpiry = 30 * 1000; // 30秒缓存，开发环境下更快更新

      // 在开发环境下强制刷新，清除缓存
      const isDev = process.env.NODE_ENV === 'development';
      if ((isDev || forceRefresh) && typeof window !== 'undefined') {
        localStorage.removeItem(cacheKey);
        localStorage.removeItem(cacheTimeKey);
      }

      if (!forceRefresh && !isDev && typeof window !== 'undefined') {
        const cachedData = localStorage.getItem(cacheKey);
        const cacheTime = localStorage.getItem(cacheTimeKey);

        if (cachedData && cacheTime) {
          const timeDiff = Date.now() - parseInt(cacheTime);
          if (timeDiff < cacheExpiry) {
            // 使用缓存数据
            const data = JSON.parse(cachedData);
            setNavigationData(data);

            // 提取各类型的子菜单，只显示活跃的导航项
            const productsNav = data.find((nav: MainNavItem) => nav.slug === 'products' && nav.isActive);
            const servicesNav = data.find((nav: MainNavItem) => nav.slug === 'services' && nav.isActive);
            const solutionsNav = data.find((nav: MainNavItem) => nav.slug === 'solutions' && nav.isActive);

            setProductCategories(productsNav?.subItems || []);
            setServiceCategories(servicesNav?.subItems || []);
            setSolutionCategories(solutionsNav?.subItems || []);
            return;
          }
        }
      }

      // 获取导航数据，添加时间戳强制刷新
      const response = await fetch(`/api/navigation?t=${Date.now()}`, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setNavigationData(data.data);

          // 提取各类型的子菜单，只显示活跃的导航项
          const productsNav = data.data.find((nav: MainNavItem) => nav.slug === 'products' && nav.isActive);
          const servicesNav = data.data.find((nav: MainNavItem) => nav.slug === 'services' && nav.isActive);
          const solutionsNav = data.data.find((nav: MainNavItem) => nav.slug === 'solutions' && nav.isActive);

          setProductCategories(productsNav?.subItems || []);
          setServiceCategories(servicesNav?.subItems || []);
          setSolutionCategories(solutionsNav?.subItems || []);

          // 更新缓存（开发环境下不缓存）
          if (typeof window !== 'undefined' && !isDev) {
            localStorage.setItem(cacheKey, JSON.stringify(data.data));
            localStorage.setItem(cacheTimeKey, Date.now().toString());
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch navigation data:', error);
    }
  }, [mounted]);

  // 获取导航数据 - 初始加载和定期刷新
  useEffect(() => {
    // 首次加载时获取数据
    fetchNavigationData();

    // 设置定时器每30秒刷新一次（开发环境下更频繁）
    const refreshInterval = setInterval(() => {
      fetchNavigationData();
    }, 30000); // 30秒刷新一次

    // 清理函数
    return () => clearInterval(refreshInterval);
  }, [fetchNavigationData]);

  // 切换移动菜单
  const toggleMobileMenu = () => {
    setMobileMenuActive(!mobileMenuActive);
    document.body.classList.toggle('menu-open');
  };

  // 切换子菜单
  const toggleSubmenu = (id: string) => {
    setActiveSubmenus(prev => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // 监听滚动实现粘性头部
  const handleScroll = useCallback(() => {
    if (!mounted) return;

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    // 滚动超过50像素时激活粘性导航（降低阈值使导航栏更快变为不透明）
    if (scrollTop > 50) {
      setIsSticky(true);

      // 向下滚动时隐藏导航，向上滚动时显示导航
      if (scrollTop > lastScrollTop + 10) {
        // 添加一点阈值，避免小幅度滚动触发
        setHeaderVisible(false);
      } else if (scrollTop < lastScrollTop - 10) {
        setHeaderVisible(true);
      }
    } else {
      // 回到顶部时重置状态，变为透明
      setIsSticky(false);
      setHeaderVisible(true);
    }

    setLastScrollTop(scrollTop);
  }, [mounted, lastScrollTop, setIsSticky, setHeaderVisible, setLastScrollTop]);

  useEffect(() => {
    if (!mounted) return;

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [mounted, handleScroll]);

  // 点击外部关闭移动菜单
  const handleClickOutsideMobileMenu = useCallback(
    (e: MouseEvent) => {
      if (!mounted) return;

      const mobileMenu = document.querySelector('.mobile-menu');
      const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');

      if (mobileMenuActive && mobileMenu && mobileMenuToggle) {
        if (
          !mobileMenu.contains(e.target as Node) &&
          e.target !== mobileMenuToggle &&
          !mobileMenuToggle.contains(e.target as Node)
        ) {
          setMobileMenuActive(false);
          document.body.classList.remove('menu-open');
        }
      }
    },
    [mounted, mobileMenuActive, setMobileMenuActive]
  );

  useEffect(() => {
    if (!mounted) return;

    document.addEventListener('click', handleClickOutsideMobileMenu);
    return () => document.removeEventListener('click', handleClickOutsideMobileMenu);
  }, [mounted, handleClickOutsideMobileMenu]);

  // 点击外部关闭语言下拉菜单
  const handleClickOutsideLanguageDropdown = useCallback(
    (e: MouseEvent) => {
      if (!mounted) return;

      if (
        languageDropdownOpen &&
        languageSelectorRef.current &&
        !languageSelectorRef.current.contains(e.target as Node)
      ) {
        setLanguageDropdownOpen(false);
      }
    },
    [mounted, languageDropdownOpen, languageSelectorRef, setLanguageDropdownOpen]
  );

  useEffect(() => {
    if (!mounted) return;

    document.addEventListener('click', handleClickOutsideLanguageDropdown);
    return () => document.removeEventListener('click', handleClickOutsideLanguageDropdown);
  }, [mounted, handleClickOutsideLanguageDropdown]);

  // 计算语言切换下拉菜单的位置
  const getLanguageDropdownPosition = () => {
    if (!languageSelectorRef.current || !mounted) return { top: 0, left: 0 };

    const rect = languageSelectorRef.current.getBoundingClientRect();
    const windowWidth = typeof window !== 'undefined' ? window.innerWidth : 1200;

    // 计算下拉菜单位置
    let left = rect.left;
    const minWidth = 120;

    // 防止菜单超出视口右侧
    if (left + minWidth > windowWidth) {
      left = Math.max(0, rect.right - minWidth);
    }

    return {
      top: rect.bottom,
      left,
    };
  };

  /**
   * 为产品导航生成额外的菜单项
   * 现在菜单项通过导航API提供，不需要额外项目
   *
   * @returns 额外的菜单项节点
   */
  const generateProductExtraItems = () => {
    return null; // 产品菜单项现在通过导航API提供，避免重复
  };

  /**
   * 为服务导航生成额外的菜单项
   *
   * @returns 额外的菜单项节点
   */
  const generateServiceExtraItems = () => {
    return null; // 服务菜单没有额外项目
  };

  /**
   * 为解决方案导航生成额外的菜单项
   * 现在菜单项通过导航API提供，不需要额外项目
   *
   * @returns 额外的菜单项节点
   */
  const generateSolutionExtraItems = () => {
    return null; // 解决方案菜单项现在通过导航API提供，避免重复
  };

  // 添加移动端菜单函数
  // 修改移动菜单中的产品分类
  const renderMobileProductCategories = () => {
    return (
      <div className={`mobile-submenu ${activeSubmenus['products'] ? 'active' : ''}`}>
        <ul>
          {productCategories.map(category => {
            const href = category.url ? `/${locale}${category.url}` : `/${locale}/products/${category.slug}`;
            // 获取翻译后的名称，优先使用translations，回退到name
            const displayName = category.translations?.[locale]?.name || category.name;
            return (
              <li key={category._id} className="mobile-submenu-item">
                <Link
                  href={href}
                  className="mobile-submenu-link"
                  onClick={() => setMobileMenuActive(false)}
                >
                  {displayName}
                </Link>
              </li>
            );
          })}
        </ul>
      </div>
    );
  };

  // 修改移动菜单中的服务分类
  const renderMobileServiceCategories = () => {
    if (serviceCategories.length === 0) return null;

    return (
      <div className={`mobile-submenu ${activeSubmenus['services'] ? 'active' : ''}`}>
        <ul>
          {serviceCategories.map(category => {
            const href = category.url ? `/${locale}${category.url}` : `/${locale}/service/${category.slug}`;
            // 获取翻译后的名称，优先使用translations，回退到name
            const displayName = category.translations?.[locale]?.name || category.name;
            return (
              <li key={category._id} className="mobile-submenu-item">
                <Link
                  href={href}
                  className="mobile-submenu-link"
                  onClick={() => setMobileMenuActive(false)}
                >
                  {displayName}
                </Link>
              </li>
            );
          })}
        </ul>
      </div>
    );
  };

  // 修改移动菜单中的解决方案分类
  const renderMobileSolutionCategories = () => {
    return (
      <div className={`mobile-submenu ${activeSubmenus['solutions'] ? 'active' : ''}`}>
        <ul>
          {solutionCategories.map(category => {
            const href = category.url ? `/${locale}${category.url}` : `/${locale}/products?category=${category.slug}`;
            // 获取翻译后的名称，优先使用translations，回退到name
            const displayName = category.translations?.[locale]?.name || category.name;
            return (
              <li key={category._id} className="mobile-submenu-item">
                <Link
                  href={href}
                  className="mobile-submenu-link"
                  onClick={() => setMobileMenuActive(false)}
                >
                  {displayName}
                </Link>
              </li>
            );
          })}
        </ul>
      </div>
    );
  };

  // 如果未挂载，返回一个静态占位符以避免水合错误
  if (!mounted || !isHydrated) {
    return (
      <header className="header">
        <div className="header-main">
          <div className="container">
            <div className="logo">
              <Image
                src="/images/logo.png"
                alt="JUNSHENG TECH"
                width={120}
                height={35}
                priority
                className="logo-image"
                style={{ width: 'auto', height: '35px' }}
              />
            </div>
            <div className="nav-container"></div>
            <div className="mobile-menu-toggle">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </header>
    );
  }

  // 修改PC端主导航的渲染，使用通用的DropdownMenu组件
  return (
    <header className="header">
      <div
        className={`header-main ${isSticky ? 'sticky' : 'transparent'}`}
        style={{
          transform: isSticky ? (headerVisible ? 'translateY(0)' : 'translateY(-100%)') : 'none',
          transition: 'transform 0.3s ease, background-color 0.3s ease',
        }}
      >
        <div className="container">
          <div className="logo">
            <Link href={`/${locale}`}>
              <Image
                src="/images/logo.png"
                alt="JUNSHENG TECH"
                width={120}
                height={35}
                priority
                className="logo-image"
                style={{ width: 'auto', height: '35px' }}
              />
            </Link>
          </div>
          <div className="nav-container">
            <nav className="main-nav">
              <ul className="nav-list">
                <li className="nav-item">
                  <Link href={`/${locale}`} className="nav-link">
                    {t('common.home')}
                  </Link>
                </li>
                <li
                  className="nav-item has-dropdown"
                  ref={productsRef}
                  onMouseEnter={() => handleMouseEnter('products')}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link href={`/${locale}/products`} className="nav-link">
                    {t('common.products')}
                    <i
                      className={`fas fa-chevron-down ${activeDropdown === 'products' ? 'rotate-180' : ''}`}
                    ></i>
                  </Link>
                  <DropdownMenu
                    key={`products-${locale}`}
                    items={productCategories}
                    navItem={productsRef.current}
                    isActive={activeDropdown === 'products'}
                    dropdownName="products"
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                    locale={locale}
                    basePath="products"
                    extraItems={generateProductExtraItems()}
                    closeMenu={() => setMobileMenuActive(false)}
                  />
                </li>
                {serviceCategories.length > 0 && (
                  <li
                    className="nav-item has-dropdown"
                    ref={servicesRef}
                    onMouseEnter={() => handleMouseEnter('services')}
                    onMouseLeave={handleMouseLeave}
                  >
                    <Link href={`/${locale}/pages/service`} className="nav-link">
                      {t('common.services')}
                      <i
                        className={`fas fa-chevron-down ${activeDropdown === 'services' ? 'rotate-180' : ''}`}
                      ></i>
                    </Link>
                    <DropdownMenu
                      key={`services-${locale}`}
                      items={serviceCategories}
                      navItem={servicesRef.current}
                      isActive={activeDropdown === 'services'}
                      dropdownName="services"
                      onMouseEnter={handleMouseEnter}
                      onMouseLeave={handleMouseLeave}
                      locale={locale}
                      basePath="service"
                      extraItems={generateServiceExtraItems()}
                      closeMenu={() => setMobileMenuActive(false)}
                    />
                  </li>
                )}
                <li
                  className="nav-item has-dropdown"
                  ref={solutionsRef}
                  onMouseEnter={() => handleMouseEnter('solutions')}
                  onMouseLeave={handleMouseLeave}
                >
                  <Link href={`/${locale}/pages/custom-solutions`} className="nav-link">
                    {t('common.solutions')}
                    <i
                      className={`fas fa-chevron-down ${activeDropdown === 'solutions' ? 'rotate-180' : ''}`}
                    ></i>
                  </Link>
                  <DropdownMenu
                    key={`solutions-${locale}`}
                    items={solutionCategories}
                    navItem={solutionsRef.current}
                    isActive={activeDropdown === 'solutions'}
                    dropdownName="solutions"
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                    locale={locale}
                    basePath="products"
                    extraItems={generateSolutionExtraItems()}
                    closeMenu={() => setMobileMenuActive(false)}
                  />
                </li>
                <li className="nav-item">
                  <Link href={`/${locale}/pages/about-us`} className="nav-link">
                    {t('common.about')}
                  </Link>
                </li>
                <li className="nav-item">
                  <Link href={`/${locale}/videos`} className="nav-link">
                    {t('video_gallery.page_title', { fallback: '视频展示' })}
                  </Link>
                </li>
                <li className="nav-item">
                  <Link href={`/${locale}/pages/contact-us`} className="nav-link">
                    {t('common.contact')}
                  </Link>
                </li>
              </ul>
            </nav>
            <div className="header-actions">
              <div className="language-selector" ref={languageSelectorRef}>
                <div
                  className="current-language"
                  onClick={() => setLanguageDropdownOpen(!languageDropdownOpen)}
                >
                  <i className="fas fa-globe"></i>
                  <span>{locale.toUpperCase()}</span>
                  <i
                    className={`fas fa-chevron-down ${languageDropdownOpen ? 'rotate-180' : ''}`}
                  ></i>
                </div>
                {languageDropdownOpen &&
                  mounted &&
                  createPortal(
                    <div
                      className="language-dropdown-portal"
                      style={{
                        position: 'fixed',
                        top: `${getLanguageDropdownPosition().top}px`,
                        left: `${getLanguageDropdownPosition().left}px`,
                        zIndex: 100000,
                        backgroundColor: 'white',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
                        borderRadius: '4px',
                        padding: '5px 0',
                        minWidth: '120px',
                        maxHeight: '80vh',
                        overflowY: 'auto',
                        display: 'block',
                      }}
                    >
                      <ul
                        className="language-list"
                        style={{ listStyle: 'none', margin: 0, padding: 0 }}
                      >
                        {i18n.locales.map(lang => (
                          <li
                            key={lang}
                            style={{
                              padding: '5px 0',
                              backgroundColor: locale === lang ? '#f0f4f8' : 'transparent',
                            }}
                          >
                            <button
                              onClick={() => {
                                changeLanguage(lang);
                                setLanguageDropdownOpen(false);
                              }}
                              style={{
                                display: 'block',
                                width: '100%',
                                padding: '8px 15px',
                                fontSize: '14px',
                                textAlign: 'left',
                                border: 'none',
                                background: 'none',
                                cursor: 'pointer',
                                fontWeight: locale === lang ? 'bold' : 'normal',
                                color: locale === lang ? '#1a65e3' : '#333',
                              }}
                            >
                              {lang === 'en' ? 'English' : lang === 'zh' ? '中文' : lang}
                            </button>
                          </li>
                        ))}
                      </ul>
                    </div>,
                    document.body
                  )}
              </div>
              <div className="quote-button">
                <Link href={`/${locale}/pages/contact-us`} className="btn-quote">
                  {t('home.ctaButton')}
                </Link>
              </div>
            </div>
          </div>
          <div className="mobile-menu-toggle" onClick={toggleMobileMenu}>
            <span className={mobileMenuActive ? 'active' : ''}></span>
            <span className={mobileMenuActive ? 'active' : ''}></span>
            <span className={mobileMenuActive ? 'active' : ''}></span>
          </div>
        </div>
      </div>

      {/* 移动菜单 */}
      <div className={`mobile-menu ${mobileMenuActive ? 'active' : ''}`}>
        <div className="container">
          <nav className="mobile-nav">
            <ul className="mobile-nav-list">
              <li className="mobile-nav-item">
                <Link href={`/${locale}`} className="mobile-nav-link">
                  {t('common.home')}
                </Link>
              </li>
              <li className="mobile-nav-item">
                <div className="mobile-nav-link">
                  <Link href={`/${locale}/products`} className="mobile-link-text">
                    {t('common.products')}
                  </Link>
                  <span
                    className={`mobile-dropdown-toggle ${activeSubmenus['products'] ? 'active' : ''}`}
                    onClick={() => toggleSubmenu('products')}
                  >
                    <i className="fas fa-chevron-down"></i>
                  </span>
                </div>
                {renderMobileProductCategories()}
              </li>
              {serviceCategories.length > 0 && (
                <li className="mobile-nav-item">
                  <div className="mobile-nav-link">
                    <Link href={`/${locale}/pages/service`} className="mobile-link-text">
                      {t('common.services')}
                    </Link>
                    <span
                      className={`mobile-dropdown-toggle ${activeSubmenus['services'] ? 'active' : ''}`}
                      onClick={() => toggleSubmenu('services')}
                    >
                      <i className="fas fa-chevron-down"></i>
                    </span>
                  </div>
                  {renderMobileServiceCategories()}
                </li>
              )}
              <li className="mobile-nav-item">
                <div className="mobile-nav-link">
                                <Link href={`/${locale}/pages/custom-solutions`} className="mobile-link-text">
                {t('common.solutions')}
              </Link>
                  <span
                    className={`mobile-dropdown-toggle ${activeSubmenus['solutions'] ? 'active' : ''}`}
                    onClick={() => toggleSubmenu('solutions')}
                  >
                    <i className="fas fa-chevron-down"></i>
                  </span>
                </div>
                {renderMobileSolutionCategories()}
              </li>
              <li className="mobile-nav-item">
                <Link href={`/${locale}/pages/about-us`} className="mobile-nav-link">
                  {t('common.about')}
                </Link>
              </li>
              <li className="mobile-nav-item">
                <Link href={`/${locale}/videos`} className="mobile-nav-link">
                  {t('video_gallery.page_title', { fallback: '视频展示' })}
                </Link>
              </li>
              <li className="mobile-nav-item">
                <Link href={`/${locale}/pages/contact-us`} className="mobile-nav-link">
                  {t('common.contact')}
                </Link>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </header>
  );
}
