'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import CustomImage from './CustomImage';
import { useLanguage } from './LanguageProvider';

interface SlideData {
  url: string;
  image: string;
  alt: string;
  title: string;
  subtitle: string;
}

export default function HeroSlider() {
  const { locale, t } = useLanguage();
  const [isMounted, setIsMounted] = useState(false);

  // Set isMounted to true on client after hydration
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 使用指定的图片作为轮播图 - 更换为更高清的图片
  const slides: SlideData[] = [
    {
      url: `/${locale}/products`,
      image: '/images/products/holographic-dining-v2-extra-1.jpg', // 全息餐厅效果图 - 高清版本
      alt: isMounted ? t('home.slider.product.alt') : 'Holographic Dining',
      title: isMounted
        ? t('home.slider.product.title')
        : 'Immersive Dining Experience',
      subtitle: isMounted
        ? t('home.slider.product.subtitle')
        : 'Premium holographic projection restaurant solutions, creating unique dining experiences',
    },
    {
      url: `/${locale}/pages/custom-playground-design`,
      image: '/images/products/children-interactive-ball-extra-1.jpg', // 儿童互动球池 - 超高清游乐场设计图
      alt: isMounted ? t('home.slider.design.alt') : 'Custom Playground Design',
      title: isMounted
        ? t('home.slider.design.title')
        : 'Custom Playground Design',
      subtitle: isMounted
        ? t('home.slider.design.subtitle')
        : 'Innovative stage visual solutions, enhancing performance and event impact',
    },
    {
      url: `/${locale}/collections/indoor-playground`,
      image: '/images/products/ar-trampoline-pro-extra-1.jpg', // AR蹦床 - 超高清互动体验图
      alt: isMounted ? t('home.slider.indoor.alt') : 'AR Interactive',
      title: isMounted ? t('home.slider.indoor.title') : 'AR Interactive Experience',
      subtitle: isMounted
        ? t('home.slider.indoor.subtitle')
        : 'Smart interactive solutions combining AR technology, creating next-generation experience spaces',
    },
    {
      url: `/${locale}/products`,
      image: '/images/products/motion-climbing-pro-extra-1.jpg', // 攀岩互动 - 超高清运动体验图
      alt: isMounted ? t('home.slider.climbing.alt') : 'Motion Climbing',
      title: isMounted
        ? t('home.slider.climbing.title')
        : 'Smart Climbing Experience',
      subtitle: isMounted
        ? t('home.slider.climbing.subtitle')
        : 'Innovative climbing solutions combining sports and technology, inspiring unlimited challenge fun',
    },
    {
      url: `/${locale}/products`,
      image: '/images/products/children-interactive-beach-extra-1.jpg', // 互动沙滩 - 超高清海洋主题图
      alt: isMounted ? t('home.slider.beach.alt') : 'Interactive Beach',
      title: isMounted
        ? t('home.slider.beach.title')
        : 'Interactive Ocean World',
      subtitle: isMounted
        ? t('home.slider.beach.subtitle')
        : 'Immersive ocean-themed interactive experience, letting children explore the magical underwater world',
    },
  ];

  const [currentSlide, setCurrentSlide] = useState(0);
  const slideCount = slides.length;

  // 下一张幻灯片
  const nextSlide = useCallback(() => {
    setCurrentSlide(prev => (prev + 1) % slideCount);
  }, [slideCount]);

  // 上一张幻灯片
  const prevSlide = useCallback(() => {
    setCurrentSlide(prev => (prev - 1 + slideCount) % slideCount);
  }, [slideCount]);

  // 选择特定幻灯片
  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  // 自动切换幻灯片
  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 8000); // 增加轮播间隔到8秒

    return () => clearInterval(interval);
  }, [nextSlide]);

  return (
    <div className="hero-slider-wrapper">
      <section className="hero-slider">
        <div className="slider-container">
          {slides.map((slide, index) => (
            <div key={index} className={`slide ${currentSlide === index ? 'active' : ''}`}>
              <Link href={slide.url}>
                <div className="slide-image-container">
                  <CustomImage
                    src={slide.image}
                    alt={slide.alt}
                    fill
                    style={{ objectFit: 'cover' }}
                    className="slide-image"
                    loading={index === 0 ? 'eager' : 'lazy'}
                  />

                  {/* 添加文本内容 */}
                  <div className="slide-content">
                    <div className="content-wrapper">
                      <h2 className="slide-title">{slide.title}</h2>
                      <p className="slide-subtitle">{slide.subtitle}</p>
                      <div className="slide-cta">
                        <span className="cta-text">
                          {t('common.learn_more')}
                        </span>
                        <span className="cta-arrow">→</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}

          {/* 导航点样式 */}
          <div className="slider-dots">
            {slides.map((_, index) => (
              <span
                key={index}
                className={`dot ${currentSlide === index ? 'active' : ''}`}
                onClick={() => goToSlide(index)}
              ></span>
            ))}
          </div>

          {/* 箭头样式 - 移动到两侧 */}
          <div className="slider-arrows">
            <button className="arrow prev" onClick={prevSlide} aria-label="Previous slide">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M19 12H5"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M12 19L5 12L12 5"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
            <button className="arrow next" onClick={nextSlide} aria-label="Next slide">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5 12H19"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M12 5L19 12L12 19"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
      </section>

      {/* 添加样式 */}
      <style jsx>{`
        .hero-slider-wrapper {
          position: relative;
          width: 100vw;
          height: 100vh;
          overflow: hidden;
          margin-left: calc(50% - 50vw);
          margin-right: calc(50% - 50vw);
        }

        .hero-slider {
          position: relative;
          height: 100vh;
          overflow: hidden;
        }

        .slider-container {
          position: relative;
          height: 100%;
        }

        .slide {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          opacity: 0;
          transition: opacity 1.8s cubic-bezier(0.19, 1, 0.22, 1); /* 增加过渡时间 */
          z-index: 0;
        }

        .slide.active {
          opacity: 1;
          z-index: 1;
        }

        .slide-image-container {
          position: relative;
          height: 100vh;
        }

        .slide-image {
          width: 100%;
          height: 100vh;
          object-fit: cover;
          filter: brightness(0.85) contrast(1.1) saturate(1.2); /* 增加饱和度 */
          transform: scale(1);
          transition: transform 12s ease-out; /* 增加过渡时间，让缩放效果更慢更明显 */
        }

        .slide.active .slide-image {
          transform: scale(1.1); /* 增加缩放幅度 */
        }

        /* 添加蒙层渐变效果 */
        .slide-image-container::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.5) 100%);
          z-index: 1;
        }

        /* 添加文本样式 */
        .slide-content {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 2;
          max-width: 800px;
          width: 80%;
          text-align: center;
        }

        /* 标题发光背景效果 */
        .title-glow {
          display: none;
        }

        .content-wrapper {
          opacity: 0;
          transform: translateY(30px);
          position: relative;
        }

        .slide.active .content-wrapper {
          opacity: 1;
          transform: translateY(0);
          transition: all 1.2s ease 0.5s;
        }

        .slide-title {
          color: white;
          font-size: 96px; /* 继续增大字体尺寸从80px到96px */
          font-weight: 700;
          margin-bottom: 35px;
          letter-spacing: -1px;
          line-height: 1.3 !important; /* 使用!important确保覆盖全局h2样式，防止英文文字换行时叠在一起 */
          text-shadow: 0 4px 30px rgba(0, 0, 0, 0.2);
        }

        .slide-subtitle {
          color: rgba(255, 255, 255, 0.9);
          font-size: 32px; /* 继续增大字体尺寸从28px到32px */
          font-weight: 300;
          margin-bottom: 45px;
          line-height: 1.5;
          text-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
        }

        .slide-cta {
          display: inline-flex;
          align-items: center;
          padding: 18px 40px; /* 进一步增大按钮内边距 */
          background-color: rgba(26, 26, 46, 0.7);
          backdrop-filter: blur(10px);
          border-radius: 50px;
          color: white;
          font-size: 22px; /* 继续增大字体尺寸从20px到22px */
          font-weight: 500;
          transition: all 0.3s ease;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
          overflow: hidden;
          position: relative;
        }

        .slide-cta:hover {
          background-color: rgba(255, 255, 255, 0.3);
          transform: translateY(-3px) scale(1.05);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .cta-text {
          margin-right: 8px;
          position: relative;
        }

        .cta-arrow {
          transition: transform 0.3s ease;
        }

        .slide-cta:hover .cta-arrow {
          transform: translateX(5px);
        }

        @keyframes fadeSlideUp {
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .slider-dots {
          position: absolute;
          left: 0;
          right: 0;
          bottom: 40px;
          z-index: 10;
          display: flex;
          gap: 12px;
          justify-content: center;
        }

        .dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.3);
          cursor: pointer;
          transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
          position: relative;
        }

        .dot.active {
          background-color: #fff;
          transform: scale(1.2);
          box-shadow: none;
        }

        .dot:hover {
          background-color: rgba(255, 255, 255, 0.7);
        }

        .slider-arrows {
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          z-index: 10;
          display: flex;
          justify-content: space-between;
          padding: 0 30px;
          transform: translateY(-50%);
          pointer-events: none;
        }

        .arrow {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background-color: rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(5px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          outline: none;
          pointer-events: auto;
        }

        .arrow:hover {
          background-color: rgba(0, 0, 0, 0.5);
          transform: scale(1.1);
        }

        /* 响应式样式调整 */
        @media (max-width: 1366px) {
          .slide-title {
            font-size: 84px; /* 继续增大响应式字体大小 */
            line-height: 1.3 !important; /* 使用!important确保响应式下也能覆盖全局样式 */
          }

          .slide-subtitle {
            font-size: 30px; /* 继续增大响应式字体大小 */
          }
        }

        @media (max-width: 992px) {
          .slide-title {
            font-size: 72px; /* 继续增大响应式字体大小 */
            line-height: 1.3 !important; /* 使用!important确保响应式下也能覆盖全局样式 */
          }

          .slide-subtitle {
            font-size: 28px; /* 继续增大响应式字体大小 */
          }

          .slide-cta {
            font-size: 20px;
            padding: 16px 35px;
          }
        }

        @media (max-width: 768px) {
          .slide-title {
            font-size: 56px; /* 继续增大响应式字体大小 */
            line-height: 1.3 !important; /* 使用!important确保响应式下也能覆盖全局样式 */
            margin-bottom: 25px;
          }

          .slide-subtitle {
            font-size: 26px; /* 继续增大响应式字体大小 */
            margin-bottom: 35px;
          }

          .slide-cta {
            font-size: 19px;
            padding: 15px 32px;
          }
        }

        @media (max-width: 576px) {
          .slide-title {
            font-size: 48px; /* 继续增大响应式字体大小 */
            line-height: 1.3 !important; /* 使用!important确保响应式下也能覆盖全局样式 */
          }

          .slide-subtitle {
            font-size: 24px; /* 继续增大响应式字体大小 */
          }

          .slide-cta {
            font-size: 18px;
            padding: 14px 28px;
          }
        }
      `}</style>
    </div>
  );
}
