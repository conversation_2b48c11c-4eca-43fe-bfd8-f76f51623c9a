'use client';

import { useEffect } from 'react';

/**
 * HighContrastFixer组件
 *
 * 此组件使用JavaScript动态移除所有包含-ms-high-contrast的CSS规则
 * 解决Microsoft Edge浏览器中-ms-high-contrast媒体查询弃用警告
 *
 * 具体操作:
 * 1. 扫描所有样式表
 * 2. 查找包含-ms-high-contrast的媒体查询
 * 3. 移除这些规则
 */
export default function HighContrastFixer() {
  useEffect(() => {
    // 确保在客户端环境中执行
    if (typeof window === 'undefined') return;

    // 1. 动态删除所有包含-ms-high-contrast的媒体查询规则
    function removeHighContrastMediaQueries() {
      try {
        // 遍历所有样式表
        for (let i = 0; i < document.styleSheets.length; i++) {
          const styleSheet = document.styleSheets[i];

          try {
            // 跳过无法访问的样式表（跨域限制）
            if (!styleSheet.cssRules) continue;

            // 遍历所有CSS规则
            for (let j = styleSheet.cssRules.length - 1; j >= 0; j--) {
              const rule = styleSheet.cssRules[j];

              // 检查是否为媒体规则
              if (rule.type === CSSRule.MEDIA_RULE) {
                const mediaRule = rule as CSSMediaRule;
                const mediaText = mediaRule.media?.mediaText || '';

                // 检查是否包含-ms-high-contrast
                if (mediaText.includes('-ms-high-contrast')) {
                  // 删除规则
                  styleSheet.deleteRule(j);
                }
              }
            }
          } catch (e) {
            // 忽略因跨域限制导致的错误
            continue;
          }
        }
      } catch (err) {
        // 忽略错误
      }
    }

    // 2. 添加现代高对比度样式，但不影响正常深色模式
    function addModernHighContrastStyles() {
      const style = document.createElement('style');
      style.textContent = `
        /* 只在高对比度模式下应用特殊样式 */
        @media (forced-colors: active) {
          /* 确保基本可访问性元素仍然适应高对比度模式 */
          a, button, input, select, textarea {
            forced-color-adjust: auto;
          }
        }
      `;
      document.head.appendChild(style);
    }

    // 执行清理操作
    removeHighContrastMediaQueries();
    addModernHighContrastStyles();

    // 监听样式表加载事件，处理动态加载的样式表
    const observer = new MutationObserver(mutations => {
      for (const mutation of mutations) {
        if (mutation.type === 'childList') {
          const addedNodes = Array.from(mutation.addedNodes);
          const hasStylesheet = addedNodes.some(
            node => node.nodeName === 'LINK' && (node as HTMLLinkElement).rel === 'stylesheet'
          );

          if (hasStylesheet) {
            // 延迟执行以确保样式表加载完成
            setTimeout(removeHighContrastMediaQueries, 100);
          }
        }
      }
    });

    // 开始监听文档头部变化
    observer.observe(document.head, { childList: true, subtree: true });

    // 清理函数
    return () => {
      observer.disconnect();
    };
  }, []);

  // 此组件不渲染任何可见内容
  return null;
}
