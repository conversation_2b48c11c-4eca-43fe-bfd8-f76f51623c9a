'use client';

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useLanguage } from './LanguageProvider';

export default function HolographicFeatures() {
  const { t, locale } = useLanguage();
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);
  const autoplayTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Feature data
  const features = useMemo(
    () => [
      {
        id: 'projection',
        title: t('features.projection.title'),
        subtitle: t('features.projection.subtitle'),
        description: t('features.projection.description'),
        image: '/images/products/3d-sandbox-1.jpg',
        icon: 'fa-cube',
        specs: [
          {
            name: t('features.projection.specs.spec1'),
            icon: 'fa-tv',
          },
          {
            name: t('features.projection.specs.spec2'),
            icon: 'fa-eye',
          },
          {
            name: t('features.projection.specs.spec3'),
            icon: 'fa-sun',
          },
        ],
      },
      {
        id: 'interactive',
        title: t('features.interactive.title'),
        subtitle: t('features.interactive.subtitle'),
        description: t('features.interactive.description'),
        image: '/images/products/ar-1.jpg',
        icon: 'fa-hand-pointer',
        specs: [
          {
            name: t('features.interactive.specs.spec1'),
            icon: 'fa-fingerprint',
          },
          {
            name: t('features.interactive.specs.spec2'),
            icon: 'fa-tachometer-alt',
          },
          {
            name: t('features.interactive.specs.spec3'),
            icon: 'fa-hands',
          },
        ],
      },
      {
        id: 'materials',
        title: t('features.materials.title'),
        subtitle: t('features.materials.subtitle'),
        description: t('features.materials.description'),
        image: '/images/products/interactive-football-1.jpg',
        icon: 'fa-mountain',
        specs: [
          {
            name: t('features.materials.specs.spec1'),
            icon: 'fa-shield-alt',
          },
          {
            name: t('features.materials.specs.spec2'),
            icon: 'fa-lightbulb',
          },
          {
            name: t('features.materials.specs.spec3'),
            icon: 'fa-cloud-sun',
          },
        ],
      },
      {
        id: 'software',
        title: t('features.software.title'),
        subtitle: t('features.software.subtitle'),
        description: t('features.software.description'),
        image: '/images/products/hologram-dining-1.jpg',
        icon: 'fa-utensils',
        specs: [
          {
            name: t('features.software.specs.spec1'),
            icon: 'fa-network-wired',
          },
          {
            name: t('features.software.specs.spec2'),
            icon: 'fa-magic',
          },
          {
            name: t('features.software.specs.spec3'),
            icon: 'fa-hand-point-up',
          },
        ],
      },
      {
        id: 'application',
        title: t('features.application.title'),
        subtitle: t('features.application.subtitle'),
        description: t('features.application.description'),
        image: '/images/products/holographic-stage-system-1.jpg',
        icon: 'fa-glass-cheers',
        specs: [
          {
            name: t('features.application.specs.spec1'),
            icon: 'fa-sliders-h',
          },
          {
            name: t('features.application.specs.spec2'),
            icon: 'fa-bolt',
          },
          {
            name: t('features.application.specs.spec3'),
            icon: 'fa-volume-up',
          },
        ],
      },
      {
        id: 'support',
        title: t('features.support.title'),
        subtitle: t('features.support.subtitle'),
        description: t('features.support.description'),
        image: '/images/products/ktv-1.jpg',
        icon: 'fa-building',
        specs: [
          {
            name: t('features.support.specs.spec1'),
            icon: 'fa-drafting-compass',
          },
          {
            name: t('features.support.specs.spec2'),
            icon: 'fa-paint-brush',
          },
          {
            name: t('features.support.specs.spec3'),
            icon: 'fa-headset',
          },
        ],
      },
    ],
    [t, locale]
  );

  // Track active feature
  const [activeFeature, setActiveFeature] = useState('projection');
  // 用于跟踪当前特性在数组中的索引
  const [currentIndex, setCurrentIndex] = useState(0);

  // Get current feature object
  const currentFeature = features.find(f => f.id === activeFeature) || features[0];

  // 加载图标字体
  useEffect(() => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css';
    document.head.appendChild(link);

    return () => {
      document.head.removeChild(link);
    };
  }, []);

  // 添加滚动显示动画
  useEffect(() => {
    const handleScroll = () => {
      const section = sectionRef.current;
      if (!section) return;

      const rect = section.getBoundingClientRect();
      const isElementVisible = rect.top < window.innerHeight * 0.75;

      if (isElementVisible) {
        setIsVisible(true);
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // 初始检查

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 自动轮播
  const goToNextSlide = useCallback(() => {
    setCurrentIndex(prevIndex => {
      const newIndex = (prevIndex + 1) % features.length;
      setActiveFeature(features[newIndex].id);
      return newIndex;
    });
  }, [features]);

  // 启动自动轮播
  useEffect(() => {
    if (isVisible) {
      autoplayTimerRef.current = setInterval(goToNextSlide, 5000);
    }

    return () => {
      if (autoplayTimerRef.current) {
        clearInterval(autoplayTimerRef.current);
      }
    };
  }, [isVisible, goToNextSlide]);

  // 手动特性切换
  const handleFeatureClick = (featureId: string) => {
    // 清除当前的自动轮播定时器
    if (autoplayTimerRef.current) {
      clearInterval(autoplayTimerRef.current);
    }

    // 设置新的活动特性
    setActiveFeature(featureId);

    // 更新当前索引
    const newIndex = features.findIndex(f => f.id === featureId);
    if (newIndex !== -1) {
      setCurrentIndex(newIndex);
    }

    // 重新启动自动轮播
    autoplayTimerRef.current = setInterval(goToNextSlide, 5000);
  };

  return (
    <div className={`features-container ${isVisible ? 'is-visible' : ''}`} ref={sectionRef}>
      <div className="features-inner">
        <div className="section-header">
          <div className="section-badge">
            <span>{t('features.badge')}</span>
          </div>
          <h2 className="section-title">
            <span className="thin-text">{t('features.title_prefix')}</span>
            <strong>{t('features.title_main')}</strong>
          </h2>

          <div className="section-subtitle">
            {t('features.description')}
          </div>

          <div className="elegant-divider">
            <span className="divider-dot"></span>
          </div>
        </div>

        <div className="features-showcase">
          <div className="carousel-indicators">
            {features.map(feature => (
              <button
                key={feature.id}
                className={`indicator ${activeFeature === feature.id ? 'active' : ''}`}
                onClick={() => handleFeatureClick(feature.id)}
                aria-label={feature.title}
              />
            ))}
          </div>

          <div className="features-carousel">
            <div className="carousel-content">
              <div className="feature-content">
                <div className="feature-image-container">
                  <div className="feature-image">
                    <Image
                      src={currentFeature.image}
                      alt={currentFeature.subtitle}
                      fill
                      style={{ objectFit: 'cover' }}
                      priority={currentIndex === 0}
                      loading={currentIndex === 0 ? "eager" : "lazy"}
                    />
                    <div className="image-overlay"></div>

                    <div className="feature-category-label">
                      <span className="category-tag">
                        <i className="fas fa-lightbulb"></i>
                        {t('features.innovative')}
                      </span>
                    </div>

                    <div className="feature-title-label">
                      <h3>{currentFeature.title}</h3>
                      <div className="feature-subtitle">{currentFeature.subtitle}</div>
                    </div>

                    <div className="feature-info-overlay">
                      <div className="feature-info-content">
                        <span className="feature-badge">
                          <i className={`fas ${currentFeature.icon}`}></i>
                          <span>{t('features.innovative')}</span>
                        </span>
                        <h3>{currentFeature.title}</h3>
                        <h4>{currentFeature.subtitle}</h4>
                        <p>{currentFeature.description}</p>

                        <div className="feature-specs">
                          {currentFeature.specs.map(spec => (
                            <div className="spec-item" key={spec.name}>
                              <div className="spec-icon">
                                <i className={`fas ${spec.icon}`}></i>
                              </div>
                              <div className="spec-text">{spec.name}</div>
                            </div>
                          ))}
                        </div>

                        <Link href={`/${locale}/products`} className="learn-more-link">
                          <span>{t('features.learn_more')}</span>
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M5 12H19"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M12 5L19 12L12 19"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </Link>
                      </div>
                    </div>
                  </div>
                  <div className="image-caption">
                    <i className={`fas ${currentFeature.icon}`}></i>
                    <span>{currentFeature.subtitle}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="carousel-controls">
              <button
                className="carousel-arrow prev"
                onClick={() => {
                  const newIndex = (currentIndex - 1 + features.length) % features.length;
                  handleFeatureClick(features[newIndex].id);
                }}
                aria-label={t('features.prev_slide')}
              >
                <i className="fas fa-chevron-left"></i>
              </button>
              <button
                className="carousel-arrow next"
                onClick={() => {
                  const newIndex = (currentIndex + 1) % features.length;
                  handleFeatureClick(features[newIndex].id);
                }}
                aria-label={t('features.next_slide')}
              >
                <i className="fas fa-chevron-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .features-container {
          padding: 0;
          opacity: 0;
          transform: translateY(40px);
          transition: all 1s cubic-bezier(0.19, 1, 0.22, 1);
          position: relative;
          overflow: hidden;
          background-color: #fafafa;
          width: 100%;
        }

        .features-container.is-visible {
          opacity: 1;
          transform: translateY(0);
        }

        .features-container::before {
          content: '';
          position: absolute;
          width: 1000px;
          height: 1000px;
          border-radius: 50%;
          background: radial-gradient(circle, rgba(14, 18, 36, 0.02) 0%, rgba(14, 18, 36, 0) 70%);
          top: -500px;
          right: -300px;
          z-index: 0;
        }

        .features-container::after {
          content: '';
          position: absolute;
          width: 800px;
          height: 800px;
          border-radius: 50%;
          background: radial-gradient(circle, rgba(14, 18, 36, 0.02) 0%, rgba(14, 18, 36, 0) 70%);
          bottom: -400px;
          left: -200px;
          z-index: 0;
        }

        .features-inner {
          max-width: 100%;
          margin: 0 auto;
          padding: 60px 0 70px;
          position: relative;
          z-index: 1;
        }

        .section-header {
          text-align: center;
          margin-bottom: 60px;
        }

        .section-badge {
          display: inline-block;
          background-color: rgba(38, 78, 202, 0.1);
          color: #264eca;
          font-size: 14px;
          font-weight: 500;
          padding: 8px 16px;
          border-radius: 20px;
          margin-bottom: 20px;
        }

        .section-title {
          font-size: 42px;
          margin-bottom: 24px;
          line-height: 1.2;
          color: #1a1a2e;
        }

        .section-subtitle {
          font-size: 18px;
          color: #546e7a;
          max-width: 720px;
          margin: 0 auto;
          line-height: 1.6;
        }

        .thin-text {
          font-weight: 300;
        }

        .section-title strong {
          font-weight: 700;
          color: #1a1a2e;
          position: relative;
          letter-spacing: 0.2px;
        }

        .elegant-divider {
          position: relative;
          width: 50px;
          height: 2px;
          background: linear-gradient(90deg, rgba(26, 26, 46, 0), #1a1a2e, rgba(26, 26, 46, 0));
          margin: 0 auto;
          display: flex;
          align-items: center;
          justify-content: center;
          transform: scaleX(0.5);
          opacity: 0;
          animation: expandDivider 1.2s forwards;
          animation-delay: 0.7s;
        }

        @keyframes expandDivider {
          to {
            opacity: 1;
            transform: scaleX(1);
          }
        }

        .divider-dot {
          position: absolute;
          width: 5px;
          height: 5px;
          background-color: #1a1a2e;
          border-radius: 50%;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }

        .features-showcase {
          display: flex;
          flex-direction: column;
          gap: 20px;
        }

        .carousel-indicators {
          display: flex;
          justify-content: center;
          gap: 10px;
          margin-bottom: 18px;
        }

        .indicator {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: rgba(26, 26, 46, 0.15);
          border: none;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .indicator.active {
          background-color: #1a1a2e;
          transform: scale(1.3);
          box-shadow: 0 0 0 4px rgba(26, 26, 46, 0.1);
        }

        .features-carousel {
          position: relative;
          overflow: visible;
          width: 100%;
        }

        .carousel-content {
          transition: all 0.6s cubic-bezier(0.19, 1, 0.22, 1);
          width: 100%;
        }

        .feature-content {
          width: 100%;
          max-width: 1800px;
          margin: 0 auto;
          padding: 0 40px;
        }

        .feature-image-container {
          width: 100%;
          position: relative;
          box-shadow: 0 35px 80px rgba(0, 0, 0, 0.18);
          border-radius: 16px;
          overflow: hidden;
          transform: translateY(0) perspective(1000px) rotateX(0deg);
          transition:
            transform 0.5s cubic-bezier(0.19, 1, 0.22, 1),
            box-shadow 0.5s ease;
        }

        .feature-image-container:hover {
          transform: translateY(-5px) perspective(1000px) rotateX(2deg);
          box-shadow: 0 40px 90px rgba(0, 0, 0, 0.25);
        }

        .feature-image {
          position: relative;
          border-radius: 16px 16px 0 0;
          overflow: hidden;
          transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
          width: 100%;
          height: 800px;
          margin-bottom: 0;
        }

        .feature-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 10s cubic-bezier(0.19, 1, 0.22, 1);
        }

        .feature-image:hover img {
          transform: scale(1.08);
        }

        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0.05) 0%,
            rgba(0, 0, 0, 0.2) 50%,
            rgba(0, 0, 0, 0.7) 85%,
            rgba(0, 0, 0, 0.85) 100%
          );
          opacity: 0.9;
          transition: opacity 0.4s ease;
        }

        .feature-image:hover .image-overlay {
          opacity: 0.7;
        }

        .feature-category-label {
          position: absolute;
          top: 25px;
          left: 25px;
          z-index: 5;
          transform: translateY(-10px);
          opacity: 0;
          transition: all 0.5s ease;
          transition-delay: 0.1s;
        }

        .feature-image-container:hover .feature-category-label {
          transform: translateY(0);
          opacity: 1;
        }

        .category-tag {
          display: inline-flex;
          align-items: center;
          gap: 8px;
          padding: 10px 18px;
          background-color: rgba(255, 255, 255, 0.15);
          backdrop-filter: blur(8px);
          border-radius: 100px;
          color: white;
          font-size: 0.9rem;
          font-weight: 500;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          transition: all 0.3s ease;
        }

        .category-tag:hover {
          background-color: rgba(255, 255, 255, 0.25);
          transform: translateY(-2px);
        }

        .feature-title-label {
          position: absolute;
          bottom: 50px;
          left: 50px;
          max-width: 60%;
          z-index: 5;
          transform: translateY(20px);
          opacity: 0;
          transition: all 0.5s ease;
          transition-delay: 0.2s;
        }

        .feature-image-container:hover .feature-title-label {
          transform: translateY(0);
          opacity: 1;
        }

        .feature-title-label h3 {
          color: white;
          font-size: 3.2rem;
          font-weight: 700;
          margin-bottom: 10px;
          line-height: 1.2;
          letter-spacing: 0.5px;
          text-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .feature-subtitle {
          color: rgba(255, 255, 255, 0.9);
          font-size: 1.4rem;
          font-weight: 400;
          letter-spacing: 0.3px;
          text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .feature-info-overlay {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 450px;
          background: rgba(0, 0, 0, 0.75);
          backdrop-filter: blur(10px);
          padding: 35px;
          border-radius: 16px 0 0 0;
          opacity: 0;
          transform: translateY(30px);
          transition: all 0.4s ease-out;
          pointer-events: none;
          overflow: auto;
          max-height: 80%;
          box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.2);
          color: white;
          z-index: 10;
        }

        .feature-image:hover .feature-info-overlay {
          opacity: 1;
          transform: translateY(0);
          pointer-events: auto;
        }

        .feature-info-content {
          display: flex;
          flex-direction: column;
          gap: 18px;
        }

        .feature-badge {
          display: inline-flex;
          align-items: center;
          padding: 8px 16px;
          background-color: rgba(255, 255, 255, 0.15);
          border-radius: 100px;
          color: white;
          font-size: 0.9rem;
          font-weight: 500;
          width: max-content;
          transform: translateX(-10px);
          opacity: 0;
          transition: all 0.3s ease;
          transition-delay: 0.1s;
        }

        .feature-image:hover .feature-badge {
          transform: translateX(0);
          opacity: 1;
        }

        .feature-badge i {
          margin-right: 8px;
          font-size: 0.9rem;
        }

        .feature-info-content h3 {
          font-size: 2.2rem;
          color: white;
          margin-bottom: 5px;
          font-weight: 700;
          letter-spacing: 0.5px;
          line-height: 1.2;
          transform: translateX(-10px);
          opacity: 0;
          transition: all 0.3s ease;
          transition-delay: 0.2s;
        }

        .feature-image:hover .feature-info-content h3 {
          transform: translateX(0);
          opacity: 1;
        }

        .feature-info-content h4 {
          font-size: 1.3rem;
          color: rgba(255, 255, 255, 0.85);
          font-weight: 400;
          margin-bottom: 10px;
          letter-spacing: 0.3px;
          transform: translateX(-10px);
          opacity: 0;
          transition: all 0.3s ease;
          transition-delay: 0.3s;
        }

        .feature-image:hover .feature-info-content h4 {
          transform: translateX(0);
          opacity: 1;
        }

        .feature-info-content p {
          font-size: 1rem;
          line-height: 1.6;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 15px;
          letter-spacing: 0.2px;
          transform: translateX(-10px);
          opacity: 0;
          transition: all 0.3s ease;
          transition-delay: 0.4s;
        }

        .feature-image:hover .feature-info-content p {
          transform: translateX(0);
          opacity: 1;
        }

        .feature-specs {
          display: grid;
          grid-template-columns: repeat(1, 1fr);
          gap: 10px;
          margin-top: 5px;
          margin-bottom: 20px;
          opacity: 0;
          transform: translateY(10px);
          transition: all 0.4s ease;
          transition-delay: 0.5s;
        }

        .feature-image:hover .feature-specs {
          opacity: 1;
          transform: translateY(0);
        }

        .spec-item {
          display: flex;
          align-items: center;
          gap: 12px;
          background-color: rgba(255, 255, 255, 0.1);
          padding: 12px 15px;
          border-radius: 8px;
          transition: all 0.3s ease;
        }

        .spec-item:hover {
          background-color: rgba(255, 255, 255, 0.15);
          transform: translateX(5px);
        }

        .spec-icon {
          width: 36px;
          height: 36px;
          min-width: 36px;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 0.9rem;
          transition: all 0.3s ease;
        }

        .spec-item:hover .spec-icon {
          background-color: rgba(255, 255, 255, 0.3);
          transform: scale(1.1);
        }

        .spec-text {
          font-size: 0.9rem;
          font-weight: 500;
          color: white;
        }

        .learn-more-link {
          display: inline-flex;
          align-items: center;
          gap: 10px;
          color: white;
          font-weight: 600;
          font-size: 0.95rem;
          text-decoration: none;
          transition: all 0.3s ease;
          padding: 10px 20px;
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 50px;
          width: max-content;
          opacity: 0;
          transform: translateY(10px);
          transition-delay: 0.6s;
        }

        .feature-image:hover .learn-more-link {
          opacity: 1;
          transform: translateY(0);
        }

        .learn-more-link:hover {
          gap: 12px;
          background-color: rgba(255, 255, 255, 0.3);
        }

        .image-caption {
          position: relative;
          left: 0;
          right: 0;
          bottom: 0;
          height: 80px;
          background: linear-gradient(to right, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.85));
          border-radius: 0 0 16px 16px;
          display: flex;
          align-items: center;
          padding: 0 35px;
          color: white;
          font-size: 22px;
          font-weight: 500;
          letter-spacing: 0.5px;
          transition: all 0.3s ease;
          overflow: hidden;
          z-index: 15;
        }

        .feature-image-container:hover .image-caption {
          background: linear-gradient(to right, rgba(0, 0, 0, 0.98), rgba(0, 0, 0, 0.9));
          box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.15);
        }

        .image-caption::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 1px;
          background: linear-gradient(
            to right,
            rgba(255, 255, 255, 0),
            rgba(255, 255, 255, 0.2),
            rgba(255, 255, 255, 0)
          );
        }

        .image-caption i {
          margin-right: 15px;
          font-size: 1.3rem;
          width: 46px;
          height: 46px;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
          transition: all 0.3s ease;
        }

        .image-caption:hover i {
          transform: scale(1.1);
          background-color: rgba(255, 255, 255, 0.15);
        }

        .carousel-controls {
          position: absolute;
          top: 45%;
          left: 0;
          right: 0;
          transform: translateY(-50%);
          display: flex;
          justify-content: space-between;
          pointer-events: none;
          z-index: 10;
          max-width: 1880px;
          margin: 0 auto;
          width: 100%;
          padding: 0;
        }

        .carousel-arrow {
          width: 64px;
          height: 64px;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.9);
          border: none;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #1a1a2e;
          font-size: 1.2rem;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
          pointer-events: auto;
        }

        .carousel-arrow:hover {
          background-color: #fff;
          transform: scale(1.1);
          box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
        }

        .carousel-arrow.prev {
          margin-left: 20px;
        }

        .carousel-arrow.next {
          margin-right: 20px;
        }

        @media (max-width: 1600px) {
          .feature-image {
            height: 700px;
          }

          .feature-title-label h3 {
            font-size: 2.8rem;
          }
        }

        @media (max-width: 1200px) {
          .features-inner {
            padding: 70px 0 90px;
          }

          .section-title {
            font-size: 36px;
          }

          .section-subtitle {
            font-size: 17px;
          }

          .feature-content {
            padding: 0 30px;
          }

          .feature-image {
            height: 600px;
          }

          .feature-info-overlay {
            width: 400px;
          }

          .feature-info-content h3 {
            font-size: 2rem;
          }

          .feature-title-label h3 {
            font-size: 2.4rem;
          }

          .carousel-arrow {
            width: 56px;
            height: 56px;
          }
        }

        @media (max-width: 992px) {
          .features-inner {
            padding: 60px 0 80px;
          }

          .section-header {
            margin-bottom: 40px;
          }

          .section-title {
            font-size: 32px;
          }

          .section-subtitle {
            font-size: 16px;
          }

          .feature-image {
            height: 380px;
          }

          .feature-info-overlay {
            width: 100%;
            position: relative;
            border-radius: 0;
            opacity: 1;
            transform: translateY(0);
            background-color: rgba(0, 0, 0, 0.9);
            padding: 20px;
          }

          .feature-title-label {
            left: 20px;
            bottom: 30px;
          }

          .feature-title-label h3 {
            font-size: 1.8rem;
          }

          .feature-subtitle {
            font-size: 1.1rem;
          }

          .image-caption {
            display: none;
          }

          .category-tag {
            padding: 8px 15px;
            font-size: 0.85rem;
          }

          .carousel-arrow {
            width: 50px;
            height: 50px;
            font-size: 1rem;
          }

          .carousel-arrow.prev {
            margin-left: 10px;
          }

          .carousel-arrow.next {
            margin-right: 10px;
          }
        }

        @media (max-width: 768px) {
          .section-title {
            font-size: 28px;
          }

          .section-subtitle {
            font-size: 15px;
          }

          .feature-image {
            height: 320px;
          }

          .feature-title-label {
            bottom: 20px;
            left: 15px;
          }

          .feature-title-label h3 {
            font-size: 1.5rem;
            margin-bottom: 5px;
          }

          .feature-subtitle {
            font-size: 1rem;
          }

          .feature-info-content h3 {
            font-size: 1.6rem;
          }

          .feature-info-content h4 {
            font-size: 1.1rem;
          }

          .feature-info-content p {
            font-size: 0.9rem;
          }

          .carousel-arrow {
            width: 44px;
            height: 44px;
            font-size: 0.9rem;
          }

          .carousel-arrow.prev {
            margin-left: 5px;
          }

          .carousel-arrow.next {
            margin-right: 5px;
          }
        }
      `}</style>
    </div>
  );
}
