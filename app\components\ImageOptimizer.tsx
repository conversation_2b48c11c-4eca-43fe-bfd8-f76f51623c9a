import Image, { ImageProps } from 'next/image';

interface OptimizedImageProps extends Omit<ImageProps, 'fill'> {
  fill?: boolean;
  fillSizes?: string;
  maintainAspectRatio?: boolean;
}

/**
 * 优化的Image组件，自动添加sizes属性和处理宽高比问题
 */
export default function OptimizedImage({
  fill,
  fillSizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  maintainAspectRatio,
  style,
  width,
  height,
  ...props
}: OptimizedImageProps) {
  // 如果使用fill属性但没有提供sizes，自动添加
  if (fill) {
    return (
      <Image
        fill={fill}
        sizes={fillSizes}
        style={{
          objectFit: 'cover',
          ...style,
        }}
        {...props}
      />
    );
  }

  // 如果宽高其中一个被修改，确保另一个设为auto以保持宽高比
  if (maintainAspectRatio && style) {
    const modifiedStyle = { ...style };

    if (style.width && !style.height) {
      modifiedStyle.height = 'auto';
    } else if (style.height && !style.width) {
      modifiedStyle.width = 'auto';
    }

    return <Image width={width} height={height} style={modifiedStyle} {...props} />;
  }

  // 默认情况下直接传递所有属性
  return <Image width={width} height={height} style={style} {...props} />;
}
