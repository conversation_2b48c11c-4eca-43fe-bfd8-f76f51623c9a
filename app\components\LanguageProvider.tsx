'use client';

import { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { i18n } from '../utils/i18n';

// Define type for language context
type LanguageContextType = {
  locale: string;
  dictionary: Record<string, unknown>;
  changeLanguage: (newLocale: string) => void;
  t: (key: string, params?: Record<string, string | number | { fallback?: string }>) => string;
  isHydrated: boolean;
};

// Create context with default values to avoid null checks
const defaultContext: LanguageContextType = {
  locale: i18n.defaultLocale,
  dictionary: {},
  changeLanguage: () => {},
  t: key => key,
  isHydrated: false,
};

// Create context
const LanguageContext = createContext<LanguageContextType>(defaultContext);

// Pre-load default dictionary to ensure SSR works consistently
let defaultDictionary: Record<string, unknown> = {};

// Try to preload the default dictionary during module initialization
try {
  // This is a workaround for Next.js to make this work in both SSR and client
  if (typeof require !== 'undefined') {
    // Use dynamic import with catch for better reliability
    try {
      // eslint-disable-next-line @typescript-eslint/no-require-imports -- Workaround for Next.js SSR/client dynamic import
      defaultDictionary = require(`../dictionaries/${i18n.defaultLocale}.json`);
    } catch {
      // 如果无法直接require，尝试使用默认导入
      // eslint-disable-next-line @typescript-eslint/no-require-imports -- Workaround for Next.js SSR/client dynamic import
      const dictModule = require(`../dictionaries/${i18n.defaultLocale}.json`);
      defaultDictionary = dictModule.default || dictModule;
    }

    // Validate that the dictionary has common keys
    if (!defaultDictionary || !defaultDictionary.common) {
      console.warn('Default dictionary missing common keys, falling back to empty dictionary');
      defaultDictionary = { common: {} };
    }
  }
} catch (error) {
  console.error('Error pre-loading default dictionary:', error);
  defaultDictionary = { common: {} };
}

// Provider component
export function LanguageProvider({ children }: { children: ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();

  // Always start with default dictionary for consistent SSR
  const [dictionary, setDictionary] = useState<Record<string, unknown>>(defaultDictionary);

  // Always start with default locale for consistent SSR
  const [locale, setLocale] = useState<string>(i18n.defaultLocale);

  // Add hydration state tracking
  const [isHydrated, setIsHydrated] = useState(false);

  // Determine current language from URL or localStorage
  const getCurrentLocale = useCallback(() => {
    // Try to get from pathname first (works on both server and client)
    if (pathname) {
      for (const loc of i18n.locales) {
        // renamed locale to loc to avoid conflict
        if (pathname.startsWith(`/${loc}/`) || pathname === `/${loc}`) {
          return loc;
        }
      }
    }

    // Try to get from localStorage (client-side only)
    if (typeof window !== 'undefined' && isHydrated) {
      try {
        const storedLocale = localStorage.getItem('NEXT_LOCALE');
        if (storedLocale && i18n.locales.includes(storedLocale)) {
          return storedLocale;
        }
      } catch (error) {
        console.error('Error accessing localStorage:', error);
      }
    }

    // Default to English
    return i18n.defaultLocale;
  }, [pathname, isHydrated]); // Added dependencies for useCallback

  // Mark when component is hydrated on client
  useEffect(() => {
    setIsHydrated(true);
    // Update locale when client-side hydration is complete
    const newLocale = getCurrentLocale();
    if (newLocale !== locale) {
      setLocale(newLocale);
    }
  }, [getCurrentLocale, locale]); // Added getCurrentLocale and locale to dependencies

  // Update locale when pathname changes
  useEffect(() => {
    // Only run this after hydration is complete
    if (isHydrated) {
      const newLocale = getCurrentLocale();
      if (newLocale !== locale) {
        setLocale(newLocale);
      }
    }
  }, [isHydrated, pathname, getCurrentLocale, locale]); // Added getCurrentLocale and locale

  // Load dictionary when locale changes, but only after hydration
  useEffect(() => {
    // Skip if not hydrated yet
    if (!isHydrated) return;

    const loadDictionary = async () => {
      try {
        // Always load dictionary regardless of locale
        const dict = await import(`../dictionaries/${locale}.json`);
        setDictionary(dict.default);
      } catch (error) {
        console.error('Error loading dictionary:', error);
        // Fallback to default locale if there's an error
        if (locale !== i18n.defaultLocale) {
          try {
            const defaultDict = await import(`../dictionaries/${i18n.defaultLocale}.json`);
            setDictionary(defaultDict.default);
          } catch (fallbackError) {
            console.error('Error loading fallback dictionary:', fallbackError);
            setDictionary(defaultDictionary);
          }
        } else {
          setDictionary(defaultDictionary);
        }
      }
    };

    loadDictionary();
  }, [locale, isHydrated]);

  // Translation function - safe for both server and client
  const t = (key: string, params: Record<string, string | number | { fallback?: string }> = {}) => {
    const fallbackString = typeof params.fallback === 'string' ? params.fallback : key;

    if (!isHydrated) {
      return fallbackString;
    }

    if (!key || typeof dictionary !== 'object' || dictionary === null) {
      return fallbackString;
    }

    const keys = key.split('.');
    let currentValue: unknown = dictionary;

    for (const k of keys) {
      if (
        typeof currentValue !== 'object' ||
        currentValue === null ||
        !Object.prototype.hasOwnProperty.call(currentValue, k)
      ) {
        return fallbackString;
      }
      currentValue = (currentValue as Record<string, unknown>)[k];
    }

    if (typeof currentValue !== 'string') {
      return fallbackString;
    }

    let result: string = currentValue;
    Object.entries(params).forEach(([paramKey, paramValue]) => {
      if (
        paramKey !== 'fallback' &&
        (typeof paramValue === 'string' || typeof paramValue === 'number')
      ) {
        result = result.replace(new RegExp(`{{${paramKey}}}`, 'g'), String(paramValue));
      }
    });

    return result;
  };

  // Change language and update URL/localStorage
  const changeLanguage = (newLocale: string) => {
    if (!i18n.locales.includes(newLocale)) {
      console.error(`Language ${newLocale} is not supported`);
      return;
    }

    // Set cookie for middleware compatibility (client-side only)
    if (typeof window !== 'undefined') {
      try {
        document.cookie = `NEXT_LOCALE=${newLocale}; path=/; max-age=31536000`; // 1 year
      } catch (error) {
        console.error('Error setting cookie:', error);
      }
    }

    // Set localStorage for persistence (client-side only)
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('NEXT_LOCALE', newLocale);
      } catch (error) {
        console.error('Error setting localStorage:', error);
      }
    }

    setLocale(newLocale);

    // Redirect to the same path but with new locale
    if (pathname) {
      const currentPath = pathname.replace(/^\/[a-zA-Z-]+/, '') || '/';
      router.push(`/${newLocale}${currentPath}`);
      router.refresh();
    } else {
      // Fallback if pathname is null
      router.push(`/${newLocale}`);
      router.refresh();
    }
  };

  const contextValue = {
    locale,
    dictionary,
    changeLanguage,
    t,
    isHydrated,
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      <div suppressHydrationWarning>{children}</div>
    </LanguageContext.Provider>
  );
}

// Custom hook to use the language context
export function useLanguage() {
  return useContext(LanguageContext);
}
