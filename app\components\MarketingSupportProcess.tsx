'use client';

// import { useLanguage } from './LanguageProvider'; // This import is unused
import { TranslatedHeading2, useTranslatedText } from './TranslatedText';

export default function MarketingSupportProcess() {
  // const { t } = useLanguage(); // This line should be removed or t should be used.

  const steps = [
    {
      number: 1,
      title: useTranslatedText('marketing_support.process.step1.title', '初步咨询'),
      description: useTranslatedText(
        'marketing_support.process.step1.description',
        '我们从详细咨询开始，了解您的业务目标、目标市场和独特卖点。'
      ),
    },
    {
      number: 2,
      title: useTranslatedText('marketing_support.process.step2.title', '策略制定'),
      description: useTranslatedText(
        'marketing_support.process.step2.description',
        '我们的营销团队根据您的特定需求和市场开发定制的营销策略。'
      ),
    },
    {
      number: 3,
      title: useTranslatedText('marketing_support.process.step3.title', '开业前营销'),
      description: useTranslatedText(
        'marketing_support.process.step3.description',
        '我们提供支持，在您的游乐场开业前建立期待并引起轰动。'
      ),
    },
    {
      number: 4,
      title: useTranslatedText('marketing_support.process.step4.title', '盛大开业'),
      description: useTranslatedText(
        'marketing_support.process.step4.description',
        '我们的团队帮助策划和执行成功的盛大开业活动，以最大化初始影响。'
      ),
    },
    {
      number: 5,
      title: useTranslatedText('marketing_support.process.step5.title', '持续支持'),
      description: useTranslatedText(
        'marketing_support.process.step5.description',
        '我们继续提供营销材料、季节性促销创意和业务增长策略。'
      ),
    },
  ];

  return (
    <section className="marketing-process">
      <div className="container">
        <TranslatedHeading2
          className="section-title"
          id="marketing_support.process.title"
          fallback="我们的营销支持流程"
        />

        <div className="process-timeline">
          {steps.map(step => (
            <div className="timeline-item" key={step.number}>
              <div className="timeline-number">{step.number}</div>
              <div className="timeline-content">
                <h3 suppressHydrationWarning>{step.title}</h3>
                <p suppressHydrationWarning>{step.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
