'use client';

import { useLanguage } from './LanguageProvider';

export default function MarketingSupportServices() {
  const { t } = useLanguage();

  const services = [
    {
      icon: 'fas fa-paint-brush',
      title: t('marketing_support.services.brand.title', { fallback: '品牌开发' }),
      items: [
        t('marketing_support.services.brand.item1', { fallback: '标志设计协助' }),
        t('marketing_support.services.brand.item2', { fallback: '品牌形象开发' }),
        t('marketing_support.services.brand.item3', { fallback: '配色方案和主题建议' }),
        t('marketing_support.services.brand.item4', { fallback: '标识设计支持' }),
      ],
    },
    {
      icon: 'fas fa-globe',
      title: t('marketing_support.services.digital.title', { fallback: '数字营销' }),
      items: [
        t('marketing_support.services.digital.item1', { fallback: '网站设计建议' }),
        t('marketing_support.services.digital.item2', { fallback: '社交媒体策略指导' }),
        t('marketing_support.services.digital.item3', { fallback: '搜索引擎优化(SEO)技巧' }),
        t('marketing_support.services.digital.item4', { fallback: '在线声誉管理建议' }),
      ],
    },
    {
      icon: 'fas fa-bullhorn',
      title: t('marketing_support.services.promotional.title', { fallback: '宣传材料' }),
      items: [
        t('marketing_support.services.promotional.item1', { fallback: '可定制的传单和宣传册模板' }),
        t('marketing_support.services.promotional.item2', { fallback: '数字广告模板' }),
        t('marketing_support.services.promotional.item3', { fallback: '电子邮件营销模板' }),
        t('marketing_support.services.promotional.item4', { fallback: '宣传视频协助' }),
      ],
    },
    {
      icon: 'fas fa-calendar-alt',
      title: t('marketing_support.services.opening.title', { fallback: '盛大开业支持' }),
      items: [
        t('marketing_support.services.opening.item1', { fallback: '盛大开业计划指导' }),
        t('marketing_support.services.opening.item2', { fallback: '媒体关系协助' }),
        t('marketing_support.services.opening.item3', { fallback: '社区外展策略' }),
        t('marketing_support.services.opening.item4', { fallback: '活动营销技巧' }),
      ],
    },
  ];

  return (
    <section className="marketing-services">
      <div className="container">
        <h2 className="section-title" suppressHydrationWarning>
          {t('marketing_support.services.title', { fallback: '我们的营销支持服务' })}
        </h2>

        <div className="services-grid">
          {services.map((service, index) => (
            <div className="service-item" key={index}>
              <div className="service-icon">
                <i className={service.icon}></i>
              </div>
              <h3>{service.title}</h3>
              <ul>
                {service.items.map((item, itemIndex) => (
                  <li key={itemIndex}>{item}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
