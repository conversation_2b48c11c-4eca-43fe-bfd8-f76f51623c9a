'use client';

import Image from "next/image";
import Link from "next/link";
import { ArrowUpRight } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { useLanguage } from './LanguageProvider';

interface ModernProductCardProps {
  title: string;
  description: string;
  category: string;
  image?: string;
  slug: string;
}

export default function ModernProductCard({
  title,
  description,
  category,
  image = "/images/products/placeholder.jpg",
  slug,
}: ModernProductCardProps) {
  const pathname = usePathname();
  const lang = pathname?.split('/')[1] || 'zh';
  const { t } = useLanguage();

  // 从URL中提取slug，如果没有提供slug的话
  const getSlug = () => {
    if (slug) return slug;
    // 如果没有slug，尝试从title生成一个简单的slug
    return title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
  };

  // 翻译产品类别
  const getTranslatedCategory = (category: string) => {
    // 使用国际化翻译
    const translationKey = `products.product_categories.${category}`;
    const translated = t(translationKey, { fallback: category });

    // 如果翻译键不存在，返回原始类别
    return translated === translationKey ? category : translated;
  };

  return (
    <Link href={`/${lang}/products/${getSlug()}`}>
      <article className="group cursor-pointer">
        <div className="space-y-4">
          {/* Image */}
          <div className="relative overflow-hidden bg-neutral-50 rounded-lg">
            <Image
              src={image || "/images/products/placeholder.jpg"}
              alt={title}
              width={600}
              height={400}
              className="w-full h-72 object-cover transition-transform duration-700 ease-out group-hover:scale-105"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.onerror = null;
                target.src = "/images/products/placeholder.jpg";
              }}
            />

          </div>

          {/* Content */}
          <div className="space-y-3">
            <div className="flex items-start justify-between gap-4">
              <h2 className="text-lg font-medium text-neutral-900 leading-tight group-hover:text-neutral-600 transition-colors duration-200">
                {title}
              </h2>
              <ArrowUpRight className="w-4 h-4 text-neutral-400 flex-shrink-0 transition-transform duration-200 group-hover:translate-x-1 group-hover:-translate-y-1" />
            </div>

            <p className="text-sm text-neutral-600 leading-relaxed line-clamp-2">
              {description}
            </p>

            <div className="text-xs text-neutral-400 font-mono tracking-wide uppercase">
              {getTranslatedCategory(category)}
            </div>
          </div>
        </div>
      </article>
    </Link>
  );
}
