'use client';

import React from 'react';
import Image from 'next/image';

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  bgImage?: string;
  height?: string;
  overlayOpacity?: number;
  useImageComponent?: boolean; // 是否使用Image组件代替背景图
  className?: string; // 添加自定义类名
  titleClassName?: string; // 标题自定义类名
  subtitleClassName?: string; // 副标题自定义类名
  animationEffect?: 'fade' | 'right' | 'left' | 'up' | 'none'; // 动画效果选项
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  bgImage = '/images/backgrounds/default-header-bg.jpg',
  height = 'md:h-[320px] h-[240px]',
  overlayOpacity = 0.5,
  useImageComponent = false,
  className = '',
  titleClassName = '',
  subtitleClassName = '',
  animationEffect = 'fade'
}) => {
  // 根据动画效果选择合适的类名
  const getAnimationClass = (type: 'title' | 'subtitle') => {
    if (animationEffect === 'none') return '';

    if (type === 'title') {
      switch (animationEffect) {
        case 'right': return 'animate-fade-in-right';
        case 'left': return 'animate-fade-in-left';
        case 'up': return 'animate-fade-in-up';
        default: return 'animate-fade-in';
      }
    } else {
      // 副标题总是比标题延迟出现
      switch (animationEffect) {
        case 'right': return 'animate-fade-in-right';
        case 'left': return 'animate-fade-in-left';
        case 'up': return 'animate-fade-in-up';
        default: return 'animate-fade-in-delay';
      }
    }
  };

  if (useImageComponent) {
    return (
      <div className={`relative ${height} overflow-hidden ${className} mt-0 pt-0`} style={{ marginTop: 0, paddingTop: 0 }}>
        {/* 使用Next.js Image组件提高性能 */}
        <div className="absolute inset-0 w-full h-full">
          <Image
            src={bgImage}
            alt={title}
            fill
            priority
            sizes="100vw"
            className="object-cover object-center bg-zoom"
          />
          <div
            className="absolute inset-0 bg-black gradient-overlay"
            style={{ opacity: overlayOpacity }}
          ></div>
        </div>

        <div className="container mx-auto px-4 text-center relative z-10 h-full flex flex-col justify-center">
          <div className="page-header-text-wrapper">
            <h1 className={`text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 ${getAnimationClass('title')} ${titleClassName}`}>
              {title}
            </h1>

            {subtitle && (
              <p className={`text-lg md:text-xl text-gray-200 max-w-3xl mx-auto ${getAnimationClass('subtitle')} ${subtitleClassName}`}>
                {subtitle}
              </p>
            )}
          </div>
        </div>

        <div className="absolute bottom-0 left-0 w-full h-12 bg-gradient-to-t from-white to-transparent"></div>
      </div>
    );
  }

  return (
    <div
      className={`relative ${height} bg-cover bg-center flex items-center justify-center ${className} mt-0 pt-0`}
      style={{
        backgroundImage: `linear-gradient(rgba(0, 0, 0, ${overlayOpacity}), rgba(0, 0, 0, ${overlayOpacity})), url(${bgImage})`,
        backgroundPosition: 'center',
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        marginTop: 0,
        paddingTop: 0
      }}
    >
      <div className="container mx-auto px-4 text-center relative z-10">
        <div className="page-header-text-wrapper">
          <h1 className={`text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 ${getAnimationClass('title')} ${titleClassName}`}>
            {title}
          </h1>

          {subtitle && (
            <p className={`text-lg md:text-xl text-gray-200 max-w-3xl mx-auto ${getAnimationClass('subtitle')} ${subtitleClassName}`}>
              {subtitle}
            </p>
          )}
        </div>
      </div>

      <div className="absolute bottom-0 left-0 w-full h-12 bg-gradient-to-t from-white to-transparent"></div>
    </div>
  );
};

export default PageHeader;