'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { featurePlaceholders } from '../utils/imagePlaceholder';
import { useLanguage } from './LanguageProvider';

export default function HolographicFeatures() {
  const { t, locale } = useLanguage();

  // Feature data
  const features = [
    {
      id: 'projection',
      title: t('features.projection.title', { fallback: '全息投影技术' }),
      subtitle: t('features.projection.subtitle', { fallback: '逼真立体视觉体验' }),
      description: t('features.projection.description', {
        fallback:
          '采用先进的空间成像技术，打造超高清晰度立体影像，无需任何眼镜或设备即可观看全息内容。',
      }),
      image: featurePlaceholders[0],
    },
    {
      id: 'interactive',
      title: t('features.interactive.title', { fallback: '智能互动系统' }),
      subtitle: t('features.interactive.subtitle', { fallback: '感应式人机互动体验' }),
      description: t('features.interactive.description', {
        fallback: '集成动作捕捉和手势识别技术，实现观众与全息内容的自然互动，创造沉浸式体验。',
      }),
      image: featurePlaceholders[1],
    },
    {
      id: 'materials',
      title: t('features.materials.title', { fallback: '高质量材料' }),
      subtitle: t('features.materials.subtitle', { fallback: '专业投影膜与光学镜面' }),
      description: t('features.materials.description', {
        fallback:
          '使用高透光特种投影膜和精密光学材料，确保最佳视觉效果和色彩还原度，适用于各种光线环境。',
      }),
      image: featurePlaceholders[2],
    },
    {
      id: 'software',
      title: t('features.software.title', { fallback: '专业内容制作' }),
      subtitle: t('features.software.subtitle', { fallback: '定制3D全息内容设计' }),
      description: t('features.software.description', {
        fallback:
          '由专业团队打造定制化3D全息内容，从创意构思到建模渲染，为您的空间创造独特的视觉故事。',
      }),
      image: featurePlaceholders[3],
    },
    {
      id: 'application',
      title: t('features.application.title', { fallback: '广泛应用场景' }),
      subtitle: t('features.application.subtitle', { fallback: '展厅、餐厅与文旅空间' }),
      description: t('features.application.description', {
        fallback:
          '全息投影系统适用于科技展厅、主题餐厅、文旅景点、商业中心等多种场景，创造独特的视觉亮点。',
      }),
      image: featurePlaceholders[4],
    },
    {
      id: 'support',
      title: t('features.support.title', { fallback: '全程技术支持' }),
      subtitle: t('features.support.subtitle', { fallback: '安装调试与售后服务' }),
      description: t('features.support.description', {
        fallback:
          '提供从设计咨询、安装调试到后期维护的一站式服务，确保系统长期稳定运行，7*24小时技术支持。',
      }),
      image: featurePlaceholders[5],
    },
  ];

  // Track active feature
  const [activeFeature, setActiveFeature] = useState('projection');

  // Get current feature object
  const currentFeature = features.find(f => f.id === activeFeature) || features[0];

  return (
    <section className="holographic-features" style={{ marginBottom: 0, paddingBottom: 0 }}>
      <div className="container">
        <h2 className="section-title" suppressHydrationWarning>
          {t('features.title', { fallback: '全息投影技术特点' })}
        </h2>
        <p className="section-description" suppressHydrationWarning>
          {t('features.description', {
            fallback:
              '探索先进的全息投影技术、智能互动系统和专业内容制作，为各类空间打造震撼的视觉体验。我们的定制化解决方案确保科技与艺术的完美结合，创造引人入胜的沉浸式环境。',
          })}
        </p>

        <div className="features-tabs">
          <div className="tab-content">
            <div className="tab-item">
              <div className="tab-image">
                {typeof currentFeature.image === 'string' ? (
                  <Image
                    src={currentFeature.image}
                    alt={currentFeature.subtitle}
                    width={600}
                    height={400}
                    style={{ objectFit: 'contain' }}
                  />
                ) : (
                  <div
                    className="placeholder-image"
                    style={{
                      backgroundColor: '#0a59f7',
                      width: '100%',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontWeight: 'bold',
                    }}
                  >
                    {currentFeature.title}
                  </div>
                )}
              </div>
              <div className="tab-info">
                <h3 suppressHydrationWarning>{currentFeature.title}</h3>
                <h4 suppressHydrationWarning>{currentFeature.subtitle}</h4>
                <p suppressHydrationWarning>{currentFeature.description}</p>
                <Link href={`/${locale}/custom-solutions`} className="btn-secondary">
                  <span suppressHydrationWarning>
                    {t('features.cta', { fallback: '立即获取免费报价' })}
                  </span>
                </Link>
              </div>
            </div>
          </div>

          <div className="features-nav">
            {features.map(feature => (
              <button
                key={feature.id}
                className={`feature-nav-btn ${activeFeature === feature.id ? 'active' : ''}`}
                onClick={() => setActiveFeature(feature.id)}
                suppressHydrationWarning
              >
                {feature.subtitle}
              </button>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
