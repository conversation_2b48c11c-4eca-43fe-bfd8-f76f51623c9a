'use client';

import React, { useState, useEffect, useRef } from 'react';
import CustomImage from './CustomImage';
import Link from 'next/link';
import { motion } from 'framer-motion';

// 产品接口定义
interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  price?: number;

  image_url: string;
  images?: string[];
  video_url?: string;
  is_featured: boolean;
  in_stock: boolean;
  created_at: string;
  updated_at: string;
  detail_images?: string[];
  tech_specs?: Record<string, string | string[]>;
  key_features?: string[];
  applications?: string[];
  installation_options?: string[];
}

interface ProductDetailProps {
  product: Product;
  lang: string;
}

const ProductDetail: React.FC<ProductDetailProps> = ({ product, lang }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [activeImage, setActiveImage] = useState(0);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  // 多语言支持
  const t = (key: string): string => {
    const translations: Record<string, Record<string, string>> = {
      overview: {
        en: 'Overview',
        zh: '概述',
      },
      features: {
        en: 'Features',
        zh: '特点',
      },
      specs: {
        en: 'Specifications',
        zh: '规格',
      },
      applications: {
        en: 'Applications',
        zh: '应用场景',
      },
      contactUs: {
        en: 'Contact Us',
        zh: '联系我们',
      },
      requestQuote: {
        en: 'Request Quote',
        zh: '获取报价',
      },
      inStock: {
        en: 'In Stock',
        zh: '有库存',
      },
      outOfStock: {
        en: 'Out of Stock',
        zh: '缺货',
      },

    };

    return translations[key]?.[lang] || key;
  };

  // 构建媒体数组
  const getMediaItems = () => {
    const media = [];

    // 添加视频（如果有）
    if (product.video_url) {
      media.push({
        type: 'video',
        url: product.video_url,
      });
    }

    // 添加图片
    if (Array.isArray(product.images) && product.images.length > 0) {
      product.images.forEach(img => {
        media.push({
          type: 'image',
          url: img,
        });
      });
    } else if (product.image_url) {
      media.push({
        type: 'image',
        url: product.image_url,
      });
    }

    return media;
  };

  const mediaItems = getMediaItems();

  // 处理视频播放/暂停
  useEffect(() => {
    if (mediaItems[activeImage]?.type === 'video' && videoRef.current) {
      if (isVideoPlaying) {
        videoRef.current.play().catch(e => console.error('视频播放失败:', e));
      } else {
        videoRef.current.pause();
      }
    }
  }, [isVideoPlaying, activeImage, mediaItems]);

  // 切换到视频时自动播放
  useEffect(() => {
    if (mediaItems[activeImage]?.type === 'video') {
      setIsVideoPlaying(true);
    } else {
      setIsVideoPlaying(false);
    }
  }, [activeImage, mediaItems]);

  return (
    <div className="product-detail-container max-w-7xl mx-auto px-4 py-8">
      {/* 面包屑导航 */}
      <div className="flex justify-center mb-6">
        <div className="breadcrumbs text-sm flex items-center flex-wrap">
          <Link href={`/${lang}`} className="text-blue-600 hover:underline">
            {lang === 'zh' ? '首页' : 'Home'}
          </Link>
          <span className="mx-2">/</span>
          <Link href={`/${lang}/products`} className="text-blue-600 hover:underline">
            {lang === 'zh' ? '产品' : 'Products'}
          </Link>
          <span className="mx-2">/</span>
          <span className="text-gray-600 break-words">{product.name}</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-10">
        {/* 左侧：产品图片/视频 */}
        <div className="product-media">
          <div
            className="main-media-container relative bg-gray-100 rounded-lg overflow-hidden"
            style={{ height: '350px' }}
          >
            {mediaItems.map((item, index) => (
              <div
                key={index}
                className={`absolute inset-0 transition-opacity duration-500 ${activeImage === index ? 'opacity-100 z-10' : 'opacity-0 z-0'}`}
              >
                {item.type === 'video' ? (
                  <div className="relative w-full h-full">
                    <video
                      ref={videoRef}
                      src={item.url}
                      className="w-full h-full object-contain"
                      controls
                      playsInline
                      onPlay={() => setIsVideoPlaying(true)}
                      onPause={() => setIsVideoPlaying(false)}
                    />
                  </div>
                ) : (
                  <CustomImage
                    src={item.url}
                    alt={`${product.name} - 图片 ${index + 1}`}
                    width={600}
                    height={400}
                    className="object-contain"
                    loading={index === 0 ? 'eager' : 'lazy'}
                  />
                )}
              </div>
            ))}
          </div>

          {/* 缩略图 */}
          {mediaItems.length > 1 && (
            <div className="thumbnails-grid grid grid-cols-5 gap-2 mt-4">
              {mediaItems.map((item, index) => (
                <button
                  key={index}
                  className={`thumbnail-item relative h-20 bg-gray-100 rounded-md overflow-hidden border-2 transition-all ${
                    activeImage === index
                      ? 'border-blue-500 ring-2 ring-blue-300'
                      : 'border-transparent'
                  }`}
                  onClick={() => setActiveImage(index)}
                >
                  {item.type === 'video' ? (
                    <div className="w-full h-full flex items-center justify-center bg-gray-800">
                      <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M6.5 5.5L13.5 10 6.5 14.5z" />
                      </svg>
                    </div>
                  ) : (
                    <CustomImage
                      src={item.url}
                      alt={`Thumbnail ${index + 1}`}
                      width={100}
                      height={20}
                      className="object-cover"
                    />
                  )}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* 右侧：产品信息 */}
        <div className="product-info">
          <motion.h1
            className="text-3xl font-bold text-gray-900 mb-3"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            {product.name}
          </motion.h1>

          <motion.div
            className="mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <span
              className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                product.in_stock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}
            >
              {product.in_stock ? t('inStock') : t('outOfStock')}
            </span>
          </motion.div>

          <motion.div
            className="product-description prose max-w-none mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            dangerouslySetInnerHTML={{ __html: product.description }}
          />

          <motion.div
            className="action-buttons flex flex-wrap gap-4 mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Link
              href={`/${lang}/pages/contact-us?product=${product.slug}`}
              className="btn-primary px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
              {t('contactUs')}
            </Link>
            <Link
              href={`/${lang}/pages/contact-us?quote=true&product=${product.slug}`}
              className="btn-secondary px-6 py-3 border-2 border-blue-600 text-blue-600 rounded-lg font-medium hover:bg-blue-50 transition-colors flex items-center"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"
                />
              </svg>
              {t('requestQuote')}
            </Link>
          </motion.div>

          {/* 产品快速特点 */}
          {product.key_features && product.key_features.length > 0 && (
            <motion.div
              className="quick-features mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <h3 className="text-lg font-semibold mb-3">
                {lang === 'zh' ? '产品特点' : 'Key Features'}
              </h3>
              <ul className="space-y-2">
                {product.key_features.slice(0, 4).map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <svg
                      className="w-5 h-5 text-green-500 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </motion.div>
          )}
        </div>
      </div>

      {/* 产品详情选项卡 */}
      <div className="product-tabs mt-12">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8">
            <button
              className={`tab-btn pb-4 font-medium text-sm ${
                activeTab === 'overview'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('overview')}
            >
              {t('overview')}
            </button>

            {product.key_features && product.key_features.length > 0 && (
              <button
                className={`tab-btn pb-4 font-medium text-sm ${
                  activeTab === 'features'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('features')}
              >
                {t('features')}
              </button>
            )}

            {product.tech_specs && Object.keys(product.tech_specs).length > 0 && (
              <button
                className={`tab-btn pb-4 font-medium text-sm ${
                  activeTab === 'specs'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('specs')}
              >
                {t('specs')}
              </button>
            )}

            {product.applications && product.applications.length > 0 && (
              <button
                className={`tab-btn pb-4 font-medium text-sm ${
                  activeTab === 'applications'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('applications')}
              >
                {t('applications')}
              </button>
            )}
          </nav>
        </div>

        <div className="tab-content py-6">
          {/* 概述选项卡 */}
          {activeTab === 'overview' && (
            <div className="overview-tab">
              <div className="prose max-w-none">
                <div dangerouslySetInnerHTML={{ __html: product.description }} />

                {/* 详情图片 */}
                {product.detail_images && product.detail_images.length > 0 && (
                  <div className="detail-images mt-8 space-y-6">
                    {product.detail_images.map((image, index) => (
                      <div key={index} className="detail-image relative rounded-lg overflow-hidden">
                        <CustomImage
                          src={image}
                          alt={`${product.name} detail ${index + 1}`}
                          width={1000}
                          height={600}
                          className="w-full"
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 特点选项卡 */}
          {activeTab === 'features' && product.key_features && (
            <div className="features-tab">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {product.key_features.map((feature, index) => (
                  <motion.div
                    key={index}
                    className="feature-card bg-white p-6 rounded-lg shadow-sm border border-gray-100"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                  >
                    <div className="feature-icon w-12 h-12 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mb-4">
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <p className="text-gray-700">{feature}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* 规格选项卡 */}
          {activeTab === 'specs' && product.tech_specs && (
            <div className="specs-tab">
              <div className="bg-white rounded-lg overflow-hidden border border-gray-200">
                <table className="min-w-full divide-y divide-gray-200">
                  <tbody className="divide-y divide-gray-200">
                    {Object.entries(product.tech_specs).map(([key, value]) => (
                      <tr key={key}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 bg-gray-50 w-1/3">
                          {key}
                        </td>
                        <td className="px-6 py-4 whitespace-normal text-sm text-gray-700">
                          {Array.isArray(value) ? value.join(', ') : value}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* 应用场景选项卡 */}
          {activeTab === 'applications' && product.applications && (
            <div className="applications-tab">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {product.applications.map((application, index) => (
                  <motion.div
                    key={index}
                    className="application-card p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                  >
                    <div className="application-icon w-12 h-12 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mb-4">
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                        <path
                          fillRule="evenodd"
                          d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      应用场景 {index + 1}
                    </h3>
                    <p className="text-gray-700">{application}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>


    </div>
  );
};

export default ProductDetail;
