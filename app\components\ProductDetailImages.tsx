'use client';

import CustomImage from './CustomImage';

interface ProductDetailImagesProps {
  images: string[];
  productName: string;
}

const ProductDetailImages: React.FC<ProductDetailImagesProps> = ({ images, productName }) => {
  if (!images || images.length === 0) {
    return null;
  }

  return (
    <div className="w-full flex flex-col gap-4">
      {images.map((image, index) => (
        <div key={index} className="w-full relative bg-black">
          <div className="relative aspect-video w-full">
            <CustomImage
              src={image}
              alt={`${productName} detail ${index + 1}`}
              fill
              sizes="100vw"
              style={{ objectFit: 'contain' }}
              className="select-none"
            />
            <div className="absolute inset-0 flex items-center justify-center text-white text-2xl font-bold pointer-events-none">
              产品大图片
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProductDetailImages;
