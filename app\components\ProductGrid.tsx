'use client';

import { useState, useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { useLanguage } from './LanguageProvider';
import ModernProductCard from './ModernProductCard';

// 产品类型定义
interface Product {
  id: number;
  name: string;
  slug: string;
  description?: string;
  image_url?: string;

  features?: any;
  is_published?: boolean;
  is_featured?: boolean;
}

// 产品网格组件
interface ProductGridProps {
  searchQuery?: string;
}

export default function ProductGrid({ searchQuery = '' }: ProductGridProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [visibleCards, setVisibleCards] = useState<Set<number>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const pathname = usePathname();
  const lang = pathname?.split('/')[1] || 'zh';
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [windowWidth, setWindowWidth] = useState(0);
  const productsPerPage = 12; // 每页显示12个产品
  const { t } = useLanguage();

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    // 初始设置
    handleResize();
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 获取网格列数
  const getGridColumns = () => {
    if (windowWidth >= 1280) return 'repeat(4, 1fr)';
    if (windowWidth >= 1024) return 'repeat(3, 1fr)';
    if (windowWidth >= 768) return 'repeat(2, 1fr)';
    return '1fr';
  };

  // 获取产品列表 - 使用API并传递语言参数
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);

        console.log(`[ProductGrid] Fetching products, language: ${lang}, page: ${currentPage}`);

        // 使用API获取产品数据，传递语言参数和搜索查询
        const searchParam = searchQuery.trim() ? `&search=${encodeURIComponent(searchQuery.trim())}` : '';
        const response = await fetch(`/api/products?lang=${lang}&page=${currentPage}&limit=${productsPerPage}${searchParam}`);
        if (!response.ok) {
          throw new Error(`${t('products.loading_error', { fallback: 'Error loading products: ' })}${response.status}`);
        }

        const data = await response.json();
        const allProducts = data.products || [];

        console.log(`[ProductGrid] Received products data, count: ${allProducts.length}, search: "${searchQuery}"`);

        // 设置产品数据（API已经处理了搜索和分页）
        setProducts(allProducts);
        setTotalProducts(data.total || allProducts.length);
        setTotalPages(data.totalPages || Math.ceil((data.total || allProducts.length) / productsPerPage));

      } catch (err) {
        console.error('Error fetching products:', err);
        setError(err instanceof Error ? err.message : t('products.loading_error', { fallback: 'Unknown error' }));
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [currentPage, productsPerPage, searchQuery, lang]); // 当页面、每页数量、搜索查询或语言变化时重新获取

  // 当搜索查询变化时，重置到第一页
  useEffect(() => {
    if (searchQuery !== undefined) {
      setCurrentPage(1);
    }
  }, [searchQuery]);

  // 卡片动画观察器
  useEffect(() => {
    const observerOptions = {
      threshold: 0.2,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const index = parseInt(entry.target.getAttribute('data-index') || '0');
          setVisibleCards(prev => new Set([...prev, index]));
        }
      });
    }, observerOptions);

    cardRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => observer.disconnect();
  }, [products]);

  // 加载状态
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="bg-gray-100 rounded-lg h-96 animate-pulse"
            style={{
              animationDelay: `${i * 0.1}s`,
              animationDuration: '1.5s'
            }}
          ></div>
        ))}
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
        <p className="text-red-700">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg"
        >
          {t('products.retry', { fallback: 'Retry' })}
        </button>
      </div>
    );
  }

  // 无产品状态
  if (products.length === 0) {
    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
        {searchQuery ? (
          <div>
            <p className="text-gray-700 text-lg mb-2">{t('products.no_search_results', { fallback: 'No matching products found' })}</p>
            <p className="text-gray-500">{t('products.search_keyword', { fallback: 'Search keyword: ' })}"{searchQuery}"</p>
            <p className="text-gray-500 mt-2">{t('products.try_other_keywords', { fallback: 'Please try other keywords or browse all products' })}</p>
          </div>
        ) : (
          <p className="text-gray-700">{t('products.no_products', { fallback: 'No products available' })}</p>
        )}
      </div>
    );
  }

  // 分页处理函数
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setVisibleCards(new Set()); // 重置可见卡片
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // 渲染产品网格
  return (
    <>
      <style jsx>{`
        .product-card-container {
          opacity: 0;
          transform: translateY(30px) scale(0.95);
          transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .product-card-container.visible {
          opacity: 1;
          transform: translateY(0) scale(1);
        }

        .stagger-animation {
          transition-delay: calc(var(--index) * 0.1s);
        }

        .hover-lift {
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .hover-lift:hover {
          transform: translateY(-8px) scale(1.02);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .pagination-button {
          transition: all 0.3s ease;
        }

        .pagination-button:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      `}</style>

      <div className="space-y-8">
        {/* 产品统计信息 */}
        <div className="text-center text-gray-600">
          {searchQuery ? (
            <p>{t('products.search_results', {
              fallback: 'Search "{{query}}" found {{count}} products, page {{current}} of {{total}}',
              query: searchQuery,
              count: totalProducts,
              current: currentPage,
              total: totalPages
            })}</p>
          ) : (
            <p>{t('products.total_products', {
              fallback: 'Total {{count}} products, page {{current}} of {{total}}',
              count: totalProducts,
              current: currentPage,
              total: totalPages
            })}</p>
          )}
        </div>

        {/* 产品网格 - 强制固定尺寸布局 */}
        <div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
          style={{
            display: 'grid',
            gridTemplateColumns: getGridColumns(),
            gap: '2rem',
            maxWidth: '100%'
          }}
        >
          {products.map((product, index) => (
            <div
              key={product.id}
              ref={(el) => (cardRefs.current[index] = el)}
              data-index={index}
              className={`product-card-container stagger-animation hover-lift ${
                visibleCards.has(index) ? 'visible' : ''
              }`}
              style={{
                '--index': index,
                maxWidth: windowWidth < 768 ? '400px' : 'none',
                margin: windowWidth < 768 ? '0 auto' : '0'
              } as React.CSSProperties}
            >
              <ModernProductCard
                title={product.name}
                description={product.description || t('products.no_description', { fallback: 'No description available' })}
                category={product.category || "Interactive"}
                image={product.images?.[0] || product.image_url}
                slug={product.slug}
              />
            </div>
          ))}
        </div>

        {/* 分页组件 */}
        {totalPages > 1 && (
          <div className="flex justify-center items-center space-x-2 mt-12">
            {/* 上一页按钮 */}
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={`pagination-button px-4 py-2 rounded-lg border ${
                currentPage === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              {t('products.previous_page', { fallback: 'Previous' })}
            </button>

            {/* 页码按钮 */}
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`pagination-button px-4 py-2 rounded-lg border ${
                  page === currentPage
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            ))}

            {/* 下一页按钮 */}
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`pagination-button px-4 py-2 rounded-lg border ${
                currentPage === totalPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              {t('products.next_page', { fallback: 'Next' })}
            </button>
          </div>
        )}
      </div>
    </>
  );
}