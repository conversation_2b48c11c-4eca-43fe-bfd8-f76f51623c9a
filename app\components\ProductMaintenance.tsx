'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useTranslation } from '../utils/useTranslation';

interface ProductMaintenanceProps {
  lang: string;
  message?: string;
  backUrl?: string;
  backText?: string;
}

/**
 * 产品详情页维护组件
 * 显示产品详情页面正在维护的信息，并提供返回按钮
 */
const ProductMaintenance: React.FC<ProductMaintenanceProps> = ({
  lang,
  message,
  backUrl = `/${lang}/products`,
  backText,
}) => {
  const { t } = useTranslation();

  // 默认文本
  const defaultMessage = t('maintenance.upgrading_message', {
    fallback: 'We are upgrading our product detail pages to provide a better user experience. Please check back later.'
  });

  const defaultBackText = t('maintenance.back_to_products', { fallback: 'Back to Products' });

  // 使用提供的文本或默认文本
  const displayMessage = message || defaultMessage;
  const displayBackText = backText || defaultBackText;

  return (
    <div className="maintenance-container">
      <div className="maintenance-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.***********.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z" />
        </svg>
      </div>

      <h1 className="maintenance-title">
        {t('maintenance.page_under_maintenance', { fallback: 'Product Detail Page Under Maintenance' })}
      </h1>

      <p className="maintenance-description">{displayMessage}</p>

      <Link href={backUrl} className="back-button">
        <span>{displayBackText}</span>
      </Link>

      {/* 添加一些产品缩略图作为装饰 */}
      <div className="product-thumbnails">
        {[1, 2, 3, 4].map(num => (
          <div key={num} className="thumbnail-item">
            <Image
              src={`/images/products/thumbnail-${num}.jpg`}
              alt="Product thumbnail"
              width={120}
              height={120}
              className="thumbnail-image"
              onError={e => {
                // 如果图片加载失败，使用默认图片
                const target = e.target as HTMLImageElement;
                target.src = '/images/products/3d-1.jpg';
              }}
            />
          </div>
        ))}
      </div>

      {/* 添加一些介绍文本 */}
      <div className="intro-text">
        <h3>{t('maintenance.about_products', { fallback: 'About Our Products' })}</h3>
        <p>
          {t('maintenance.product_description', {
            fallback: 'We offer high-quality holographic projection solutions for various scenarios. Our products use the latest technology to provide customers with an immersive experience.'
          })}
        </p>
        <p>
          {t('maintenance.team_working', {
            fallback: 'Our team is working hard to improve the product detail pages so you can better understand our product features and technical specifications.'
          })}
        </p>
      </div>
    </div>
  );
};

export default ProductMaintenance;
