'use client';

import { useState, useRef, useEffect } from 'react';
import { X } from 'lucide-react';
import { useLanguage } from './LanguageProvider';

interface ProductSearchProps {
  onSearch?: (query: string) => void;
  placeholder?: string;
  className?: string;
}

export default function ProductSearch({
  onSearch,
  placeholder,
  className = ""
}: ProductSearchProps) {
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const { t } = useLanguage();

  // 使用国际化的占位符
  const searchPlaceholder = placeholder || t('products.search_placeholder', { fallback: 'Search product name, model or keywords...' });

  const handleSearch = (value: string) => {
    setQuery(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  const clearSearch = () => {
    setQuery('');
    if (onSearch) {
      onSearch('');
    }
    inputRef.current?.focus();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (onSearch) {
        onSearch(query);
      }
    }
  };

  // 处理点击外部关闭建议
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // 检查点击是否在建议框或输入框内
      const isInsideSuggestions = suggestionsRef.current && suggestionsRef.current.contains(target);
      const isInsideInput = inputRef.current && inputRef.current.contains(target);

      // 如果点击在外部，关闭建议框
      if (!isInsideSuggestions && !isInsideInput) {
        setShowSuggestions(false);
        setIsFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理输入框焦点
  const handleFocus = () => {
    setIsFocused(true);
    setShowSuggestions(true);
  };

  const handleBlur = () => {
    // 延迟关闭，让点击事件有时间触发
    setTimeout(() => {
      setIsFocused(false);
      setShowSuggestions(false);
    }, 200);
  };

  // 处理热门搜索点击
  const handleTagClick = (tag: string) => {
    console.log('Popular search tag clicked:', tag);
    setQuery(tag);
    setShowSuggestions(false);
    setIsFocused(false);
    if (onSearch) {
      onSearch(tag);
    }
    // 短暂延迟后重新聚焦输入框
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  };

  return (
    <div className={`w-full max-w-4xl mx-auto ${className}`}>
      <style jsx>{`
        .search-container {
          position: relative;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .search-input {
          background: rgba(255, 255, 255, 0.98);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.8);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08), 0 2px 16px rgba(59, 130, 246, 0.04);
        }

        .search-input:focus {
          background: rgba(255, 255, 255, 1);
          border-color: #3b82f6;
          box-shadow: 0 8px 30px rgba(59, 130, 246, 0.15);
          transform: translateY(-2px);
        }

        .search-input.has-value {
          border-color: #10b981;
        }

        .clear-button {
          opacity: 0;
          transform: scale(0.8);
          transition: all 0.2s ease;
        }

        .clear-button.visible {
          opacity: 1;
          transform: scale(1);
        }

        .search-glow {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border-radius: 16px;
          background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
          opacity: 0;
          z-index: -1;
          transition: opacity 0.3s ease;
          filter: blur(20px);
        }

        .search-container:focus-within .search-glow {
          opacity: 0.3;
        }


      `}</style>

      <div className="search-container">
        {/* 发光背景 */}
        <div className="search-glow"></div>

        <div className="relative">
          {/* 搜索输入框 */}
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => handleSearch(e.target.value)}
            onKeyDown={handleKeyDown}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder={searchPlaceholder}
            className={`search-input w-full pl-6 pr-12 py-4 rounded-xl text-gray-700 placeholder-gray-400 outline-none text-lg font-medium ${
              query ? 'has-value' : ''
            }`}
          />

          {/* 清除按钮 */}
          {query && (
            <button
              onClick={clearSearch}
              className={`clear-button absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 hover:text-gray-600 ${
                query ? 'visible' : ''
              }`}
            >
              <X className="w-full h-full" />
            </button>
          )}
        </div>

        {/* 搜索建议/快捷标签 */}
        {showSuggestions && !query && (
          <div
            ref={suggestionsRef}
            className="absolute top-full left-0 right-0 mt-2 bg-white/98 backdrop-blur-sm rounded-xl shadow-2xl border border-white/80 p-4 z-[100]"
          >
            <div className="text-sm text-gray-500 mb-2">{t('products.popular_searches', { fallback: 'Popular Searches' })}</div>
            <div className="flex flex-wrap gap-2">
              {(t('products.search_tags', { fallback: 'Interactive Projection,Digital Sandbox,Holographic Projection,AR Experience,VR Equipment' }) as string).split(',').map((tag) => (
                <button
                  key={tag}
                  onMouseDown={(e) => {
                    e.preventDefault(); // 防止失焦
                    e.stopPropagation(); // 阻止事件冒泡
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleTagClick(tag);
                  }}
                  className="px-3 py-1 bg-gray-50 hover:bg-blue-50 hover:text-blue-600 rounded-full text-sm transition-colors duration-200 cursor-pointer"
                >
                  {tag.trim()}
                </button>
              ))}
            </div>
          </div>
        )}


      </div>
    </div>
  );
}
