'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useLanguage } from './LanguageProvider';

export default function ProductSectionCarousel() {
  const { t, locale } = useLanguage();
  const router = useRouter();

  // 轮播相关状态
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isVisible, setIsVisible] = useState(false);
  const [slideDirection, setSlideDirection] = useState<'next' | 'prev'>('next');
  const carouselRef = useRef<HTMLDivElement>(null);

  // 板块图轮播数据
  const sectionImages = [
    {
      src: '/images/product-sections/0007.jpg',
      title: t('productSections.section1.title', { fallback: '互动投影系列' }),
      description: t('productSections.section1.description', { fallback: '创新的互动投影技术，打造沉浸式体验空间' }),
      slug: 'interactive-projection',
      category: 'interactive'
    },
    {
      src: '/images/product-sections/0012.jpg',
      title: t('productSections.section2.title', { fallback: '全息展示系列' }),
      description: t('productSections.section2.description', { fallback: '先进的全息投影技术，呈现震撼视觉效果' }),
      slug: 'holographic-display',
      category: 'holographic'
    },
    {
      src: '/images/product-sections/0021.jpg',
      title: t('productSections.section3.title', { fallback: '数字沙盘系列' }),
      description: t('productSections.section3.description', { fallback: '智能数字沙盘，实现精准展示与互动控制' }),
      slug: 'digital-sandbox',
      category: 'digital'
    },
    {
      src: '/images/product-sections/0035.jpg',
      title: t('productSections.section4.title', { fallback: 'AR增强现实系列' }),
      description: t('productSections.section4.description', { fallback: 'AR增强现实技术，融合虚拟与现实世界' }),
      slug: 'ar-reality',
      category: 'ar'
    },
    {
      src: '/images/product-sections/0046.jpg',
      title: t('productSections.section5.title', { fallback: '智能一体机系列' }),
      description: t('productSections.section5.description', { fallback: '集成化智能设备，提供完整解决方案' }),
      slug: 'smart-integrated',
      category: 'smart'
    }
  ];

  // 组件可见性检测
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (carouselRef.current) {
      observer.observe(carouselRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // 轮播自动播放
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setSlideDirection('next');
      setCurrentSlide(prev => (prev + 1) % sectionImages.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, sectionImages.length]);

  // 轮播控制函数
  const nextSlide = () => {
    setSlideDirection('next');
    setCurrentSlide(prev => (prev + 1) % sectionImages.length);
  };

  const prevSlide = () => {
    setSlideDirection('prev');
    setCurrentSlide(prev => (prev - 1 + sectionImages.length) % sectionImages.length);
  };

  const goToSlide = (index: number) => {
    setSlideDirection(index > currentSlide ? 'next' : 'prev');
    setCurrentSlide(index);
  };

  // 处理轮播图点击事件
  const handleSlideClick = (section: typeof sectionImages[0]) => {
    router.push(`/${locale}/sections/${section.slug}`);
  };

  return (
    <div className={`product-section-carousel ${isVisible ? 'visible' : ''}`}>
      <div className="carousel-header">
        <h2 className="section-title">
          {t('productSections.title', { fallback: '产品系列展示' })}
        </h2>
        <p className="section-description">
          {t('productSections.description', { fallback: '探索我们的全系列产品解决方案，涵盖多个技术领域' })}
        </p>
      </div>

      <div className="carousel-container" ref={carouselRef}>
        <div
          className="carousel-track"
          style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        >
          {sectionImages.map((image, index) => (
            <div key={index} className="carousel-slide">
              <div
                className="carousel-image"
                onClick={() => handleSlideClick(image)}
                style={{ cursor: 'pointer' }}
              >
                <Image
                  src={image.src}
                  alt={image.title}
                  fill
                  style={{ objectFit: 'cover' }}
                  priority={index === 0}
                  sizes="100vw"
                />
                <div className="carousel-overlay">
                  <div className="carousel-content">
                    <h3>{image.title}</h3>
                    <p>{image.description}</p>
                    <div className="click-hint">
                      <span>{t('productSections.clickToView', { fallback: 'Click to view more' })}</span>
                      <i className="fas fa-arrow-right"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 轮播控制按钮 */}
        <button
          className="carousel-btn carousel-btn-prev"
          onClick={prevSlide}
          onMouseEnter={() => setIsAutoPlaying(false)}
          onMouseLeave={() => setIsAutoPlaying(true)}
        >
          <i className="fas fa-chevron-left"></i>
        </button>
        <button
          className="carousel-btn carousel-btn-next"
          onClick={nextSlide}
          onMouseEnter={() => setIsAutoPlaying(false)}
          onMouseLeave={() => setIsAutoPlaying(true)}
        >
          <i className="fas fa-chevron-right"></i>
        </button>

        {/* 轮播指示器 */}
        <div className="carousel-indicators">
          {sectionImages.map((_, index) => (
            <button
              key={index}
              className={`carousel-indicator ${index === currentSlide ? 'active' : ''}`}
              onClick={() => goToSlide(index)}
              onMouseEnter={() => setIsAutoPlaying(false)}
              onMouseLeave={() => setIsAutoPlaying(true)}
            />
          ))}
        </div>
      </div>

      <style jsx>{`
        .product-section-carousel {
          margin: 60px auto 80px;
          max-width: 1400px;
          padding: 0 20px;
          opacity: 0;
          transform: translateY(50px);
          transition: all 1.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .product-section-carousel.visible {
          opacity: 1;
          transform: translateY(0);
        }

        .carousel-header {
          text-align: center;
          margin-bottom: 50px;
        }

        .section-title {
          font-size: 2.5rem;
          font-weight: 600;
          color: #1a1a2e;
          margin-bottom: 20px;
        }

        .section-description {
          font-size: 1.1rem;
          color: #666;
          max-width: 600px;
          margin: 0 auto;
          line-height: 1.6;
        }

        .carousel-container {
          position: relative;
          width: 100%;
          aspect-ratio: 1933 / 1087; /* 使用图片的实际宽高比 */
          overflow: hidden;
          border-radius: 20px;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .carousel-track {
          display: flex;
          width: 100%;
          height: 100%;
          transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .carousel-slide {
          min-width: 100%;
          height: 100%;
          position: relative;
          animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
          from {
            opacity: 0;
            transform: scale(1.05);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }

        .carousel-image {
          width: 100%;
          height: 100%;
          position: relative;
        }

        .carousel-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(
            to top,
            rgba(0, 0, 0, 0.8) 0%,
            rgba(0, 0, 0, 0.4) 50%,
            transparent 100%
          );
          padding: 50px 60px;
          z-index: 2;
        }

        .carousel-content h3 {
          font-size: 2.2rem;
          font-weight: 600;
          color: white;
          margin-bottom: 15px;
          text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .carousel-content p {
          font-size: 1.1rem;
          color: rgba(255, 255, 255, 0.9);
          line-height: 1.6;
          max-width: 500px;
          text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
          margin-bottom: 20px;
        }

        .click-hint {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 0.9rem;
          color: rgba(255, 255, 255, 0.8);
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
          padding: 8px 16px;
          border-radius: 20px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          transition: all 0.3s ease;
          cursor: pointer;
          width: fit-content;
        }

        .click-hint:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateX(5px);
        }

        .click-hint i {
          transition: transform 0.3s ease;
        }

        .click-hint:hover i {
          transform: translateX(3px);
        }

        .carousel-btn {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 55px;
          height: 55px;
          background: rgba(255, 255, 255, 0.15);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          color: white;
          font-size: 1.1rem;
          cursor: pointer;
          transition: all 0.3s ease;
          z-index: 3;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .carousel-btn:hover {
          background: rgba(255, 255, 255, 0.25);
          transform: translateY(-50%) scale(1.1);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .carousel-btn:active {
          transform: translateY(-50%) scale(0.95);
        }

        .carousel-btn-prev {
          left: 25px;
        }

        .carousel-btn-next {
          right: 25px;
        }

        .carousel-indicators {
          position: absolute;
          bottom: 25px;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          gap: 10px;
          z-index: 3;
        }

        .carousel-indicator {
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.4);
          border: none;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .carousel-indicator.active {
          background: white;
          transform: scale(1.2);
          box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
          animation: pulse 2s infinite;
        }

        .carousel-indicator:hover {
          background: rgba(255, 255, 255, 0.7);
          transform: scale(1.1);
        }

        @keyframes pulse {
          0%, 100% {
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
          }
          50% {
            box-shadow: 0 0 25px rgba(255, 255, 255, 0.8);
          }
        }

        @media (max-width: 768px) {
          .section-title {
            font-size: 2rem;
          }

          .carousel-overlay {
            padding: 30px 25px;
          }

          .carousel-content h3 {
            font-size: 1.6rem;
          }

          .carousel-content p {
            font-size: 0.95rem;
          }

          .carousel-btn {
            width: 45px;
            height: 45px;
            font-size: 0.9rem;
          }

          .carousel-btn-prev {
            left: 15px;
          }

          .carousel-btn-next {
            right: 15px;
          }
        }

        @media (max-width: 576px) {
          .carousel-overlay {
            padding: 25px 20px;
          }

          .carousel-content h3 {
            font-size: 1.4rem;
          }

          .carousel-content p {
            font-size: 0.9rem;
          }

          .carousel-btn {
            width: 40px;
            height: 40px;
            font-size: 0.8rem;
          }

          .carousel-btn-prev {
            left: 10px;
          }

          .carousel-btn-next {
            right: 10px;
          }

          .carousel-indicators {
            bottom: 15px;
            gap: 8px;
          }

          .carousel-indicator {
            width: 8px;
            height: 8px;
          }
        }
      `}</style>
    </div>
  );
}
