import React from 'react';

/**
 * 产品详情页的骨架屏组件
 * 显示加载中的占位UI，符合新的简洁设计
 */
const ProductSkeleton = () => {
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* 图片展示区域骨架 */}
      <div className="w-full border-[2px] border-gray-200 p-1 flex gap-1">
        {/* 左侧缩略图列表 */}
        <div className="w-1/4 flex flex-col gap-1">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="w-full aspect-square bg-gray-200 animate-pulse"></div>
          ))}
        </div>

        {/* 右侧主图 */}
        <div className="w-3/4 aspect-video bg-gray-200 animate-pulse"></div>
      </div>

      {/* 介绍和按钮骨架 */}
      <div className="mt-4 flex justify-between items-center">
        <div className="flex items-center">
          <div className="h-6 w-16 bg-gray-200 animate-pulse mr-2"></div>
          <div className="h-6 w-64 bg-gray-200 animate-pulse"></div>
        </div>

        <div className="h-10 w-28 bg-gray-200 animate-pulse rounded-full"></div>
      </div>

      {/* 产品详情大图骨架 */}
      <div className="mt-8 space-y-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="w-full aspect-video bg-gray-200 animate-pulse"></div>
        ))}
      </div>
    </div>
  );
};

export default ProductSkeleton;
