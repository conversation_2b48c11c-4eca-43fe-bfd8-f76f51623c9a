'use client';

import { useState } from 'react';
import { useTranslation } from '../utils/useTranslation';
import { motion } from 'framer-motion';

interface QuoteFormProps {
  className?: string;
}

export default function QuoteForm({ className = '' }: QuoteFormProps) {
  const { t } = useTranslation();

  // 表单状态
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // 模拟表单提交
    try {
      // 这里可以替换为实际的API调用
      await new Promise(resolve => setTimeout(resolve, 800));
      setSubmitted(true);
      setName('');
      setPhone('');
      setMessage('');

      // 5秒后重置提交状态
      setTimeout(() => {
        setSubmitted(false);
      }, 5000);
    } catch (error) {
      console.error('提交失败', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`quote-form-container ${className}`}
    >
      <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-blue-500 to-purple-600"></div>

      <div className="form-inner">
        <h3 suppressHydrationWarning>
          {t('quote_form.title')}
        </h3>

        {submitted ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-green-50 border border-green-200 rounded-lg p-6 text-center"
          >
            <svg
              className="w-12 h-12 text-green-500 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M5 13l4 4L19 7"
              ></path>
            </svg>
            <h4 className="text-lg font-semibold text-green-800 mb-2" suppressHydrationWarning>
              {t('quote_form.success_title')}
            </h4>
            <p className="text-green-700" suppressHydrationWarning>
              {t('quote_form.success_message')}
            </p>
          </motion.div>
        ) : (
          <form onSubmit={handleSubmit}>
            <motion.div
              className="form-grid"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <motion.div
                whileFocus={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <input
                  type="text"
                  value={name}
                  onChange={e => setName(e.target.value)}
                  placeholder={t('quote_form.name')}
                  required
                />
              </motion.div>

              <motion.div
                whileFocus={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <input
                  type="tel"
                  value={phone}
                  onChange={e => setPhone(e.target.value)}
                  placeholder={t('quote_form.phone')}
                  required
                />
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              whileFocus={{ scale: 1.01 }}
            >
              <textarea
                value={message}
                onChange={e => setMessage(e.target.value)}
                placeholder={t('quote_form.message')}
                rows={3}
              ></textarea>
            </motion.div>

            <motion.button
              type="submit"
              disabled={isSubmitting}
              whileHover={{ scale: 1.02, boxShadow: "0 10px 30px rgba(59, 130, 246, 0.3)" }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              suppressHydrationWarning
            >
              {isSubmitting ? (
                <svg
                  className="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              ) : null}
              {isSubmitting ? t('quote_form.submitting') : t('quote_form.submit')}
            </motion.button>
          </form>
        )}

        <div className="privacy-note">
          <p suppressHydrationWarning>{t('quote_form.privacy_note')}</p>
        </div>
      </div>

      <div className="absolute bottom-0 right-0 w-32 h-32 -mb-16 -mr-16 bg-gradient-to-tl from-blue-100 to-transparent rounded-full opacity-20"></div>
    </motion.div>
  );
}
