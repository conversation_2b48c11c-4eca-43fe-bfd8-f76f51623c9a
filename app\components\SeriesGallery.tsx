'use client';

import { useState } from 'react';
import Image from 'next/image';
import { useLanguage } from './LanguageProvider';

interface SeriesData {
  id: string;
  name: string;
  coverImage: string;
  images: string[];
  description: string;
}

interface SeriesGalleryProps {
  sectionSlug: string;
}

// 获取本地化的系列数据
const getLocalizedSeriesData = (sectionSlug: string, locale: string): SeriesData[] => {
  const seriesDataMap: Record<string, SeriesData[]> = {
    'interactive-projection': [
      {
        id: 'interactive-education',
        name: locale === 'zh' ? '互动教育板块' : 'Interactive Education Module',
        coverImage: '/images/product-sections/互动教育板块/0046.jpg',
        images: [
          '/images/product-sections/互动教育板块/0046.jpg',
          '/images/product-sections/互动教育板块/0047.jpg',
          '/images/product-sections/互动教育板块/0048.jpg',
        ],
        description: locale === 'zh' ? '创新的互动教育解决方案，结合全息投影技术打造沉浸式学习体验' : 'Innovative interactive education solutions combining holographic projection technology to create immersive learning experiences'
      },
      {
        id: 'interactive-tourism',
        name: locale === 'zh' ? '互动文旅板块' : 'Interactive Tourism Module',
        coverImage: '/images/product-sections/互动文旅板块/0035.jpg',
        images: [
          '/images/product-sections/互动文旅板块/0035.jpg',
          '/images/product-sections/互动文旅板块/0036.jpg',
          '/images/product-sections/互动文旅板块/0037.jpg',
          '/images/product-sections/互动文旅板块/0038.jpg',
          '/images/product-sections/互动文旅板块/0039.jpg',
          '/images/product-sections/互动文旅板块/0040.jpg',
          '/images/product-sections/互动文旅板块/0041.jpg',
          '/images/product-sections/互动文旅板块/0042.jpg',
          '/images/product-sections/互动文旅板块/0043.jpg',
          '/images/product-sections/互动文旅板块/0044.jpg',
          '/images/product-sections/互动文旅板块/0045.jpg',
        ],
        description: locale === 'zh' ? '融合文化与科技的互动文旅体验，为游客带来前所未有的沉浸式旅程' : 'Interactive cultural tourism experiences merging culture and technology, bringing unprecedented immersive journeys to visitors'
      },
      {
        id: 'children-entertainment',
        name: locale === 'zh' ? '儿童游乐板块' : 'Children Entertainment Module',
        coverImage: '/images/product-sections/儿童游乐板块/0012.jpg',
        images: [
          '/images/product-sections/儿童游乐板块/0012.jpg',
          '/images/product-sections/儿童游乐板块/0013.jpg',
          '/images/product-sections/儿童游乐板块/0014.jpg',
          '/images/product-sections/儿童游乐板块/0015.jpg',
          '/images/product-sections/儿童游乐板块/0016.jpg',
          '/images/product-sections/儿童游乐板块/0017.jpg',
          '/images/product-sections/儿童游乐板块/0018.jpg',
          '/images/product-sections/儿童游乐板块/0019.jpg',
          '/images/product-sections/儿童游乐板块/0020.jpg',
        ],
        description: locale === 'zh' ? '专为儿童设计的安全有趣的互动游乐设备，激发孩子的想象力和创造力' : 'Safe and fun interactive entertainment equipment designed specifically for children, inspiring imagination and creativity'
      },
      {
        id: 'digital-sports',
        name: locale === 'zh' ? '数字运动板块' : 'Digital Sports Module',
        coverImage: '/images/product-sections/数字运动板块/0021.jpg',
        images: [
          '/images/product-sections/数字运动板块/0021.jpg',
          '/images/product-sections/数字运动板块/0022.jpg',
          '/images/product-sections/数字运动板块/0023.jpg',
          '/images/product-sections/数字运动板块/0024.jpg',
          '/images/product-sections/数字运动板块/0025.jpg',
          '/images/product-sections/数字运动板块/0026.jpg',
          '/images/product-sections/数字运动板块/0027.jpg',
          '/images/product-sections/数字运动板块/0028.jpg',
          '/images/product-sections/数字运动板块/0029.jpg',
          '/images/product-sections/数字运动板块/0030.jpg',
          '/images/product-sections/数字运动板块/0031.jpg',
          '/images/product-sections/数字运动板块/0032.jpg',
          '/images/product-sections/数字运动板块/0033.jpg',
          '/images/product-sections/数字运动板块/0034.jpg',
        ],
        description: locale === 'zh' ? '结合体感技术的数字运动解决方案，让运动更加智能化和趣味化' : 'Digital sports solutions combining motion sensing technology, making sports more intelligent and engaging'
      },
      {
        id: 'new-featured',
        name: locale === 'zh' ? '新品主打' : 'New Featured Products',
        coverImage: '/images/product-sections/新品主打/0007.jpg',
        images: [
          '/images/product-sections/新品主打/0007.jpg',
          '/images/product-sections/新品主打/0008.jpg',
          '/images/product-sections/新品主打/0009.jpg',
          '/images/product-sections/新品主打/0010.jpg',
          '/images/product-sections/新品主打/0011.jpg',
        ],
        description: locale === 'zh' ? '最新推出的创新产品系列，代表了我们在全息投影技术领域的最新突破' : 'Latest innovative product series representing our newest breakthroughs in holographic projection technology'
      }
    ],
    // 为其他板块使用相同的系列数据结构
    'holographic-display': [
      {
        id: 'interactive-education',
        name: locale === 'zh' ? '互动教育板块' : 'Interactive Education Module',
        coverImage: '/images/product-sections/互动教育板块/0046.jpg',
        images: [
          '/images/product-sections/互动教育板块/0046.jpg',
          '/images/product-sections/互动教育板块/0047.jpg',
          '/images/product-sections/互动教育板块/0048.jpg',
        ],
        description: locale === 'zh' ? '创新的互动教育解决方案，结合全息投影技术打造沉浸式学习体验' : 'Innovative interactive education solutions combining holographic projection technology to create immersive learning experiences'
      },
      {
        id: 'interactive-tourism',
        name: locale === 'zh' ? '互动文旅板块' : 'Interactive Tourism Module',
        coverImage: '/images/product-sections/互动文旅板块/0035.jpg',
        images: [
          '/images/product-sections/互动文旅板块/0035.jpg',
          '/images/product-sections/互动文旅板块/0036.jpg',
          '/images/product-sections/互动文旅板块/0037.jpg',
          '/images/product-sections/互动文旅板块/0038.jpg',
          '/images/product-sections/互动文旅板块/0039.jpg',
          '/images/product-sections/互动文旅板块/0040.jpg',
          '/images/product-sections/互动文旅板块/0041.jpg',
          '/images/product-sections/互动文旅板块/0042.jpg',
          '/images/product-sections/互动文旅板块/0043.jpg',
          '/images/product-sections/互动文旅板块/0044.jpg',
          '/images/product-sections/互动文旅板块/0045.jpg',
        ],
        description: locale === 'zh' ? '融合文化与科技的互动文旅体验，为游客带来前所未有的沉浸式旅程' : 'Interactive cultural tourism experiences merging culture and technology, bringing unprecedented immersive journeys to visitors'
      },
      {
        id: 'children-entertainment',
        name: locale === 'zh' ? '儿童游乐板块' : 'Children Entertainment Module',
        coverImage: '/images/product-sections/儿童游乐板块/0012.jpg',
        images: [
          '/images/product-sections/儿童游乐板块/0012.jpg',
          '/images/product-sections/儿童游乐板块/0013.jpg',
          '/images/product-sections/儿童游乐板块/0014.jpg',
          '/images/product-sections/儿童游乐板块/0015.jpg',
          '/images/product-sections/儿童游乐板块/0016.jpg',
          '/images/product-sections/儿童游乐板块/0017.jpg',
          '/images/product-sections/儿童游乐板块/0018.jpg',
          '/images/product-sections/儿童游乐板块/0019.jpg',
          '/images/product-sections/儿童游乐板块/0020.jpg',
        ],
        description: locale === 'zh' ? '专为儿童设计的安全有趣的互动游乐设备，激发孩子的想象力和创造力' : 'Safe and fun interactive entertainment equipment designed specifically for children, inspiring imagination and creativity'
      },
      {
        id: 'digital-sports',
        name: locale === 'zh' ? '数字运动板块' : 'Digital Sports Module',
        coverImage: '/images/product-sections/数字运动板块/0021.jpg',
        images: [
          '/images/product-sections/数字运动板块/0021.jpg',
          '/images/product-sections/数字运动板块/0022.jpg',
          '/images/product-sections/数字运动板块/0023.jpg',
          '/images/product-sections/数字运动板块/0024.jpg',
          '/images/product-sections/数字运动板块/0025.jpg',
          '/images/product-sections/数字运动板块/0026.jpg',
          '/images/product-sections/数字运动板块/0027.jpg',
          '/images/product-sections/数字运动板块/0028.jpg',
          '/images/product-sections/数字运动板块/0029.jpg',
          '/images/product-sections/数字运动板块/0030.jpg',
          '/images/product-sections/数字运动板块/0031.jpg',
          '/images/product-sections/数字运动板块/0032.jpg',
          '/images/product-sections/数字运动板块/0033.jpg',
          '/images/product-sections/数字运动板块/0034.jpg',
        ],
        description: locale === 'zh' ? '结合体感技术的数字运动解决方案，让运动更加智能化和趣味化' : 'Digital sports solutions combining motion sensing technology, making sports more intelligent and engaging'
      },
      {
        id: 'new-featured',
        name: locale === 'zh' ? '新品主打' : 'New Featured Products',
        coverImage: '/images/product-sections/新品主打/0007.jpg',
        images: [
          '/images/product-sections/新品主打/0007.jpg',
          '/images/product-sections/新品主打/0008.jpg',
          '/images/product-sections/新品主打/0009.jpg',
          '/images/product-sections/新品主打/0010.jpg',
          '/images/product-sections/新品主打/0011.jpg',
        ],
        description: locale === 'zh' ? '最新推出的创新产品系列，代表了我们在全息投影技术领域的最新突破' : 'Latest innovative product series representing our newest breakthroughs in holographic projection technology'
      }
    ]
  };

  return seriesDataMap[sectionSlug] || [];
};

export default function SeriesGallery({ sectionSlug }: SeriesGalleryProps) {
  const { locale } = useLanguage();
  const [selectedSeries, setSelectedSeries] = useState<SeriesData | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const seriesData = getLocalizedSeriesData(sectionSlug, locale);

  const handleSeriesClick = (series: SeriesData) => {
    setSelectedSeries(series);
    setSelectedImageIndex(0);
  };

  const handleBackToSeries = () => {
    setSelectedSeries(null);
    setSelectedImageIndex(0);
  };

  if (selectedSeries) {
    // 显示选中系列的所有图片
    return (
      <div className="series-detail-view">
        <div className="py-8 mb-8">
          <button
            onClick={handleBackToSeries}
            className="flex items-center text-blue-600 hover:text-blue-800 transition-colors mb-6"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            {locale === 'zh' ? '返回系列选择' : 'Back to Series Selection'}
          </button>

          <div className="text-center">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">{selectedSeries.name}</h3>
            <p className="text-lg text-gray-600">{selectedSeries.description}</p>
          </div>
        </div>

        {/* 全屏主图展示 - 按原始尺寸 */}
        <div className="fullscreen-image-container mb-8">
          <div className="relative w-full">
            <Image
              src={selectedSeries.images[selectedImageIndex]}
              alt={`${selectedSeries.name} - ${locale === 'zh' ? '图片' : 'Image'} ${selectedImageIndex + 1}`}
              width={1933}
              height={1087}
              style={{ width: '100%', height: 'auto' }}
              className="transition-all duration-500"
            />
          </div>
        </div>

        <style jsx>{`
          .fullscreen-image-container {
            width: 100vw;
            margin-left: calc(-50vw + 50%);
            margin-right: calc(-50vw + 50%);
          }
        `}</style>

        {/* 缩略图导航 */}
        <div className="thumbnails-grid">
          <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
            {selectedSeries.images.map((image, index) => (
              <div
                key={index}
                className={`thumbnail-item cursor-pointer rounded-lg overflow-hidden transition-all duration-300 ${
                  selectedImageIndex === index
                    ? 'ring-4 ring-blue-500 scale-105 shadow-xl'
                    : 'hover:scale-105 hover:shadow-lg ring-2 ring-gray-200'
                }`}
                onClick={() => setSelectedImageIndex(index)}
              >
                <div className="relative w-full aspect-video">
                  <Image
                    src={image}
                    alt={`${selectedSeries.name} - ${locale === 'zh' ? '缩略图' : 'Thumbnail'} ${index + 1}`}
                    fill
                    style={{ objectFit: 'cover' }}
                    className="transition-transform duration-300"
                  />
                  {selectedImageIndex === index && (
                    <div className="absolute inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // 显示系列封面选择
  return (
    <div className="series-gallery">
      <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
        {locale === 'zh' ? '系列展示' : 'Series Showcase'}
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {seriesData.map((series) => (
          <div
            key={series.id}
            className="series-card group cursor-pointer bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1"
            onClick={() => handleSeriesClick(series)}
          >
            <div className="relative w-full h-48">
              <Image
                src={series.coverImage}
                alt={series.name}
                fill
                style={{ objectFit: 'cover' }}
                className="transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="text-white text-sm font-medium">
                    {series.images.length} {locale === 'zh' ? '张图片' : 'Images'}
                  </div>
                </div>
              </div>
            </div>
            <div className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                {series.name}
              </h3>
              <p className="text-gray-600 text-sm line-clamp-2">
                {series.description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
