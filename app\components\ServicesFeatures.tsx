'use client';

import { useLanguage } from './LanguageProvider';

export default function ServicesFeatures() {
  const { t } = useLanguage();

  const features = [
    {
      icon: 'fas fa-compass',
      title: t('serviceFeatures.guidance.title'),
      description: t('serviceFeatures.guidance.description'),
    },
    {
      icon: 'fas fa-shipping-fast',
      title: t('serviceFeatures.delivery.title'),
      description: t('serviceFeatures.delivery.description'),
    },
    {
      icon: 'fas fa-headset',
      title: t('serviceFeatures.afterSales.title'),
      description: t('serviceFeatures.afterSales.description'),
    },
    {
      icon: 'fas fa-award',
      title: t('serviceFeatures.quality.title'),
      description: t('serviceFeatures.quality.description'),
    },
  ];

  return (
    <section className="services-features">
      <div className="container">
        <h2 className="section-title" suppressHydrationWarning>
          {t('serviceFeatures.title')}
        </h2>
        <p className="section-description" suppressHydrationWarning>
          {t('serviceFeatures.description')}
        </p>

        <div className="features-grid">
          {features.map((feature, index) => (
            <div key={index} className="feature-card">
              <div className="feature-icon">
                <i className={feature.icon}></i>
              </div>
              <h3 suppressHydrationWarning>{feature.title}</h3>
              <p suppressHydrationWarning>{feature.description}</p>
            </div>
          ))}
        </div>
      </div>

      <style jsx>{`
        .services-features {
          padding: 8rem 0;
          background-color: white;
          position: relative;
          overflow: hidden;
        }

        .services-features::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 300px;
          background: linear-gradient(
            180deg,
            rgba(243, 244, 246, 0.5) 0%,
            rgba(255, 255, 255, 0) 100%
          );
          z-index: 1;
        }

        .container {
          position: relative;
          z-index: 2;
          max-width: 1600px;
          margin: 0 auto;
          padding: 0 40px;
        }

        .section-title {
          text-align: center;
          margin-bottom: 1.5rem;
          font-size: 3rem;
          font-weight: 700;
          color: #1a1a2e;
          position: relative;
          padding-bottom: 1.5rem;
        }

        .section-title::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 120px;
          height: 5px;
          background: linear-gradient(90deg, #1a1a2e, #4040bf);
          border-radius: 3px;
        }

        .section-description {
          text-align: center;
          max-width: 1000px;
          margin: 0 auto 4rem;
          font-size: 1.3rem;
          color: #6c757d;
          line-height: 1.7;
        }

        .features-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 3rem;
        }

        @media (max-width: 1200px) {
          .features-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 2.5rem;
          }

          .section-title {
            font-size: 2.8rem;
          }

          .section-description {
            font-size: 1.2rem;
          }
        }

        @media (max-width: 768px) {
          .features-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
          }

          .section-title {
            font-size: 2.5rem;
          }
        }

        @media (max-width: 576px) {
          .features-grid {
            grid-template-columns: 1fr;
          }

          .container {
            padding: 0 20px;
          }
        }

        .feature-card {
          text-align: center;
          padding: 3.5rem 2rem;
          border-radius: 16px;
          background-color: #f8f9fa;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
          transition:
            transform 0.3s ease,
            box-shadow 0.3s ease;
          border-bottom: 5px solid #1a1a2e;
          border-top: 1px solid #9ca3af;
          border-left: 1px solid #9ca3af;
          border-right: 1px solid #9ca3af;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .feature-card:hover {
          transform: translateY(-15px);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
          border-top-color: #6b7280;
          border-left-color: #6b7280;
          border-right-color: #6b7280;
        }

        .feature-icon {
          margin: 0 auto 2rem;
          width: 120px;
          height: 120px;
          background: linear-gradient(135deg, #1a1a2e 0%, #2d2d4a 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 3.5rem;
          box-shadow: 0 15px 30px rgba(26, 26, 46, 0.25);
          position: relative;
          overflow: hidden;
        }

        .feature-icon::after {
          content: '';
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          background: radial-gradient(
            circle at center,
            rgba(255, 255, 255, 0.2) 0%,
            transparent 70%
          );
        }

        .feature-card h3 {
          font-size: 1.8rem;
          margin-bottom: 1.5rem;
          font-weight: 600;
          color: #333;
        }

        .feature-card p {
          color: #6c757d;
          font-size: 1.2rem;
          line-height: 1.7;
        }

        @media (max-width: 992px) {
          .feature-icon {
            width: 100px;
            height: 100px;
            font-size: 3rem;
          }

          .feature-card h3 {
            font-size: 1.6rem;
          }

          .feature-card p {
            font-size: 1.1rem;
          }
        }

        @media (max-width: 576px) {
          .feature-icon {
            width: 90px;
            height: 90px;
            font-size: 2.5rem;
          }
        }
      `}</style>
    </section>
  );
}
