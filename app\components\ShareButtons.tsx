'use client';

import React from 'react';
// For actual implementation, you might use a library like react-share
// Or implement direct links to social media share URLs

interface ShareButtonsProps {
  url: string;
  title: string;
  className?: string;
}

const ShareButtons: React.FC<ShareButtonsProps> = ({ url, title, className }) => {
  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);

  const services = [
    {
      name: 'Facebook',
      icon: (
        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24">
          <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z" />
        </svg>
      ),
      shareUrl: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
    },
    {
      name: 'Twitter',
      icon: (
        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24">
          <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-.422.724-.665 1.56-.665 2.456 0 1.492.758 2.895 1.945 3.682-.699-.021-1.356-.216-1.931-.535v.052c0 2.128 1.482 3.857 3.517 4.244-.34.092-.706.141-1.082.141-.268 0-.531-.026-.781-.074.545 1.714 2.117 2.995 3.947 3.031-1.529 1.198-3.419 1.871-5.447 1.871-.344 0-.687-.02-1.023-.06C.975 19.39 3.056 20.5 5.423 20.5c6.458 0 9.998-5.363 9.998-10.073 0-.153 0-.305-.01-.455A7.06 7.06 0 0024 4.557z" />
        </svg>
      ),
      shareUrl: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}`,
    },
    {
      name: 'LinkedIn',
      icon: (
        <svg className="w-5 h-5 fill-current" viewBox="0 0 24 24">
          <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.594-11.018-3.714v-2.155z" />
        </svg>
      ),
      shareUrl: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
    },
    // Add more services as needed (e.g., Pinterest, WhatsApp)
  ];

  return (
    <div className={`flex space-x-2 ${className || ''}`}>
      {services.map(service => (
        <a
          key={service.name}
          href={service.shareUrl}
          target="_blank"
          rel="noopener noreferrer"
          title={`Share on ${service.name}`}
          className="text-gray-600 hover:text-blue-600 transition-colors duration-200 p-2 rounded-full hover:bg-gray-100"
        >
          {service.icon}
          <span className="sr-only">{`Share on ${service.name}`}</span>
        </a>
      ))}
    </div>
  );
};

export default ShareButtons;
