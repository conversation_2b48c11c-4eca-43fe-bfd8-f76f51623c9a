import React from 'react';

interface SkeletonProps {
  type?: 'text' | 'title' | 'image' | 'card' | 'product';
  width?: string | number;
  height?: string | number;
  count?: number;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 骨架屏加载组件
 * 用于内容加载时显示占位符
 */
export default function SkeletonLoader({
  type = 'text',
  width,
  height,
  count = 1,
  className = '',
  style = {},
}: SkeletonProps) {
  // 根据不同类型返回不同的骨架屏样式
  const getTypeStyles = () => {
    switch (type) {
      case 'title':
        return {
          width: width || '70%',
          height: height || '32px',
          marginBottom: '12px',
        };
      case 'image':
        return {
          width: width || '100%',
          height: height || '200px',
          marginBottom: '12px',
        };
      case 'card':
        return {
          width: width || '100%',
          height: height || '320px',
          marginBottom: '20px',
        };
      case 'product':
        return {
          width: width || '100%',
          height: height || '400px',
          borderRadius: '8px',
        };
      default:
        return {
          width: width || '100%',
          height: height || '16px',
          marginBottom: '8px',
        };
    }
  };

  const baseStyles: React.CSSProperties = {
    backgroundColor: 'rgba(226, 232, 240, 0.7)',
    borderRadius: '4px',
    animation: 'pulse 1.5s infinite',
    ...getTypeStyles(),
    ...style,
  };

  // 创建多个骨架项
  const items = [];
  for (let i = 0; i < count; i++) {
    items.push(<div key={i} className={`skeleton-item ${className}`} style={baseStyles} />);
  }

  return (
    <>
      {items}
      <style jsx global>{`
        @keyframes pulse {
          0% {
            opacity: 0.6;
          }
          50% {
            opacity: 0.8;
          }
          100% {
            opacity: 0.6;
          }
        }
      `}</style>
    </>
  );
}

/**
 * 产品卡片骨架屏
 */
export function ProductCardSkeleton() {
  return (
    <div className="product-card-skeleton" style={{ padding: '16px' }}>
      <SkeletonLoader type="image" height={220} />
      <SkeletonLoader type="title" width="80%" style={{ marginTop: '12px' }} />
      <SkeletonLoader type="text" count={2} style={{ marginTop: '8px' }} />
      <SkeletonLoader type="text" width="40%" style={{ marginTop: '16px' }} />
    </div>
  );
}

/**
 * 产品详情页骨架屏
 */
export function ProductDetailSkeleton() {
  return (
    <div
      className="product-detail-skeleton"
      style={{ display: 'flex', flexWrap: 'wrap', gap: '20px' }}
    >
      <div style={{ flex: '1 1 500px' }}>
        <SkeletonLoader type="image" height={400} />
        <div style={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
          {Array(4)
            .fill(0)
            .map((_, i) => (
              <SkeletonLoader key={i} type="image" width="80px" height="80px" />
            ))}
        </div>
      </div>
      <div style={{ flex: '1 1 400px' }}>
        <SkeletonLoader type="title" width="80%" />
        <SkeletonLoader type="text" count={3} />
        <SkeletonLoader type="title" width="40%" style={{ marginTop: '20px' }} />
        <SkeletonLoader type="text" count={4} />
        <SkeletonLoader type="text" width="30%" style={{ marginTop: '30px' }} />
      </div>
    </div>
  );
}
