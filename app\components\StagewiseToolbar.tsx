'use client';

import { useEffect } from 'react';
import { StagewiseToolbar as NextStagewiseToolbar } from '@stagewise/toolbar-next';

/**
 * Stagewise工具栏组件，用于在开发环境中提供AI驱动的编辑功能
 * 该组件只在开发环境中加载，不会包含在生产构建中
 */
const StagewiseToolbar = () => {
  // 配置对象
  const stagewiseConfig = {
    plugins: [],
  };

  // 检查是否为开发环境和客户端环境
  const isDev = process.env.NODE_ENV === 'development';
  const isBrowser = typeof window !== 'undefined';

  // 只在开发环境和客户端环境中渲染工具栏
  if (!isDev || !isBrowser) {
    return null;
  }

  return <NextStagewiseToolbar config={stagewiseConfig} />;
};

export default StagewiseToolbar;
