'use client';

import Link from 'next/link';
import { ReactNode } from 'react';
import { useLanguage } from './LanguageProvider';

interface TranslatedLinkProps {
  href: string;
  children: ReactNode;
  className?: string;
  withLocale?: boolean;
  ariaLabel?: string;
  onClick?: () => void;
}

/**
 * 处理翻译和水合问题的链接组件
 * 提供自动locale处理和水合警告抑制
 */
export default function TranslatedLink({
  href,
  children,
  className,
  withLocale = true,
  ariaLabel,
  onClick,
}: TranslatedLinkProps) {
  const { locale } = useLanguage();

  // 确定是否需要添加locale前缀
  const finalHref =
    withLocale && !href.startsWith('/')
      ? `/${locale}/${href}`
      : withLocale &&
          !href.startsWith(`/${locale}`) &&
          !href.startsWith('http') &&
          !href.startsWith('mailto:') &&
          !href.startsWith('tel:')
        ? `/${locale}${href}`
        : href;

  return (
    <Link
      href={finalHref}
      className={className}
      aria-label={ariaLabel}
      onClick={onClick}
      suppressHydrationWarning
    >
      {children}
    </Link>
  );
}
