'use client';

import React from 'react';
import { useLanguage } from './LanguageProvider';

/**
 * 通用的翻译文本组件，简单版本，使用span作为默认元素
 */
interface TranslatedTextProps extends React.ComponentPropsWithoutRef<'span'> {
  id: string;
  fallback: string;
}

export function TranslatedText({
  id,
  fallback,
  className,
  children,
  ...props
}: TranslatedTextProps) {
  const { t } = useLanguage();
  const translatedText = t(id, { fallback });

  return (
    <span className={className} suppressHydrationWarning {...props}>
      {translatedText}
      {children}
    </span>
  );
}

/**
 * h1标题翻译组件
 */
interface TranslatedHeading1Props extends React.ComponentPropsWithoutRef<'h1'> {
  id: string;
  fallback: string;
}

export function TranslatedHeading1({
  id,
  fallback,
  className,
  children,
  ...props
}: TranslatedHeading1Props) {
  const { t } = useLanguage();
  const translatedText = t(id, { fallback });

  return (
    <h1 className={className} suppressHydrationWarning {...props}>
      {translatedText}
      {children}
    </h1>
  );
}

/**
 * h2标题翻译组件
 */
interface TranslatedHeading2Props extends React.ComponentPropsWithoutRef<'h2'> {
  id: string;
  fallback: string;
}

export function TranslatedHeading2({
  id,
  fallback,
  className,
  children,
  ...props
}: TranslatedHeading2Props) {
  const { t } = useLanguage();
  const translatedText = t(id, { fallback });

  return (
    <h2 className={className} suppressHydrationWarning {...props}>
      {translatedText}
      {children}
    </h2>
  );
}

/**
 * h3标题翻译组件
 */
interface TranslatedHeading3Props extends React.ComponentPropsWithoutRef<'h3'> {
  id: string;
  fallback: string;
}

export function TranslatedHeading3({
  id,
  fallback,
  className,
  children,
  ...props
}: TranslatedHeading3Props) {
  const { t } = useLanguage();
  const translatedText = t(id, { fallback });

  return (
    <h3 className={className} suppressHydrationWarning {...props}>
      {translatedText}
      {children}
    </h3>
  );
}

/**
 * 段落翻译组件
 */
interface TranslatedParagraphProps extends React.ComponentPropsWithoutRef<'p'> {
  id: string;
  fallback: string;
}

export function TranslatedParagraph({
  id,
  fallback,
  className,
  children,
  ...props
}: TranslatedParagraphProps) {
  const { t } = useLanguage();
  const translatedText = t(id, { fallback });

  return (
    <p className={className} suppressHydrationWarning {...props}>
      {translatedText}
      {children}
    </p>
  );
}

/**
 * 标签翻译组件
 */
interface TranslatedLabelProps extends React.ComponentPropsWithoutRef<'label'> {
  id: string;
  fallback: string;
  htmlFor?: string;
}

export function TranslatedLabel({
  id,
  fallback,
  className,
  htmlFor,
  children,
  ...props
}: TranslatedLabelProps) {
  const { t } = useLanguage();
  const translatedText = t(id, { fallback });

  return (
    <label htmlFor={htmlFor} className={className} suppressHydrationWarning {...props}>
      {translatedText}
      {children}
    </label>
  );
}

/**
 * 表单元素的placeholder处理hook
 */
export function useTranslatedText(id: string, fallback: string): string {
  const { t } = useLanguage();
  return t(id, { fallback });
}
