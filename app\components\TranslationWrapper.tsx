'use client';

import React from 'react';
import { useLanguage } from './LanguageProvider';

interface TranslationWrapperProps {
  children: (
    t: (key: string, params?: Record<string, string | number>) => string,
    locale: string
  ) => React.ReactNode;
}

/**
 * 一个辅助组件，使非客户端组件也能使用翻译功能
 * 用法示例:
 * <TranslationWrapper>
 *   {(t, locale) => (
 *     <div>
 *       <h1>{t('some.translation.key')}</h1>
 *       <p>Current language: {locale}</p>
 *     </div>
 *   )}
 * </TranslationWrapper>
 */
export default function TranslationWrapper({ children }: TranslationWrapperProps) {
  const { t, locale } = useLanguage();

  return <>{children(t, locale)}</>;
}
