"use client"

import { useState, useRef, useEffect } from "react"
import { Play, Pause, Volume2, VolumeX, Maximize, X } from "lucide-react"
import { useTranslation } from '../utils/useTranslation'

interface Video {
  id: string
  title: string
  title_en: string
  duration: string
  category: string
  category_en: string
  aspectRatio: "landscape" | "portrait"
  resolution: string
  videoUrl: string
}

const videos: Video[] = [
  {
    id: "10",
    title: "咯咯魔法椅",
    title_en: "Magic Chair",
    duration: "2:15",
    category: "儿童娱乐",
    category_en: "Children Entertainment",
    aspectRatio: "portrait",
    resolution: "1080x1920",
    videoUrl: "/videos/13395311668128730.mp4"
  },
  {
    id: "11",
    title: "重力玩家",
    title_en: "Gravity Player",
    duration: "3:24",
    category: "体感运动",
    category_en: "Motion Sensing",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395311687228488.mp4"
  },
  {
    id: "12",
    title: "水跃星空",
    title_en: "Water Jump Starry Sky",
    duration: "2:45",
    category: "互动投影",
    category_en: "Interactive Projection",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395311722426731.mp4"
  },
  {
    id: "13",
    title: "宇宙墙面",
    title_en: "Universe Wall",
    duration: "4:12",
    category: "墙面互动",
    category_en: "Wall Interactive",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395311740172146.mp4"
  },
  {
    id: "14",
    title: "互动砸球",
    title_en: "Interactive Ball Smashing",
    duration: "3:56",
    category: "互动娱乐",
    category_en: "Interactive Entertainment",
    aspectRatio: "portrait",
    resolution: "1080x1920",
    videoUrl: "/videos/13395311756835607.mp4"
  },
  {
    id: "15",
    title: "互动滑梯",
    title_en: "Interactive Slide",
    duration: "2:18",
    category: "儿童娱乐",
    category_en: "Children Entertainment",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395311785464099.mp4"
  },
  {
    id: "16",
    title: "神笔绘画",
    title_en: "Magic Brush Painting",
    duration: "5:03",
    category: "AR教育",
    category_en: "AR Education",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395311798953886.mp4"
  },
  {
    id: "17",
    title: "AR智慧教室",
    title_en: "AR Smart Classroom",
    duration: "3:37",
    category: "AR教育",
    category_en: "AR Education",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395311818043592.mp4"
  },
  {
    id: "18",
    title: "互动沙池",
    title_en: "Interactive Sand Pool",
    duration: "4:28",
    category: "儿童互动",
    category_en: "Children Interactive",
    aspectRatio: "portrait",
    resolution: "1080x1920",
    videoUrl: "/videos/13395311832319856.mp4"
  },
  {
    id: "19",
    title: "AR沙桌",
    title_en: "AR Sand Table",
    duration: "6:15",
    category: "AR技术",
    category_en: "AR Technology",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395311848006869.mp4"
  },
  {
    id: "20",
    title: "互动滑板",
    title_en: "Interactive Skateboard",
    duration: "2:15",
    category: "体感运动",
    category_en: "Motion Sensing",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395311866301039.mp4"
  },
  {
    id: "21",
    title: "益智流水墙",
    title_en: "Educational Water Wall",
    duration: "3:24",
    category: "智能教育",
    category_en: "Smart Education",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395311897107446.mp4"
  },
  {
    id: "22",
    title: "体感大师",
    title_en: "Motion Sensing Master",
    duration: "2:45",
    category: "体感运动",
    category_en: "Motion Sensing",
    aspectRatio: "portrait",
    resolution: "1080x1920",
    videoUrl: "/videos/13395311945076997.mp4"
  },
  {
    id: "23",
    title: "体感攀岩",
    title_en: "Motion Sensing Climbing",
    duration: "4:12",
    category: "体感运动",
    category_en: "Motion Sensing",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395311986456123.mp4"
  },
  {
    id: "24",
    title: "跳跃格子",
    title_en: "Jumping Grid",
    duration: "3:56",
    category: "儿童互动",
    category_en: "Children Interactive",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395312019160663.mp4"
  },
  {
    id: "25",
    title: "运动一体机",
    title_en: "Sports All-in-One",
    duration: "2:18",
    category: "体感运动",
    category_en: "Motion Sensing",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395312071571352.mp4"
  },
  {
    id: "26",
    title: "移动射箭",
    title_en: "Mobile Archery",
    duration: "5:03",
    category: "体感运动",
    category_en: "Motion Sensing",
    aspectRatio: "portrait",
    resolution: "1080x1920",
    videoUrl: "/videos/13395312104138908.mp4"
  },
  {
    id: "27",
    title: "实弹射击",
    title_en: "Live Ammunition Shooting",
    duration: "3:37",
    category: "体感运动",
    category_en: "Motion Sensing",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395312143443916.mp4"
  },
  {
    id: "28",
    title: "互动足球",
    title_en: "Interactive Football",
    duration: "4:28",
    category: "体感运动",
    category_en: "Motion Sensing",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395312156081604.mp4"
  },
  {
    id: "29",
    title: "魔力滚球",
    title_en: "Magic Rolling Ball",
    duration: "6:15",
    category: "互动娱乐",
    category_en: "Interactive Entertainment",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395312211986425.mp4"
  },
  {
    id: "30",
    title: "人体闯关",
    title_en: "Human Adventure",
    duration: "2:15",
    category: "体感运动",
    category_en: "Motion Sensing",
    aspectRatio: "portrait",
    resolution: "1080x1920",
    videoUrl: "/videos/13395312244333114.mp4"
  },
  {
    id: "31",
    title: "体感蹦床",
    title_en: "Motion Sensing Trampoline",
    duration: "3:24",
    category: "体感运动",
    category_en: "Motion Sensing",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395312273652728.mp4"
  },
  {
    id: "32",
    title: "动感单车",
    title_en: "Dynamic Bicycle",
    duration: "2:45",
    category: "体感运动",
    category_en: "Motion Sensing",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395312320307968.mp4"
  },
  {
    id: "33",
    title: "模拟拳击",
    title_en: "Simulated Boxing",
    duration: "4:12",
    category: "体感运动",
    category_en: "Motion Sensing",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395312403438645.mp4"
  },
  {
    id: "34",
    title: "模拟网球",
    title_en: "Simulated Tennis",
    duration: "3:56",
    category: "体感运动",
    category_en: "Motion Sensing",
    aspectRatio: "portrait",
    resolution: "1080x1920",
    videoUrl: "/videos/13395312421142831.mp4"
  },
  {
    id: "35",
    title: "全息宴会厅",
    title_en: "Holographic Banquet Hall",
    duration: "2:18",
    category: "全息技术",
    category_en: "Holographic Technology",
    aspectRatio: "landscape",
    resolution: "1920x1080",
    videoUrl: "/videos/13395312446431334.mp4"
  }
]

interface VideoGalleryProps {
  className?: string
}

export default function VideoGallery({ className = "" }: VideoGalleryProps) {
  const { t, locale } = useTranslation()
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [showControls, setShowControls] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string>(t('video_gallery.filter_all', { fallback: '全部' }))
  const [selectedFormat, setSelectedFormat] = useState<string>(t('video_gallery.filter_all', { fallback: '全部' }))

  const videoRef = useRef<HTMLVideoElement>(null)
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const getLocalizedCategories = () => {
    const allCategories = videos.map((v) => locale === 'zh' ? v.category : v.category_en)
    return [t('video_gallery.filter_all', { fallback: '全部' }), ...Array.from(new Set(allCategories))]
  }

  const getLocalizedFormats = () => [
    t('video_gallery.filter_all', { fallback: '全部' }),
    t('video_gallery.filter_landscape', { fallback: '横屏视频' }),
    t('video_gallery.filter_portrait', { fallback: '竖屏视频' })
  ]

  const filteredVideos = videos.filter((video) => {
    const videoCategory = locale === 'zh' ? video.category : video.category_en
    const allText = t('video_gallery.filter_all', { fallback: '全部' })
    const landscapeText = t('video_gallery.filter_landscape', { fallback: '横屏视频' })
    const portraitText = t('video_gallery.filter_portrait', { fallback: '竖屏视频' })
    
    const categoryMatch = selectedCategory === allText || videoCategory === selectedCategory
    const formatMatch =
      selectedFormat === allText ||
      (selectedFormat === landscapeText && video.aspectRatio === "landscape") ||
      (selectedFormat === portraitText && video.aspectRatio === "portrait")
    return categoryMatch && formatMatch
  }).sort((a, b) => {
    // 横屏视频排在前面，竖屏视频排在后面
    if (a.aspectRatio === "landscape" && b.aspectRatio === "portrait") return -1
    if (a.aspectRatio === "portrait" && b.aspectRatio === "landscape") return 1
    return 0
  })

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const updateTime = () => setCurrentTime(video.currentTime)
    const updateDuration = () => setDuration(video.duration)

    video.addEventListener("timeupdate", updateTime)
    video.addEventListener("loadedmetadata", updateDuration)
    video.addEventListener("ended", () => setIsPlaying(false))

    return () => {
      video.removeEventListener("timeupdate", updateTime)
      video.removeEventListener("loadedmetadata", updateDuration)
      video.removeEventListener("ended", () => setIsPlaying(false))
    }
  }, [selectedVideo])

  const openVideo = (video: Video) => {
    setSelectedVideo(video)
    setIsPlaying(false)
    setCurrentTime(0)
  }

  const closeVideo = () => {
    setSelectedVideo(null)
    setIsPlaying(false)
    if (videoRef.current) {
      videoRef.current.pause()
    }
  }

  const togglePlay = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
    setIsPlaying(!isPlaying)
  }

  const handleProgressChange = (value: number[]) => {
    const video = videoRef.current
    if (!video) return

    const newTime = (value[0] / 100) * duration
    video.currentTime = newTime
    setCurrentTime(newTime)
  }

  const handleVolumeChange = (value: number[]) => {
    const video = videoRef.current
    if (!video) return

    const newVolume = value[0] / 100
    video.volume = newVolume
    setVolume(newVolume)
    setIsMuted(newVolume === 0)
  }

  const toggleMute = () => {
    const video = videoRef.current
    if (!video) return

    if (isMuted) {
      video.volume = volume
      setIsMuted(false)
    } else {
      video.volume = 0
      setIsMuted(true)
    }
  }

  const toggleFullscreen = () => {
    const video = videoRef.current
    if (!video) return

    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      video.requestFullscreen()
    }
  }

  const handleMouseMove = () => {
    setShowControls(true)
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current)
    }
    controlsTimeoutRef.current = setTimeout(() => {
      if (isPlaying) {
        setShowControls(false)
      }
    }, 3000)
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, "0")}`
  }

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0

  return (
    <div className={`w-full max-w-7xl mx-auto p-6 ${className}`}>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          {t('video_gallery.title', { fallback: '产品演示视频' })}
        </h1>
        <p className="text-xl text-gray-600">
          {t('video_gallery.subtitle', { fallback: '探索我们完整的交互式技术解决方案' })}
        </p>
      </div>



      {/* Video Grid */}
      <div className="video-grid">
        <style jsx>{`
          .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
            align-items: start;
            justify-items: center;
          }
          @media (max-width: 1024px) {
            .video-grid {
              grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
              gap: 1rem;
            }
          }
          @media (max-width: 768px) {
            .video-grid {
              grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
              gap: 0.75rem;
            }
          }
          @media (max-width: 640px) {
            .video-grid {
              grid-template-columns: repeat(2, 1fr);
              gap: 0.5rem;
            }
          }
        `}</style>
        {filteredVideos.map((video) => (
          <div
            key={video.id}
            className={`relative group cursor-pointer overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gray-100 ${
              video.aspectRatio === "landscape" ? "aspect-video" : "aspect-[9/16]"
            } w-full max-w-xs`}
            onClick={() => openVideo(video)}
          >
              <video
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                preload="metadata"
                muted
                playsInline
              >
                <source src={video.videoUrl} type="video/mp4" />
              </video>

              {/* Play Button Overlay */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                <div className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 shadow-lg">
                  <Play className="w-6 h-6 text-gray-900 ml-1" fill="currentColor" />
                </div>
              </div>

              {/* Video Info Overlay */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent p-2 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <h3 className="font-medium mb-1 line-clamp-2 text-xs leading-tight">
                  {video.title}
                </h3>
                <div className="flex items-center justify-between text-xs text-gray-300">
                  <span className="text-xs">{video.views} views</span>
                  <span className="text-xs">{video.uploadDate}</span>
                </div>
              </div>
          </div>
        ))}
      </div>

      {/* Video Modal */}
      {selectedVideo && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div
            className={`relative bg-black rounded-lg overflow-hidden ${
              selectedVideo.aspectRatio === "portrait" ? "max-w-2xl" : "max-w-5xl"
            } w-full`}
          >
            {/* Close Button */}
            <button
              className="absolute top-4 right-4 z-50 text-white hover:bg-white/20 rounded-full w-10 h-10 flex items-center justify-center"
              onClick={closeVideo}
            >
              <X className="w-5 h-5" />
            </button>

            {/* Video Player */}
            <div
              className={`relative group ${
                selectedVideo.aspectRatio === "landscape" ? "aspect-video" : "aspect-[9/16]"
              }`}
              onMouseMove={handleMouseMove}
              onMouseLeave={() => isPlaying && setShowControls(false)}
            >
              <video
                ref={videoRef}
                className="w-full h-full object-cover"
                onClick={togglePlay}
              >
                <source
                  src={selectedVideo.videoUrl || "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"}
                  type="video/mp4"
                />
                {t('video_gallery.unsupported_browser', { fallback: '您的浏览器不支持视频播放。' })}
              </video>

              {/* Play Button Overlay */}
              {!isPlaying && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                  <button
                    className="w-20 h-20 rounded-full bg-white/90 hover:bg-white text-black shadow-lg flex items-center justify-center"
                    onClick={togglePlay}
                  >
                    <Play className="w-8 h-8 ml-1" fill="currentColor" />
                  </button>
                </div>
              )}

              {/* Video Controls */}
              <div
                className={`absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent transition-opacity duration-300 ${showControls ? "opacity-100" : "opacity-0"}`}
              >
                {/* Progress Bar */}
                <div className="px-6 pb-2">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={progress}
                    onChange={(e) => handleProgressChange([parseFloat(e.target.value)])}
                    className="w-full h-1 bg-white/30 rounded-lg appearance-none cursor-pointer"
                  />
                </div>

                {/* Control Buttons */}
                <div className="flex items-center justify-between px-6 pb-4">
                  <div className="flex items-center gap-4">
                    <button
                      className="text-white hover:bg-white/20 w-10 h-10 rounded flex items-center justify-center"
                      onClick={togglePlay}
                    >
                      {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
                    </button>

                    <div className="flex items-center gap-2">
                      <button
                        className="text-white hover:bg-white/20 w-10 h-10 rounded flex items-center justify-center"
                        onClick={toggleMute}
                      >
                        {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                      </button>
                      <div className="w-20">
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={isMuted ? 0 : volume * 100}
                          onChange={(e) => handleVolumeChange([parseFloat(e.target.value)])}
                          className="w-full h-1 bg-white/30 rounded-lg appearance-none cursor-pointer"
                        />
                      </div>
                    </div>

                    <div className="text-white text-sm font-medium">
                      {formatTime(currentTime)} / {formatTime(duration)}
                    </div>

                    <div className="hidden md:block text-white text-lg font-medium max-w-md truncate">
                      {locale === 'zh' ? selectedVideo.title : selectedVideo.title_en}
                    </div>

                    <div className="text-white text-sm bg-white/20 px-2 py-1 rounded">{selectedVideo.resolution}</div>
                  </div>

                  <button
                    className="text-white hover:bg-white/20 w-10 h-10 rounded flex items-center justify-center"
                    onClick={toggleFullscreen}
                  >
                    <Maximize className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}