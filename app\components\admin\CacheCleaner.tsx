'use client';

import { useState } from 'react';

export default function CacheCleaner() {
  const [isClearing, setIsClearing] = useState(false);
  const [message, setMessage] = useState('');

  const clearCache = async () => {
    setIsClearing(true);
    setMessage('');

    try {
      const response = await fetch('/api/admin/clear-cache', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        setMessage('缓存清理成功！');
      } else {
        setMessage('缓存清理失败，请重试。');
      }
    } catch (error) {
      console.error('清理缓存时出错:', error);
      setMessage('缓存清理失败，请重试。');
    } finally {
      setIsClearing(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">缓存管理</h2>
      <p className="text-gray-600 mb-4">
        清理系统缓存可以确保最新的内容和配置生效。
      </p>
      
      <div className="flex items-center gap-4">
        <button
          onClick={clearCache}
          disabled={isClearing}
          className={`px-4 py-2 rounded-md font-medium transition-colors ${
            isClearing
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-red-600 text-white hover:bg-red-700'
          }`}
        >
          {isClearing ? '清理中...' : '清理缓存'}
        </button>
        
        {message && (
          <span
            className={`text-sm ${
              message.includes('成功') ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {message}
          </span>
        )}
      </div>
    </div>
  );
}
