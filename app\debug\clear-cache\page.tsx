'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function ClearCachePage() {
  const [isClearing, setIsClearing] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // 清除缓存的函数
  const clearCache = async () => {
    try {
      setIsClearing(true);
      setError(null);
      setResult(null);

      // 添加时间戳防止浏览器缓存
      const response = await fetch(`/api/cache/clear?t=${Date.now()}`, {
        method: 'POST',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      const data = await response.json();
      setResult(data);

      // 如果成功清除缓存，刷新页面
      if (data.success) {
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      }
    } catch (err) {
      console.error('清除缓存失败:', err);
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setIsClearing(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">缓存清理工具</h1>
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <p className="mb-4 text-gray-700">
          此工具可以清除应用程序的各种缓存，包括内存缓存、模块缓存和浏览器缓存，帮助解决由于缓存导致的问题。
        </p>
        <button
          onClick={clearCache}
          disabled={isClearing}
          className={`px-4 py-2 rounded-md ${
            isClearing
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          } transition-colors`}
        >
          {isClearing ? '清除中...' : '清除所有缓存'}
        </button>

        {/* 显示结果 */}
        {result && (
          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-md">
            <h2 className="text-lg font-semibold text-green-800 mb-2">
              {result.success ? '缓存清除成功' : '操作完成但有警告'}
            </h2>
            <pre className="bg-white p-3 rounded text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
            {result.success && (
              <p className="mt-2 text-green-600">页面将在几秒后刷新...</p>
            )}
          </div>
        )}

        {/* 显示错误 */}
        {error && (
          <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <h2 className="text-lg font-semibold text-red-800 mb-2">清除缓存失败</h2>
            <p className="text-red-700">{error}</p>
          </div>
        )}
      </div>

      <div className="bg-gray-50 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">其他调试工具</h2>
        <ul className="space-y-2">
          <li>
            <Link
              href="/debug"
              className="text-blue-600 hover:text-blue-800 hover:underline"
            >
              返回调试页面
            </Link>
          </li>
          <li>
            <Link
              href="/api/cache/clear"
              className="text-blue-600 hover:text-blue-800 hover:underline"
              target="_blank"
            >
              直接访问缓存清理API
            </Link>
          </li>
          <li>
            <Link
              href="/zh/products"
              className="text-blue-600 hover:text-blue-800 hover:underline"
            >
              测试产品列表页面
            </Link>
          </li>
        </ul>
      </div>
    </div>
  );
} 