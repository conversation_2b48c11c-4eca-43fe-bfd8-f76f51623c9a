'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';

export default function DebugPage() {
  const [diagnosticData, setDiagnosticData] = useState<any>(null);
  const [apiData, setApiData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [productToTest, setProductToTest] = useState('ar-trampoline');
  const [apiStatus, setApiStatus] = useState<string>('未检查');
  const [apiResponse, setApiResponse] = useState<any>(null);
  
  // 获取诊断数据
  const fetchDiagnosticData = async () => {
    try {
      setLoading(true);
      const res = await fetch('/api/diagnose-route');
      if (!res.ok) throw new Error('Failed to fetch diagnostic data');
      const data = await res.json();
      setDiagnosticData(data);
      setLoading(false);
    } catch (err: any) {
      setError(err.message || 'Unknown error');
      setLoading(false);
    }
  };
  
  // 测试产品API
  const testProductApi = async () => {
    try {
      setLoading(true);
      const res = await fetch(`/api/test-product?slug=${productToTest}`);
      if (!res.ok) throw new Error('Failed to fetch product API data');
      const data = await res.json();
      setApiData(data);
      setLoading(false);
    } catch (err: any) {
      setError(err.message || 'Unknown error');
      setLoading(false);
    }
  };
  
  // 检查API状态
  const checkApiStatus = async () => {
    try {
      setApiStatus('检查中...');
      const response = await fetch('/api/db-health');
      const data = await response.json();
      setApiResponse(data);
      setApiStatus(data.success ? '正常' : '异常');
    } catch (error) {
      console.error('API检查失败:', error);
      setApiStatus('异常');
      setApiResponse({ error: String(error) });
    }
  };
  
  // 初始加载
  useEffect(() => {
    fetchDiagnosticData();
  }, []);
  
  return (
    <div className="container mx-auto px-4 py-8 max-w-5xl">
      <h1 className="text-3xl font-bold mb-6">网站诊断工具</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p><strong>错误:</strong> {error}</p>
        </div>
      )}
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">API状态</h2>
          <div className="flex items-center">
            <div
              className={`w-3 h-3 rounded-full mr-2 ${
                apiStatus === '正常'
                  ? 'bg-green-500'
                  : apiStatus === '异常'
                  ? 'bg-red-500'
                  : 'bg-gray-400'
              }`}
            ></div>
            <span>{apiStatus}</span>
          </div>
        </div>
        <button
          onClick={checkApiStatus}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
        >
          检查API状态
        </button>

        {apiResponse && (
          <div className="mt-4 p-4 bg-gray-100 rounded overflow-x-auto">
            <pre className="text-sm">{JSON.stringify(apiResponse, null, 2)}</pre>
          </div>
        )}
      </div>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">测试产品API</h2>
        <div className="flex gap-2 mb-4">
          <input 
            type="text" 
            value={productToTest} 
            onChange={(e) => setProductToTest(e.target.value)}
            className="border p-2 rounded flex-grow"
            placeholder="输入产品slug"
          />
          <button 
            onClick={testProductApi}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            disabled={loading}
          >
            测试API
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 诊断结果 */}
        <div className="bg-gray-50 p-4 rounded shadow">
          <h2 className="text-xl font-semibold mb-4">路由诊断结果</h2>
          {loading && !diagnosticData ? (
            <p>加载中...</p>
          ) : diagnosticData ? (
            <div>
              <div className="mb-4">
                <h3 className="font-semibold">系统信息:</h3>
                <p>Node环境: {diagnosticData.nextjsInfo?.nodeEnv || 'Unknown'}</p>
                <p>Next.js版本: {diagnosticData.nextjsInfo?.nextVersion || 'Unknown'}</p>
              </div>
              
              <div className="mb-4">
                <h3 className="font-semibold">文件系统检查:</h3>
                <p>产品详情页文件存在: {diagnosticData.fileSystem?.productPageExists ? '✅ 是' : '❌ 否'}</p>
                {diagnosticData.fileSystem?.productPageExists && (
                  <div className="mt-2">
                    <p className="font-medium">文件内容预览:</p>
                    <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                      {diagnosticData.fileSystem?.productPagePreview}
                    </pre>
                  </div>
                )}
              </div>
              
              <div className="mb-4">
                <h3 className="font-semibold">导航链接测试:</h3>
                <div className="flex flex-col gap-2 mt-2">
                  <Link 
                    href={diagnosticData.navigationTest?.productListLink || '#'} 
                    target="_blank"
                    className="text-blue-500 hover:underline"
                  >
                    测试产品列表页链接
                  </Link>
                  <Link 
                    href={diagnosticData.navigationTest?.productDetailLinkTest || '#'} 
                    target="_blank"
                    className="text-blue-500 hover:underline"
                  >
                    测试产品详情页链接
                  </Link>
                </div>
              </div>
            </div>
          ) : (
            <p>无诊断数据</p>
          )}
        </div>
        
        {/* API测试结果 */}
        <div className="bg-gray-50 p-4 rounded shadow">
          <h2 className="text-xl font-semibold mb-4">API测试结果</h2>
          {loading && !apiData ? (
            <p>加载中...</p>
          ) : apiData ? (
            <div>
              <div className="mb-4">
                <h3 className="font-semibold">数据库状态:</h3>
                <p>数据库健康: {apiData.database?.isHealthy ? '✅ 正常' : '❌ 异常'}</p>
                {apiData.database?.error && (
                  <div className="mt-2">
                    <p className="text-red-500">数据库错误:</p>
                    <pre className="bg-red-50 p-2 rounded text-xs overflow-auto max-h-40">
                      {JSON.stringify(apiData.database.error, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
              
              <div className="mb-4">
                <h3 className="font-semibold">API响应:</h3>
                {apiData.api?.response ? (
                  <div>
                    <p>状态: {apiData.api.response.status} {apiData.api.response.statusText}</p>
                    {apiData.api.data && (
                      <div className="mt-2">
                        <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-40">
                          {JSON.stringify(apiData.api.data, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                ) : (
                  <p>无API响应数据</p>
                )}
              </div>
              
              <div>
                <h3 className="font-semibold">可用的模拟产品:</h3>
                {apiData.mockData?.products?.length > 0 ? (
                  <ul className="list-disc list-inside">
                    {apiData.mockData.products.map((product: any, index: number) => (
                      <li key={index} className="text-sm">
                        <span className="font-medium">{product.name}</span> (slug: {product.slug})
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p>无模拟产品数据</p>
                )}
              </div>
            </div>
          ) : (
            <p>无API测试数据</p>
          )}
        </div>
      </div>
      
      <div className="mt-8 p-4 bg-yellow-50 rounded shadow">
        <h2 className="text-xl font-semibold mb-2">问题修复建议</h2>
        <ul className="list-disc list-inside space-y-2">
          <li>检查数据库连接是否正常 - 尝试使用模拟数据测试</li>
          <li>检查API路由是否正确响应 - 测试<code>/api/products/by-slug/[slug]</code>接口</li>
          <li>检查产品详情页组件是否正确处理数据 - 查看<code>app/[lang]/products/[slug]/page.tsx</code></li>
          <li>检查网络请求是否有跨域或其他限制 - 查看浏览器控制台</li>
          <li>测试在不同浏览器中的表现 - 清除缓存后再试</li>
        </ul>
      </div>
      
      <div className="mt-6 bg-blue-50 rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">国际化问题修复</h2>
        <p className="text-gray-700 mb-4">
          如果遇到国际化字典加载失败或字典未定义错误，请使用缓存清理工具清除应用缓存，然后刷新页面。如果问题仍然存在，可能需要重启服务器。
        </p>
        <div className="flex space-x-4">
          <Link
            href="/debug/clear-cache"
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
          >
            清理缓存
          </Link>
        </div>
      </div>
    </div>
  );
} 