// favicon.ts
// 生成基本的favicon数据URL
export function generateFavicon(): string {
  // 简单的SVG图标，蓝色的圆形
  const svg = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
    <circle cx="50" cy="50" r="45" fill="#0a59f7" />
    <text x="50" y="57" font-family="Arial" font-size="50" fill="white" text-anchor="middle">I</text>
  </svg>`;

  // 转换为Base64
  const base64 = Buffer.from(svg).toString('base64');
  return `data:image/svg+xml;base64,${base64}`;
}
