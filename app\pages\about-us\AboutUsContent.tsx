'use client';

import Image from 'next/image';
import Link from 'next/link';

export default function AboutUsContent() {
  return (
    <main className="main-content">
      <section className="about-header">
        <div className="container">
          <h1 className="page-title">About Us</h1>
          <div className="breadcrumbs">
            <Link href="/">Home</Link> &gt; <span>About Us</span>
          </div>
        </div>
      </section>

      <section className="about-intro">
        <div className="container">
          <div className="about-grid">
            <div className="about-image">
              <Image
                src="/images/company/about-company.jpg"
                alt="About Guangzhou Junsheng Technology Co., Ltd."
                width={600}
                height={400}
                style={{ objectFit: 'cover', borderRadius: '8px' }}
              />
            </div>
            <div className="about-content">
              <h2>Infinity Playground Park</h2>
              <p>
                Founded in 2005, Infinity Playground Park has become a leading manufacturer and
                designer of indoor playground equipment. With over 1,000 successful projects
                worldwide, we specialize in creating unique and engaging play experiences for
                children of all ages.
              </p>
              <p>
                Our 20,000 square meter manufacturing facility and team of over 200 dedicated
                employees ensure that every product meets the highest quality and safety standards.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="our-mission">
        <div className="container">
          <h2 className="section-title">Our Mission</h2>
          <div className="mission-content">
            <p>
              At Infinity Playground Park, our mission is to create innovative and safe play
              environments that inspire imagination, promote physical activity, and bring joy to
              children around the world.
            </p>
            <p>
              We believe in the power of play to develop essential physical, cognitive, and social
              skills in children, contributing to their overall well-being and growth.
            </p>
          </div>
          <div className="stats-grid">
            <div className="stat-item">
              <div className="stat-number">1,000+</div>
              <div className="stat-label">Projects Worldwide</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">15+</div>
              <div className="stat-label">Years Experience</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">20+</div>
              <div className="stat-label">Countries Served</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">100%</div>
              <div className="stat-label">Safety Compliance</div>
            </div>
          </div>
        </div>
      </section>

      <section className="team-section">
        <div className="container">
          <h2 className="section-title">Our Professional Team</h2>
          <div className="team-image" style={{ marginBottom: '30px' }}>
            <Image
              src="/images/company/team-group.jpg"
              alt="Guangzhou Junsheng Technology Team"
              width={1000}
              height={400}
              style={{ width: '100%', height: 'auto', objectFit: 'cover', borderRadius: '8px' }}
            />
          </div>
        </div>
      </section>

      <section className="company-building">
        <div className="container">
          <h2 className="section-title">Company Headquarters</h2>
          <div className="building-image" style={{ marginBottom: '30px' }}>
            <Image
              src="/images/company/company-building.jpg"
              alt="Guangzhou Junsheng Technology Headquarters"
              width={1000}
              height={400}
              style={{ width: '100%', height: 'auto', objectFit: 'cover', borderRadius: '8px' }}
            />
          </div>
        </div>
      </section>

      <section className="contact-cta">
        <div className="container">
          <h2>Ready to Start Your Playground Project?</h2>
          <p>Contact our team today to discuss your custom playground needs.</p>
          <Link href="/pages/contact-us" className="btn-primary">
            Contact Us Now
          </Link>
        </div>
      </section>
    </main>
  );
}
