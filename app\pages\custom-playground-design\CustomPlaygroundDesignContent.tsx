'use client';

import Image from 'next/image';
import Link from 'next/link';
import { generatePlaceholderSVG } from '../../utils/imagePlaceholder';

export default function CustomPlaygroundDesignContent() {
  return (
    <main className="custom-playground-design-page">
      <section className="page-header">
        <div className="container">
          <h1 className="page-title">全息定制方案</h1>
          <div className="breadcrumbs">
            <Link href="/">首页</Link> &gt; <Link href="/pages/custom-solutions">全息解决方案</Link> &gt;{' '}
            <span>全息定制方案</span>
          </div>
        </div>
      </section>

      <section className="design-intro">
        <div className="container">
          <div className="content-grid">
            <div className="content-image">
              <Image
                src="/images/holographic/consultation.jpg"
                alt="全息投影定制方案"
                width={600}
                height={400}
                style={{ objectFit: 'cover' }}
              />
            </div>
            <div className="content-text">
              <h2>将您的创意变为现实</h2>
              <p>
                广州钧声专注于创建定制化全息投影解决方案，完美匹配您的创意、空间和需求。我们经验丰富的设计团队将与您紧密合作，开发创新、沉浸式的全息展示环境，让您的项目与众不同。
              </p>
              <p>
                无论您是为商业空间规划全息投影展厅、为家庭娱乐中心设计主题体验区，还是为特殊场馆打造专业展示系统，我们的定制服务都能确保您项目的成功。
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="design-process">
        <div className="container">
          <h2 className="section-title">我们的设计流程</h2>

          <div className="process-steps">
            <div className="step-item">
              <div className="step-number">01</div>
              <div className="step-content">
                <h3>初步咨询</h3>
                <p>
                  我们从详细了解您的创意、需求、空间限制、目标受众和预算开始。这有助于我们为您的全息投影定制方案奠定基础。
                </p>
              </div>
              <div className="step-image">
                <Image
                  src="/images/holographic/consultation.jpg"
                  alt="初步咨询"
                  width={300}
                  height={200}
                  style={{ objectFit: 'cover' }}
                />
              </div>
            </div>

            <div className="step-item">
              <div className="step-number">02</div>
              <div className="step-content">
                <h3>概念开发</h3>
                <p>
                  我们的设计师根据您的需求创建初步概念，整合创新的全息投影元素、最佳交互流程和不同空间区域的规划。我们会提供多个概念供您选择。
                </p>
              </div>
              <div className="step-image">
                <Image
                  src="/images/holographic/concept-development.jpg"
                  alt="概念开发"
                  width={300}
                  height={200}
                  style={{ objectFit: 'cover' }}
                />
              </div>
            </div>

            <div className="step-item">
              <div className="step-number">03</div>
              <div className="step-content">
                <h3>3D Visualization</h3>
                <p>
                  Once you&apos;ve selected a preferred concept, we create detailed 3D renderings
                  that bring your playground to life. These visualizations help you see exactly how
                  your playground will look and function.
                </p>
              </div>
              <div className="step-image">
                <Image
                  src="/images/holographic/3d-visualization.jpg"
                  alt="3D Visualization"
                  width={300}
                  height={200}
                  style={{ objectFit: 'cover' }}
                />
              </div>
            </div>

            <div className="step-item">
              <div className="step-number">04</div>
              <div className="step-content">
                <h3>Detailed Design & Engineering</h3>
                <p>
                  Our engineering team develops detailed technical specifications, ensuring all
                  designs meet safety standards and structural requirements while maintaining the
                  creative vision.
                </p>
              </div>
              <div className="step-image">
                <Image
                  src="/images/holographic/engineering-design.jpg"
                  alt="Detailed Design & Engineering"
                  width={300}
                  height={200}
                  style={{ objectFit: 'cover' }}
                />
              </div>
            </div>

            <div className="step-item">
              <div className="step-number">05</div>
              <div className="step-content">
                <h3>生产与安装</h3>
                <p>
                  设计方案确认后，我们按照精确规格制造所有组件。我们的专业安装团队将您的定制全息项目变为现实，确保正确安装和安全运行。
                </p>
              </div>
              <div className="step-image">
                <Image
                  src="/images/holographic/installation.jpg"
                  alt="生产与安装"
                  width={300}
                  height={200}
                  style={{ objectFit: 'cover' }}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="design-features">
        <div className="container">
          <h2 className="section-title">定制方案特点</h2>

          <div className="features-grid">
            <div className="feature-item">
              <div className="feature-icon">
                <i className="fas fa-paint-brush"></i>
              </div>
              <h3>主题环境</h3>
              <p>创建沉浸式全息主题体验，从海底世界到太空探索，从丛林探险到童话城堡，应有尽有。</p>
            </div>

            <div className="feature-item">
              <div className="feature-icon">
                <i className="fas fa-ruler-combined"></i>
              </div>
              <h3>空间优化</h3>
              <p>
                通过巧妙的设计解决方案，在任何空间大小内实现最大的全息展示效果，充分利用可用面积。
              </p>
            </div>

            <div className="feature-item">
              <div className="feature-icon">
                <i className="fas fa-child"></i>
              </div>
              <h3>交互区域规划</h3>
              <p>设计不同的交互区域，确保不同年龄段和需求的用户都能获得引人入胜的全息体验。</p>
            </div>

            <div className="feature-item">
              <div className="feature-icon">
                <i className="fas fa-palette"></i>
              </div>
              <h3>定制解决方案</h3>
              <p>匹配您的品牌形象，整合创新技术，为您打造真正独特的全息体验。</p>
            </div>
          </div>
        </div>
      </section>

      <section className="case-studies">
        <div className="container">
          <h2 className="section-title">我们的定制项目案例</h2>

          <div className="case-grid">
            <div className="case-item">
              <div className="case-image">
                <Image
                  src="/images/holographic/holographic-restaurant.jpg"
                  alt="全息主题餐厅"
                  width={400}
                  height={300}
                  style={{ objectFit: 'cover' }}
                />
              </div>
              <div className="case-content">
                <h3>全息主题餐厅</h3>
                <p>
                  1,200平方米的海底主题全息餐厅，结合定制海洋生物投影、互动海底场景和沉浸式餐饮体验。
                </p>
              </div>
            </div>

            <div className="case-item">
              <div className="case-image">
                <Image
                  src="/images/holographic/exhibition-hall.jpg"
                  alt="企业展厅"
                  width={400}
                  height={300}
                  style={{ objectFit: 'cover' }}
                />
              </div>
              <div className="case-content">
                <h3>企业全息展厅</h3>
                <p>800平方米的企业展示空间，融合产品全息展示、互动公司历史墙和沉浸式技术演示区。</p>
              </div>
            </div>

            <div className="case-item">
              <div className="case-image">
                <Image
                  src="/images/holographic/museum.jpg"
                  alt="博物馆全息展区"
                  width={400}
                  height={300}
                  style={{ objectFit: 'cover' }}
                />
              </div>
              <div className="case-content">
                <h3>博物馆全息展区</h3>
                <p>
                  2,000平方米的历史主题全息展区，包括文物全息复原、互动历史场景和多媒体教育体验。
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="design-testimonials">
        <div className="container">
          <h2 className="section-title">客户评价</h2>

          <div className="testimonial-slider">
            <div className="testimonial-item">
              <div className="testimonial-quote">
                <p>
                  广州钧声的定制方案超出了我们的期望。他们将我们的创意转化为令人惊叹的现实，成为我们娱乐中心的主要亮点。
                </p>
                <h4>李女士</h4>
                <p className="client-title">欢乐家庭中心 总经理</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="cta-section cta-particles">
        <div className="container">
          <div className="cta-content">
            <h2>准备好讨论您的全息定制解决方案？</h2>
            <p>今天就联系我们的团队，探索我们如何为您的需求创造完美的全息解决方案。</p>
            <Link
              href="/contact-us"
              className="btn-primary"
              data-text="立即联系我们"
            >
              {'立即联系我们'.split('').map((char, index) => (
                <i key={index}>{char === ' ' ? '\u00A0' : char}</i>
              ))}
            </Link>
          </div>
        </div>
      </section>


    </main>
  );
}
