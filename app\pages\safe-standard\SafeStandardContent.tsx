'use client';

import Image from 'next/image';
import Link from 'next/link';
import { generatePlaceholderSVG } from '../../utils/imagePlaceholder';

export default function SafeStandardContent() {
  return (
    <main className="main-content">
      <section className="page-header">
        <div className="container">
          <h1 className="page-title">Safety Standards</h1>
          <div className="breadcrumbs">
            <Link href="/">Home</Link> &gt; <Link href="/service">Service</Link> &gt;{' '}
            <span>Safety Standards</span>
          </div>
        </div>
      </section>

      <section className="safety-intro">
        <div className="container">
          <div className="content-grid">
            <div className="content-image">
              <Image
                src={generatePlaceholderSVG(600, 400, 'Safety Standards')}
                alt="Safety Standards at Infinity Playground"
                width={600}
                height={400}
              />
            </div>
            <div className="content-text">
              <h2>Safety Is Our Top Priority</h2>
              <p>
                At Infinity Playground Park, we are committed to creating fun and engaging play
                environments that prioritize safety above all else. We understand the trust that
                parents, educators, and facility owners place in us when choosing our playground
                equipment.
              </p>
              <p>
                Our dedication to safety begins at the design stage and continues through
                manufacturing, installation, and beyond. Every component, material, and feature is
                carefully engineered to meet or exceed international safety standards.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="safety-standards">
        <div className="container">
          <h2 className="section-title">International Safety Standards</h2>

          <div className="standards-grid">
            <div className="standard-item">
              <div className="standard-icon">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>ASTM F1487</h3>
              <p>
                Standard Consumer Safety Performance Specification for Playground Equipment for
                Public Use (United States)
              </p>
            </div>

            <div className="standard-item">
              <div className="standard-icon">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>EN 1176 & EN 1177</h3>
              <p>
                European Standards for Playground Equipment and Impact Absorbing Playground
                Surfacing
              </p>
            </div>

            <div className="standard-item">
              <div className="standard-icon">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>ISO 9001</h3>
              <p>International Standard for Quality Management Systems</p>
            </div>

            <div className="standard-item">
              <div className="standard-icon">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>CE Marking</h3>
              <p>European Conformity for Health, Safety, and Environmental Protection Standards</p>
            </div>

            <div className="standard-item">
              <div className="standard-icon">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>TÜV Certification</h3>
              <p>German Technical Inspection Association Certification for Safety and Quality</p>
            </div>

            <div className="standard-item">
              <div className="standard-icon">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>CSA Z614</h3>
              <p>
                Canadian Standards Association Guidelines for Children&apos;s Playspaces and
                Equipment
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="safety-features">
        <div className="container">
          <h2 className="section-title">Safety Features in Our Equipment</h2>

          <div className="features-content">
            <ul className="features-list">
              <li>
                <strong>Rounded Edges:</strong> All components have rounded edges and corners to
                prevent injuries.
              </li>
              <li>
                <strong>Non-Toxic Materials:</strong> We use only non-toxic, lead-free materials and
                paints that are safe for children.
              </li>
              <li>
                <strong>Anti-Slip Surfaces:</strong> Platforms and steps feature anti-slip surfaces
                to prevent falls.
              </li>
              <li>
                <strong>Impact-Absorbing Flooring:</strong> Our flooring systems are designed to
                cushion falls and reduce injury risk.
              </li>
              <li>
                <strong>Secure Connections:</strong> All parts are securely connected with
                tamper-resistant hardware.
              </li>
              <li>
                <strong>Adequate Spacing:</strong> Equipment is spaced to prevent crowding and
                collisions.
              </li>
              <li>
                <strong>Enclosed Climbing Areas:</strong> High platforms are enclosed with barriers
                to prevent falls.
              </li>
              <li>
                <strong>Age-Appropriate Design:</strong> Equipment is designed with age-appropriate
                features and challenges.
              </li>
            </ul>

            <div className="features-image">
              <Image
                src={generatePlaceholderSVG(500, 400, 'Safety Features')}
                alt="Safety Features in Infinity Playground Equipment"
                width={500}
                height={400}
              />
            </div>
          </div>
        </div>
      </section>

      <section className="safety-testing">
        <div className="container">
          <h2 className="section-title">Our Testing Process</h2>
          <div className="testing-grid">
            <div className="testing-item">
              <div className="testing-number">01</div>
              <h3>Design Review</h3>
              <p>
                Our safety engineers review every design to identify and eliminate potential hazards
                before production begins.
              </p>
            </div>

            <div className="testing-item">
              <div className="testing-number">02</div>
              <h3>Material Testing</h3>
              <p>
                All materials undergo rigorous testing for durability, weather resistance, and
                safety compliance.
              </p>
            </div>

            <div className="testing-item">
              <div className="testing-number">03</div>
              <h3>Structural Testing</h3>
              <p>
                Equipment undergoes load testing to ensure structural integrity under various
                conditions.
              </p>
            </div>

            <div className="testing-item">
              <div className="testing-number">04</div>
              <h3>Impact Testing</h3>
              <p>
                We conduct impact testing to ensure our surfaces provide adequate protection against
                falls.
              </p>
            </div>

            <div className="testing-item">
              <div className="testing-number">05</div>
              <h3>Independent Verification</h3>
              <p>
                Third-party testing labs verify compliance with all applicable safety standards.
              </p>
            </div>

            <div className="testing-item">
              <div className="testing-number">06</div>
              <h3>Post-Installation Inspection</h3>
              <p>
                Final safety inspections are conducted after installation to ensure proper assembly
                and function.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="cta-section cta-particles">
        <div className="container">
          <div className="cta-content">
            <h2>准备好讨论您的全息定制解决方案？</h2>
            <p>今天就联系我们的团队，探索我们如何为您的需求创造完美的全息解决方案。</p>
            <Link
              href="/contact-us"
              className="btn-primary"
              data-text="立即联系我们"
            >
              {'立即联系我们'.split('').map((char, index) => (
                <i key={index}>{char === ' ' ? '\u00A0' : char}</i>
              ))}
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
