/**
 * 预加载模块 - 负责预加载关键资源以提高性能
 */
import { clientCache } from '@/lib/cache';
import { getBaseUrl, getApiUrl } from '@/lib/apiUtils';

// 是否为开发环境
const isDev = process.env.NODE_ENV === 'development';

// 保存当前有效的热门产品slug列表
const VALID_PRODUCT_SLUGS: string[] = [
  'ktv-interactive-system',
  'ar-motion-trampoline',
  '3d-electronic-sandbox',
  'interactive-football-system',
  'motion-sensing-climbing',
  'interactive-ball-smash',
  'ar-education-system',
  'bowling-interactive-system',
  'holographic-dining-system',
  'holographic-table-system'
];

// 产品接口定义
interface Product {
  id: string;
  slug: string;
  name: string;
  // 其他产品属性...
}

// 预加载产品图片
export function preloadProductImages() {
  if (typeof window === 'undefined') return; // 只在客户端执行

  // 预加载首页热门产品图片 - 使用高清版本
  const hotImages = [
    '/images/products/ktv-system-pro-main.jpg',
    '/images/products/ar-trampoline-pro-main.jpg',
    '/images/products/3d-sandbox-pro-main.jpg',
    '/images/products/interactive-football-pro-main.jpg',
    '/images/products/motion-climbing-pro-extra-1.jpg',
    '/images/products/interactive-ball-pro-main.jpg',
    '/images/products/ar-education-pro-main.jpg',
    '/images/products/bowling-system-v2-main.jpg',
    '/images/products/holographic-dining-v2-extra-1.jpg',
    '/images/products/holographic-table-v2-main.jpg',
    '/images/products/children-ball-v2-main.jpg',
    '/images/products/children-beach-v2-main.jpg'
  ];



  // 使用Promise.all同时预加载所有图片
  const preloadPromises = hotImages.map(imgSrc => {
    return new Promise(resolve => {
      const img = new window.Image();
      img.onload = () => resolve(true);
      img.onerror = () => {
        console.warn(`图片 ${imgSrc} 预加载失败`);
        resolve(false);
      };
      img.src = imgSrc;
    });
  });

  return Promise.all(preloadPromises);
}

// 预加载API数据
export async function prefetchProductData() {
  if (typeof window === 'undefined') return; // 只在客户端执行



  // 动态获取热门产品列表
  try {
    // 使用动态API URL生成工具
    const productsApiUrl = getApiUrl('products?limit=5&is_featured=true');

    // 首先尝试获取产品列表
    const productsRes = await fetch(productsApiUrl, {
      priority: 'high',
      next: { revalidate: 600 }, // 10分钟重新验证
    });

    if (productsRes.ok) {
      const productsData = await productsRes.json();
      if (productsData.data && productsData.data.length > 0) {
        // 使用获取到的热门产品
        const featuredProducts = productsData.data.map((p: Product) => p.slug);


        // 为这些产品预加载详情数据
        return prefetchProductDetails(featuredProducts);
      } else {

        // 如果没有热门产品，尝试获取最新产品
        const latestApiUrl = getApiUrl('products?limit=3');
        const latestRes = await fetch(latestApiUrl, {
          priority: 'high',
          next: { revalidate: 600 },
        });

        if (latestRes.ok) {
          const latestData = await latestRes.json();
          if (latestData.data && latestData.data.length > 0) {
            const latestProducts = latestData.data.map((p: Product) => p.slug);

            return prefetchProductDetails(latestProducts);
          }
        }
      }
    }
  } catch (err) {
    console.error('获取产品列表失败:', err);
  }

  // 如果仍然没有获取到产品，使用指定的产品列表
  if (VALID_PRODUCT_SLUGS.length > 0) {
    return prefetchProductDetails(VALID_PRODUCT_SLUGS);
  } else {

    return Promise.resolve([]);
  }
}

// 预加载指定产品的详细信息
async function prefetchProductDetails(slugs: string[]) {
  const preloadPromises = slugs.map(async slug => {
    try {
      // 首先检查客户端缓存
      const cacheKey = `product_${slug}`;
      const cachedData = clientCache.get(cacheKey);

      if (cachedData) {

        return true;
      }

      // 无缓存数据，从API获取


      // 使用动态API URL生成工具
      const apiUrl = getApiUrl(`products/by-slug/${slug}`);

      const res = await fetch(apiUrl, {
        priority: 'high',
        next: { revalidate: 600 }, // 10分钟重新验证
      });

      if (res.ok) {
        const data = await res.json();
        // 使用clientCache工具保存数据
        clientCache.set(cacheKey, data);
        return true;
      } else {
        // 保留这个错误日志，因为它是重要的
        console.warn(`产品 ${slug} 返回状态码: ${res.status}`);
        // 如果返回404，则不再继续尝试该产品
        if (res.status === 404) {
          const index = VALID_PRODUCT_SLUGS.indexOf(slug);
          if (index > -1) {

            VALID_PRODUCT_SLUGS.splice(index, 1);
          }
        }
        return false;
      }
    } catch (err) {
      console.error(`预加载产品 ${slug} 失败:`, err);
      return false;
    }
  });

  return Promise.all(preloadPromises);
}

// 主预加载函数
export function initPreload() {
  if (typeof window === 'undefined') return;





  // 使用 requestIdleCallback 在浏览器空闲时执行预加载
  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(
      () => {
        preloadProductImages();
        prefetchProductData().catch(err => {
          console.error('预加载过程出错:', err);
          // 错误处理，确保网站仍能正常运行
        });
      },
      { timeout: 5000 }
    );
  } else {
    // 降级处理
    setTimeout(() => {
      preloadProductImages();
      prefetchProductData().catch(err => {
        console.error('预加载过程出错:', err);
        // 错误处理，确保网站仍能正常运行
      });
    }, 2000);
  }
}
