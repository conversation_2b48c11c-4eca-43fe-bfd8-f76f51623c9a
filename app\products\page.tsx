'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function ProductsPage() {
  const router = useRouter();

  // 重定向到带语言前缀的产品页面
  useEffect(() => {
    // 检测浏览器语言或使用默认语言
    const browserLang = navigator.language.startsWith('zh') ? 'zh' : 'en';
    router.replace(`/${browserLang}/products`);
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">正在重定向到产品页面...</p>
      </div>
    </div>
  );
}
