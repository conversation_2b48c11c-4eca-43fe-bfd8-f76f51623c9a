.customSolutions {
  padding: 60px 0 80px !important;
  background-color: #f8f9fa !important;
  position: relative !important;
  overflow: hidden !important;
}

.customSolutions:before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: radial-gradient(circle at 10% 10%, rgba(97, 92, 237, 0.03), transparent 400px),
              radial-gradient(circle at 90% 90%, rgba(83, 74, 209, 0.03), transparent 400px) !important;
  pointer-events: none !important;
}

.container {
  max-width: 1800px !important;
  margin: 0 auto !important;
  padding: 0 30px !important;
}

.sectionTitle {
  text-align: center !important;
  margin-bottom: 40px !important;
  font-size: 3.2rem !important;
  font-weight: 700 !important;
  color: #1a1a2e !important;
  position: relative !important;
  padding-bottom: 18px !important;
}

.sectionTitle:after {
  content: '' !important;
  position: absolute !important;
  bottom: 0 !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 140px !important;
  height: 5px !important;
  background: linear-gradient(90deg, #1a1a2e, #4040bf) !important;
  border-radius: 3px !important;
}

.solutionsGrid {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 30px !important;
  margin: 40px 0 !important;
}

.solutionCard {
  position: relative !important;
  border-radius: 15px !important;
  overflow: hidden !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.4s ease !important;
  height: 0 !important; /* 重要：确保没有默认高度 */
  padding-bottom: 75% !important; /* 3:4 比例 */
  margin: 0 !important;
  background: none !important;
  display: block !important;
  width: 100% !important;
}

.solutionCard:hover {
  transform: translateY(-10px) !important;
  box-shadow: 0 20px 35px rgba(0, 0, 0, 0.15) !important;
}

.solutionLink {
  display: block !important;
  width: 100% !important;
  height: 100% !important;
  position: absolute !important; /* 重要: 确保链接覆盖整个卡片区域 */
  top: 0 !important;
  left: 0 !important;
  text-decoration: none !important;
}

.cardImg {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  object-position: center !important;
  transition: transform 0.7s ease !important;
  transform: scale(1) !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  margin: 0 !important; /* 确保没有边距 */
  padding: 0 !important; /* 确保没有内边距 */
  display: block !important; /* 确保图片是块级元素 */
}

/* 移除解决方案卡片图片悬停放大效果 */

.cardGradient {
  position: absolute !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  height: 50% !important;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.85), transparent) !important;
  z-index: 2 !important;
  margin: 0 !important; /* 确保没有边距 */
  padding: 0 !important; /* 确保没有内边距 */
}

.cardTitle {
  position: absolute !important;
  bottom: 25px !important;
  left: 0 !important;
  right: 0 !important;
  text-align: center !important;
  color: white !important;
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin: 0 !important;
  padding: 0 15px !important;
  z-index: 3 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.cardBadge {
  position: absolute !important;
  top: 15px !important;
  right: 15px !important;
  background: rgba(83, 82, 237, 0.9) !important;
  color: white !important;
  padding: 8px 15px !important;
  border-radius: 50px !important;
  font-size: 0.9rem !important;
  font-weight: 500 !important;
  z-index: 3 !important;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2) !important;
}

.ctaButton {
  display: flex !important;
  justify-content: center !important;
  margin-top: 60px !important;
  width: 100% !important; /* 确保容器有足够宽度 */
  overflow: visible !important; /* 防止内容被裁剪 */
  min-width: 700px !important; /* 为更大的英文按钮提供足够空间 */
  padding: 0 25px !important; /* 增加左右内边距 */
}

.customBtn {
  position: relative !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 520px !important;
  padding: 28px 55px !important;
  font-size: 1.6rem !important;
  font-weight: 700 !important;
  text-decoration: none !important;
  text-transform: none !important;
  color: #fff !important;
  background: linear-gradient(135deg, #264ECA 0%, #2239B4 100%) !important;
  border: none !important;
  border-radius: 16px !important;
  box-shadow: 0 20px 45px rgba(38, 78, 202, 0.5) !important;
  overflow: hidden !important;
  cursor: pointer !important;
  transition: all 0.4s ease !important;
  z-index: 1 !important;
}

.customBtn:hover {
  transform: translateY(-10px) !important;
  box-shadow: 0 30px 60px rgba(38, 78, 202, 0.6) !important;
}

.btnGlow {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg,
                            rgba(255,255,255,0) 10%,
                            rgba(255,255,255,0.3) 20%,
                            rgba(255,255,255,0) 30%) !important;
  z-index: -1 !important;
  transform: translateX(-100%) !important;
  animation: btnGlowAnimation 2.5s infinite !important;
}

@keyframes btnGlowAnimation {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.btnTextWrapper {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  transition: all 0.3s ease !important;
}

.btnIconLeft {
  margin-right: 20px !important;
  font-size: 1.6rem !important;
  transition: transform 0.3s ease !important;
}

.btnIconRight {
  margin-left: 20px !important;
  font-size: 1.6rem !important;
  transition: transform 0.3s ease !important;
}

.customBtn:hover .btnIconLeft {
  transform: translateX(-8px) rotate(-10deg) !important;
}

.customBtn:hover .btnIconRight {
  transform: translateX(8px) !important;
}

.btnText {
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
}

@media (max-width: 1600px) {
  .solutionsGrid {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 25px !important;
  }
}

@media (max-width: 1100px) {
  .solutionsGrid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 25px !important;
  }

  .sectionTitle {
    font-size: 2.6rem !important;
  }
}

@media (max-width: 992px) {
  .customBtn {
    min-width: 400px !important;
    padding: 22px 44px !important;
    font-size: 1.5rem !important;
  }

  .btnIconLeft {
    margin-right: 18px !important;
    font-size: 1.5rem !important;
  }

  .btnIconRight {
    margin-left: 18px !important;
    font-size: 1.5rem !important;
  }
}

@media (max-width: 768px) {
  .customBtn {
    min-width: 360px !important;
    padding: 20px 40px !important;
    font-size: 1.4rem !important;
  }

  .btnIconLeft {
    margin-right: 15px !important;
    font-size: 1.4rem !important;
  }

  .btnIconRight {
    margin-left: 15px !important;
    font-size: 1.4rem !important;
  }

  .ctaButton {
    min-width: 400px !important; /* 在中等屏幕上减少最小宽度 */
    padding: 0 15px !important;
  }
}

@media (max-width: 576px) {
  .solutionsGrid {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }

  .solutionCard {
    padding-bottom: 66.67% !important; /* 2:3 比例，在移动端更好 */
  }

  .customBtn {
    min-width: 300px !important;
    padding: 18px 30px !important;
    font-size: 1.3rem !important;
  }

  .btnIconLeft {
    margin-right: 12px !important;
    font-size: 1.3rem !important;
  }

  .btnIconRight {
    margin-left: 12px !important;
    font-size: 1.3rem !important;
  }

  .ctaButton {
    min-width: 320px !important; /* 在小屏幕上进一步减少最小宽度 */
    padding: 0 10px !important;
  }
}