/* 修复双滚动条问题 */

/* 诊断和修复可能导致双滚动条的元素 */

/* 1. 确保html和body不会创建额外的滚动上下文 */
html {
  overflow: auto !important; /* 让html处理滚动 */
  height: 100% !important;
}

body {
  overflow: visible !important; /* body不处理滚动，避免双滚动条 */
  height: auto !important; /* 让body高度自适应内容 */
  min-height: 100vh !important;
}

/* 2. 修复可能的容器问题 */
#__next {
  overflow: visible !important;
  height: auto !important;
  min-height: 100vh !important;
}

/* 3. 修复main容器 */
.main-content {
  overflow: visible !important;
  height: auto !important;
  min-height: auto !important;
}

/* 4. 修复hero slider可能的问题 */
.hero-slider-wrapper {
  overflow: hidden !important;
  height: 100vh !important;
  position: relative !important;
}

.hero-slider {
  overflow: hidden !important;
  height: 100vh !important;
  position: relative !important;
}

/* 5. 确保所有section不会创建滚动上下文 */
section {
  overflow: visible !important;
}

/* 6. 修复可能的固定高度容器 */
.container {
  overflow: visible !important;
  height: auto !important;
}

/* 7. 强制移除所有可能创建额外滚动条的元素 */
* {
  /* 检查是否有元素意外设置了overflow */
}

/* 8. 特别处理可能的问题元素 */
.page-wrapper,
.app-wrapper,
.layout-wrapper {
  overflow: visible !important;
  height: auto !important;
}

/* 9. 移动端菜单保持原有滚动 */
.mobile-menu {
  overflow-y: auto !important; /* 移动端菜单需要滚动 */
}

/* 10. 调试样式 - 临时添加边框来识别问题元素 */
/*
body {
  border: 2px solid red !important;
}

#__next {
  border: 2px solid blue !important;
}

.main-content {
  border: 2px solid green !important;
}
*/

/* 11. 强制修复任何可能的height: 100vh + overflow组合 */
[style*="height: 100vh"] {
  overflow: visible !important;
}

[style*="height: 100%"] {
  overflow: visible !important;
}

/* 12. 确保没有元素意外设置了position: fixed + height: 100% */
.fixed-height-container {
  overflow: visible !important;
  height: auto !important;
}
