/* Reset and Base Styles */
@import './tailwind.css'; /* 核心 Tailwind CSS 样式 - 必须在最前面 */
@import './about-us.css';
@import './banner-fix.css'; /* 确保新样式覆盖产品页banner */
@import './modern-contrast-fix.css'; /* 使用现代forced-colors替代-ms-high-contrast */
@import './ms-fix.css'; /* 修复-ms-high-contrast弃用警告 */
@import './high-contrast-override.css'; /* 强力覆盖所有第三方库的-ms-high-contrast媒体查询 */
@import './ms-high-contrast-blocker.css'; /* 完全阻止所有-ms-high-contrast媒体查询 */
@import './animations.css'; /* 添加动画效果 */
@import './unified-cta.css'; /* 统一CTA样式 */
@import './step-image-unified.css'; /* 统一步骤图片尺寸样式 */
@import './force-modern-product-style.css'; /* 强制现代化产品样式 - 最高优先级 */
@import './custom-playground-design.css'; /* 定制页面样式 */
@import './holographic-guide.css'; /* 全息指南页面样式 */
/* 移除旧的产品页面修复样式，使用最新的product-detail-fix.css */

/* 产品图片1920x1080比例样式 */
.product-image-1920x1080 {
    aspect-ratio: 16/9 !important;
    width: 100% !important;
    min-height: 400px !important;
    max-height: 600px !important;
}

.product-image-1920x1080 img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    border-radius: 12px !important;
}

/* 产品页面优化样式 */
.product-detail-page {
    position: relative;
    overflow-x: hidden;
}

/* 缩略图hover效果 - 移除放大效果 */
.hover\:scale-102:hover {
    /* transform: scale(1.02); */
}

/* 平滑过渡动画 */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 产品特点卡片动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-card {
    animation: fadeInUp 0.6s ease-out forwards;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }

/* 骨架屏动画优化 */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.animate-pulse {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 2s infinite linear;
}

/* 响应式优化 */
@media (max-width: 640px) {
    .product-image-1920x1080 {
        aspect-ratio: 16/9 !important;
    }
}

/* 下拉菜单动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 应用动画到下拉菜单 */
body > div[style*="position: fixed"][style*="z-index: 100000"] {
    animation: fadeIn 0.2s ease forwards;
}

:root {
    /* Primary Colors */
    --primary-color: #1a1a2e; /* Main brand color - Deep Navy */
    --primary-hover: #13132a; /* Darker shade for hover states */
    --secondary-color: #ff6b6b; /* Secondary accent color */
    --secondary-hover: #ff5252; /* Darker shade for hover states */

    /* Text Colors */
    --text-dark: #333333; /* Main text color */
    --text-medium: #666666; /* Secondary text color */
    --text-light: #999999; /* Light text color */

    /* Background Colors */
    --bg-white: #ffffff; /* White background */
    --bg-light: #f8f8f8; /* Light background */
    --bg-gray: #eeeeee; /* Gray background */
    --bg-primary: #1a1a2e; /* Primary background color */

    /* Border Colors */
    --border-light: #eeeeee; /* Light border color */
    --border-medium: #dddddd; /* Medium border color */

    /* Shadow */
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.05); /* Light shadow */
    --shadow-medium: 0 5px 15px rgba(0, 0, 0, 0.1); /* Medium shadow */

    /* New Colors */
    --primary-text: #333333;
    --secondary-text: #666666;
    --light-text: #999999;
    --accent-color: #333333;
    --light-bg: #f9f9f9;
    --border-color: #eeeeee;
    --transition-fast: 0.3s ease;
    --transition-medium: 0.5s ease;
    --box-shadow-subtle: 0 10px 30px rgba(0, 0, 0, 0.05);
    --box-shadow-medium: 0 15px 40px rgba(0, 0, 0, 0.1);

    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 255, 255, 255;
    --background-end-rgb: 255, 255, 255;
}

/*
@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}
*/

/* CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    min-height: 100vh;
    overflow-x: hidden;
    font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
    color: var(--primary-text);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-poppins, sans-serif);
    font-weight: 600;
    line-height: 1.2;
}

strong, b {
    font-weight: 600;
}

a {
    color: inherit;
    text-decoration: none;
}

ul {
    list-style: none;
}

.container {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .container {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .container {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* Header Styles */
.header {
    position: relative;
    z-index: 9999; /* 提高头部的z-index到最高 */
}

/* Header Main */
.header-main {
    padding: 15px 0;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
    /* 移除可能限制下拉菜单显示的overflow属性 */
}

.header-main.transparent {
    background-color: transparent;
    background-image: linear-gradient(to bottom, rgba(0,0,0,0.3), rgba(0,0,0,0));
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9999; /* 提高透明导航的层级 */
}

.header-main.sticky {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9999; /* 提高固定导航的层级 */
    padding: 10px 0;
    box-shadow: var(--shadow-medium);
    animation: slideDown 0.3s ease-out;
    background-color: var(--bg-primary);
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

.header-main .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Logo */
.logo a {
    display: flex;
    align-items: center;
}

.logo img,
.logo-image {
    height: 35px;
    width: auto;
    margin-top: 3px;
    object-fit: contain;
}

/* Nav Container */
.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    margin-left: 30px;
    position: static; /* 确保容器不会意外创建新的定位上下文 */
}

/* Main Navigation */
.main-nav {
    display: flex;
    position: static; /* 确保不创建新的定位上下文 */
}

.nav-list {
    display: flex;
    align-items: center;
    gap: 5px;
    position: static; /* 确保不创建新的定位上下文 */
}

.nav-item {
    position: relative;
    margin: 0 5px;
}

.nav-link {
    display: block;
    padding: 10px 10px;
    font-weight: 300;
    color: white !important;
    position: relative;
    transition: color 0.3s ease;
    font-size: 15px;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 1.5px;
    text-transform: uppercase;
}

.nav-link:hover {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* 添加首页链接的特殊样式 */
.nav-item:first-child .nav-link {
    color: white !important;
    font-weight: 600;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
    position: relative;
}

.nav-item:first-child .nav-link::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 10px;
    right: 10px;
    height: 2px;
    background-color: white;
    transform: scaleX(0.7);
    transition: transform 0.3s ease;
}

.nav-item:first-child .nav-link:hover::after {
    transform: scaleX(1);
}

/* 移动菜单中的首页链接样式 */
.mobile-nav-item:first-child .mobile-nav-link {
    color: white;
    font-weight: 600;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
}

/* Dropdown Menus */
.has-dropdown {
    position: relative;
}

.has-dropdown > .nav-link i {
    font-size: 9px;
    margin-left: 2px;
    transition: transform 0.3s ease;
    position: relative;
    top: -1px;
    opacity: 0.8;
}

.has-dropdown:hover > .nav-link i {
    transform: rotate(180deg);
}

/* 保留下拉菜单项目的样式 */
.dropdown-item {
    position: relative;
}

.dropdown-link {
    display: block;
    padding: 8px 20px;
    color: var(--text-dark);
    font-size: 14px;
    transition: color 0.3s ease, background-color 0.3s ease;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.dropdown-link:hover {
    color: var(--primary-color);
    background-color: rgba(10, 89, 247, 0.05);
}

.has-submenu {
    position: relative;
}

.has-submenu > .dropdown-link::after {
    content: '\f054';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 8px;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
    color: var(--text-light);
}

.has-submenu:hover > .dropdown-link::after {
    transform: translateY(-50%) rotate(90deg);
}

.submenu {
    position: absolute;
    top: 0;
    left: 100%;
    min-width: 200px;
    background-color: var(--bg-white);
    box-shadow: var(--shadow-medium);
    border-radius: 4px;
    padding: 10px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateX(10px);
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s;
    z-index: 101;
}

.submenu-link {
    display: block;
    padding: 8px 20px;
    color: var(--text-dark);
    font-size: 14px;
    transition: color 0.3s ease, background-color 0.3s ease;
}

.submenu-link:hover {
    color: var(--primary-color);
    background-color: rgba(10, 89, 247, 0.05);
}

.header-actions {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.search-icon {
    color: var(--bg-white);
    font-size: 16px;
    margin-right: 15px;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.account-icon {
    color: var(--bg-white);
    font-size: 16px;
    margin-right: 20px;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.search-icon:hover, .account-icon:hover {
    opacity: 0.8;
}

.language-selector {
    position: relative;
    margin-right: 20px;
}

.current-language {
    display: flex;
    align-items: center;
    color: var(--bg-white);
    cursor: pointer;
}

.current-language .fa-globe {
    margin-right: 4px;
    font-size: 14px;
}

.current-language .fa-chevron-down {
    font-size: 8px;
    margin-left: 4px;
    transition: transform 0.3s ease;
    position: relative;
    top: -1px;
    opacity: 0.8;
}

.language-selector:hover .current-language .fa-chevron-down {
    transform: rotate(180deg);
}

.language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 150px;
    background-color: var(--bg-white);
    box-shadow: var(--shadow-medium);
    border-radius: 4px;
    padding: 10px 0;
    margin-top: 10px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s;
    z-index: 100;
}

.language-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.language-selector:hover .language-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.rotate-180 {
    transform: rotate(180deg);
}

.language-link {
    display: block;
    padding: 8px 20px;
    color: var(--text-dark);
    font-size: 14px;
    transition: color 0.3s ease, background-color 0.3s ease;
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-family: inherit;
}

.language-link:hover {
    color: var(--primary-color);
    background-color: rgba(10, 89, 247, 0.05);
}

.language-item.active .language-link {
    color: var(--primary-color);
    font-weight: 600;
    background-color: rgba(10, 89, 247, 0.05);
}

.btn-quote {
    display: inline-block;
    padding: 12px 24px;
    background-color: white;
    color: #0a59f7;
    font-weight: 600;
    border-radius: 4px;
    font-size: 15px;
    transition: all 0.3s ease;
    border: 2px solid white;
    white-space: nowrap;
}

.btn-quote:hover {
    background-color: transparent;
    color: white;
    box-shadow: 0 4px 10px rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    width: 30px;
    height: 20px;
    justify-content: space-between;
    background: transparent;
    border: none;
    cursor: pointer;
    z-index: 1001;
}

.mobile-menu-toggle span {
    width: 100%;
    height: 2px;
    background-color: var(--bg-white);
    transition: all 0.3s ease;
    position: relative;
}

.mobile-menu-toggle span.active:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle span.active:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle span.active:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.mobile-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-white);
    padding: 80px 20px 40px;
    z-index: 2000; /* 提高层级，确保在其他元素之上 */
    overflow-y: auto;
    transform: translateY(-100%);
    transition: transform 0.5s ease;
    visibility: hidden;
}

.mobile-menu.active {
    transform: translateY(0);
    visibility: visible;
}

.mobile-nav-item {
    margin-bottom: 10px;
    border-bottom: 1px solid var(--border-light);
}

.mobile-nav-link {
    display: block;
    padding: 12px 0;
    color: var(--text-dark);
    font-weight: 300;
    position: relative;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    transition: all 0.3s ease;
}

.mobile-nav-link:hover {
    color: var(--primary-color);
}

.mobile-dropdown-toggle {
    position: absolute;
    right: 0;
    top: 12px;
    font-size: 12px;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.mobile-dropdown-toggle.active {
    transform: rotate(180deg);
    color: var(--primary-color);
}

.mobile-submenu, .mobile-submenu-level2 {
    display: block;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background-color: var(--bg-light);
}

.mobile-submenu.active, .mobile-submenu-level2.active {
    max-height: 1000px;
    padding: 5px 0;
    display: block;
}

.mobile-submenu-item {
    margin-bottom: 0;
}

.mobile-submenu-link {
    display: block;
    padding: 10px 20px;
    color: var(--text-medium);
    font-size: 14px;
}

.mobile-submenu-link:hover {
    color: var(--primary-color);
}

body.menu-open {
    overflow: hidden;
}

/* Main Content */
.main-content {
    position: relative;
    z-index: 1; /* 确保主内容区域层级低于导航 */
}

/* Hero Slider */
.hero-slider-wrapper {
    width: 100%;
    overflow: hidden;
    margin-bottom: 0;
    position: relative;
    z-index: 1; /* 确保slider层级低于导航 */
}

.hero-slider {
    position: relative;
    width: 100%;
    height: 100vh; /* 使用100vh使其全屏 */
    overflow: hidden;
    z-index: 1;
}

.slider-container {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;
}

.slider-wrapper {
    display: flex;
    transition: transform 0.5s ease;
    height: 100%; /* 调整为100%高度 */
}

.slide {
    flex: 0 0 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 1s ease, visibility 1s;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
}

.slide.active {
    opacity: 1;
    visibility: visible;
}

.slide-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: #000; /* 黑色背景，更适合全屏显示 */
}

.slide-image {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 使用cover确保整个区域都被填满 */
    object-position: center;
}

.slide-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 5;
    width: 80%;
    max-width: 1200px;
}

.slide-content h2 {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

@media (max-width: 992px) {
    .slide-content h2 {
        font-size: 3rem;
    }
}

@media (max-width: 768px) {
    .slide-content h2 {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    .slide-content h2 {
        font-size: 2rem;
    }
}

.slide a {
    display: block;
    width: 100%;
    height: 100%;
    position: relative;
}

.slider-dots {
    position: absolute;
    bottom: 30px; /* 增大底部距离 */
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 12px; /* 增大间距 */
    z-index: 10;
}

.dot {
    width: 14px; /* 增大点的尺寸 */
    height: 14px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
}

.dot:hover {
    background-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.2);
}

.dot.active {
    background-color: var(--primary-color);
    transform: scale(1.3);
}

.slider-arrows {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 30px; /* 增大左右间距 */
    z-index: 10;
}

.arrow {
    width: 50px; /* 增大箭头尺寸 */
    height: 50px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
    color: var(--bg-white);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
    border: none;
    font-size: 18px;
}

.arrow:hover {
    background-color: rgba(0, 0, 0, 0.5);
    transform: scale(1.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .hero-slider {
        height: 100vh; /* 保持全屏 */
    }

    .arrow {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

@media (max-width: 576px) {
    .hero-slider {
        height: 100vh; /* 保持全屏 */
    }

    .slider-arrows {
        padding: 0 15px;
    }
}

/* Services Features Section */
.services-features {
    padding: 80px 0;
    overflow: hidden;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 20px;
    padding: 20px 0;
}

.feature-card {
    background-color: var(--bg-white);
    padding: 30px 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    width: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.feature-icon {
    font-size: 30px;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.feature-card h3 {
    font-size: 18px;
    margin-bottom: 10px;
    font-weight: 600;
}

.feature-card p {
    font-size: 14px;
    color: var(--text-medium);
    line-height: 1.5;
}

/* Custom Solutions Section */
.custom-solutions {
    padding: 60px 0;
    background-color: var(--bg-light);
}

.section-title {
    font-size: 32px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    color: var(--text-dark);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 10px;
}

.solutions-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.solution-card {
    background-color: var(--bg-white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.solution-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.solution-image {
    height: 200px;
    overflow: hidden;
}

.solution-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

/* 移除解决方案卡片图片悬停放大效果 */

.solution-card h3 {
    padding: 15px;
    font-size: 16px;
    font-weight: 600;
    transition: color 0.3s ease;
}

.solution-card:hover h3 {
    color: var(--primary-color);
}

.cta-button {
    text-align: center;
    margin-top: 40px;
}

.btn-primary {
    display: inline-block;
    padding: 12px 30px;
    background-color: var(--primary-color);
    color: var(--bg-white);
    font-weight: 600;
    border-radius: 5px;
    font-size: 16px;
    transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(10, 89, 247, 0.2);
}

/* Media Queries */
@media (max-width: 1200px) {
    .container {
        max-width: 960px;
    }
}

@media (max-width: 992px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 20px;
    }

    .container {
        max-width: 720px;
    }

    .footer-columns {
        grid-template-columns: repeat(3, 1fr);
    }

    .main-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .header-actions {
        margin-left: auto;
    }

    .search-icon, .account-icon {
        margin-right: 15px;
    }

    .language-selector {
        margin-right: 15px;
    }

    .mobile-menu {
        display: block;
    }

    .header-main.sticky {
        padding: 8px 0;
    }

    .logo img {
        height: 30px;
    }
}

@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .solutions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .search-icon, .account-icon {
        margin-right: 10px;
    }

    .btn-quote {
        padding: 8px 15px;
        font-size: 13px;
    }
}

@media (max-width: 576px) {
    .header-actions {
        margin-left: 10px;
    }

    .search-icon, .language-selector, .account-icon {
        margin-right: 8px;
    }

    .btn-quote {
        display: none;
    }

    .features-grid {
        grid-template-columns: 1fr !important;
    }

    .solutions-grid {
        grid-template-columns: 1fr;
    }
}

/* Factory Showcase Section */
.factory-section {
    padding: 80px 0;
    background-color: var(--bg-light);
}

.section-description {
    text-align: center;
    max-width: 1200px;
    margin: 0 auto 30px;
    color: var(--text-medium);
    line-height: 1.6;
}

.factory-sliders-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: center;
    margin: 40px auto;
    width: 96%; /* 占据屏幕96%的宽度 */
    max-width: 100%; /* 使用全屏宽度 */
    padding: 0;
}

.factory-slider {
    position: relative;
    width: calc(50% - 15px);
    min-width: 500px; /* 增加最小宽度 */
    max-width: 800px; /* 限制最大宽度 */
    overflow: hidden;
}

.factory-slider-container {
    position: relative;
    width: 100%;
    border-radius: 12px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
}

.factory-slides {
    position: relative;
    height: 450px;
    background-color: var(--bg-gray);
}

.factory-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.8s ease, visibility 0.8s;
}

.factory-slide.active {
    opacity: 1;
    visibility: visible;
}

.factory-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 确保只有活动幻灯片的文字标题显示 */
.factory-slide .slide-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    color: white;
    padding: 25px 80px 25px 25px;
    font-size: 1.2rem;
    font-weight: 300;
    z-index: 2;
    transform: translateY(20px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.8s ease 0.3s;
}

.factory-slide.active .slide-caption {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.factory-slider-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 12px;
    z-index: 10;
}

.factory-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
}

.factory-dot:hover {
    background-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.2);
}

.factory-dot.active {
    background-color: var(--primary-color);
    transform: scale(1.2);
}

.factory-slider-arrows {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 10;
}

.factory-arrow {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
    border: none;
    font-size: 16px;
}

.factory-arrow:hover {
    background-color: rgba(0, 0, 0, 0.5);
    transform: scale(1.1);
}

/* 响应式设计调整 */
@media (max-width: 1800px) {
    .factory-sliders-container {
        width: 98%;
    }

    .factory-slider {
        min-width: 450px;
    }
}

@media (max-width: 1200px) {
    .factory-slider {
        min-width: 400px;
    }
}

@media (max-width: 992px) {
    .factory-slider {
        width: 94%;
        min-width: 300px;
        margin: 0 auto 30px;
    }
}

@media (max-width: 768px) {
    .factory-slides {
        height: 380px;
    }

    .factory-arrow {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .factory-slides {
        height: 300px;
    }

    .factory-slider-arrows {
        padding: 0 10px;
    }

    .factory-arrow {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }
}

/* Global Case Showcase */
.global-showcase {
    padding: 60px 0;
}

.country-tabs {
    margin: 30px 0;
}

.country-list {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
}

.country-item {
    padding: 8px 20px;
    background-color: var(--bg-light);
    border-radius: 30px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.country-item:hover,
.country-item.active {
    background-color: var(--primary-color);
    color: white;
}

.case-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.case-item {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background-color: var(--bg-white);
}

.case-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.case-image {
    height: 250px;
    overflow: hidden;
}

.case-info {
    padding: 20px;
}

.case-info h3 {
    font-size: 18px;
    margin-bottom: 5px;
}

.case-info p {
    color: var(--text-medium);
    font-size: 14px;
}

/* Responsive styles for factory and case sections */
@media (max-width: 992px) {
    .case-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .case-grid {
        grid-template-columns: 1fr;
    }

    .country-list {
        gap: 10px;
    }

    .country-item {
        padding: 6px 15px;
        font-size: 14px;
    }
}

/* About Section Styles */
.about-section {
    padding: 60px 0;
    background-color: var(--bg-light);
}

.about-content {
    margin-top: 40px;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.about-text-container {
    flex: 1;
}

.about-text p {
    margin-bottom: 20px;
    line-height: 1.7;
}

.about-text .btn-primary {
    margin-top: 10px;
}

/* Stats Container */
.stats-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 50px 0;
    gap: 20px;
}

.stat-box {
    flex: 1;
    min-width: 200px;
    background-color: var(--bg-white);
    padding: 25px 20px;
    border-radius: 8px;
    box-shadow: var(--shadow-light);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-box:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stat-description {
    font-size: 0.9rem;
    color: var(--text-medium);
    line-height: 1.4;
}

/* Team Section */
.team-section {
    margin: 50px 0;
}

.section-subtitle {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin-bottom: 20px;
    text-align: center;
}

.team-placeholder {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
    height: 400px;
}

/* Certificates Section */
.certificates-section {
    margin: 50px 0;
}

.certificate-description {
    text-align: center;
    max-width: 700px;
    margin: 0 auto 30px;
    color: var(--text-medium);
}

.certificate-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.certificate-item {
    width: 100%;
    height: 200px;
    padding: 10px;
    background-color: var(--bg-white);
    border-radius: 8px;
    box-shadow: var(--shadow-light);
    transition: transform 0.3s ease;
}

.certificate-item:hover {
    transform: scale(1.05);
}

/* CTA Section */
.about-cta {
    margin: 50px 0 20px;
    text-align: center;
    padding: 40px;
    background-color: var(--bg-white);
    border-radius: 8px;
    box-shadow: var(--shadow-light);
}

.cta-title {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: var(--text-dark);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .certificate-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .about-content {
        flex-direction: column;
    }

    .about-text-container {
        width: 100%;
    }

    .stats-container {
        flex-direction: column;
    }

    .stat-box {
        min-width: 100%;
    }

    .team-placeholder {
        height: 300px;
    }

    .certificate-grid {
        grid-template-columns: 1fr;
    }

    .about-cta {
        padding: 30px 20px;
    }
}

/* Contact Form Styles */
.contact-form-container {
    background-color: var(--bg-white);
    border-radius: 8px;
    box-shadow: var(--shadow-light);
    padding: 40px;
    margin: 50px 0;
}

.contact-form-container h2 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.contact-form-container > p {
    color: var(--text-medium);
    margin-bottom: 30px;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 250px;
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-size: 0.9rem;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 15px;
    border: 1px solid var(--border-medium);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

.form-group textarea {
    resize: vertical;
}

.error-message {
    color: #e74c3c;
    font-size: 0.9rem;
    margin-top: 5px;
}

.success-message {
    text-align: center;
    padding: 40px 20px;
}

.success-message i {
    font-size: 3rem;
    color: #2ecc71;
    margin-bottom: 20px;
}

.success-message h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.success-message p {
    color: var(--text-medium);
}

@media (max-width: 768px) {
    .contact-form-container {
        padding: 30px 20px;
    }

    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-group {
        width: 100%;
    }
}

/* Contact Page Styles */
.contact-page {
    padding: 60px 0;
}

.page-header {
    text-align: center;
    margin-bottom: 50px;
}

.page-title {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.page-description {
    max-width: 800px;
    margin: 0 auto;
    color: var(--text-medium);
    line-height: 1.6;
}

.contact-container {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
}

.contact-info {
    flex: 1;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.info-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-top: 5px;
}

.info-content h3 {
    font-size: 1.2rem;
    margin-bottom: 8px;
    color: var(--text-dark);
}

.info-content p {
    color: var(--text-medium);
    line-height: 1.5;
}

@media (max-width: 992px) {
    .contact-container {
        flex-direction: column;
    }

    .contact-info {
        width: 100%;
    }
}

/* 移除旧的产品详情页样式，使用最新的product-detail-fix.css */

/* Products Page Styles */
.products-page {
    padding: 60px 0;
}

.product-filters {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    margin: 30px 0 50px;
}

.filter-item {
    padding: 10px 20px;
    background-color: var(--bg-light);
    border-radius: 30px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.filter-item:hover,
.filter-item.active {
    background-color: var(--primary-color);
    color: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.product-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    height: 280px;
    display: block;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.product-image {
    height: 100%;
    width: 100%;
    overflow: hidden;
    position: relative;
    background-color: rgba(0, 0, 0, 0.1);
}

.product-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px;
    font-size: 1rem;
    color: white;
    margin: 0;
    text-align: center;
    font-weight: 500;
    z-index: 2;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.6);
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.product-card:hover .product-title {
    opacity: 1;
    transform: translateY(0);
}

.product-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 2;
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0);
    opacity: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
    z-index: 1;
}

.product-card:hover .product-overlay {
    background: rgba(0, 0, 0, 0.5);
}

.view-details-btn {
    padding: 8px 16px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
    transform: translateY(10px);
    opacity: 0;
    transition: all 0.3s ease;
}

.product-card:hover .view-details-btn {
    transform: translateY(0);
    opacity: 1;
}

/* Global Defaults for Product Cards */
.product-card:not(:has(.product-overlay)) .product-image {
    position: relative;
}

.product-card:not(:has(.product-overlay)) .product-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0);
    transition: background-color 0.3s ease;
    z-index: 1;
}

.product-card:not(:has(.product-overlay)):hover .product-image::after {
    background: rgba(0, 0, 0, 0.5);
}

.product-card:not(:has(.product-content)) .product-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px;
    font-size: 1rem;
    color: white;
    margin: 0;
    text-align: center;
    font-weight: 500;
    z-index: 2;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.6);
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    }

.product-card:not(:has(.product-content)):hover .product-title {
    opacity: 1;
    transform: translateY(0);
}

.product-description {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0 0 10px 0;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 15px 15px;
}

/* Footer Styles */
.footer {
    background-color: var(--bg-light);
    color: var(--text-medium);
    border-top: 1px solid var(--border-light);
}

.footer-top {
    padding: 60px 0 40px;
}

.footer-columns {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 30px;
}

.footer-column h4 {
    color: var(--text-dark);
    font-size: 18px;
    margin-bottom: 20px;
    font-weight: 600;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: var(--text-medium);
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-contact li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    font-size: 14px;
}

.footer-contact i {
    margin-right: 10px;
    color: var(--primary-color);
    font-size: 16px;
    margin-top: 2px;
}

.footer-contact a {
    color: var(--text-medium);
    transition: color 0.3s ease;
}

.footer-contact a:hover {
    color: var(--primary-color);
}

.footer-logo {
    margin-bottom: 15px;
}

.footer-description {
    font-size: 14px;
    line-height: 1.6;
}

.footer-bottom {
    padding: 15px 0;
    border-top: 1px solid var(--border-light);
    text-align: center;
}

.footer-bottom .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.copyright {
    font-size: 14px;
    color: var(--text-medium);
}

.footer-social {
    display: flex;
    gap: 10px;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #f0f0f0;
    color: #666;
    transition: all 0.3s ease;
    font-size: 16px;
}

.social-icon:hover {
    background-color: #0a59f7;
    color: white;
    transform: translateY(-3px);
}

@media (max-width: 992px) {
    .footer-columns {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .footer-columns {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-bottom .container {
        flex-direction: column;
        gap: 15px;
    }
}

@media (max-width: 576px) {
    .footer-columns {
        grid-template-columns: 1fr;
    }
}

.nav-item:hover > .nav-link {
    color: rgba(255, 255, 255, 0.9);
}

.has-submenu:hover .submenu {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

/* About Us Page Styles - 移除重叠的样式，使用PageHeader组件 */

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
}

.about-content h2 {
    font-size: 36px;
    color: #0a59f7;
    margin-bottom: 20px;
}

.about-content p {
    margin-bottom: 20px;
    line-height: 1.7;
}

.our-mission {
    background-color: #f5f8fd;
    padding: 60px 0;
}

.section-title {
    font-size: 36px;
    color: #0a59f7;
    margin-bottom: 30px;
    text-align: center;
}

.mission-content {
    max-width: 800px;
    margin: 0 auto 40px;
    text-align: center;
    line-height: 1.7;
}

.mission-content p {
    margin-bottom: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.stat-item {
    text-align: center;
    padding: 30px 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.stat-number {
    font-size: 42px;
    font-weight: 700;
    color: #0a59f7;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 16px;
    color: #333;
}

.team-section {
    padding: 60px 0;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.team-member {
    text-align: center;
}

.member-image img {
    border-radius: 50%;
    margin-bottom: 15px;
}

.member-name {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #0a59f7;
}

.member-title {
    font-size: 16px;
    color: #666;
    margin-bottom: 10px;
}

.member-bio {
    font-size: 14px;
    line-height: 1.6;
    color: #555;
}

.contact-cta {
    background-color: #0a59f7;
    padding: 60px 0;
    text-align: center;
    color: white;
}

.contact-cta h2 {
    font-size: 36px;
    margin-bottom: 15px;
}

.contact-cta p {
    font-size: 18px;
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.btn-primary {
    display: inline-block;
    background-color: white;
    color: #0a59f7;
    font-weight: 600;
    padding: 15px 30px;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #f5f8fd;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Responsive About Page */
@media (max-width: 992px) {
    .about-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .team-grid {
        grid-template-columns: 1fr;
    }

    .page-title {
        font-size: 32px;
    }

    .section-title {
        font-size: 28px;
    }
}

/* Service Pages Shared Styles */
.page-header {
    background-color: #f5f8fd;
    padding: 60px 0 30px;
    text-align: center;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
    margin: 40px 0;
}

.content-text h2 {
    font-size: 32px;
    color: #0a59f7;
    margin-bottom: 20px;
}

.content-text p {
    margin-bottom: 20px;
    line-height: 1.7;
}

/* Services List Grid */
.services-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

@media (min-width: 768px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .services-grid {
        gap: 3rem;
    }
}

.service-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.service-content {
    padding: 25px;
}

.service-content h3 {
    font-size: 22px;
    margin-bottom: 15px;
    color: #0a59f7;
}

.service-features {
    margin: 20px 0;
    padding-left: 20px;
}

.service-features li {
    margin-bottom: 8px;
    position: relative;
}

.service-features li:before {
    content: "•";
    color: #0a59f7;
    position: absolute;
    left: -15px;
}

.btn-secondary {
    display: inline-block;
    padding: 10px 20px;
    background-color: transparent;
    color: #0a59f7;
    border: 2px solid #0a59f7;
    border-radius: 4px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    margin-top: 10px;
}

.btn-secondary:hover {
    background-color: #0a59f7;
    color: white;
    transform: translateY(-2px);
}

/* Process Timeline */
.process-timeline {
    margin-top: 40px;
}

.timeline-item {
    display: flex;
    margin-bottom: 30px;
    position: relative;
}

.timeline-number {
    background-color: #0a59f7;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 700;
    margin-right: 20px;
    flex-shrink: 0;
}

.timeline-content h3 {
    font-size: 22px;
    margin-bottom: 10px;
    color: #333;
}

/* Process Steps */
.process-steps {
    margin-top: 40px;
}

.step-item {
    margin-bottom: 60px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.step-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.step-number {
    background-color: #0a59f7;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 700;
    margin-right: 20px;
}

.step-header h3 {
    font-size: 24px;
    color: #333;
}

.step-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 30px;
    align-items: center;
}

.step-list {
    margin-bottom: 20px;
    padding-left: 20px;
}

.step-list li {
    margin-bottom: 10px;
    position: relative;
}

.step-list li:before {
    content: "•";
    color: #0a59f7;
    position: absolute;
    left: -15px;
}

/* Feature Grids */
.features-grid, .standards-grid, .benefits-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.considerations-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 40px;
}

.feature-item, .standard-item, .benefit-item {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.consideration-item {
    background-color: #fff;
    padding: 25px 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.feature-item:hover, .standard-item:hover, .consideration-item:hover, .benefit-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon, .standard-icon, .consideration-icon, .benefit-icon {
    font-size: 36px;
    color: #0a59f7;
    margin-bottom: 20px;
}

.feature-item h3, .standard-item h3, .consideration-item h3, .benefit-item h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #333;
}

/* Case Studies */
.case-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.case-item {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.case-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.case-content {
    padding: 20px;
}

.case-content h3 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #0a59f7;
}

/* Testimonials */
.testimonial-slider {
    margin-top: 40px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.testimonial-item {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    text-align: center;
}

.testimonial-quote p {
    font-size: 18px;
    font-style: italic;
    color: #555;
    margin-bottom: 20px;
    line-height: 1.6;
}

.testimonial-quote h4 {
    font-size: 20px;
    margin-bottom: 5px;
    color: #333;
}

.client-title {
    color: #777;
    font-size: 16px;
}

/* CTA Section */
.cta-section {
    background-color: #0a59f7;
    padding: 60px 0;
    margin: 60px 0 0;
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 32px;
    margin-bottom: 15px;
}

.cta-content p {
    font-size: 18px;
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Quality Standards Features */
.features-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 40px;
    align-items: center;
}

.features-list {
    margin-bottom: 20px;
}

.features-list li {
    margin-bottom: 15px;
    position: relative;
    padding-left: 20px;
}

.features-list li:before {
    content: "•";
    color: #0a59f7;
    position: absolute;
    left: 0;
}

.standards-list {
    padding-left: 20px;
    margin-bottom: 30px;
}

.standards-list li {
    margin-bottom: 15px;
    position: relative;
}

.standards-image {
    margin-top: 30px;
    text-align: center;
}

/* Testing Grid */
.testing-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.testing-item {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.testing-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.testing-number {
    font-size: 24px;
    font-weight: 700;
    color: #0a59f7;
    margin-bottom: 20px;
}

.testing-item h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #333;
}

/* Team Stats */
.team-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.team-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    line-height: 1.7;
}

/* Resources Section */
.resources-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    line-height: 1.7;
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
}

.resource-item {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.resource-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.resource-icon {
    font-size: 36px;
    color: #0a59f7;
    margin-bottom: 20px;
}

.resource-item h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #333;
}

.story-item {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    align-items: center;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .content-grid {
        grid-template-columns: 1fr;
    }

    .services-grid, .features-grid, .standards-grid, .benefits-grid, .testing-grid, .resources-grid, .case-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .considerations-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .team-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .step-content {
        grid-template-columns: 1fr;
    }

    .story-item {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .services-grid, .features-grid, .standards-grid, .benefits-grid, .testing-grid {
        grid-template-columns: 1fr;
    }

    .considerations-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .resources-grid, .case-grid {
        grid-template-columns: 1fr;
    }

    .team-stats {
        grid-template-columns: 1fr;
    }

    .service-card, .feature-item, .standard-item, .consideration-item, .benefit-item, .testing-item, .resource-item {
        max-width: 100%;
    }
}

/* Playground Features Section */
.playground-features {
    padding: 60px 0;
    background-color: var(--bg-white);
    position: relative;
}

.playground-features .section-title {
    color: var(--primary-color);
    margin-bottom: 20px;
}

.playground-features .section-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    color: var(--text-medium);
    font-size: 16px;
    line-height: 1.7;
}

.features-tabs {
    margin-bottom: 30px;
}

.tab-content {
    position: relative;
}

.tab-item {
    display: flex;
    background-color: var(--bg-light);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.tab-image {
    flex: 1;
    height: 400px;
    position: relative;
    overflow: hidden;
}

.tab-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.tab-item:hover .tab-image img {
    transform: scale(1.05);
}

.tab-info {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: white;
}

.tab-info h3 {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.tab-info h4 {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--primary-color);
    font-weight: 500;
}

.tab-info p {
    color: var(--text-medium);
    font-size: 16px;
    line-height: 1.7;
    margin-bottom: 30px;
}

.features-nav {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 25px;
}

.feature-nav-btn {
    padding: 9px 18px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 30px;
    color: var(--text-medium);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    outline: none;
}

.feature-nav-btn:hover,
.feature-nav-btn.active {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

@media (max-width: 992px) {
    .tab-item {
        flex-direction: column;
    }

    .tab-image {
        height: 300px;
        width: 100%;
    }

    .tab-info {
        padding: 30px;
    }
}

@media (max-width: 768px) {
    .features-nav {
        gap: 8px;
    }

    .feature-nav-btn {
        padding: 8px 14px;
        font-size: 13px;
    }

    .tab-info h3 {
        font-size: 20px;
    }

    .tab-info h4 {
        font-size: 16px;
    }
}

@media (max-width: 576px) {
    .feature-nav-btn {
        padding: 6px 12px;
        font-size: 12px;
    }

    .considerations-grid {
        grid-template-columns: 1fr;
    }
}

/* 设计特性部分改进 */
.design-features {
    padding: 60px 0;
    background-color: #f8f9fd;
}

.design-features .section-title {
    margin-bottom: 40px;
    text-align: center;
}

.design-features .features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.design-features .feature-item {
    background-color: #fff;
    padding: 25px 20px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.design-features .feature-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: rgba(10, 89, 247, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.design-features .feature-icon i {
    font-size: 28px;
    color: #0a59f7;
}

.design-features .feature-item h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    position: relative;
}

.design-features .feature-item h3:after {
    content: '';
    width: 35px;
    height: 3px;
    background-color: #0a59f7;
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 2px;
}

.design-features .feature-item p {
    color: #666;
    line-height: 1.5;
    font-size: 14px;
}

/* 设计特性部分响应式样式 */
@media (max-width: 1200px) {
    .design-features .features-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
    }

    .design-features .feature-item {
        padding: 20px 15px;
    }
}

@media (max-width: 992px) {
    .design-features .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .design-features .feature-item {
        padding: 25px 20px;
    }
}

@media (max-width: 576px) {
    .design-features .features-grid {
        grid-template-columns: 1fr;
    }

    .design-features .feature-item {
        padding: 25px 20px;
    }
}

/* 浮动按钮样式 */
.floating-buttons {
    position: fixed;
    right: 20px;
    bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    z-index: 999;
}

.floating-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    color: white;
    font-size: 24px;
    border: none;
}

.floating-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
}

.whatsapp-btn {
    background-color: #25D366;
}

.email-btn {
    background-color: #FF9F40;
}

.top-btn {
    background-color: #9E9E9E;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s, transform 0.3s ease;
}

.top-btn.visible {
    opacity: 1;
    visibility: visible;
}

@media (max-width: 768px) {
    .floating-btn {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .floating-buttons {
        right: 15px;
        bottom: 15px;
        gap: 12px;
    }
}

/* 添加container-fluid样式 */
.container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

/* 修改section-title样式使其在全宽容器下居中 */
.factory-section .section-title {
    text-align: center;
    margin-bottom: 15px;
}

/* 调整轮播图容器布局 */
.factory-sliders-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: center;
    margin: 40px auto;
    width: 96%; /* 占据屏幕96%的宽度 */
    max-width: 100%; /* 使用全屏宽度 */
    padding: 0;
}

.factory-slider {
    position: relative;
    width: calc(50% - 15px);
    min-width: 500px; /* 增加最小宽度 */
    max-width: 800px; /* 限制最大宽度 */
    overflow: hidden;
}

/* 响应式调整 */
@media (max-width: 1800px) {
    .factory-sliders-container {
        width: 98%;
    }

    .factory-slider {
        min-width: 450px;
    }
}

@media (max-width: 1200px) {
    .factory-slider {
        min-width: 400px;
    }
}

@media (max-width: 992px) {
    .factory-slider {
        width: 94%;
        min-width: 300px;
        margin: 0 auto 30px;
    }
}

@media (max-width: 768px) {
    .factory-slides {
        height: 380px;
    }

    .factory-arrow {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .factory-slides {
        height: 300px;
    }

    .factory-slider-arrows {
        padding: 0 10px;
    }

    .factory-arrow {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }
}

@media (max-width: 1200px) {
    .slide-image {
        object-position: center top; /* Adjust position for smaller screens */
    }
}

/* 现代化产品网格样式 - 最高优先级 */
@import './modern-product-grid.css'; /* 现代化产品网格样式 - 最后导入确保最高优先级 */