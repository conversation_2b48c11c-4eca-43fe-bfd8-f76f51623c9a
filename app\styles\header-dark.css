/* 黑色导航栏样式 */

/* 基础样式，确保定位正确 */
.header-main {
  position: fixed; /* 改为固定定位，确保始终在顶部 */
  top: 0; /* 确保定位在页面顶部 */
  left: 0; /* 确保水平铺满 */
  width: 100%; /* 确保宽度100% */
  z-index: 9999 !important; /* 使用非常高的z-index确保在其他元素之上 */
  overflow: visible; /* 修改overflow为visible */
  padding: 10px 0 !important; /* 减少内边距 */
  margin: 0 !important; /* 移除所有外边距 */
  background: transparent; /* 完全透明背景 */
}

/* 文字Logo样式 */
.text-logo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 300;
  letter-spacing: 3px;
  color: #ffffff;
  background: linear-gradient(135deg, #fff 0%, #e0e0e0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  text-transform: uppercase;
}

/* 中文logo样式优化 */
:lang(zh) .logo-text {
  font-size: 1.6rem;
  letter-spacing: 2px;
  font-weight: 300;
  text-transform: none;
}

:lang(zh) .logo-tag {
  font-size: 0.8rem;
  letter-spacing: 2px;
}

.logo-tag {
  font-size: 0.7rem;
  font-weight: 300;
  letter-spacing: 3px;
  color: #3498db;
  margin-top: 2px;
  position: relative;
  padding-left: 2px;
}

/* 固定状态的导航栏背景色 - 增加不透明度 */
.header-main.sticky {
  background: linear-gradient(135deg, rgba(18, 18, 18, 0.95) 0%, rgba(26, 26, 26, 0.95) 50%, rgba(18, 18, 18, 0.95) 100%) !important;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);
  padding: 10px 0; /* 减少内边距 */
  margin-bottom: 0; /* 移除底部外边距 */
}

/* 透明状态的导航栏背景渐变 - 完全透明 */
.header-main.transparent {
  background: transparent; /* 完全透明 */
  padding: 15px 0; /* 减少内边距 */
  margin-bottom: 0; /* 移除底部外边距 */
}

/* 添加微妙的光泽效果 */
.header-main.sticky::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

/* 添加背景纹理效果 */
.header-main.sticky::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.05;
  background-image:
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 15%),
    radial-gradient(circle at 80% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 15%);
  pointer-events: none;
}

/* 导航链接增强 */
.nav-link {
  position: relative;
  transition: color 0.3s ease !important;
}

.nav-link::after {
  content: '';
  position: absolute;
  left: 10px;
  right: 10px;
  bottom: 6px;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.7);
  transform: scaleX(0);
  transition: transform 0.4s ease;
  transform-origin: center;
}

.nav-link:hover::after {
  transform: scaleX(0.6);
}

/* 按钮样式增强 */
.btn-quote {
  background-image: linear-gradient(to right, #e9e9e9, #ffffff, #e9e9e9) !important;
  transition: all 0.3s ease, transform 0.2s ease !important;
}

.btn-quote:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* 适配移动设备 */
@media (max-width: 992px) {
  .header-main.sticky {
    background: linear-gradient(135deg, rgba(18, 18, 18, 0.98) 0%, rgba(26, 26, 26, 0.98) 50%, rgba(18, 18, 18, 0.98) 100%) !important;
  }

  .mobile-menu {
    background: linear-gradient(180deg, #121212 0%, #171717 100%);
  }

  .mobile-nav-link {
    color: #f5f5f5;
    font-weight: 300;
    letter-spacing: 1.2px;
    text-transform: uppercase;
  }

  .mobile-submenu {
    background-color: #1a1a1a;
    border-left: 1px solid rgba(255, 255, 255, 0.05);
  }

  .logo img,
  .logo-image {
    height: 30px;
  }
}