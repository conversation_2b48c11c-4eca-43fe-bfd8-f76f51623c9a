/* 轮播图全屏样式 - 最高优先级 */

/* 确保轮播图容器占据整个视口 */
.hero-slider-wrapper {
  position: relative !important;
  width: 100vw !important;
  height: 100vh !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  left: 0 !important;
  right: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
  margin-left: calc(50% - 50vw) !important;
  margin-right: calc(50% - 50vw) !important;
  z-index: 1 !important; /* 确保在导航栏下方 */
}

/* 确保轮播图本身也是全屏 */
.hero-slider {
  position: relative !important;
  width: 100vw !important;
  height: 100vh !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  z-index: 1 !important;
}

/* 确保滑动容器占满整个空间 */
.hero-slider .slider-container {
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
}

/* 确保每个幻灯片都是全屏 */
.hero-slider .slide {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 0 !important;
}

.hero-slider .slide.active {
  z-index: 1 !important;
}

/* 确保图片容器是全屏 */
.hero-slider .slide-image-container {
  position: relative !important;
  width: 100% !important;
  height: 100vh !important;
  overflow: hidden !important;
}

/* 确保图片填满整个容器 */
.hero-slider .slide-image {
  width: 100% !important;
  height: 100vh !important;
  object-fit: cover !important;
  object-position: center !important;
}

/* 确保文字内容在屏幕中央 */
.hero-slider .slide-content {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 5 !important;
  width: 80% !important;
  max-width: 800px !important;
  text-align: center !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 确保导航按钮在正确位置 */
.hero-slider .slider-arrows {
  position: absolute !important;
  top: 50% !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 10 !important;
  transform: translateY(-50%) !important;
  padding: 0 30px !important;
}

.hero-slider .arrow {
  z-index: 10 !important;
}

/* 确保指示器在底部 */
.hero-slider .slider-dots {
  position: absolute !important;
  bottom: 40px !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 10 !important;
}

/* 移动端响应式调整 */
@media (max-width: 768px) {
  .hero-slider-wrapper,
  .hero-slider,
  .hero-slider .slide-image-container,
  .hero-slider .slide-image {
    height: 100vh !important;
  }
  
  .hero-slider .slide-content {
    width: 90% !important;
    padding: 0 20px !important;
  }
}

@media (max-width: 576px) {
  .hero-slider .slider-arrows {
    padding: 0 15px !important;
  }
  
  .hero-slider .slider-dots {
    bottom: 20px !important;
  }
}

/* 确保导航栏在轮播图上方 */
.header,
.header-main {
  z-index: 9999 !important;
}

/* 防止页面内容影响轮播图 */
body {
  margin: 0 !important;
  padding: 0 !important;
}

.main-content {
  position: relative !important;
  z-index: 2 !important;
}
