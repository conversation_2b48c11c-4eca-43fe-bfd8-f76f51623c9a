/* 全息投影购买指南页面专用样式覆盖 - 最高优先级 */

/* 使用最高优先级强制隐藏所有旧样式元素 */
html body div.steps-container,
html body div.step-item,
html body div.premium-card,
html body div.reveal-animation,
html body div.jsx-7ed770b744ab1351,
html body div.purchase-steps,
html body div.step-header,
html body div.step-number,
html body div.step-content,
html body div.step-image,
html body div.step-text,
html body ul.step-list,
html body li.step-list,
html body [class*="step-"]:not(.modern-guide-container):not(.modern-guide-container *),
html body [class*="purchase-"]:not(.modern-guide-container):not(.modern-guide-container *),
html body [class*="guide-"]:not(.modern-guide-container):not(.modern-guide-container *) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
  overflow: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  z-index: -1 !important;
}

/* 确保我们的现代化组件以最高优先级显示 */
html body .modern-guide-container {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  width: 100% !important;
  overflow: visible !important;
  position: relative !important;
  left: auto !important;
  z-index: 1000 !important;
  background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 100%) !important;
  min-height: 100vh !important;
}

/* 确保所有子元素正常显示 */
html body .modern-guide-container * {
  position: relative !important;
  z-index: 1001 !important;
}

/* 特别处理flex和grid布局 */
html body .modern-guide-container .flex {
  display: flex !important;
}

html body .modern-guide-container .grid {
  display: grid !important;
}

html body .modern-guide-container .inline-block {
  display: inline-block !important;
}

html body .modern-guide-container .inline {
  display: inline !important;
}

/* 强制移除任何可能的旧样式表 */
link[href*="purchase-guide"],
style[data-href*="purchase-guide"] {
  display: none !important;
}

/* 强制覆盖任何可能的背景 */
html body {
  background: transparent !important;
}

/* 额外的强制覆盖规则 */
html body .modern-guide-container .container {
  display: block !important;
  visibility: visible !important;
}

html body .modern-guide-container .space-y-8 > * {
  display: block !important;
  visibility: visible !important;
}

html body .modern-guide-container .space-y-4 > * {
  display: block !important;
  visibility: visible !important;
}

/* 确保卡片组件正常显示 */
html body .modern-guide-container .bg-gradient-to-br {
  display: block !important;
  visibility: visible !important;
}

html body .modern-guide-container .rounded-2xl {
  display: block !important;
  visibility: visible !important;
}

/* 确保文本元素正常显示 */
html body .modern-guide-container h1,
html body .modern-guide-container h2,
html body .modern-guide-container h3,
html body .modern-guide-container h4,
html body .modern-guide-container p,
html body .modern-guide-container span,
html body .modern-guide-container div {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 确保按钮和链接正常显示 */
html body .modern-guide-container button,
html body .modern-guide-container a {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 确保图标正常显示 */
html body .modern-guide-container svg {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}
