/* Holographic Guide Page Styles */

.holographic-guide-page {
  min-height: 100vh !important;
  background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 100%) !important;
  position: relative !important;
  z-index: 1 !important;
  width: 100% !important;
  overflow-x: hidden !important;
}

.holographic-guide-breadcrumb {
  background: white !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 1rem 0 !important;
}

.holographic-guide-breadcrumb nav {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  font-size: 0.875rem !important;
  color: #6b7280 !important;
}

.holographic-guide-breadcrumb a {
  color: #6b7280 !important;
  transition: color 0.3s ease !important;
}

.holographic-guide-breadcrumb a:hover {
  color: #2563eb !important;
}

.holographic-guide-container {
  max-width: 1280px !important;
  margin: 0 auto !important;
  padding: 0 1rem !important;
}

.holographic-guide-header {
  text-align: center !important;
  margin-bottom: 3rem !important;
  padding: 4rem 0 !important;
}

.holographic-guide-badge {
  display: inline-flex !important;
  align-items: center !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 9999px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  background-color: #dbeafe !important;
  color: #2563eb !important;
  margin-bottom: 1rem !important;
}

.holographic-guide-title {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  color: #111827 !important;
  line-height: 1.2 !important;
  margin-bottom: 1.5rem !important;
}

.holographic-guide-title-accent {
  color: #2563eb !important;
}

.holographic-guide-description {
  font-size: 1.125rem !important;
  color: #6b7280 !important;
  line-height: 1.7 !important;
  max-width: 48rem !important;
  margin: 0 auto !important;
}

.holographic-guide-stats {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 4rem !important;
}

.holographic-guide-stat {
  text-align: center !important;
}

.holographic-guide-stat-number {
  font-size: 1.875rem !important;
  font-weight: 700 !important;
  color: #111827 !important;
  margin-bottom: 0.5rem !important;
}

.holographic-guide-stat-label {
  font-size: 0.875rem !important;
  color: #6b7280 !important;
}

.holographic-guide-steps {
  display: flex !important;
  flex-direction: column !important;
  gap: 2rem !important;
}

.holographic-guide-step {
  background: white !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  padding: 2rem !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

.holographic-guide-step:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

.holographic-guide-step-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  color: #111827 !important;
  margin-bottom: 1rem !important;
}

.holographic-guide-step-list {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.holographic-guide-step-item {
  color: #374151 !important;
  margin-bottom: 0.5rem !important;
  padding-left: 1rem !important;
  position: relative !important;
}

.holographic-guide-step-item:before {
  content: "•" !important;
  color: #2563eb !important;
  font-weight: bold !important;
  position: absolute !important;
  left: 0 !important;
}

.holographic-guide-cta {
  margin-top: 4rem !important;
  text-align: center !important;
}

.holographic-guide-cta-box {
  background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%) !important;
  color: white !important;
  border-radius: 0.5rem !important;
  padding: 2rem !important;
}

.holographic-guide-cta-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 1rem !important;
  color: white !important;
}

.holographic-guide-cta-description {
  color: #bfdbfe !important;
  margin-bottom: 1.5rem !important;
  max-width: 32rem !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.holographic-guide-cta-button {
  display: inline-flex !important;
  align-items: center !important;
  padding: 0.75rem 1.5rem !important;
  background: white !important;
  color: #2563eb !important;
  border-radius: 0.375rem !important;
  font-weight: 500 !important;
  text-decoration: none !important;
  transition: background-color 0.3s ease !important;
}

.holographic-guide-cta-button:hover {
  background-color: #f3f4f6 !important;
  color: #2563eb !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .holographic-guide-title {
    font-size: 2.25rem !important;
  }

  .holographic-guide-container {
    padding: 0 1.5rem !important;
  }
}

@media (max-width: 768px) {
  .holographic-guide-title {
    font-size: 2rem !important;
  }

  .holographic-guide-stats {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .holographic-guide-step {
    padding: 1.5rem !important;
  }

  .holographic-guide-step-title {
    font-size: 1.25rem !important;
  }

  .holographic-guide-header {
    padding: 2rem 0 !important;
  }

  .holographic-guide-cta-box {
    padding: 1.5rem !important;
  }

  .holographic-guide-cta-title {
    font-size: 1.25rem !important;
  }
}

@media (max-width: 640px) {
  .holographic-guide-title {
    font-size: 1.75rem !important;
  }

  .holographic-guide-description {
    font-size: 1rem !important;
  }

  .holographic-guide-stat-number {
    font-size: 1.5rem !important;
  }

  .holographic-guide-cta-button {
    width: 100% !important;
    justify-content: center !important;
    max-width: 300px !important;
  }
}

/* Force override any conflicting styles */
.holographic-guide-page * {
  box-sizing: border-box !important;
}

.holographic-guide-page .container {
  max-width: 1280px !important;
  margin: 0 auto !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

/* Ensure proper spacing */
.holographic-guide-page .space-y-8 > * + * {
  margin-top: 2rem !important;
}

.holographic-guide-page .space-y-2 > * + * {
  margin-top: 0.5rem !important;
}

.holographic-guide-page .mb-16 {
  margin-bottom: 4rem !important;
}

.holographic-guide-page .mb-12 {
  margin-bottom: 3rem !important;
}

.holographic-guide-page .mb-6 {
  margin-bottom: 1.5rem !important;
}

.holographic-guide-page .mb-4 {
  margin-bottom: 1rem !important;
}

/* 确保页面内容不被其他元素覆盖 */
.holographic-guide-page {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.holographic-guide-breadcrumb {
  position: relative !important;
  z-index: 10 !important;
}

.holographic-guide-container {
  position: relative !important;
  z-index: 5 !important;
  display: block !important;
  visibility: visible !important;
}

.holographic-guide-header {
  position: relative !important;
  z-index: 5 !important;
  display: block !important;
  visibility: visible !important;
}

.holographic-guide-stats {
  position: relative !important;
  z-index: 5 !important;
  display: grid !important;
  visibility: visible !important;
}

.holographic-guide-steps {
  position: relative !important;
  z-index: 5 !important;
  display: flex !important;
  visibility: visible !important;
}

.holographic-guide-cta {
  position: relative !important;
  z-index: 5 !important;
  display: block !important;
  visibility: visible !important;
}

/* 防止被弹窗或其他元素覆盖 */
body.holographic-guide-active {
  overflow-x: hidden !important;
}

/* 强制显示页面内容 */
.holographic-guide-page > * {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}
