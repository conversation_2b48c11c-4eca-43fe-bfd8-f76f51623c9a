/* 首页轮播图样式优化 - 全屏显示 */
.hero-slider-wrapper {
  position: relative !important;
  overflow: hidden !important;
  width: 100vw !important; /* 占据整个视口宽度 */
  height: 100vh !important; /* 占据整个视口高度 */
  max-width: 100vw !important;
  margin-left: calc(50% - 50vw) !important; /* 向左延伸到屏幕边缘 */
  margin-right: calc(50% - 50vw) !important; /* 向右延伸到屏幕边缘 */
  left: 0 !important;
  right: 0 !important;
  top: 0 !important;
  margin-top: 0 !important; /* 移除负边距，让轮播图从顶部开始 */
  padding-top: 0 !important; /* 移除顶部内边距，实现真正的全屏 */
}

.hero-slider {
  margin-top: 0 !important;
  padding-top: 0 !important;
  height: 100vh !important; /* 确保轮播图高度为全屏 */
  width: 100vw !important; /* 确保轮播图宽度为全屏 */
}

/* 轮播图内容样式优化 - 全屏模式下的文字定位 */
.hero-slider .slide-content {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 5 !important; /* 确保内容在最上层 */
  padding-top: 0 !important; /* 移除顶部内边距，让文字真正居中 */
  width: 80% !important;
  max-width: 800px !important;
  text-align: center !important;
}

/* 轮播图导航按钮样式优化 */
.hero-slider .arrow {
  z-index: 10 !important; /* 确保导航按钮在最上层 */
}

/* 轮播图指示器样式优化 */
.hero-slider .slider-dots {
  bottom: 20px !important; /* 调整指示器位置 */
  z-index: 10 !important; /* 确保指示器在最上层 */
}
