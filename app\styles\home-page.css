/* 页面布局优化 */
.section-spacer {
  height: 120px;
}

.section-container {
  max-width: 1240px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
}

/* 背景样式优化 */
.bg-light {
  background-color: #f7f9fc;
  position: relative;
  overflow: hidden;
}

.bg-dark {
  background-color: #0e1224;
  color: #fff;
  position: relative;
  overflow: hidden;
}

/* 区域模式和装饰 */
.section-pattern {
  position: absolute;
  left: 0;
  width: 100%;
  height: 80px;
  z-index: 1;
}

.section-pattern-top {
  top: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
}

.section-pattern-bottom {
  bottom: 0;
  background: linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
}

.section-pattern-dark {
  background: linear-gradient(180deg, rgba(14, 18, 36, 1) 0%, rgba(14, 18, 36, 0) 100%);
}

.section-pattern-dark.section-pattern-bottom {
  background: linear-gradient(0deg, rgba(14, 18, 36, 1) 0%, rgba(14, 18, 36, 0) 100%);
}

/* 区域样式优化 */
.section-features, .section-about, .section-custom {
  padding: 120px 0;
  position: relative;
  z-index: 1;
}

/* 高端UI元素全局样式 */
.home-page h1,
.home-page h2,
.home-page h3,
.home-page h4,
.home-page h5,
.home-page h6 {
  font-weight: 300;
  letter-spacing: -0.5px;
}

.section-title {
  font-size: 42px; /* 增大字体大小 */
  margin-bottom: 24px;
  line-height: 1.2;
}

.section-subtitle {
  font-size: 18px; /* 增大描述文字大小 */
  line-height: 1.6;
}

.thin-text {
  font-weight: 200;
}

.home-page strong {
  font-weight: 500;
}

.home-page p {
  font-weight: 300;
  line-height: 1.8;
}

.home-page button, .home-page .btn {
  transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
}

/* 高端分隔线 */
.elegant-divider {
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #1a1a2e, rgba(26, 26, 46, 0.3));
  margin: 30px 0;
}

/* 微妙的阴影效果 */
.premium-shadow {
  box-shadow: 0 20px 60px rgba(26, 26, 46, 0.08);
}

/* 图片悬停效果 */
.hover-scale {
  overflow: hidden;
}

.hover-scale img {
  transition: transform 0.7s cubic-bezier(0.19, 1, 0.22, 1);
}

/* 移除图片悬停放大效果 */

/* 加载动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

/* 响应式布局优化 */
@media (max-width: 1366px) {
  .section-spacer {
    height: 100px;
  }

  .section-features, .section-about, .section-custom {
    padding: 100px 0;
  }

  .section-title {
    font-size: 36px;
  }

  .section-subtitle {
    font-size: 17px;
  }
}

@media (max-width: 992px) {
  .section-spacer {
    height: 80px;
  }

  .section-features, .section-about, .section-custom {
    padding: 80px 0;
  }

  .section-title {
    font-size: 32px;
  }

  .section-subtitle {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .section-spacer {
    height: 60px;
  }

  .section-features, .section-about, .section-custom {
    padding: 60px 0;
  }

  .section-title {
    font-size: 28px;
  }

  .section-subtitle {
    font-size: 15px;
  }
}

@media (max-width: 576px) {
  .section-spacer {
    height: 50px;
  }

  .section-features, .section-about, .section-custom {
    padding: 50px 0;
  }
}
