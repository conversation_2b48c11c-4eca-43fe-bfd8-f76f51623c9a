/**
 * 页面加载过程中的样式修复
 * 解决产品页面在刷新过程中出现的样式重叠问题
 */

/* 在页面加载过程中隐藏内容，直到CSS完全加载 */
.js-loading {
  visibility: hidden;
}

/* 页面加载完成后显示内容 */
.js-loading-complete {
  visibility: visible;
}

/* 产品详情页容器 - 防止在加载过程中出现重叠 */
.product-detail-container {
  position: relative;
  z-index: 1;
  background-color: #fff;
  transition: opacity 0.3s ease;
}

/* 产品详情页顶部横幅 - 确保在加载过程中有正确的位置 */
.product-detail-container .w-full.absolute {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 0 !important;
}

/* 产品详情内容 - 确保在加载过程中有正确的位置 */
.product-detail-container .bg-white.relative {
  position: relative !important;
  z-index: 1 !important;
  background-color: #fff !important;
}

/* 确保导航栏在页面加载过程中有正确的位置和样式 */
.header-main {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;
  transition: background-color 0.3s ease, transform 0.3s ease !important;
}

/* 确保产品页面的内容在导航栏下方 */
.product-list-page,
.product-detail-page {
  padding-top: 80px !important;
  position: relative;
  z-index: 1;
}

/* 添加页面加载过渡效果 */
body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 9999;
  pointer-events: none;
  opacity: 1;
  transition: opacity 0.5s ease;
}

body.loaded::after {
  opacity: 0;
}

/* 确保产品详情页的banner图片在加载过程中有正确的位置 */
.product-detail-container .w-full.absolute img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 防止产品详情页内容在加载过程中闪烁 */
.product-detail-container .container {
  opacity: 0;
  animation: fadeIn 0.5s ease forwards;
  animation-delay: 0.3s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 确保产品列表页在加载过程中有正确的样式 */
.grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3.gap-8,
.products-grid {
  opacity: 0;
  animation: fadeIn 0.5s ease forwards;
  animation-delay: 0.2s;
}

/* 产品列表页面样式修复 */
.product-list-page {
  position: relative;
  z-index: 1;
}

.product-list-content {
  position: relative;
  z-index: 2;
  background-color: #fff;
}

.product-list-container {
  opacity: 0;
  animation: fadeIn 0.5s ease forwards;
  animation-delay: 0.3s;
}

/* 修复产品详情页和产品列表页在刷新过程中的样式重叠问题 */
@media (min-width: 768px) {
  .product-detail-container {
    margin-top: 0 !important;
  }

  .product-detail-container .w-full.absolute {
    height: 450px !important;
  }

  .product-detail-container .bg-white.relative {
    margin-top: 450px !important;
  }
}

/* 移动端样式修复 */
@media (max-width: 767px) {
  .product-detail-container {
    margin-top: 0 !important;
  }

  .product-detail-container .w-full.absolute {
    height: 300px !important;
  }

  .product-detail-container .bg-white.relative {
    margin-top: 300px !important;
  }
}
