/* Marketing Support Services Styles */

/* Marketing Services Grid */
.marketing-services {
  padding: 60px 0;
  background-color: #f8f9fd;
}

.marketing-services .section-title {
  text-align: center;
  margin-bottom: 40px;
  color: #333;
}

.marketing-services .services-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

/* Override for the specific page in the screenshot */
.services-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-top: 40px;
}

.marketing-services .service-item, .service-item {
  background-color: #fff;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
}

.marketing-services .service-item:hover, .service-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.marketing-services .service-icon, .service-icon {
  font-size: 36px;
  color: #0a59f7;
  margin-bottom: 20px;
}

.marketing-services .service-item h3, .service-item h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
}

.marketing-services .service-item ul, .service-item ul {
  text-align: left;
  padding-left: 20px;
  margin-top: 15px;
}

.marketing-services .service-item ul li, .service-item ul li {
  margin-bottom: 8px;
  position: relative;
  font-size: 14px;
  color: #666;
}

.marketing-services .service-item ul li:before, .service-item ul li:before {
  content: "•";
  color: #0a59f7;
  position: absolute;
  left: -15px;
}

/* Marketing Process Timeline */
.marketing-process {
  padding: 60px 0;
}

.marketing-process .section-title {
  text-align: center;
  margin-bottom: 40px;
  color: #333;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .marketing-services .services-grid, .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .marketing-services .services-grid, .services-grid {
    grid-template-columns: 1fr;
  }
}
