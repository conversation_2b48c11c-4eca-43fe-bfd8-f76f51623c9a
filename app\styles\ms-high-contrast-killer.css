/**
 * Complete killer for -ms-high-contrast media queries
 * This file uses multiple CSS hacks to completely prevent any -ms-high-contrast media queries
 * from being processed or causing deprecation warnings
 */

/*
 * Technique 1: Use @supports to create a rule that will always be applied
 * and override any -ms-high-contrast media queries
 */
@supports (display: block) {
  /* This will match all elements */
  * {
    /* Reset any properties that might be set in -ms-high-contrast media queries */
    forced-color-adjust: none !important;
    forced-color-adjust: none !important;
  }
}

/*
 * Technique 1.5: Use !important to ensure our overrides take precedence
 */
* {
  forced-color-adjust: none !important;
  forced-color-adjust: none !important;
}

/*
 * Technique 2: Create empty media queries that match the deprecated ones
 * This tricks the browser into thinking they're already handled
 */
@media (forced-colors: active) { #ms-high-contrast-blocker {} }
@media (forced-colors: black-on-white) { #ms-high-contrast-blocker {} }
@media (forced-colors: white-on-black) { #ms-high-contrast-blocker {} }
@media (forced-colors: none) { #ms-high-contrast-blocker {} }

/*
 * Technique 3: Use a more specific selector to override any styles
 * that might be set in -ms-high-contrast media queries
 */
html body * {
  forced-color-adjust: none !important;
  forced-color-adjust: none !important;
}

/*
 * Technique 4: Use @media all to create a rule that will always be applied
 * and override any -ms-high-contrast media queries
 */
@media all {
  @media (forced-colors: active),
         (forced-colors: black-on-white),
         (forced-colors: white-on-black),
         (forced-colors: none) {
    /* This empty rule will be processed but won't affect styling */
    #ms-high-contrast-blocker {}
  }
}

/*
 * Technique 5: Use !important to override any styles that might be set
 * in -ms-high-contrast media queries
 */
@media (forced-colors: active),
       (forced-colors: black-on-white),
       (forced-colors: white-on-black),
       (forced-colors: none) {
  * {
    forced-color-adjust: none !important;
    forced-color-adjust: none !important;
  }
}

/*
 * Technique 6: Add proper modern replacements for high contrast mode
 */
@media (forced-colors: active) {
  /* Basic elements */
  body {
    color: CanvasText;
    background-color: Canvas;
    forced-color-adjust: none;
  }

  /* Links */
  a {
    color: LinkText;
    forced-color-adjust: none;
  }

  a:visited {
    color: VisitedText;
    forced-color-adjust: none;
  }

  /* Buttons */
  button,
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    color: ButtonText;
    background-color: ButtonFace;
    border: 1px solid ButtonText;
    forced-color-adjust: none;
  }

  /* Form elements */
  input,
  textarea,
  select {
    color: FieldText;
    background-color: Field;
    border: 1px solid FieldText;
    forced-color-adjust: none;
  }
}
