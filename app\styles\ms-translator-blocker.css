/**
 * 微软翻译器属性阻止器
 * 
 * 此CSS文件专门用于阻止Microsoft Translator添加额外属性，
 * 例如_msttexthash, _msthidden, _msthash等，这些属性会导致React hydration警告
 */

/* 重置Microsoft Translator可能添加的所有属性 */
[_msttexthash],
[_msthidden],
[_msthash],
[_mstvisible],
[_mstlang],
[_mstaltindent],
[_mstalt],
[_mstwidth],
[_msthiddenattr],
[_mstplaceholder] {
  /* 覆盖这些属性 */
  _msttexthash: initial !important;
  _msthidden: initial !important;
  _msthash: initial !important;
  _mstvisible: initial !important;
  _mstlang: initial !important;
  _mstaltindent: initial !important;
  _mstalt: initial !important;
  _mstwidth: initial !important;
  _msthiddenattr: initial !important;
  _mstplaceholder: initial !important;
}

/* 为文档添加全局翻译跳过指令 */
html {
  translate: no;
  -webkit-translate: no;
  -ms-translate: no;
}

/* 强制所有元素不被自动翻译 */
body * {
  translate: no;
  -webkit-translate: no;
  -ms-translate: no;
} 