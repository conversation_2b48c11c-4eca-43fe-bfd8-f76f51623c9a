/* PageHeader组件文字垂直居中修复 - 使用最高优先级 */

/* 强力修复 - 使用最高优先级选择器 */
html body .product-list-page .relative.overflow-hidden .container.mx-auto,
html body div[class*="product-list-page"] div[class*="relative overflow-hidden"] div[class*="container mx-auto"] {
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  height: 100% !important;
  padding-top: 80px !important; /* 为导航栏留出空间 */
  padding-bottom: 80px !important; /* 保持对称 */
  margin-top: 0 !important;
  box-sizing: border-box !important;
}

/* 移动端调整 */
@media (max-width: 768px) {
  html body .product-list-page .relative.overflow-hidden .container.mx-auto,
  html body div[class*="product-list-page"] div[class*="relative overflow-hidden"] div[class*="container mx-auto"] {
    padding-top: 60px !important;
    padding-bottom: 60px !important;
  }
}

/* 通用修复 - 针对所有PageHeader - 使用最强选择器 */
html body div[class*="relative overflow-hidden"] div[class*="container mx-auto"],
html body .about-us-page div[class*="relative overflow-hidden"] div[class*="container mx-auto"],
html body main div[class*="relative overflow-hidden"] div[class*="container mx-auto"],
html body div.relative.overflow-hidden div.container.mx-auto,
html body .main-content div.relative.overflow-hidden div.container.mx-auto {
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  height: 100% !important;
  padding-top: 80px !important; /* 为导航栏留出空间 */
  padding-bottom: 80px !important; /* 保持对称 */
  margin-top: 0 !important;
  box-sizing: border-box !important;
}

/* 移动端调整 */
@media (max-width: 768px) {
  html body div[class*="relative overflow-hidden"] div[class*="container mx-auto"],
  html body .about-us-page div[class*="relative overflow-hidden"] div[class*="container mx-auto"],
  html body main div[class*="relative overflow-hidden"] div[class*="container mx-auto"],
  html body div.relative.overflow-hidden div.container.mx-auto,
  html body .main-content div.relative.overflow-hidden div.container.mx-auto {
    padding-top: 60px !important;
    padding-bottom: 60px !important;
  }
}

/* 最强力修复 - 使用属性选择器和通配符 */
* div[class*="relative"][class*="overflow-hidden"] * div[class*="container"][class*="mx-auto"] {
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  height: 100% !important;
  padding-top: 80px !important;
  padding-bottom: 80px !important;
  margin-top: 0 !important;
  box-sizing: border-box !important;
}

@media (max-width: 768px) {
  * div[class*="relative"][class*="overflow-hidden"] * div[class*="container"][class*="mx-auto"] {
    padding-top: 60px !important;
    padding-bottom: 60px !important;
  }
}
