/* 淘宝风格产品详情页 */

.product-detail {
  padding: 40px 0 40px; /* 减小上内边距 */
  background-color: #f5f5f5;
  margin-top: 60px !important; /* 减小顶部外边距，使内容更靠近导航栏 */
  position: relative; /* 添加相对定位 */
  z-index: 1; /* 确保内容在导航栏下方 */
}

/* 面包屑导航 */
.breadcrumb {
  padding: 10px 0;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.breadcrumb a {
  color: #666;
  transition: color 0.3s;
}

.breadcrumb a:hover {
  color: #ff4400;
}

/* 淘宝风格的商品画廊 */
.taobao-gallery {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.thumbnails-column {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 80px;
  margin-top: 20px; /* 添加顶部外边距，确保缩略图与顶部有足够间距 */
  padding-top: 20px; /* 添加顶部内边距 */
}

.thumbnail-item {
  width: 80px;
  height: 80px;
  border: 2px solid #e1e1e1;
  border-radius: 2px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.thumbnail-item.active {
  border-color: #ff4400;
}

.thumbnail-item:hover {
  border-color: #ff4400;
}

.main-image-container {
  flex: 1;
  height: 450px;
  border: 1px solid #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
  position: relative;
  background-color: #fff;
}

.main-image, .main-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 产品信息区域 */
.product-info {
  background: white;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.product-category {
  color: #888;
  font-size: 0.9rem;
}

.product-title {
  font-size: 1.6rem;
  font-weight: 600;
  margin: 10px 0;
  color: #333;
  line-height: 1.3;
}

.product-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

/* 价格区域 */
.price-container {
  background-color: #fff9f9;
  padding: 15px;
  margin: 15px 0;
  border-radius: 4px;
}

.price-label {
  color: #666;
  font-size: 0.9rem;
}

.price-value {
  font-size: 2rem;
  color: #ff4400;
  font-weight: 600;
  margin-right: 10px;
}

.original-price {
  font-size: 1rem;
  color: #999;
  text-decoration: line-through;
}

.discount-badge {
  display: inline-block;
  background-color: #ff4400;
  color: white;
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 2px;
  margin-left: 8px;
}

/* 规格选择区域 */
.specifications {
  margin: 20px 0;
}

.spec-title {
  color: #666;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.spec-option {
  border: 1px solid #ddd;
  padding: 6px 15px;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.3s;
}

.spec-option:hover {
  border-color: #ff4400;
  color: #ff4400;
}

.spec-option.selected {
  border-color: #ff4400;
  color: #ff4400;
  background-color: #fff1f1;
}

/* 数量选择器 */
.quantity-selector {
  display: flex;
  align-items: center;
  margin: 20px 0;
}

.quantity-label {
  margin-right: 15px;
  color: #666;
  font-size: 0.9rem;
}

.quantity-controls {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 2px;
}

.quantity-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7f7f7;
  border: none;
  cursor: pointer;
}

.quantity-input {
  width: 50px;
  height: 30px;
  border: none;
  border-left: 1px solid #ddd;
  border-right: 1px solid #ddd;
  text-align: center;
}

/* 动作按钮 */
.action-buttons {
  display: flex;
  gap: 15px;
  margin-top: 25px;
}

.btn-primary {
  background-color: #ff4400;
  border: none;
  color: white;
  padding: 12px 25px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary:hover {
  background-color: #e63c00;
}

.btn-secondary {
  background-color: #fff2e8;
  border: 1px solid #ffded3;
  color: #ff4400;
  padding: 12px 25px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-secondary:hover {
  background-color: #ffeadf;
}

.btn-outline {
  background-color: white;
  border: 1px solid #ddd;
  color: #666;
  padding: 12px 25px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-outline:hover {
  border-color: #999;
  color: #333;
}

/* 分享按钮 */
.share-buttons {
  margin: 20px 0;
  display: flex;
  align-items: center;
}

/* 产品详情选项卡 */
.product-tabs {
  margin-top: 30px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.tabs-nav {
  display: flex;
  border-bottom: 1px solid #eee;
}

.tabs-nav button {
  padding: 15px 25px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 1rem;
  color: #666;
  transition: all 0.3s;
  position: relative;
}

.tabs-nav button::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: transparent;
  transition: background-color 0.3s;
}

.tabs-nav button:hover {
  color: #ff4400;
}

.tabs-nav button.active {
  color: #ff4400;
  font-weight: 600;
}

.tabs-nav button.active::after {
  background-color: #ff4400;
}

.tab-content {
  padding: 25px;
}

/* 特性卡片 */
.feature-card {
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.12);
}

/* 技术规格 */
.specs-tab {
  padding: 20px;
}

.spec-item {
  margin-bottom: 15px;
}

/* 应用场景 */
.application-card {
  height: 200px;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  margin-bottom: 15px;
}

.application-card h3 {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 15px;
  margin: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
  color: white;
  font-size: 1.2rem;
}

/* 安装选项 */
.installation-tab {
  padding: 20px;
}

/* 相关产品 */
.related-products {
  padding: 40px 0;
  background-color: white;
}

.related-products h2 {
  text-align: center;
  font-size: 1.8rem;
  margin-bottom: 30px;
  color: #333;
}

/* 响应式样式 */
@media (max-width: 992px) {
  .taobao-gallery {
    flex-direction: column;
  }

  .thumbnails-column {
    flex-direction: row;
    width: 100%;
    overflow-x: auto;
  }

  .main-image-container {
    height: 350px;
  }

  .action-buttons {
    flex-wrap: wrap;
  }

  .btn-primary, .btn-secondary {
    flex: 1 0 45%;
  }

  .btn-outline {
    flex: 1 0 100%;
    margin-top: 10px;
  }
}

@media (max-width: 768px) {
  .product-title {
    font-size: 1.4rem;
  }

  .price-value {
    font-size: 1.6rem;
  }

  .tabs-nav button {
    padding: 12px 15px;
    font-size: 0.9rem;
  }

  .tab-content {
    padding: 15px;
  }
}

@media (max-width: 576px) {
  .main-image-container {
    height: 300px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .btn-primary, .btn-secondary, .btn-outline {
    width: 100%;
    margin-top: 10px;
  }

  .tabs-nav {
    overflow-x: auto;
  }
}

/* 产品大图展示区域优化 - 被product-detail-fix.css覆盖 */
.product-large-gallery {
  margin-top: 60px;
  margin-bottom: 60px;
}

.product-large-gallery .space-y-12 > div {
  margin-bottom: 40px;
}

.product-large-gallery .group {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.product-large-gallery .group:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 产品大图容器 */
.product-detail-image-container {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  min-height: 400px;
  max-height: 600px;
  overflow: hidden;
  border-radius: 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.product-detail-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.product-detail-image-container:hover img {
  transform: scale(1.05);
}

/* 产品展示标题区域 */
.product-showcase-header {
  text-align: center;
  margin-bottom: 48px;
  padding: 0 16px;
}

.product-showcase-title {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 16px;
}

.product-showcase-divider {
  width: 96px;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  margin: 0 auto 16px;
  border-radius: 2px;
}

.product-showcase-description {
  color: #6b7280;
  font-size: 1.125rem;
  max-width: 32rem;
  margin: 0 auto;
  line-height: 1.6;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .product-showcase-title {
    font-size: 2rem;
  }

  .product-detail-image-container {
    min-height: 300px;
    max-height: 400px;
  }

  .product-large-gallery {
    margin-top: 40px;
    margin-bottom: 40px;
  }
}