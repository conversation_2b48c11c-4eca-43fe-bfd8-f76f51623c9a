/* 产品页面样式 - 复制自解决方案页面样式 */

/* 页面顶部横幅样式 */
.page-banner {
  background-size: cover;
  background-position: center;
  padding: 100px 0 80px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.page-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(10, 36, 99, 0.85) 0%, rgba(30, 80, 162, 0.75) 100%);
  z-index: 1;
}

.page-banner::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 35%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  z-index: 2;
}

.page-banner .container {
  position: relative;
  z-index: 3;
}

.page-banner .page-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  letter-spacing: 1px;
}

.page-banner .breadcrumb {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
}

.page-banner .breadcrumb a {
  color: rgba(255, 255, 255, 0.9);
  transition: color 0.3s ease;
}

.page-banner .breadcrumb a:hover {
  color: #ffffff;
  text-decoration: none;
}

.page-banner .separator {
  margin: 0 10px;
  opacity: 0.7;
}

/* 媒体查询 */
@media (max-width: 992px) {
  .page-banner .page-title {
    font-size: 2.8rem;
  }
}

@media (max-width: 768px) {
  .page-banner {
    padding: 80px 0 60px;
  }
  
  .page-banner .page-title {
    font-size: 2.2rem;
  }
}

@media (max-width: 576px) {
  .page-banner {
    padding: 60px 0 40px;
  }
  
  .page-banner .page-title {
    font-size: 1.8rem;
  }
} 