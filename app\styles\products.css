/* Products Page Styles - Unified with Homepage Design */
.products-page {
  padding: 100px 0 60px;
  margin-top: 30px;
  position: relative;
  background: linear-gradient(to bottom, #f5f9ff 0%, #ffffff 300px);
}

/* 页面标题区域 */
.page-banner {
  /* background-image: url('/images/products/slide1.jpg') !important; */ /* <-- 注释掉此行 */
  background-size: cover !important;
  background-position: center top !important;
  padding: 150px 0 100px !important;
  color: white !important;
  position: relative !important;
  overflow: hidden !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
  margin-bottom: 50px !important; /* 增加与下方内容的间距 */
  margin-top: 0 !important; /* 移除顶部外边距，让banner紧贴导航栏 */
}

.page-banner:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(120deg, rgba(10, 89, 247, 0.5), rgba(26, 26, 46, 0.6)) !important;
  z-index: 1;
}

.page-banner:after {
  content: '';
  position: absolute;
  bottom: -50px;
  right: -50px;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  z-index: 1;
}

.page-banner .container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 页面内容与顶部横幅分隔线 */
.products-page:before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 6px;
  background: linear-gradient(90deg, transparent, rgba(26, 101, 227, 0.3), transparent);
  border-radius: 3px;
  z-index: 2;
}

.page-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  letter-spacing: 1px;
  color: white;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  animation: fadeInDown 0.8s ease-out;
}

.breadcrumb {
  display: flex;
  gap: 10px;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  align-items: center;
  animation: fadeIn 1s ease-out;
}

.breadcrumb a {
  color: rgba(255, 255, 255, 0.9);
  transition: color 0.3s;
}

.breadcrumb a:hover {
  color: white;
}

.breadcrumb .separator {
  color: rgba(255, 255, 255, 0.7);
  margin: 0 5px;
}

.page-header {
  text-align: center;
  margin-bottom: 50px;
  position: relative;
  padding-top: 20px;
}

.page-description {
  font-size: 1.1rem;
  max-width: 800px;
  margin: 0 auto 30px;
  color: #556;
  font-weight: 300;
  line-height: 1.8;
}

/* 产品筛选区域 */
.product-filters {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: 30px 0 50px;
  gap: 20px;
}

.filter-item {
  background-color: #f0f4f8;
  border: 1px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  padding: 12px 28px;
  font-weight: 500;
  font-size: 1.05rem;
  color: #333;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.filter-item.active {
  background-color: #1a65e3;
  color: white;
  box-shadow: 0 5px 15px rgba(26, 101, 227, 0.2);
}

/* 产品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 50px;
  margin-bottom: 60px;
  padding: 20px;
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 40px;
    padding: 15px;
  }
}

@media (max-width: 576px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 30px;
    padding: 10px;
  }
}

.product-card {
  background-color: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(26, 26, 46, 0.08);
  transition: transform 0.4s cubic-bezier(0.19, 1, 0.22, 1), box-shadow 0.4s cubic-bezier(0.19, 1, 0.22, 1);
  border: 1px solid rgba(26, 26, 46, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 380px;
  justify-self: center;
  margin: 0 auto;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(26, 26, 46, 0.12);
}

.product-image {
  height: 280px !important;
  overflow: hidden;
  position: relative;
  width: 100%;
}

.product-image img, .product-img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
  transition: transform 0.7s cubic-bezier(0.19, 1, 0.22, 1);
}

.product-card:hover .product-image img,
.product-card:hover .product-img {
  transform: scale(1.05);
}

.card-link {
  display: flex;
  flex-direction: column;
  height: 100%;
  color: inherit;
  text-decoration: none;
}

.product-content {
  padding: 30px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.product-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
  padding: 1.2rem;
  color: var(--text-dark);
  line-height: 1.4;
  transition: color 0.3s;
  text-align: center;
  border-top: 1px solid rgba(0,0,0,0.05);
  min-height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-card:hover .product-title {
  color: #1a65e3;
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.view-details-btn {
  background-color: #1a65e3;
  color: white;
  padding: 10px 20px;
  border-radius: 30px;
  font-weight: 500;
  transform: translateY(20px);
  transition: transform 0.3s ease;
  box-shadow: 0 5px 15px rgba(26, 101, 227, 0.3);
}

.product-card:hover .view-details-btn {
  transform: translateY(0);
}

.product-description {
  color: var(--text-medium);
  font-weight: 300;
  line-height: 1.6;
  margin-bottom: 20px;
  flex-grow: 1;
}

.product-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: auto;
}

.product-size, .product-type {
  font-size: 0.85rem;
  padding: 5px 10px;
  border-radius: 4px;
  display: inline-block;
}

.product-size {
  background-color: rgba(26, 101, 227, 0.1);
  color: #1a65e3;
}

.product-type {
  background-color: rgba(20, 184, 116, 0.1);
  color: #14b874;
}

.product-link {
  display: inline-flex;
  align-items: center;
  margin-top: 15px;
  color: #1a65e3;
  font-weight: 500;
  transition: all 0.3s;
}

.product-link i {
  margin-left: 8px;
  transition: transform 0.3s;
}

.product-link:hover {
  color: #0a4bc1;
}

.product-link:hover i {
  transform: translateX(3px);
}

/* 产品详情页 */
.product-detail {
  padding: 60px 0 80px;
}

.product-detail .container {
  max-width: 1200px;
}

.product-header {
  margin-bottom: 50px;
  text-align: center;
}

.product-title {
  font-size: 2.5rem;
  font-weight: 400;
  margin-bottom: 15px;
  color: var(--text-dark);
}

.product-description {
  font-size: 1.1rem;
  max-width: 800px;
  margin: 0 auto;
  color: var(--text-medium);
  font-weight: 300;
  line-height: 1.8;
}

.product-gallery {
  margin-bottom: 60px;
}

.gallery-main {
  height: 500px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(26, 26, 46, 0.1);
  margin-bottom: 20px;
}

.main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.gallery-thumbnails {
  display: flex;
  gap: 15px;
  padding-bottom: 10px;
}

.thumbnail {
  width: 100px;
  height: 100px;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 5px 15px rgba(26, 26, 46, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  flex-shrink: 0;
  border: 2px solid transparent;
}

.thumbnail.active {
  border-color: #1a65e3;
}

.thumbnail:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(26, 26, 46, 0.15);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
}

.product-details h2,
.product-inquiry h2 {
  font-size: 1.8rem;
  margin-bottom: 25px;
  color: var(--text-dark);
  font-weight: 300;
  position: relative;
  padding-bottom: 15px;
}

.product-details h2:after,
.product-inquiry h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, rgba(26, 26, 46, 0.5), #1a65e3, rgba(26, 26, 46, 0.1));
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.detail-item {
  background-color: #f9f9fd;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(26, 26, 46, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.detail-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(26, 26, 46, 0.08);
}

.detail-item h3 {
  font-size: 1.1rem;
  margin-bottom: 10px;
  color: #1a65e3;
  font-weight: 500;
}

.detail-item p {
  color: var(--text-medium);
  font-weight: 300;
}

.product-inquiry {
  background-color: #f9f9fd;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(26, 26, 46, 0.08);
}

.product-inquiry p {
  color: var(--text-medium);
  margin-bottom: 25px;
  font-weight: 300;
  line-height: 1.7;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .gallery-main {
    height: 450px;
  }
}

@media (max-width: 992px) {
  .product-info-container {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .gallery-main {
    height: 400px;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .page-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .page-banner {
    padding: 60px 0 40px;
  }

  .page-title {
    font-size: 2rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
  }

  .product-image {
    height: 180px;
  }

  .product-content {
    padding: 20px;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .gallery-main {
    height: 300px;
  }

  .product-title {
    font-size: 1.8rem;
  }
}

@media (max-width: 576px) {
  .products-grid {
    grid-template-columns: 1fr;
  }

  .product-image {
    height: 220px;
  }

  .page-title {
    font-size: 1.8rem;
  }

  .page-description {
    font-size: 1rem;
  }

  .product-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .filter-item {
    width: 100%;
    text-align: center;
  }

  .main-media {
    height: 280px;
  }
}

/* Collection Page Specific Styles */
.collection-page {
  padding: 60px 0;
}

.category-intro {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin: 50px 0;
}

.intro-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.intro-content h2 {
  margin-top: 0;
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 20px;
}

.intro-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.benefits-list {
  padding-left: 20px;
}

.benefits-list li {
  margin-bottom: 10px;
  color: #444;
}

.intro-image {
  border-radius: 8px;
  overflow: hidden;
  height: 350px;
}

.collection-filters {
  background-color: #f5f5f5;
  padding: 25px;
  border-radius: 8px;
  margin-bottom: 40px;
}

.collection-filters h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.2rem;
  color: #333;
}

.filter-options {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.filter-group {
  margin-bottom: 15px;
}

.filter-group h4 {
  margin: 0 0 10px 0;
  font-size: 1rem;
  color: #555;
}

.filter-option {
  display: inline-block;
  padding: 6px 12px;
  background-color: white;
  border-radius: 20px;
  margin-right: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  font-size: 0.9rem;
  color: #444;
  border: 1px solid #ddd;
  transition: all 0.2s ease;
}

.filter-option:hover {
  background-color: #0a59f7;
  color: white;
  border-color: #0a59f7;
}

.space-optimization, .business-benefits, .large-venue-advantages {
  background-color: #f9f9f9;
  padding: 40px;
  border-radius: 8px;
  margin: 50px 0;
}

.space-optimization h2, .business-benefits h2, .large-venue-advantages h2 {
  margin-top: 0;
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.strategy-content, .benefits-content, .advantages-content {
  max-width: 900px;
  margin: 0 auto;
}

.benefits-content, .advantages-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
}

.benefit-item, .advantage-item {
  background-color: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.benefit-item h3, .advantage-item h3 {
  margin-top: 0;
  font-size: 1.2rem;
  color: #0a59f7;
  margin-bottom: 15px;
}

.cta-section {
  text-align: center;
  background-color: #0a59f7;
  padding: 60px;
  border-radius: 8px;
  color: white;
}

.cta-section h2 {
  margin-top: 0;
  font-size: 2rem;
  margin-bottom: 20px;
}

.cta-section p {
  font-size: 1.1rem;
  margin-bottom: 30px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.btn-primary {
  display: inline-block;
  padding: 12px 30px;
  background-color: white;
  color: #0a59f7;
  border-radius: 30px;
  font-weight: bold;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .category-intro {
    grid-template-columns: 1fr;
  }

  .intro-image {
    order: -1;
    height: 300px;
  }

  .space-optimization, .business-benefits, .large-venue-advantages {
    padding: 30px 20px;
  }

  .cta-section {
    padding: 40px 20px;
  }
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }

  .product-image {
    height: 180px;
  }

  .benefits-content, .advantages-content {
    grid-template-columns: 1fr;
  }

  .page-title {
    font-size: 2rem;
  }
}

.product-features {
  margin-top: 30px;
}

.product-features h3 {
  font-size: 1.4rem;
  margin-bottom: 20px;
  color: var(--text-dark);
  font-weight: 300;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-top: 20px;
}

.features-list li {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-medium);
  font-weight: 300;
  line-height: 1.6;
}

.features-list li i {
  color: #1a65e3;
  font-size: 1.1rem;
}

/* 相关产品部分 */
.related-products {
  margin-top: 80px;
  padding-top: 60px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.related-products h2 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 40px;
  color: var(--text-dark);
  font-weight: 300;
  position: relative;
  padding-bottom: 15px;
}

.related-products h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, rgba(26, 26, 46, 0.2), #1a65e3, rgba(26, 26, 46, 0.2));
}

.related-products-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

@media (max-width: 1200px) {
  .related-products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .related-products-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .features-list {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .related-products-grid {
    grid-template-columns: 1fr;
  }

  .related-products h2 {
    font-size: 1.6rem;
  }
}

/* 产品列表页样式 */
.products-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  padding: 40px 0;
}

/* 更新淘宝风格产品详情页样式 */
.product-gallery-taobao {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.thumb-gallery {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 80px;
}



.thumbnail-item {
  width: 80px;
  height: 80px;
  border: 2px solid #e1e1e1;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.thumbnail-item.active {
  border-color: #1a65e3;
  box-shadow: 0 0 8px rgba(26, 101, 227, 0.4);
}

.thumbnail-item:hover {
  border-color: #1a65e3;
  transform: translateY(-2px);
}

.play-icon {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  font-size: 24px;
}

.main-media {
  flex: 1;
  height: 450px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  background-color: #fafafa;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.main-image, .main-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 产品详情图片样式 */
.product-details-section {
  padding: 40px 0;
  background-color: #fafafa;
}

.details-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 30px;
}

.tab {
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab:hover {
  color: #1a65e3;
}

.tab.active {
  color: #1a65e3;
  border-bottom-color: #1a65e3;
}

.details-content {
  padding: 20px 0;
}

.detail-images {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.detail-image {
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 响应式样式 */
@media (max-width: 992px) {
  .product-main-content {
    flex-direction: column;
  }

  .product-gallery-container {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .product-gallery-taobao {
    flex-direction: column-reverse;
  }

  .thumb-gallery {
    flex-direction: row;
    width: 100%;
    max-height: none;
    padding-bottom: 10px;
  }

  .main-media {
    height: 350px;
  }

  .tab {
    padding: 10px 15px;
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .main-media {
    height: 280px;
  }

  .product-title {
    font-size: 20px;
  }

  .price-value {
    font-size: 20px;
  }
}

/* 添加动画关键帧 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 产品列表页面样式 */

.product-list-page {
  position: relative;
}

/* 产品页面表单样式 - 简化版本 */
.products-page-quote-form {
  display: block !important;
}