 /* Quality Control Page Styles */
.quality-control-page {
  display: flex;
  flex-direction: column;
}
.page-banner {
  background-image: url('/images/holographic-tech-bg.jpg');
  background-size: cover;
  background-position: center;
  padding: 80px 0 60px;
  text-align: center;
  position: relative;
  color: white;
}

.page-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(10, 89, 247, 0.8), rgba(26, 26, 46, 0.9));
  z-index: 1;
}

.page-banner .container {
  position: relative;
  z-index: 2;
}

.quality-intro {
  padding: 60px 0;
  background-color: #f8f9fd;
  margin-top: 0;
}

.quality-process {
  padding: 60px 0;
  background-color: #fff;
}

.quality-process .section-title {
  text-align: center;
  margin-bottom: 40px;
  color: #0a59f7;
  font-size: 32px;
}

.process-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  margin-top: 40px;
}

.process-item {
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
  border: 1px solid #eee;
}

.process-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.process-icon {
  font-size: 36px;
  color: #0a59f7;
  margin-bottom: 20px;
}

.process-item h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
  font-weight: 600;
}

.process-item p {
  color: #666;
  line-height: 1.6;
}

/* Responsive styles */
@media (max-width: 992px) {
  .process-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .process-grid {
    grid-template-columns: 1fr;
  }

  .quality-process .section-title {
    font-size: 28px;
  }
}
