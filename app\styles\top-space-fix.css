/* 修复页面顶部空白问题 */

/* 全局修复 */
body, html {
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden !important;
}

/* 修复页面顶部空白 */
.product-list-page,
.product-detail-page,
.page-banner,
.PageHeader_pageHeader__wrapper,
[class*='PageHeader_'],
.about-us-page {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* 确保内容不会被导航栏遮挡 */
.page-banner .container,
.PageHeader_pageHeader__content,
[class*='PageHeader_'] .container {
  padding-top: 80px !important; /* 为导航栏留出空间 */
}

/* 修复产品详情页顶部空白 */
.product-detail-container {
  margin-top: 450px !important; /* 为更大的背景图片留出空间 */
}

/* 移除所有可能导致顶部空白的元素 */
.product-page-spacer,
.section-spacer,
.nav-content-separator {
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  display: none !important;
}

/* 确保页面内容从顶部开始 */
main, .main-content {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-banner .container,
  .PageHeader_pageHeader__content,
  [class*='PageHeader_'] .container {
    padding-top: 60px !important; /* 移动端减少内边距 */
  }

  .product-detail-container {
    margin-top: 300px !important; /* 移动端调整，为更大的背景图片留出空间 */
  }
}
