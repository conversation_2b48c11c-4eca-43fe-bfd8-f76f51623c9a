/* 透明导航栏样式 - 覆盖所有其他样式 */

/* 基础样式，确保定位正确 */
.header-main {
  position: fixed !important; /* 固定定位，确保始终在顶部 */
  top: 0 !important; /* 确保定位在页面顶部 */
  left: 0 !important; /* 确保水平铺满 */
  width: 100% !important; /* 确保宽度100% */
  z-index: 9999 !important; /* 使用非常高的z-index确保在其他元素之上 */
  overflow: visible !important; /* 修改overflow为visible */
  padding: 10px 0 !important; /* 减少内边距 */
  margin: 0 !important; /* 移除所有外边距 */
  background: transparent !important; /* 完全透明背景 */
  background-color: transparent !important; /* 确保背景颜色透明 */
  background-image: none !important; /* 移除所有背景图像 */
  border: none !important; /* 移除所有边框 */
  box-shadow: none !important; /* 移除所有阴影 */
}

/* 透明状态的导航栏 - 完全透明 */
.header-main.transparent {
  background: transparent !important; /* 完全透明 */
  background-image: none !important; /* 移除所有背景图像 */
  background-color: transparent !important; /* 确保背景颜色透明 */
  box-shadow: none !important; /* 移除所有阴影 */
  border: none !important; /* 移除所有边框 */
}

/* 固定状态的导航栏 - 半透明黑色 */
.header-main.sticky {
  background: rgba(0, 0, 0, 0.7) !important; /* 半透明黑色背景 */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important; /* 轻微阴影 */
}

/* 移除所有可能的背景渐变 */
.header-main::before,
.header-main::after,
.header-main.transparent::before,
.header-main.transparent::after,
.header-main.sticky::before,
.header-main.sticky::after {
  display: none !important; /* 隐藏所有伪元素 */
  content: none !important; /* 移除所有内容 */
  background: none !important; /* 移除所有背景 */
}

/* 确保导航文字在透明背景上可见 */
.nav-link {
  color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5) !important; /* 添加文字阴影增强可读性 */
}

/* Logo文字样式增强 */
.logo-text, .logo-tag {
  color: white !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5) !important; /* 添加文字阴影增强可读性 */
}

/* 移动端菜单按钮颜色 */
.mobile-menu-toggle span {
  background-color: white !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important; /* 添加阴影增强可见性 */
}

/* 适配移动设备 */
@media (max-width: 992px) {
  .header-main.sticky {
    background: rgba(0, 0, 0, 0.8) !important; /* 移动端稍微加深背景 */
  }

  .mobile-menu {
    background: rgba(0, 0, 0, 0.95) !important; /* 移动端菜单深色背景 */
  }
}
