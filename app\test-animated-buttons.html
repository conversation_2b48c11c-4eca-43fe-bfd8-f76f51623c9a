<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动画按钮测试</title>
    <link rel="stylesheet" href="styles/unified-cta.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            padding: 50px;
            margin: 0;
        }
        
        .test-section {
            background: var(--primary-color, #0a59f7);
            padding: 5rem 0;
            margin: 30px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        h1, h2 {
            color: white;
            margin-bottom: 30px;
        }
        
        p {
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 40px;
            font-size: 18px;
        }
        
        .button-group {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .demo-info {
            background: white;
            padding: 30px;
            margin: 30px 0;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .demo-info h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .demo-info p {
            color: #666;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="demo-info">
        <h1>动画按钮效果测试</h1>
        <p>这个页面用于测试新的动画按钮效果。将鼠标悬停在按钮上查看字母逐个出现的动画效果。</p>
    </div>

    <!-- CTA Section 测试 -->
    <section class="test-section cta-section">
        <div class="cta-particles"></div>
        <div class="container">
            <h2>CTA Section 按钮测试</h2>
            <p>测试 cta-section 类的按钮样式</p>
            <div class="button-group">
                <a href="#" class="btn-primary" data-text="CONTACT US NOW">
                    <i>C</i><i>O</i><i>N</i><i>T</i><i>A</i><i>C</i><i>T</i><i>&nbsp;</i><i>U</i><i>S</i><i>&nbsp;</i><i>N</i><i>O</i><i>W</i>
                </a>
                <a href="#" class="btn-glow" data-text="GET STARTED">
                    <i>G</i><i>E</i><i>T</i><i>&nbsp;</i><i>S</i><i>T</i><i>A</i><i>R</i><i>T</i><i>E</i><i>D</i>
                </a>
            </div>
        </div>
    </section>

    <!-- Contact CTA 测试 -->
    <section class="test-section contact-cta">
        <div class="cta-particles"></div>
        <div class="container">
            <h2>Contact CTA 按钮测试</h2>
            <p>测试 contact-cta 类的按钮样式</p>
            <div class="button-group">
                <a href="#" class="btn-primary" data-text="LEARN MORE">
                    <i>L</i><i>E</i><i>A</i><i>R</i><i>N</i><i>&nbsp;</i><i>M</i><i>O</i><i>R</i><i>E</i>
                </a>
            </div>
        </div>
    </section>

    <!-- Premium CTA 测试 -->
    <section class="test-section premium-cta">
        <div class="cta-particles"></div>
        <div class="container">
            <h2>Premium CTA 按钮测试</h2>
            <p>测试 premium-cta 类的按钮样式</p>
            <div class="button-group">
                <a href="#" class="btn-primary" data-text="SCHEDULE CONSULTATION">
                    <i>S</i><i>C</i><i>H</i><i>E</i><i>D</i><i>U</i><i>L</i><i>E</i><i>&nbsp;</i><i>C</i><i>O</i><i>N</i><i>S</i><i>U</i><i>L</i><i>T</i><i>A</i><i>T</i><i>I</i><i>O</i><i>N</i>
                </a>
                <a href="#" class="btn-glow" data-text="CUSTOM SOLUTION">
                    <i>C</i><i>U</i><i>S</i><i>T</i><i>O</i><i>M</i><i>&nbsp;</i><i>S</i><i>O</i><i>L</i><i>U</i><i>T</i><i>I</i><i>O</i><i>N</i>
                </a>
            </div>
        </div>
    </section>

    <div class="demo-info">
        <h3>使用说明</h3>
        <p><strong>悬停效果：</strong> 将鼠标悬停在按钮上，观察字母从下方逐个出现的动画效果</p>
        <p><strong>点击效果：</strong> 点击按钮时会有向下按压的效果</p>
        <p><strong>样式统一：</strong> 所有CTA区域的按钮都使用相同的动画效果</p>
        <p><strong>响应式：</strong> 按钮在不同屏幕尺寸下都能正常显示</p>
    </div>

    <script>
        // 添加粒子效果
        document.addEventListener('DOMContentLoaded', function() {
            const particles = document.querySelectorAll('.cta-particles');
            particles.forEach(particle => {
                for (let i = 0; i < 50; i++) {
                    const span = document.createElement('span');
                    span.style.left = Math.random() * 100 + '%';
                    span.style.animationDelay = Math.random() * 20 + 's';
                    span.style.animationDuration = (Math.random() * 10 + 10) + 's';
                    particle.appendChild(span);
                }
            });
        });
    </script>
</body>
</html>
