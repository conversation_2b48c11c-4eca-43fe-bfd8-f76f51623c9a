'use client';

import { useState, useEffect } from 'react';
// import mockProducts from '../data/mock-products.json';

export default function TestProductsPage() {
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 从public目录加载mock数据
    fetch('/mock-products.json')
      .then(res => res.json())
      .then(data => {
        setProducts(data);
        setLoading(false);
      })
      .catch(err => {
        console.error('加载产品数据失败:', err);
        setProducts([]);
        setLoading(false);
      });
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">测试产品页面</h1>
        <div className="text-center">加载中...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">测试产品页面</h1>
      
      <div className="mb-6">
        <p className="text-lg">
          <strong>总产品数：</strong>{products.length}
        </p>
        <p className="text-lg">
          <strong>特色产品：</strong>{products.filter(p => p.is_featured).length}
        </p>
        <p className="text-lg">
          <strong>总图片数：</strong>{products.reduce((total, p) => total + (p.images?.length || 0), 0)}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {products.slice(0, 12).map((product) => (
          <div key={product.id} className="border rounded-lg p-4 shadow-sm">
            <div className="mb-4">
              {product.images && product.images.length > 0 ? (
                <img
                  src={product.images[0]}
                  alt={product.name}
                  className="w-full h-48 object-cover rounded"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/images/products/placeholder.jpg';
                  }}
                />
              ) : (
                <div className="w-full h-48 bg-gray-200 rounded flex items-center justify-center">
                  无图片
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <h3 className="font-bold text-lg">
                {product.is_featured && <span className="text-yellow-500">⭐ </span>}
                {product.name}
              </h3>
              
              <p className="text-sm text-gray-600">
                分类: {product.category}
              </p>
              
              <p className="text-sm text-gray-600">
                Slug: {product.slug}
              </p>
              
              <p className="text-sm text-gray-600">
                图片数量: {product.images?.length || 0}
              </p>
              
              <p className="text-sm text-gray-600">
                价格: ¥{product.price}
              </p>
              
              <p className="text-sm text-gray-700 line-clamp-3">
                {product.description}
              </p>
              
              {product.features && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {product.features.slice(0, 3).map((feature: string, index: number) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-8 text-center">
        <p className="text-gray-600">
          显示前12个产品，总共{products.length}个产品
        </p>
      </div>
    </div>
  );
}
