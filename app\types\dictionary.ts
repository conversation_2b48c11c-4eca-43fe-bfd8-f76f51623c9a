// 字典类型定义
export interface Dictionary {
  common?: {
    home?: string;
    products?: string;
    loading?: string;
    back_to_products?: string;
    view_details?: string;
    no_products?: string;
    [key: string]: string | undefined;
  };
  products?: {
    title?: string;
    all_products_title?: string;
    all_products_subtitle?: string;
    description?: string;
    filter?: {
      all?: string;
      [key: string]: string | undefined;
    };
    [key: string]: any;
  };
  [key: string]: any;
} 