// Type declaration for formidable
declare module 'formidable' {
  import { IncomingMessage } from 'http';

  export interface Fields {
    [key: string]: string | string[];
  }

  export interface Files {
    [key: string]: {
      filepath: string;
      originalFilename: string;
      mimetype: string;
      size: number;
      [key: string]: unknown;
    };
  }

  export interface Options {
    keepExtensions?: boolean;
    uploadDir?: string;
    maxFileSize?: number;
    multiples?: boolean;
    [key: string]: unknown;
  }

  export interface IncomingForm {
    parse(req: IncomingMessage, callback: (err: Error, fields: Fields, files: Files) => void): void;
    parseAsync(req: IncomingMessage): Promise<{ fields: Fields; files: Files }>;
  }

  export function IncomingForm(options?: Options): IncomingForm;
}
