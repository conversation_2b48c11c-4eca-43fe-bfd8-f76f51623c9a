export interface Product {
  id: string | number;
  slug: string;
  name: string;
  images?: string[]; // Optional array of image URLs
  videos?: string[]; // Optional array of video URLs
  image_url?: string; // Optional single image URL (fallback)
  description?: string;
  shortDescription?: string; // 简短描述，用于产品详情页顶部
  detailedDescription?: string | null; // Optional
  price: number;
  originalPrice?: number | null; // Optional
  priceUnit?: string; // 价格单位，如"套"、"件"等

  stock?: number;
  sku?: string;
  tags?: string[];
  // Add any other fields that your product object might have
  size?: string;
  style?: string;
  features?: string[];
  title?: string; // often used as a display name
  specifications?: Record<string, string | number>; // 产品规格信息，键值对形式
}
