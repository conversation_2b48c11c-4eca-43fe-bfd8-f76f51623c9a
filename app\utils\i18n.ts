// i18n.ts - Simplified implementation without external dependencies
import zhDict from '../dictionaries/zh.json';
import enDict from '../dictionaries/en.json';

// Supported languages
export const i18n = {
  locales: ['en', 'zh'],
  defaultLocale: 'en',
};

// 静态字典 - 在动态导入失败时作为后备
const staticDictionaries: Record<string, any> = {
  en: enDict,
  zh: zhDict
};

// Create a simple i18n instance (without i18next)
export function createI18nInstance(locale: string = i18n.defaultLocale) {
  return {
    locale,
    t: (key: string) => {
      return key; // This is a placeholder; actual translation will be done in LanguageProvider
    },
  };
}

// Load dictionary
export async function getDictionary(locale: string) {
  // Ensure locale is one of the supported locales
  const validLocale = i18n.locales.includes(locale) ? locale : i18n.defaultLocale;

  try {
    // 先尝试使用静态导入的字典
    if (validLocale in staticDictionaries) {
      return staticDictionaries[validLocale];
    }
    throw new Error('Locale not found in static dictionaries');
  } catch (error) {
    // 静态导入失败，尝试动态导入
    try {
      // Use explicit imports for each language to avoid dynamic import issues
      if (validLocale === 'en') {
        const enDict = await import('../dictionaries/en.json');
        return enDict.default || enDict;
      } else if (validLocale === 'zh') {
        const zhDict = await import('../dictionaries/zh.json');
        return zhDict.default || zhDict;
      } else {
        // Fallback to English
        const enDict = await import('../dictionaries/en.json');
        return enDict.default || enDict;
      }
    } catch (importError) {
      console.error('Error loading dictionary:', importError);
      // Provide a minimal fallback dictionary to prevent crashes
      return {
        common: {
          home: 'Home',
          products: 'Products',
          loading: 'Loading...',
        },
        products: {
          title: 'Products',
          all_products_title: 'All Products',
          all_products_subtitle: 'Browse our full range of products',
        }
      };
    }
  }
}
