'use client';

import { useEffect, useState } from 'react';
import { useLanguage } from '../components/LanguageProvider';

export function useTranslation() {
  const { locale, t, isHydrated } = useLanguage();
  const [isMounted, setIsMounted] = useState(false);

  // 确保只在客户端渲染后才使用客户端翻译
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 创建一个安全的翻译函数，用于处理服务器/客户端同构渲染
  const safeT = (
    key: string,
    params: Record<string, string | number | { fallback?: string }> = {}
  ) => {
    // 在服务器端或未挂载时始终返回 fallback 值以确保一致的渲染
    if (!isMounted && typeof params.fallback === 'string') {
      return params.fallback;
    }
    return t(key, params);
  };

  return {
    locale,
    t: safeT,
    isClient: isMounted,
    isHydrated,
  };
}
