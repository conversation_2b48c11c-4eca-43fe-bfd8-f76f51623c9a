import type { Metadata } from 'next';
import '../../styles/globals.css';
import AdminProviders from './providers';

export const metadata: Metadata = {
  title: '管理员后台 - Guangzhou Junsheng',
  description: '管理员后台管理系统',
  robots: 'noindex, nofollow', // 防止搜索引擎索引管理员页面
};

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="bg-gray-100 min-h-screen">
      <AdminProviders>
        {children}
      </AdminProviders>
    </div>
  );
}
