import type { Metadata } from 'next';
import '../../../styles/globals.css';
import AdminProviders from '../providers';

export const metadata: Metadata = {
  title: '管理员登录 - Guangzhou Junsheng',
  description: '管理员登录页面',
  robots: 'noindex, nofollow',
};

export default function LoginLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
      <AdminProviders>
        {children}
      </AdminProviders>
    </div>
  );
}
