'use client';

import { useState, useEffect } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';

type FormData = {
  identifier: string;
  password: string;
};

export default function AdminLoginPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>();

  useEffect(() => {
    setMounted(true);
  }, []);

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    setError(null);

    try {
      console.log('尝试登录:', data);

      const result = await signIn('credentials', {
        redirect: false,
        callbackUrl: '/zh/admin',
        username: data.identifier,
        password: data.password,
      });

      console.log('登录结果:', result);

      if (result?.error) {
        setError('用户名或密码错误');
        console.error('登录失败:', result.error);
      } else if (!result?.ok) {
        setError('登录失败，请重试');
        console.error('登录失败，无明确错误信息');
      } else {
        console.log('登录成功，正在跳转...');
        router.push('/zh/admin');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      setError(`登录过程中发生错误: ${errorMessage}`);
      console.error('登录过程错误:', error);
    } finally {
      setLoading(false);
    }
  };

  // 防止水合错误
  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* 独立的管理员登录头部 */}
        <div className="text-center">
          <div className="mx-auto h-12 w-12 bg-indigo-600 rounded-lg flex items-center justify-center">
            <i className="fas fa-shield-alt text-white text-xl"></i>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            管理员后台
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            请使用管理员账号登录系统
          </p>
          <div className="mt-4 text-center">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
              <i className="fas fa-lock mr-1"></i>
              安全登录
            </span>
          </div>
        </div>

        <div className="bg-white py-8 px-6 shadow-xl rounded-lg">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            {error && (
              <div className="rounded-md bg-red-50 p-4 border border-red-200">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <i className="fas fa-exclamation-triangle text-red-400"></i>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      {error}
                    </h3>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label htmlFor="identifier" className="block text-sm font-medium text-gray-700 mb-1">
                  用户名或邮箱
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="fas fa-user text-gray-400"></i>
                  </div>
                  <input
                    id="identifier"
                    type="text"
                    autoComplete="username"
                    className={`block w-full pl-10 pr-3 py-3 border ${
                      errors.identifier ? 'border-red-300' : 'border-gray-300'
                    } rounded-md placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
                    placeholder="请输入用户名或邮箱"
                    {...register('identifier', {
                      required: '请输入用户名或邮箱',
                    })}
                  />
                </div>
                {errors.identifier && (
                  <p className="mt-1 text-sm text-red-600">{errors.identifier.message}</p>
                )}
              </div>
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  密码
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="fas fa-lock text-gray-400"></i>
                  </div>
                  <input
                    id="password"
                    type="password"
                    autoComplete="current-password"
                    className={`block w-full pl-10 pr-3 py-3 border ${
                      errors.password ? 'border-red-300' : 'border-gray-300'
                    } rounded-md placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
                    placeholder="请输入密码"
                    {...register('password', { required: '请输入密码' })}
                  />
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
                )}
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                {loading ? (
                  <>
                    <i className="fas fa-spinner fa-spin mr-2"></i>
                    登录中...
                  </>
                ) : (
                  <>
                    <i className="fas fa-sign-in-alt mr-2"></i>
                    登录管理后台
                  </>
                )}
              </button>
            </div>

            <div className="text-center">
              <div className="bg-gray-50 rounded-md p-3">
                <p className="text-sm text-gray-600 mb-1">
                  <i className="fas fa-info-circle mr-1"></i>
                  测试账号信息
                </p>
                <p className="text-xs text-gray-500">
                  用户名：<code className="bg-gray-200 px-1 rounded">admin</code>
                  密码：<code className="bg-gray-200 px-1 rounded">admin123</code>
                </p>
              </div>
            </div>
          </form>
        </div>

        <div className="text-center mt-6">
          <a
            href="/zh"
            className="inline-flex items-center text-indigo-600 hover:text-indigo-500 text-sm font-medium"
          >
            <i className="fas fa-arrow-left mr-2"></i>
            返回网站首页
          </a>
        </div>
      </div>
    </div>
  );
}
