'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { signOut } from 'next-auth/react';

interface Product {
  id: number;
  name: string;
  slug: string;
  category: string;
  is_published: boolean;
  created_at: string;
}

export default function ProductsManagement() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || status === 'loading') return;
    if (!session) {
      router.push('/zh/admin/login');
      return;
    }

    loadProducts();
  }, [mounted, session, status, router]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      // 这里可以调用API获取产品列表
      // const response = await fetch('/api/admin/products');
      // const data = await response.json();
      // setProducts(data.products);
      
      // 暂时使用模拟数据
      setProducts([
        {
          id: 1,
          name: 'AR蹦床',
          slug: 'ar-trampoline',
          category: '互动娱乐',
          is_published: true,
          created_at: '2024-01-15'
        },
        {
          id: 2,
          name: '全息沙盘',
          slug: 'holographic-sand-table',
          category: '展示设备',
          is_published: true,
          created_at: '2024-01-14'
        },
        {
          id: 3,
          name: '互动足球墙',
          slug: 'interactive-soccer-wall',
          category: '体感游戏',
          is_published: false,
          created_at: '2024-01-13'
        }
      ]);
    } catch (error) {
      console.error('加载产品列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    await signOut({ callbackUrl: '/zh/admin/login' });
  };

  // 防止水合错误
  if (!mounted) {
    return null;
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* 独立的管理员导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-xl font-bold text-gray-900">管理员后台</h1>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                <Link
                  href="/zh/admin"
                  className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  仪表板
                </Link>
                <Link
                  href="/zh/admin/products"
                  className="border-blue-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  产品管理
                </Link>
                <Link
                  href="/zh/admin/categories"
                  className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  分类管理
                </Link>
                <Link
                  href="/zh/admin/users"
                  className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  用户管理
                </Link>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                欢迎, {session.user?.name || session.user?.email}
              </span>
              <Link
                href="/zh"
                className="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-2 rounded-md text-sm font-medium"
              >
                返回网站
              </Link>
              <button
                onClick={handleLogout}
                className="bg-red-600 text-white hover:bg-red-700 px-3 py-2 rounded-md text-sm font-medium"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容区域 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">产品管理</h2>
              <p className="mt-1 text-sm text-gray-600">
                管理您的产品信息，包括添加、编辑和删除产品
              </p>
            </div>
            <Link
              href="/zh/admin/products/new"
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              <i className="fas fa-plus mr-2"></i>
              添加产品
            </Link>
          </div>

          {/* 产品列表 */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600">加载产品列表...</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          产品名称
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          分类
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          状态
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          创建时间
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          操作
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {products.map((product) => (
                        <tr key={product.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {product.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {product.slug}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {product.category}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              product.is_published 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {product.is_published ? '已发布' : '未发布'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {product.created_at}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <Link
                              href={`/zh/admin/products/${product.id}/edit`}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              <i className="fas fa-edit mr-1"></i>
                              编辑
                            </Link>
                            <Link
                              href={`/zh/products/${product.slug}`}
                              target="_blank"
                              className="text-green-600 hover:text-green-900"
                            >
                              <i className="fas fa-eye mr-1"></i>
                              预览
                            </Link>
                            <button
                              onClick={() => {
                                if (confirm('确定要删除这个产品吗？')) {
                                  // 删除产品逻辑
                                  console.log('删除产品:', product.id);
                                }
                              }}
                              className="text-red-600 hover:text-red-900"
                            >
                              <i className="fas fa-trash mr-1"></i>
                              删除
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
