'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { signOut } from 'next-auth/react';

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  created_at: string;
  last_login?: string;
}

export default function UsersManagement() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || status === 'loading') return;
    if (!session) {
      router.push('/zh/admin/login');
      return;
    }

    loadUsers();
  }, [mounted, session, status, router]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      // 暂时使用模拟数据
      setUsers([
        {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          role: 'super_admin',
          created_at: '2024-01-01',
          last_login: '2024-01-15'
        },
        {
          id: 2,
          username: 'editor',
          email: '<EMAIL>',
          role: 'editor',
          created_at: '2024-01-10',
          last_login: '2024-01-14'
        },
        {
          id: 3,
          username: 'user1',
          email: '<EMAIL>',
          role: 'user',
          created_at: '2024-01-12',
          last_login: '2024-01-13'
        }
      ]);
    } catch (error) {
      console.error('加载用户列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    await signOut({ callbackUrl: '/zh/admin/login' });
  };

  // 防止水合错误
  if (!mounted) {
    return null;
  }

  const getRoleDisplay = (role: string) => {
    const roleMap: { [key: string]: { text: string; color: string } } = {
      'super_admin': { text: '超级管理员', color: 'bg-purple-100 text-purple-800' },
      'admin': { text: '管理员', color: 'bg-blue-100 text-blue-800' },
      'editor': { text: '编辑员', color: 'bg-green-100 text-green-800' },
      'user': { text: '普通用户', color: 'bg-gray-100 text-gray-800' }
    };
    return roleMap[role] || { text: role, color: 'bg-gray-100 text-gray-800' };
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* 独立的管理员导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-xl font-bold text-gray-900">管理员后台</h1>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                <Link
                  href="/zh/admin"
                  className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  仪表板
                </Link>
                <Link
                  href="/zh/admin/products"
                  className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  产品管理
                </Link>
                <Link
                  href="/zh/admin/categories"
                  className="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  分类管理
                </Link>
                <Link
                  href="/zh/admin/users"
                  className="border-blue-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium"
                >
                  用户管理
                </Link>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                欢迎, {session.user?.name || session.user?.email}
              </span>
              <Link
                href="/zh"
                className="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-2 rounded-md text-sm font-medium"
              >
                返回网站
              </Link>
              <button
                onClick={handleLogout}
                className="bg-red-600 text-white hover:bg-red-700 px-3 py-2 rounded-md text-sm font-medium"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容区域 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">用户管理</h2>
              <p className="mt-1 text-sm text-gray-600">
                管理系统用户，包括管理员、编辑员和普通用户
              </p>
            </div>
            <Link
              href="/zh/admin/users/new"
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              <i className="fas fa-user-plus mr-2"></i>
              添加用户
            </Link>
          </div>

          {/* 用户列表 */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600">加载用户列表...</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          用户信息
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          角色
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          注册时间
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          最后登录
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          操作
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {users.map((user) => {
                        const roleInfo = getRoleDisplay(user.role);
                        return (
                          <tr key={user.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10">
                                  <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                    <i className="fas fa-user text-gray-600"></i>
                                  </div>
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900">
                                    {user.username}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    {user.email}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${roleInfo.color}`}>
                                {roleInfo.text}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {user.created_at}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {user.last_login || '从未登录'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                              <Link
                                href={`/zh/admin/users/${user.id}/edit`}
                                className="text-blue-600 hover:text-blue-900"
                              >
                                <i className="fas fa-edit mr-1"></i>
                                编辑
                              </Link>
                              {user.role !== 'super_admin' && (
                                <button
                                  onClick={() => {
                                    if (confirm('确定要删除这个用户吗？')) {
                                      console.log('删除用户:', user.id);
                                    }
                                  }}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <i className="fas fa-trash mr-1"></i>
                                  删除
                                </button>
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
