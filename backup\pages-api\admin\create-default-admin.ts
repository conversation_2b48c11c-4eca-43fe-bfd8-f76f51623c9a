import { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '../../../lib/mongodb';
import User from '../../../models/User';

// 默认管理员凭据
const DEFAULT_ADMIN = {
  username: 'admin',
  email: '<EMAIL>',
  password: 'Admin123!',
  role: 'admin',
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // 只允许在开发环境中使用
  if (process.env.NODE_ENV !== 'development') {
    return res.status(403).json({
      success: false,
      message: '此API端点仅在开发环境中可用',
    });
  }

  try {
    await dbConnect();
    console.log('数据库连接成功');

    // 检查管理员是否已存在
    const existingAdmin = await User.findOne({
      $or: [{ username: DEFAULT_ADMIN.username }, { email: DEFAULT_ADMIN.email }],
    });

    if (existingAdmin) {
      return res.status(200).json({
        success: true,
        message: '默认管理员已存在',
        admin: {
          id: existingAdmin._id.toString(),
          username: existingAdmin.username,
          email: existingAdmin.email,
          role: existingAdmin.role,
        },
      });
    }

    // 创建默认管理员
    const adminUser = new User(DEFAULT_ADMIN);
    await adminUser.save();

    return res.status(201).json({
      success: true,
      message: '默认管理员已创建',
      admin: {
        id: adminUser._id.toString(),
        username: adminUser.username,
        email: adminUser.email,
        role: adminUser.role,
      },
      loginCredentials: {
        username: DEFAULT_ADMIN.username,
        email: DEFAULT_ADMIN.email,
        password: DEFAULT_ADMIN.password,
      },
    });
  } catch (error) {
    console.error('创建默认管理员时出错:', error);
    return res.status(500).json({
      success: false,
      message: '创建默认管理员时出错',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
