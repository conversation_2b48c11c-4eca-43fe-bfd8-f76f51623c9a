import NextAuth, { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
// 使用 PostgreSQL 连接而不是 MongoDB
import dbConnect, { isConnected } from '../../../lib/postgresql';
import * as UserPg from '../../../models/UserPg';

// 扩展NextAuth用户类型
declare module 'next-auth' {
  interface User {
    id: string;
    email: string;
    name: string;
    role: string;
  }

  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      role: string;
    };
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    role: string;
  }
}

interface FallbackAdminUser {
  id: string;
  email: string;
  name: string;
  role: string;
  password?: string;
}

// 本地开发的紧急备用账号 - 仅在PostgreSQL连接失败时使用
const FALLBACK_ADMIN: FallbackAdminUser = {
  id: 'fallback-admin-id',
  email: '<EMAIL>',
  name: 'Admin',
  role: 'admin',
  password: 'admin123', // 开发环境的简单密码
};

// 简单的密码比较函数 - 仅用于备用账号
const comparePassword = (inputPassword: string, correctPassword: string) => {
  return inputPassword === correctPassword;
};

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      // 同时定义username和email认证字段
      credentials: {
        username: { label: 'Username', type: 'text' },
        email: { label: 'Email', type: 'text' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        // 增加详细的日志
        interface AuthAttemptLog {
          username: string;
          email: string;
          hasPassword: boolean;
        }
        const logPayload: AuthAttemptLog = {
          username: credentials?.username || 'not provided',
          email: credentials?.email || 'not provided',
          hasPassword: !!credentials?.password,
        };
        console.log('Authorization attempt with credentials:', JSON.stringify(logPayload));

        try {
          if (!credentials?.password) {
            console.error('Missing password in credentials');
            return null;
          }

          // 确保至少提供了用户名或邮箱之一
          if (!credentials?.username && !credentials?.email) {
            console.error('Neither username nor email provided');
            return null;
          }

          // 始终检查是否匹配备用管理员账号 - 优先级最高
          const email = credentials.email || '';
          const username = credentials.username || '';

          // 始终先检查是否为应急管理员账号
          if (
            (email === FALLBACK_ADMIN.email || username === FALLBACK_ADMIN.name) &&
            comparePassword(credentials.password, FALLBACK_ADMIN.password)
          ) {
            console.log('Authentication successful with emergency admin account');
            return FALLBACK_ADMIN;
          }

          // 尝试连接数据库
          let databaseAvailable = false;
          try {
            await dbConnect();
            databaseAvailable = isConnected();
            if (databaseAvailable) {
              console.log('Database connected successfully');
            } else {
              console.log('Database unavailable, only fallback authentication is possible');
            }
          } catch (dbError) {
            console.error('Database connection error:', dbError);
            console.log('Database unavailable, only fallback authentication is possible');
          }

          // 如果数据库不可用，到这里就应该直接返回认证失败
          if (!databaseAvailable) {
            console.error('Database unavailable and credentials do not match emergency admin');
            return null;
          }

          // 正常数据库验证流程
          // 构建查询条件 - 优先使用用户名，如果没有则使用邮箱
          const query = {
            username: credentials.username || '',
            email: credentials.email || '',
          };

          console.log('Searching user with query:', JSON.stringify(query));

          // 查找用户
          let user;
          try {
            user = await UserPg.findUserByCredentials(query);
          } catch (findError) {
            console.error('Error finding user:', findError);
            return null;
          }

          if (!user) {
            console.error('No user found with provided credentials');
            return null;
          }

          console.log(`User found: ${user.username}, comparing password...`);

          // 检查密码
          let isValid = false;
          try {
            isValid = await UserPg.comparePassword(credentials.password, user.password);
          } catch (pwError) {
            console.error('Error comparing password:', pwError);
            return null;
          }

          if (!isValid) {
            console.error('Password comparison failed');
            return null;
          }

          console.log('Authentication successful with database user');

          // 返回用户对象
          return {
            id: user.id?.toString() || '',
            email: user.email,
            name: user.username,
            role: user.role,
          };
        } catch (error) {
          console.error('Error in NextAuth authorize function:', error);
          // 返回null而不是抛出错误，这样NextAuth可以正常处理
          return null;
        }
      },
    }),
  ],
  // 在开发环境启用调试模式
  debug: process.env.NODE_ENV === 'development',
  logger: {
    error(code, ...message) {
      console.error('NEXTAUTH_ERROR:', code, ...message);
    },
    warn(code, ...message) {
      console.warn('NEXTAUTH_WARNING:', code, ...message);
    },
    debug(code, ...message) {
      console.log('NEXTAUTH_DEBUG:', code, ...message);
    },
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user && token) {
        session.user.id = token.id;
        session.user.role = token.role;
      }
      return session;
    },
  },
  pages: {
    signIn: '/admin/login',
    error: '/admin/error', // 指定错误页面
  },
  secret: process.env.NEXTAUTH_SECRET || 'your-default-secret-do-not-use-in-production',
  // 确保URL正确指向当前环境，而不是依赖环境变量
  useSecureCookies: false, // 开发环境使用不安全的cookie
};

export default NextAuth(authOptions);
