import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { query } from '../../../lib/db';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;
  const session = await getServerSession(req, res, authOptions);

  // 检查ID是否为数字
  if (!id || isNaN(Number(id))) {
    return res.status(400).json({ success: false, message: 'Invalid ID format' });
  }

  switch (req.method) {
    case 'GET':
      try {
        const result = await query('SELECT * FROM categories WHERE id = $1', [id]);

        if (result.rows.length === 0) {
          return res.status(404).json({ success: false, message: 'Category not found' });
        }

        const row = result.rows[0];

        // 转换响应格式以兼容前端
        const category = {
          _id: row.id.toString(),
          name: row.name,
          slug: row.slug,
          description: row.description,
          isActive: row.is_active,
          type: row.type || 'product_type',
          createdAt: row.created_at,
          parent_id: row.parent_id,
          order: row.order_num || 0,
          isFeatured: row.is_featured || false,
          featuredType: row.featured_type || 'product',
        };

        return res.status(200).json({ success: true, data: category });
      } catch (error: unknown) {
        console.error('Error fetching category:', error);
        const message = error instanceof Error ? error.message : 'Server error';
        return res.status(500).json({ success: false, message: message });
      }

    case 'PUT':
      try {
        // 检查用户是否有权限更新分类
        if (!session || (session.user.role !== 'admin' && session.user.role !== 'editor')) {
          return res.status(403).json({ success: false, message: 'Forbidden' });
        }

        const { name, slug, description, type, isActive, isFeatured, featuredType } = req.body;

        // 检查是否有同名但不同ID的slug
        if (slug) {
          const duplicateCheck = await query(
            'SELECT id FROM categories WHERE slug = $1 AND id != $2',
            [slug, id]
          );

          if (duplicateCheck.rows.length > 0) {
            return res.status(400).json({
              success: false,
              message: 'A category with this slug already exists',
            });
          }
        }

        // 如果要设置为精选分类，检查已有精选分类数量
        if (isFeatured) {
          const featuredCount = await query(
            'SELECT COUNT(*) as count FROM categories WHERE is_featured = true AND id != $1',
            [id]
          );

          if (parseInt(featuredCount.rows[0].count) >= 2) {
            return res.status(400).json({
              success: false,
              message: '最多只能设置2个精选分类，请先取消其他精选分类',
            });
          }
        }

        // 构建更新查询
        const updateFields = [];
        const values = [];
        let paramIndex = 1;

        if (name !== undefined) {
          updateFields.push(`name = $${paramIndex}`);
          values.push(name);
          paramIndex++;
        }

        if (slug !== undefined) {
          updateFields.push(`slug = $${paramIndex}`);
          values.push(slug);
          paramIndex++;
        }

        if (description !== undefined) {
          updateFields.push(`description = $${paramIndex}`);
          values.push(description);
          paramIndex++;
        }

        if (type !== undefined) {
          updateFields.push(`type = $${paramIndex}`);
          values.push(type);
          paramIndex++;
        }

        if (isActive !== undefined) {
          updateFields.push(`is_active = $${paramIndex}`);
          values.push(isActive);
          paramIndex++;
        }

        if (isFeatured !== undefined) {
          updateFields.push(`is_featured = $${paramIndex}`);
          values.push(isFeatured);
          paramIndex++;
        }

        if (featuredType !== undefined) {
          updateFields.push(`featured_type = $${paramIndex}`);
          values.push(featuredType);
          paramIndex++;
        }

        if (req.body.order !== undefined) {
          updateFields.push(`order_num = $${paramIndex}`);
          values.push(req.body.order);
          paramIndex++;
        }

        // 添加更新时间
        updateFields.push(`updated_at = CURRENT_TIMESTAMP`);

        // 如果没有要更新的字段，返回错误
        if (updateFields.length === 0) {
          return res.status(400).json({
            success: false,
            message: 'No fields to update',
          });
        }

        // 添加ID到值数组
        values.push(id);

        const updateResult = await query(
          `UPDATE categories SET ${updateFields.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
          values
        );

        if (updateResult.rows.length === 0) {
          return res.status(404).json({ success: false, message: 'Category not found' });
        }

        const updatedRow = updateResult.rows[0];

        // 转换响应格式以兼容前端
        const updatedCategory = {
          _id: updatedRow.id.toString(),
          name: updatedRow.name,
          slug: updatedRow.slug,
          description: updatedRow.description,
          isActive: updatedRow.is_active,
          type: updatedRow.type || 'product_type',
          createdAt: updatedRow.created_at,
          updatedAt: updatedRow.updated_at,
          order: updatedRow.order_num || 0,
          isFeatured: updatedRow.is_featured || false,
          featuredType: updatedRow.featured_type || 'product',
        };

        return res.status(200).json({ success: true, data: updatedCategory });
      } catch (error: unknown) {
        console.error('Error updating category:', error);
        const message = error instanceof Error ? error.message : 'Server error';
        return res.status(500).json({ success: false, message: message });
      }

    case 'DELETE':
      try {
        // 检查用户是否有权限删除分类
        if (!session || session.user.role !== 'admin') {
          return res.status(403).json({ success: false, message: 'Forbidden' });
        }

        // 查看是否有产品依赖此分类（可选）
        const categoryResult = await query('SELECT slug FROM categories WHERE id = $1', [id]);

        if (categoryResult.rows.length === 0) {
          return res.status(404).json({ success: false, message: 'Category not found' });
        }

        const categorySlug = categoryResult.rows[0].slug;

        const productsCheck = await query(
          'SELECT COUNT(*) as count FROM products WHERE category = $1',
          [categorySlug]
        );

        if (productsCheck.rows[0].count > 0) {
          // 获取使用此分类的产品信息
          const productsInfo = await query(
            'SELECT id, name, slug FROM products WHERE category = $1 LIMIT 5',
            [categorySlug]
          );

          const productsList = productsInfo.rows.map(p => p.name).join(', ');

          return res.status(400).json({
            success: false,
            message: `无法删除有关联产品的分类。请先修改以下产品的分类：${productsList}${productsCheck.rows[0].count > 5 ? '等' : ''}`,
            associatedProducts: productsInfo.rows,
          });
        }

        // 删除分类
        const deleteResult = await query('DELETE FROM categories WHERE id = $1 RETURNING id', [id]);

        if (deleteResult.rows.length === 0) {
          return res.status(404).json({ success: false, message: 'Category not found' });
        }

        return res.status(200).json({ success: true, message: 'Category deleted successfully' });
      } catch (error: unknown) {
        console.error('Error deleting category:', error);
        const message = error instanceof Error ? error.message : 'Server error';
        return res.status(500).json({ success: false, message: message });
      }

    default:
      res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
      return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }
}
