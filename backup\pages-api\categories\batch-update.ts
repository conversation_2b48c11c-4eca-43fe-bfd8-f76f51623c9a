import type { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '../../../lib/postgresql';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // 只允许PUT请求
  if (req.method !== 'PUT') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { items } = req.body;

    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request: items array is required',
      });
    }

    const pool = await dbConnect();
    const client = await pool.connect();

    // 使用事务处理批量更新
    try {
      await client.query('BEGIN');

      // 循环更新每个菜单项
      for (const item of items) {
        // 安全检查
        if (!item._id) {
          continue;
        }

        // 构建更新对象
        const updates: any = {};

        // 检查并更新顺序
        if (typeof item.order === 'number') {
          updates.order = item.order;
        }

        // 检查并更新翻译信息
        if (item.translations) {
          updates.translations = item.translations;
        }

        // 如果有需要更新的字段，执行更新
        if (Object.keys(updates).length > 0) {
          const setClause = Object.keys(updates)
            .map((key, index) => `${key} = $${index + 2}`)
            .join(', ');

          const values = [item._id, ...Object.values(updates)];

          await client.query(
            `UPDATE categories 
             SET ${setClause} 
             WHERE _id = $1`,
            values
          );
        }
      }

      await client.query('COMMIT');

      return res.status(200).json({
        success: true,
        message: 'Menu items updated successfully',
      });
    } catch (error) {
      // 错误时回滚事务
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error updating menu items:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update menu items',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
