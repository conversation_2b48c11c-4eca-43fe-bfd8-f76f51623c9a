import { NextApiRequest, NextApiResponse } from 'next';
import { query } from '../../../lib/db';

// 模拟的特色分类，当数据库连接失败时使用
const mockFeaturedCategories = [
  {
    _id: '1',
    name: '室内游乐场',
    slug: 'indoor-playground',
    description: '适合儿童的室内游乐设施',
    isActive: true,
    type: 'product_type',
    order: 1,
    featuredOrder: 1,
    featuredType: 'product',
    translations: {
      zh: { name: '室内游乐场' },
      en: { name: 'Indoor Playground' },
    },
  },
  {
    _id: '2',
    name: '蹦床公园',
    slug: 'trampoline-park',
    description: '专业蹦床游乐设施',
    isActive: true,
    type: 'product_type',
    order: 2,
    featuredOrder: 2,
    featuredType: 'product',
    translations: {
      zh: { name: '蹦床公园' },
      en: { name: 'Trampoline Park' },
    },
  },
  {
    _id: '3',
    name: '100-500平方米',
    slug: '100-500-sqm',
    description: '小型游乐场解决方案',
    isActive: true,
    type: 'area_size',
    order: 3,
    featuredOrder: 1,
    featuredType: 'solution',
    translations: {
      zh: { name: '100-500平方米' },
      en: { name: '100-500 sqm' },
    },
  },
];

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }

  try {
    // 获取查询参数，允许按导航类型进行过滤
    const { type } = req.query; // type can be string | string[] | undefined

    // 构建查询条件
    let sql = 'SELECT * FROM categories WHERE is_featured = true';
    const params: string[] = []; // Changed from any[]

    // 如果指定了导航类型，添加过滤条件
    if (type) {
      sql += ' AND featured_type = $1';
      // Ensure type is a string before pushing
      const typeString = Array.isArray(type) ? type[0] : type;
      if (typeString !== undefined) {
        // Ensure typeString is not undefined after potential array access
        params.push(typeString);
      }
    }

    // 添加排序
    sql += ' ORDER BY featured_order ASC, order_num ASC';

    // 尝试执行查询
    const featuredResult = await query(sql, params);

    // 转换响应格式以兼容前端
    const featuredCategories = featuredResult.rows.map(row => ({
      _id: row.id.toString(),
      name: row.name,
      slug: row.slug,
      description: row.description || '',
      isActive: row.is_active,
      type: row.type || 'product_type',
      order: row.order_num || 0,
      featuredOrder: row.featured_order || 0,
      featuredType: row.featured_type || 'product',
      translations: row.translations || {
        zh: { name: row.name },
        en: { name: row.name_en || row.name },
      },
    }));

    return res.status(200).json({
      success: true,
      data: featuredCategories,
      count: featuredCategories.length,
    });
  } catch (error: unknown) {
    console.error('Error fetching featured categories:', error);

    // 数据库连接失败时使用模拟数据
    const { type } = req.query;
    let filteredMock = mockFeaturedCategories;

    if (type) {
      const typeString = Array.isArray(type) ? type[0] : type;
      filteredMock = mockFeaturedCategories.filter(cat => cat.featuredType === typeString);
    }

    console.log('Using mock data due to database connection failure');

    return res.status(200).json({
      success: true,
      data: filteredMock,
      count: filteredMock.length,
      isMock: true,
      message:
        'Using mock data due to database connection issues. Please check your database connection.',
    });
  }
}
