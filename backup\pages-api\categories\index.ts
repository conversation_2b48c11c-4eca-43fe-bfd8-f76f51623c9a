import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { query } from '../../../lib/db';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);

  // 处理不同的HTTP方法
  switch (req.method) {
    case 'GET':
      try {
        // 构建查询
        const { type, isActive, isFeatured, featuredType } = req.query;
        const whereConditions = [];
        const queryParams = [];
        let paramIndex = 1;

        let sql = 'SELECT * FROM categories';

        if (type) {
          whereConditions.push(`type = $${paramIndex}`);
          queryParams.push(type);
          paramIndex++;
        }

        if (isActive !== undefined) {
          whereConditions.push(`is_active = $${paramIndex}`);
          queryParams.push(isActive === 'true');
          paramIndex++;
        }

        if (isFeatured !== undefined) {
          whereConditions.push(`is_featured = $${paramIndex}`);
          queryParams.push(isFeatured === 'true');
          paramIndex++;
        }

        if (featuredType) {
          whereConditions.push(`featured_type = $${paramIndex}`);
          queryParams.push(featuredType);
          paramIndex++;
        }

        if (whereConditions.length > 0) {
          sql += ' WHERE ' + whereConditions.join(' AND ');
        }

        // 添加排序: 先按精选排序，再按普通排序，再按ID
        sql += ' ORDER BY featured_order, order_num, id';

        const result = await query(sql, queryParams);

        // 转换响应格式
        const categories = result.rows.map(row => ({
          _id: row.id.toString(),
          name: row.name,
          slug: row.slug,
          description: row.description,
          isActive: row.is_active,
          type: row.type || 'product_type',
          createdAt: row.created_at,
          updatedAt: row.updated_at,
          order: row.order_num || 0,
          isFeatured: row.is_featured || false,
          featuredOrder: row.featured_order || 0,
          featuredType: row.featured_type || 'product',
          translations: row.translations || {
            zh: { name: row.name },
            en: { name: row.name_en || row.name },
          },
        }));

        return res.status(200).json({
          success: true,
          data: categories,
        });
      } catch (error: unknown) {
        console.error('Error fetching categories:', error);
        const message =
          error instanceof Error ? error.message : 'Error fetching categories. Showing empty data.';
        return res.status(500).json({
          success: false,
          message: message,
          data: [],
        });
      }

    case 'POST':
      try {
        // 检查用户是否有权限创建分类
        if (!session || (session.user.role !== 'admin' && session.user.role !== 'editor')) {
          return res.status(403).json({ success: false, message: 'Forbidden' });
        }

        const {
          name,
          slug,
          description,
          type = 'product_type',
          isActive = true,
          order = 0,
          isFeatured = false,
          featuredOrder = 0,
          featuredType = 'product',
          translations = {
            zh: { name },
            en: { name },
          },
        } = req.body;

        // 检查slug是否已存在
        const existingCheck = await query('SELECT id FROM categories WHERE slug = $1', [slug]);

        if (existingCheck.rows.length > 0) {
          return res.status(400).json({
            success: false,
            message: 'A category with this slug already exists',
          });
        }

        // 创建新分类
        const result = await query(
          'INSERT INTO categories (name, slug, description, is_active, type, order_num, is_featured, featured_order, featured_type, translations) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10) RETURNING *',
          [
            name,
            slug,
            description,
            isActive,
            type,
            order,
            isFeatured,
            featuredOrder,
            featuredType,
            JSON.stringify(translations),
          ]
        );

        const newCategory = result.rows[0];

        // 转换响应格式
        const formattedCategory = {
          _id: newCategory.id.toString(),
          name: newCategory.name,
          slug: newCategory.slug,
          description: newCategory.description,
          isActive: newCategory.is_active,
          type: newCategory.type,
          createdAt: newCategory.created_at,
          updatedAt: newCategory.updated_at,
          order: newCategory.order_num || 0,
          isFeatured: newCategory.is_featured || false,
          featuredOrder: newCategory.featured_order || 0,
          featuredType: newCategory.featured_type || 'product',
          translations: newCategory.translations || {
            zh: { name: newCategory.name },
            en: { name: newCategory.name },
          },
        };

        return res.status(201).json({
          success: true,
          data: formattedCategory,
        });
      } catch (error: unknown) {
        console.error('Error creating category:', error);
        const message = error instanceof Error ? error.message : 'Server error';
        return res.status(500).json({ success: false, message: message });
      }

    default:
      res.setHeader('Allow', ['GET', 'POST']);
      return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }
}
