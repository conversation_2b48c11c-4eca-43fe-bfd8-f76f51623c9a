import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import Content from '../../../models/Content';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);

  // Check if user is authenticated
  if (!session) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Connect to database
  await dbConnect();

  // Get content ID from query
  const { id } = req.query;

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      try {
        const content = await Content.findById(id);

        if (!content) {
          return res.status(404).json({ success: false, message: 'Content not found' });
        }

        return res.status(200).json({ success: true, data: content });
      } catch (error) {
        console.error('Error fetching content:', error);
        return res.status(500).json({ success: false, message: 'Server error' });
      }

    case 'PUT':
      try {
        // Check if user has permission to update content
        if (session.user.role !== 'admin' && session.user.role !== 'editor') {
          return res.status(403).json({ success: false, message: 'Forbidden' });
        }

        const content = await Content.findByIdAndUpdate(id, req.body, {
          new: true,
          runValidators: true,
        });

        if (!content) {
          return res.status(404).json({ success: false, message: 'Content not found' });
        }

        return res.status(200).json({ success: true, data: content });
      } catch (error) {
        console.error('Error updating content:', error);
        return res.status(500).json({ success: false, message: 'Server error' });
      }

    case 'DELETE':
      try {
        // Check if user has permission to delete content
        if (session.user.role !== 'admin') {
          return res.status(403).json({ success: false, message: 'Forbidden' });
        }

        const content = await Content.findByIdAndDelete(id);

        if (!content) {
          return res.status(404).json({ success: false, message: 'Content not found' });
        }

        return res.status(200).json({ success: true, data: {} });
      } catch (error) {
        console.error('Error deleting content:', error);
        return res.status(500).json({ success: false, message: 'Server error' });
      }

    default:
      res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
      return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }
}
