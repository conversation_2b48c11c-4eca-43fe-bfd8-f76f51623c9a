import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/mongodb';
import Content from '../../../models/Content';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);

  // Check if user is authenticated for write operations
  if (req.method !== 'GET' && !session) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Connect to database with error handling
  let isConnected = false;
  try {
    const conn = await dbConnect;
    isConnected = !!conn;
  } catch (error: unknown) {
    console.error('Database connection error:', error);
    // Return empty data for GET requests to prevent frontend errors
    if (req.method === 'GET') {
      return res.status(200).json({
        success: true,
        message: 'Database connection error. Showing empty data.',
        data: [], // Return empty array to prevent client-side errors
      });
    }
    return res.status(500).json({
      success: false,
      message: 'Database connection error. Please try again later.',
    });
  }

  if (!isConnected) {
    console.warn('Failed to connect to database, returning empty data');
    // Return empty data for GET requests to prevent frontend errors
    if (req.method === 'GET') {
      return res.status(200).json({
        success: true,
        message: 'Database not available. Showing empty data.',
        data: [], // Return empty array to prevent client-side errors
      });
    }
    return res.status(503).json({
      success: false,
      message: 'Database service unavailable. Please try again later.',
    });
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      try {
        const { page = '', section = '' } = req.query;

        // Build query
        const query: { page?: string | string[]; section?: string | string[] } = {};

        // Filter by page
        if (page) {
          query.page = page;
        }

        // Filter by section
        if (section) {
          query.section = section;
        }

        // Get content
        const content = await Content.find(query).sort({ section: 1, key: 1 });

        return res.status(200).json({ success: true, data: content });
      } catch (error) {
        console.error('Error fetching content:', error);
        return res.status(200).json({
          success: true,
          message: 'Error fetching content. Showing empty data.',
          data: [], // Return empty array to prevent client-side errors
        });
      }

    case 'POST':
      try {
        // Check if user has permission to create content
        if (!session || (session.user.role !== 'admin' && session.user.role !== 'editor')) {
          return res.status(403).json({ success: false, message: 'Forbidden' });
        }

        // Create new content
        const content = await Content.create(req.body);

        return res.status(201).json({ success: true, data: content });
      } catch (error: unknown) {
        console.error('Error creating content:', error);

        // Handle duplicate key error
        if (
          typeof error === 'object' &&
          error !== null &&
          'code' in error &&
          (error as { code: unknown }).code === 11000
        ) {
          return res.status(400).json({
            success: false,
            message: 'Content with this key already exists',
          });
        }

        const message = error instanceof Error ? error.message : 'Server error';
        return res.status(500).json({ success: false, message: message });
      }

    default:
      res.setHeader('Allow', ['GET', 'POST']);
      return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }
}
