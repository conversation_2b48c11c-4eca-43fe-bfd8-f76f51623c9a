import { NextApiRequest, NextApiResponse } from 'next';
// import { getServerSession } from 'next-auth/next'; // Unused import
// import { authOptions } from './auth/[...nextauth]'; // Unused import
import clientPromise from '../../lib/mongodb'; // Renamed import
import Product from '../../models/Product';

// 安全导入令牌，生产环境应从环境变量读取
// const IMPORT_TOKEN = 'products-import-2024'; // Unused variable

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // 只处理POST请求
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }

  try {
    // 连接数据库
    await clientPromise; // Use the renamed import

    // 获取请求体中的产品数据
    const { products } = req.body;

    // 验证产品数据
    if (!Array.isArray(products) || products.length === 0) {
      return res
        .status(400)
        .json({ success: false, message: 'No products data provided or invalid format' });
    }

    // 导入结果
    const results = {
      total: products.length,
      created: 0,
      failed: 0,
      errors: [] as string[],
    };

    // 批量导入产品
    for (const productData of products) {
      try {
        // 检查产品是否已存在（通过slug）
        const existingProduct = await Product.findOne({ slug: productData.slug });

        if (existingProduct) {
          // 如果已存在，更新产品
          await Product.findByIdAndUpdate(existingProduct._id, productData);
          results.created++;
        } else {
          // 创建新产品
          await Product.create(productData);
          results.created++;
        }
      } catch (error: unknown) {
        results.failed++;
        const message = error instanceof Error ? error.message : 'Unknown error';
        results.errors.push(`Error with product ${productData.slug || 'unknown'}: ${message}`);
      }
    }

    // 返回导入结果
    return res.status(200).json({
      success: true,
      message: `Imported ${results.created} products (${results.failed} failed)`,
      results,
    });
  } catch (error: unknown) {
    console.error('Error importing products:', error);
    const message = error instanceof Error ? error.message : 'Unknown server error';
    return res.status(500).json({ success: false, message: `Server error: ${message}` });
  }
}
