import { NextApiRequest, NextApiResponse } from 'next';
// import { getServerSession } from 'next-auth/next'; // Potentially unused if session is removed
// import { authOptions } from '../auth/[...nextauth]'; // Potentially unused if session is removed
import { query } from '../../../lib/db';
import { memoryCache } from '../../../lib/cache';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // const session = await getServerSession(req, res, authOptions); // session is unused

  // 处理不同的HTTP方法
  switch (req.method) {
    case 'GET':
      try {
        // 构建查询参数
        const { page = '1', limit = '10', search = '', category = '', published = '' } = req.query;

        const pageNumber = parseInt(page as string, 10);
        const limitNumber = parseInt(limit as string, 10);
        const offset = (pageNumber - 1) * limitNumber;

        // 生成缓存键
        const cacheKey = `products-${page}-${limit}-${search}-${category}-${published}`;

        // 尝试从缓存获取数据
        const cachedData = memoryCache.get(cacheKey);
        if (cachedData) {
          console.log('[API] Serving cached products list');
          return res.status(200).json(cachedData);
        }

        // 构建查询条件
        const whereConditions = [];
        const queryParams: (string | number | boolean | null)[] = [];
        let paramIndex = 1;

        // 搜索条件
        if (search) {
          whereConditions.push(`(title ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`);
          queryParams.push(`%${search}%`);
          paramIndex++;
        }

        // 分类条件
        if (category) {
          whereConditions.push(`(
            category = $${paramIndex} OR 
            categories LIKE $${paramIndex + 1} OR
            categories::jsonb @> $${paramIndex + 2}::jsonb
          )`);
          queryParams.push(category as string, `%${category}%`, JSON.stringify([category]));
          paramIndex += 3;
        }

        // 发布状态条件
        if (published !== '') {
          whereConditions.push(`is_published = $${paramIndex}`);
          queryParams.push(published === 'true');
          paramIndex++;
        }

        // 构建SQL查询
        let sql = 'SELECT * FROM products';

        if (whereConditions.length > 0) {
          sql += ' WHERE ' + whereConditions.join(' AND ');
        }

        // 添加排序和分页
        sql += ` ORDER BY is_featured DESC, id DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
        queryParams.push(limitNumber, offset);

        console.log('[API] Fetching products from database');
        // 执行查询
        const result = await query(sql, queryParams);

        // 计算总数
        let countSql = 'SELECT COUNT(*) as total FROM products';

        if (whereConditions.length > 0) {
          countSql += ' WHERE ' + whereConditions.join(' AND ');
        }

        // 这里修复类型问题
        const countParams = queryParams.slice(0, paramIndex - 1) as (
          | string
          | number
          | boolean
          | null
        )[];
        const countResult = await query(countSql, countParams);
        const total = parseInt(countResult.rows[0].total);

        // 转换为前端格式
        const products = result.rows.map(row => {
          let productCategories = [];
          try {
            if (row.categories) {
              if (typeof row.categories === 'string') {
                try {
                  productCategories = JSON.parse(row.categories);
                } catch {
                  productCategories = [row.categories];
                }
              } else if (Array.isArray(row.categories)) {
                productCategories = row.categories;
              }
            } else if (row.category) {
              productCategories = [row.category];
            }
          } catch (_e: unknown) {
            console.error('Error processing categories:', _e);
          }

          return {
            _id: row.id.toString(),
            title: row.name,
            slug: row.slug,
            description: row.description,
            type: row.type || '',
            size: row.size || '',
            style: row.style || '',
            images: row.image_url ? [row.image_url] : [],
            features: row.features || [],
            categories: productCategories,
            isPublished: row.in_stock || false,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
          };
        });

        const responseData = {
          success: true,
          data: products,
          pagination: {
            total,
            page: pageNumber,
            limit: limitNumber,
            pages: Math.ceil(total / limitNumber),
          },
        };

        // 将结果存入缓存，默认有效期10分钟
        memoryCache.set(cacheKey, responseData);

        return res.status(200).json(responseData);
      } catch (error: unknown) {
        console.error('Error fetching products:', error);
        const message = error instanceof Error ? error.message : 'Error fetching products';
        return res.status(500).json({
          success: false,
          message: message,
        });
      }

    case 'POST':
      // 创建产品后清除相关缓存
      try {
        // 获取请求体
        const {
          title,
          slug,
          description,
          size,
          style,
          type,
          features = [],
          categories = [],
          images = [],
          isPublished = false,
          translations = {},
        } = req.body;

        // 验证必填字段
        if (!title || !slug || !description) {
          return res.status(400).json({
            success: false,
            message: 'Title, slug and description are required',
          });
        }

        // 检查slug是否已存在
        const existingProduct = await query('SELECT id FROM products WHERE slug = $1', [slug]);

        if (existingProduct.rows.length > 0) {
          return res.status(400).json({
            success: false,
            message: `Product with slug "${slug}" already exists`,
          });
        }

        // 插入新产品
        const result = await query(
          `INSERT INTO products 
           (title, slug, description, size, style, type, features, categories, images, is_published, translations) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
           RETURNING id`,
          [
            title,
            slug,
            description,
            size || null,
            style || null,
            type || null,
            JSON.stringify(features),
            JSON.stringify(categories),
            JSON.stringify(images),
            isPublished,
            translations ? JSON.stringify(translations) : null,
          ]
        );

        // 清除所有产品列表缓存
        const cacheKeys = memoryCache.keys().filter(key => key.startsWith('products-'));
        cacheKeys.forEach(key => memoryCache.delete(key));

        return res.status(201).json({
          success: true,
          message: 'Product created successfully',
          productId: result.rows[0].id,
        });
      } catch (error: unknown) {
        console.error('Error creating product:', error);
        const message = error instanceof Error ? error.message : 'Error creating product';
        return res.status(500).json({
          success: false,
          message: message,
        });
      }

    default:
      res.setHeader('Allow', ['GET', 'POST']);
      return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }
}
