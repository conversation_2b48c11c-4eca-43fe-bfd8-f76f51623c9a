import { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '../../lib/mongodb';
import User from '../../models/User';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    console.log('正在连接数据库...');
    await dbConnect();
    console.log('数据库连接成功');

    // 获取所有用户（不返回密码）
    const users = await User.find({}).select('-password');

    return res.status(200).json({
      success: true,
      message: '数据库连接正常',
      count: users.length,
      users: users.map(user => ({
        id: user._id.toString(),
        username: user.username,
        email: user.email,
        role: user.role,
      })),
    });
  } catch (error) {
    console.error('数据库连接或查询错误:', error);
    return res.status(500).json({
      success: false,
      message: '数据库连接或查询错误',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
