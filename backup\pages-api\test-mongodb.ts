import { NextApiRequest, NextApiResponse } from 'next';
import mongoose from 'mongoose';

interface ServerInfo {
  version: string;
  uptime: number;
  [key: string]: unknown; // Changed any to unknown
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // 直接使用环境变量连接MongoDB
    const MONGODB_URI =
      process.env.MONGODB_URI || 'mongodb://localhost:27017/crossborder-ecommerce';

    console.log('MongoDB URI:', MONGODB_URI);
    console.log('正在连接MongoDB...');

    await mongoose.connect(MONGODB_URI);
    console.log('MongoDB连接成功');

    // 获取MongoDB服务器状态
    let serverInfo: ServerInfo = { version: 'Unknown', uptime: 0 };

    if (mongoose.connection.db) {
      try {
        const admin = mongoose.connection.db.admin();
        const result = await admin.serverInfo();
        serverInfo = {
          version: result.version || 'Unknown',
          uptime: result.uptime || 0,
        };
      } catch (adminError) {
        console.error('无法获取MongoDB服务器信息:', adminError);
      }
    }

    await mongoose.disconnect();
    console.log('MongoDB连接已关闭');

    return res.status(200).json({
      success: true,
      message: 'MongoDB连接测试成功',
      serverInfo: {
        version: serverInfo.version,
        uptime: serverInfo.uptime,
        connection: MONGODB_URI,
      },
    });
  } catch (error) {
    console.error('MongoDB连接错误:', error);

    return res.status(500).json({
      success: false,
      message: 'MongoDB连接失败',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
