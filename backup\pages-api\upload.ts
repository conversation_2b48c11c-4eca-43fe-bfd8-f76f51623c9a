import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';
import { IncomingForm, Fields, Files } from 'formidable';
import { S3Client, PutObjectCommand, ObjectCannedACL } from '@aws-sdk/client-s3';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Disable the default body parser to handle form data
export const config = {
  api: {
    bodyParser: false,
  },
};

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
});

// Helper function to parse form data with formidable
const parseFormAsync = (
  req: NextApiRequest,
  form: IncomingForm
): Promise<{ fields: Fields; files: Files }> => {
  return new Promise((resolve, reject) => {
    form.parse(req, (err, fields, files) => {
      // err is Error | null from formidable
      if (err) {
        console.error('Error parsing form in helper:', err);
        // Create a new error to wrap the original, preserving its type
        // Also, make it identifiable if needed later
        const wrappedError = new Error(`Formidable parse error: ${err.message}`);
        (wrappedError as { formidableError?: boolean }).formidableError = true;
        (wrappedError as { originalFormidableError?: Error }).originalFormidableError = err;
        return reject(wrappedError);
      }
      resolve({ fields, files });
    });
  });
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }

  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // @ts-expect-error IncomingForm constructor type is not correctly inferred by TypeScript here
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const form = new IncomingForm({
    keepExtensions: true,
    maxFileSize: 10 * 1024 * 1024, // 10MB limit
  });

  try {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { fields: _fields, files } = await parseFormAsync(req, form); // _fields as it's unused

    const uploadedFileArray = files.file; // 'file' is the expected field name from client

    if (
      !uploadedFileArray ||
      (Array.isArray(uploadedFileArray) && uploadedFileArray.length === 0)
    ) {
      return res.status(400).json({ success: false, message: 'No file uploaded' });
    }

    // Rely on TypeScript inference for the 'file' variable type
    const file = Array.isArray(uploadedFileArray) ? uploadedFileArray[0] : uploadedFileArray;

    if (!file || !file.filepath) {
      // Added check for filepath as it's crucial
      return res
        .status(400)
        .json({ success: false, message: 'Uploaded file data is invalid or filepath is missing.' });
    }

    // All file processing logic (local or S3) starts here
    try {
      const uniqueFilename = `${uuidv4()}${path.extname(file.originalFilename || '')}`;
      const fileMimeType = file.mimetype || 'application/octet-stream';

      if (process.env.STORAGE_TYPE === 'local') {
        const uploadDir = path.join(process.cwd(), 'public', 'uploads');
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }
        const localFilePath = path.join(uploadDir, uniqueFilename);

        // formidable v2/v3 stores files temporarily, need to move/copy
        fs.renameSync(file.filepath, localFilePath); // Or fs.copyFileSync + fs.unlinkSync

        const fileUrl = `/uploads/${uniqueFilename}`;
        return res.status(200).json({
          success: true,
          url: fileUrl,
          filename: uniqueFilename,
        });
      } else {
        // S3 Storage
        const fileContent = fs.readFileSync(file.filepath);

        const params = {
          Bucket: process.env.AWS_S3_BUCKET || 'your-bucket-name', // Ensure this has a default or is validated
          Key: `uploads/${uniqueFilename}`,
          Body: fileContent,
          ContentType: fileMimeType,
          ACL: ObjectCannedACL.public_read,
        };

        await s3Client.send(new PutObjectCommand(params));
        fs.unlinkSync(file.filepath); // Clean up temp file

        const fileUrl = `https://${process.env.AWS_S3_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/uploads/${uniqueFilename}`;
        return res.status(200).json({
          success: true,
          url: fileUrl,
          filename: uniqueFilename,
        });
      }
    } catch (uploadError) {
      console.error('Error uploading file (local/S3):', uploadError);
      // Clean up temp file if it still exists and wasn't the source of the error
      if (file.filepath && fs.existsSync(file.filepath)) {
        try {
          fs.unlinkSync(file.filepath);
        } catch (e) {
          console.error('Failed to cleanup temp file', e);
        }
      }
      return res.status(500).json({ success: false, message: 'Error uploading file' });
    }
  } catch (error: unknown) {
    console.error('Error in API handler:', error);

    // Check if it's our wrapped formidableError
    if (error instanceof Error && (error as { formidableError?: boolean }).formidableError) {
      // Further check if the original error was about file size
      const originalError = (error as { originalFormidableError?: Error }).originalFormidableError;
      if (
        originalError &&
        'httpCode' in originalError &&
        (originalError as { httpCode: number }).httpCode === 413
      ) {
        return res
          .status(413)
          .json({ success: false, message: 'File too large. Max size 10MB. (Formidable)' });
      }
      // Generic formidable parse error
      return res
        .status(500)
        .json({ success: false, message: error.message || 'Error parsing form' });
    }

    // Check for direct Formidable errors (e.g., if thrown before our wrapper)
    // Formidable errors might have an httpCode property (e.g., 413 for file too large)
    if (
      error instanceof Error &&
      'httpCode' in error &&
      (error as { httpCode: number }).httpCode === 413
    ) {
      return res.status(413).json({ success: false, message: 'File too large. Max size 10MB.' });
    }

    // Fallback for other unknown errors
    const message = error instanceof Error ? error.message : 'Server error during upload process';
    return res.status(500).json({ success: false, message });
  }
}
