import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/postgresql';
import bcrypt from 'bcrypt';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);

  // Check if user is authenticated and is admin
  if (!session || session.user.role !== 'admin') {
    return res.status(403).json({ success: false, message: 'Forbidden' });
  }

  // 获取PostgreSQL连接池
  let pool;
  try {
    pool = await dbConnect();
  } catch (error) {
    console.error('Database connection error:', error);
    return res.status(500).json({
      success: false,
      message: 'Database connection error',
      data: {},
    });
  }

  // Get user ID from query
  const { id } = req.query;
  const userId = Array.isArray(id) ? id[0] : id;

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      try {
        // 从PostgreSQL查询特定用户
        const result = await pool.query(
          `
          SELECT id, username, email, name, role, created_at, updated_at
          FROM users
          WHERE id = $1
        `,
          [userId]
        );

        if (result.rows.length === 0) {
          return res.status(404).json({ success: false, message: 'User not found' });
        }

        const user = {
          _id: result.rows[0].id.toString(),
          id: result.rows[0].id,
          username: result.rows[0].username,
          email: result.rows[0].email,
          name: result.rows[0].name,
          role: result.rows[0].role,
          createdAt: result.rows[0].created_at,
          updatedAt: result.rows[0].updated_at,
        };

        return res.status(200).json({ success: true, data: user });
      } catch (error) {
        console.error('Error fetching user:', error);
        return res.status(500).json({ success: false, message: 'Server error' });
      }

    case 'PUT':
      try {
        // 检查用户是否存在
        const checkUser = await pool.query(
          `
          SELECT * FROM users WHERE id = $1
        `,
          [userId]
        );

        if (checkUser.rows.length === 0) {
          return res.status(404).json({ success: false, message: 'User not found' });
        }

        // 提取要更新的数据
        const { username, email, name, role, password } = req.body;

        // 检查email/username是否已被其他用户使用
        if (username || email) {
          const existingUser = await pool.query(
            `
            SELECT id FROM users 
            WHERE (username = $1 OR email = $2) 
            AND id != $3
          `,
            [username || checkUser.rows[0].username, email || checkUser.rows[0].email, userId]
          );

          if (existingUser.rows.length > 0) {
            return res.status(400).json({
              success: false,
              message: 'A user with this email or username already exists',
            });
          }
        }

        // 构建更新语句
        let updateQuery = `
          UPDATE users 
          SET 
            username = $1, 
            email = $2, 
            name = $3, 
            role = $4,
            updated_at = NOW()
        `;

        let queryParams = [
          username || checkUser.rows[0].username,
          email || checkUser.rows[0].email,
          name || checkUser.rows[0].name,
          role || checkUser.rows[0].role,
        ];

        // 如果提供了密码，则加密并更新
        if (password) {
          const hashedPassword = await bcrypt.hash(password, 10);
          updateQuery +=
            ', password = $5 WHERE id = $6 RETURNING id, username, email, name, role, created_at, updated_at';
          queryParams.push(hashedPassword, userId);
        } else {
          updateQuery +=
            ' WHERE id = $5 RETURNING id, username, email, name, role, created_at, updated_at';
          queryParams.push(userId);
        }

        // 执行更新
        const result = await pool.query(updateQuery, queryParams);

        const updatedUser = {
          _id: result.rows[0].id.toString(),
          id: result.rows[0].id,
          username: result.rows[0].username,
          email: result.rows[0].email,
          name: result.rows[0].name,
          role: result.rows[0].role,
          createdAt: result.rows[0].created_at,
          updatedAt: result.rows[0].updated_at,
        };

        return res.status(200).json({ success: true, data: updatedUser });
      } catch (error) {
        console.error('Error updating user:', error);
        return res.status(500).json({ success: false, message: 'Server error' });
      }

    case 'DELETE':
      try {
        // 查询要删除的用户
        const userToDelete = await pool.query(
          `
          SELECT id, role FROM users WHERE id = $1
        `,
          [userId]
        );

        if (userToDelete.rows.length === 0) {
          return res.status(404).json({ success: false, message: 'User not found' });
        }

        // 检查是否是最后一个管理员
        if (userToDelete.rows[0].role === 'admin') {
          const adminCount = await pool.query(`
            SELECT COUNT(*) as count FROM users WHERE role = 'admin'
          `);

          if (parseInt(adminCount.rows[0].count) <= 1) {
            return res.status(400).json({
              success: false,
              message: 'Cannot delete the last admin user',
            });
          }
        }

        // 执行删除
        await pool.query(`DELETE FROM users WHERE id = $1`, [userId]);

        return res.status(200).json({ success: true, data: {} });
      } catch (error) {
        console.error('Error deleting user:', error);
        return res.status(500).json({ success: false, message: 'Server error' });
      }

    default:
      res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
      return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }
}
