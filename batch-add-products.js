const fs = require('fs');
const path = require('path');

// 产品配置
const products = [
  { id: 37, folder: '6.互动滑梯', name: '互动滑梯', slug: 'interactive-slide', nameEn: 'Interactive Slide', category: '儿童娱乐', categoryEn: 'Children Entertainment', price: 42999, description: '互动滑梯投影画面完全贴合滑梯，可一键遥控开关系统，当小孩子滑下滑梯时的身体动作和速度被激光感应器捕捉，滑梯上会出现各种不同的炫丽多媒体效果，小孩子与虚拟动物和场景进行趣味互动。' },
  { id: 38, folder: '7.神笔绘画', name: '神笔绘画', slug: 'magic-painting', nameEn: 'Magic Painting', category: '创意互动', categoryEn: 'Creative Interactive', price: 35999, description: '神笔绘画系统让孩子们在纸上自由绘画，通过先进的图像识别技术，将画作实时投影到大屏幕上，并为画中的角色赋予生命力，创造出奇幻的互动体验。' },
  { id: 39, folder: '8.互动沙池', name: '互动沙池', slug: 'interactive-sandbox', nameEn: 'Interactive Sandbox', category: '体感游戏', categoryEn: 'Motion Gaming', price: 38999, description: '互动沙池结合AR技术，当玩家在沙池中堆积沙子时，投影系统会在沙子表面呈现出山脉、河流、海洋等地形效果，创造出真实的地理环境模拟。' },
  { id: 40, folder: '9.AR沙桌', name: 'AR沙桌', slug: 'ar-sand-table', nameEn: 'AR Sand Table', category: '科普教育', categoryEn: 'Science Education', price: 46999, description: 'AR沙桌采用增强现实技术，玩家可以通过手势和工具在沙桌上塑造地形，系统会实时显示高程线、水流、植被等地理信息，是地理教学的理想工具。' },
  { id: 41, folder: '10.互动滑板', name: '互动滑板', slug: 'interactive-skateboard', nameEn: 'Interactive Skateboard', category: '体感游戏', categoryEn: 'Motion Gaming', price: 41999, description: '互动滑板通过体感技术捕捉玩家的滑板动作，在屏幕上模拟真实的滑板运动，玩家可以执行各种滑板技巧，享受极限运动的刺激。' }
];

// 读取当前的JSON文件
const jsonPath = './public/mock-products.json';
const currentData = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));

// 为每个产品生成完整的数据结构
products.forEach(product => {
  const productData = {
    id: product.id,
    name: product.name,
    name_en: product.nameEn,
    slug: product.slug,
    description: product.description,
    description_en: `${product.nameEn} provides an innovative interactive experience combining advanced technology with engaging gameplay.`,
    type: 'interactive_equipment',
    category: product.category,
    category_en: product.categoryEn,
    style: '互动,体感,娱乐',
    style_en: 'Interactive,Motion Sensing,Entertainment',
    features: [
      '先进体感技术',
      '实时互动反馈',
      '高清投影显示',
      '多人同时游戏',
      '安全防护设计'
    ],
    features_en: [
      'Advanced Motion Technology',
      'Real-time Interactive Feedback',
      'HD Projection Display',
      'Multiplayer Gaming',
      'Safety Protection Design'
    ],
    images: [`/images/products/${product.slug}/image1.png`, `/images/products/${product.slug}/image2.png`],
    videos: [`/videos/products/${product.slug}/video.mp4`],
    in_stock: true,
    is_featured: true,
    price: product.price,
    created_at: `2025-01-25T${15 + (product.id - 37)}:00:00.000Z`,
    updated_at: `2025-01-25T${15 + (product.id - 37)}:00:00.000Z`
  };
  
  currentData.push(productData);
});

// 写回文件
fs.writeFileSync(jsonPath, JSON.stringify(currentData, null, 2));
console.log(`已添加 ${products.length} 个产品到JSON文件`);