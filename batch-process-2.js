const fs = require('fs');
const path = require('path');

// 第二批产品数据 (ID 43-52)
const batch2Products = [
  {
    id: 43,
    folder: '12.体感大师',
    name: '体感大师',
    nameEn: 'Motion Master',
    slug: 'motion-master',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '体感大师是一款全身体感互动系统，通过先进的动作捕捉技术，玩家可以通过肢体动作控制游戏角色，体验沉浸式的互动娱乐。',
    price: 55999
  },
  {
    id: 44,
    folder: '13.体感攀岩',
    name: '体感攀岩',
    nameEn: 'Motion Climbing',
    slug: 'motion-climbing',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '体感攀岩结合真实攀岩动作与虚拟场景，通过体感技术识别攀爬动作，在屏幕上呈现各种挑战性的攀岩场景。',
    price: 49999
  },
  {
    id: 45,
    folder: '14.跳跃格子',
    name: '跳跃格子',
    nameEn: 'Jumping Grid',
    slug: 'jumping-grid',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '跳跃格子是一款地面互动游戏，玩家需要在格子间跳跃，通过压力感应技术识别玩家位置，提供有趣的跳跃挑战。',
    price: 33999
  },
  {
    id: 46,
    folder: '15.运动一体机',
    name: '运动一体机',
    nameEn: 'Sports All-in-One',
    slug: 'sports-all-in-one',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '运动一体机集成多种体感运动游戏，包括拳击、网球、足球等多项运动，为用户提供全方位的运动体验。',
    price: 62999
  },
  {
    id: 47,
    folder: '16.移动射箭',
    name: '移动射箭',
    nameEn: 'Mobile Archery',
    slug: 'mobile-archery',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '移动射箭通过体感技术模拟真实射箭体验，玩家可以通过拉弓动作在虚拟环境中进行射箭比赛。',
    price: 37999
  },
  {
    id: 48,
    folder: '17.实弹射击',
    name: '实弹射击',
    nameEn: 'Live Fire Shooting',
    slug: 'live-fire-shooting',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '实弹射击模拟器提供逼真的射击体验，通过高精度传感器和真实枪械模型，让玩家体验专业级射击训练。',
    price: 75999
  },
  {
    id: 49,
    folder: '18.互动足球',
    name: '互动足球',
    nameEn: 'Interactive Football',
    slug: 'interactive-football',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '互动足球通过投影技术在地面创建虚拟足球场，玩家可以通过踢球动作与虚拟足球进行互动。',
    price: 44999
  },
  {
    id: 50,
    folder: '19.魔力滚球',
    name: '魔力滚球',
    nameEn: 'Magic Rolling Ball',
    slug: 'magic-rolling-ball',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '魔力滚球是一款创新的体感游戏，玩家通过控制球体在虚拟迷宫中滚动，解决各种有趣的谜题。',
    price: 36999
  },
  {
    id: 51,
    folder: '20.人体闯关',
    name: '人体闯关',
    nameEn: 'Human Challenge',
    slug: 'human-challenge',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '人体闯关是一款全身体感游戏，玩家需要通过各种身体动作来完成挑战关卡，锻炼身体协调性。',
    price: 52999
  },
  {
    id: 52,
    folder: '21.动物赛跑',
    name: '动物赛跑',
    nameEn: 'Animal Racing',
    slug: 'animal-racing',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '动物赛跑让玩家模拟各种动物的奔跑动作，通过体感技术识别动作，在屏幕上与虚拟动物一起赛跑。',
    price: 38999
  }
];

// 读取当前JSON
const jsonPath = './public/mock-products.json';
let currentData = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));

console.log('开始处理第二批产品 (ID 43-52)...');

// 处理每个产品
batch2Products.forEach((product, index) => {
  console.log(`处理产品 ${index + 1}/${batch2Products.length}: ${product.name}`);
  
  // 创建目录
  const imageDir = `./public/images/products/${product.slug}`;
  const videoDir = `./public/videos/products/${product.slug}`;
  
  if (!fs.existsSync(imageDir)) {
    fs.mkdirSync(imageDir, { recursive: true });
  }
  if (!fs.existsSync(videoDir)) {
    fs.mkdirSync(videoDir, { recursive: true });
  }
  
  // 复制文件
  const sourceDir = `./产品更新/${product.folder}`;
  if (fs.existsSync(sourceDir)) {
    try {
      // 复制图片文件
      const files = fs.readdirSync(sourceDir);
      const imageFiles = files.filter(f => f.endsWith('.png') || f.endsWith('.jpg'));
      const videoFiles = files.filter(f => f.endsWith('.mp4'));
      
      imageFiles.forEach(file => {
        const src = path.join(sourceDir, file);
        const dest = path.join(imageDir, file);
        fs.copyFileSync(src, dest);
      });
      
      videoFiles.forEach(file => {
        const src = path.join(sourceDir, file);
        const dest = path.join(videoDir, file);
        fs.copyFileSync(src, dest);
      });
      
      // 构建产品数据
      const productData = {
        id: product.id,
        name: product.name,
        name_en: product.nameEn,
        slug: product.slug,
        description: product.description,
        description_en: `${product.nameEn} provides an innovative interactive experience combining advanced motion sensing technology with engaging gameplay for immersive entertainment.`,
        type: 'interactive_equipment',
        category: product.category,
        category_en: product.categoryEn,
        style: '互动,体感,娱乐',
        style_en: 'Interactive,Motion Sensing,Entertainment',
        features: [
          '先进体感技术',
          '实时动作捕捉',
          '高清显示效果',
          '多人同时游戏',
          '安全防护系统'
        ],
        features_en: [
          'Advanced Motion Technology',
          'Real-time Motion Capture',
          'HD Display Effects',
          'Multiplayer Gaming',
          'Safety Protection System'
        ],
        images: imageFiles.map(f => `/images/products/${product.slug}/${f}`),
        videos: videoFiles.map(f => `/videos/products/${product.slug}/${f}`),
        in_stock: true,
        is_featured: true,
        price: product.price,
        created_at: `2025-01-25T${21 + index}:00:00.000Z`,
        updated_at: `2025-01-25T${21 + index}:00:00.000Z`
      };
      
      currentData.push(productData);
      console.log(`✓ 已添加产品: ${product.name} (ID: ${product.id})`);
      
    } catch (error) {
      console.error(`处理产品 ${product.name} 时出错:`, error.message);
    }
  } else {
    console.log(`⚠ 源目录不存在: ${sourceDir}`);
  }
});

// 保存更新的JSON
fs.writeFileSync(jsonPath, JSON.stringify(currentData, null, 2));
console.log(`\n第二批处理完成！已添加 ${batch2Products.length} 个产品到系统。`);
console.log(`当前总产品数量: ${currentData.length}`);