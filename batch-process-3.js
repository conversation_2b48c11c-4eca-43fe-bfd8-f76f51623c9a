const fs = require('fs');
const path = require('path');

// 第三批产品数据 (ID 53-62) - 剩余产品
const batch3Products = [
  {
    id: 53,
    folder: '21.体感蹦床',
    name: '体感蹦床',
    nameEn: 'Motion Trampoline',
    slug: 'motion-trampoline',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '体感蹦床结合蹦床运动与互动投影，当玩家在蹦床上弹跳时，通过体感技术捕捉动作，在屏幕上呈现精彩的互动内容。',
    price: 48999
  },
  {
    id: 54,
    folder: '22.动感单车',
    name: '动感单车',
    nameEn: 'Exercise Bike',
    slug: 'exercise-bike',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '动感单车通过虚拟现实技术，让用户在骑行中体验不同的虚拟场景，如山地、海滨等，增加运动的趣味性。',
    price: 35999
  },
  {
    id: 55,
    folder: '23.模拟拳击',
    name: '模拟拳击',
    nameEn: 'Simulated Boxing',
    slug: 'simulated-boxing',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '模拟拳击通过高精度传感器捕捉拳击动作，在虚拟环境中进行拳击训练和比赛，提供真实的拳击体验。',
    price: 42999
  },
  {
    id: 56,
    folder: '24.模拟网球',
    name: '模拟网球',
    nameEn: 'Simulated Tennis',
    slug: 'simulated-tennis',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '模拟网球通过体感技术识别挥拍动作，在虚拟网球场中进行比赛，让玩家体验专业级网球运动。',
    price: 39999
  },
  {
    id: 57,
    folder: '25.全息宴会厅',
    name: '全息宴会厅',
    nameEn: 'Holographic Banquet Hall',
    slug: 'holographic-banquet-hall',
    category: '沉浸体验',
    categoryEn: 'Immersive Experience',
    description: '全息宴会厅运用先进的全息投影技术，营造奢华梦幻的宴会氛围，为各种庆典活动提供震撼的视觉体验。',
    price: 299999
  },
  {
    id: 58,
    folder: '26.沉浸式空间',
    name: '沉浸式空间',
    nameEn: 'Immersive Space',
    slug: 'immersive-space',
    category: '沉浸体验',
    categoryEn: 'Immersive Experience',
    description: '沉浸式空间通过360度投影和空间音效，创造完全沉浸的虚拟环境，让用户置身于不同的奇妙世界。',
    price: 199999
  },
  {
    id: 59,
    folder: '27.CAVE空间',
    name: 'CAVE空间',
    nameEn: 'CAVE Space',
    slug: 'cave-space',
    category: '沉浸体验',
    categoryEn: 'Immersive Experience',
    description: 'CAVE空间是一个立体投影环境，用户可以在其中与三维虚拟对象进行自然交互，提供前所未有的沉浸体验。',
    price: 450999
  },
  {
    id: 60,
    folder: '28.科鲁兹互动墙',
    name: '科鲁兹互动墙',
    nameEn: 'Cruise Interactive Wall',
    slug: 'cruise-interactive-wall',
    category: '互动展示',
    categoryEn: 'Interactive Display',
    description: '科鲁兹互动墙提供大屏幕触控互动体验，支持多点触控和手势识别，适用于展览、教育和商业展示。',
    price: 85999
  },
  {
    id: 61,
    folder: '29.沉浸式星空灯',
    name: '沉浸式星空灯',
    nameEn: 'Immersive Starry Light',
    slug: 'immersive-starry-light',
    category: '沉浸体验',
    categoryEn: 'Immersive Experience',
    description: '沉浸式星空灯通过精密的光学投影技术，在室内营造逼真的星空效果，带来浪漫梦幻的氛围体验。',
    price: 25999
  },
  {
    id: 62,
    folder: '30.地面互动',
    name: '地面互动',
    nameEn: 'Floor Interactive',
    slug: 'floor-interactive',
    category: '互动展示',
    categoryEn: 'Interactive Display',
    description: '地面互动系统通过投影和传感技术，将普通地面转化为互动娱乐空间，支持脚踩、踩踏等多种互动方式。',
    price: 58999
  }
];

// 读取当前JSON
const jsonPath = './public/mock-products.json';
let currentData = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));

console.log('开始处理第三批产品 (ID 53-62)...');

// 处理每个产品
batch3Products.forEach((product, index) => {
  console.log(`处理产品 ${index + 1}/${batch3Products.length}: ${product.name}`);
  
  // 创建目录
  const imageDir = `./public/images/products/${product.slug}`;
  const videoDir = `./public/videos/products/${product.slug}`;
  
  if (!fs.existsSync(imageDir)) {
    fs.mkdirSync(imageDir, { recursive: true });
  }
  if (!fs.existsSync(videoDir)) {
    fs.mkdirSync(videoDir, { recursive: true });
  }
  
  // 复制文件
  const sourceDir = `./产品更新/${product.folder}`;
  if (fs.existsSync(sourceDir)) {
    try {
      // 复制图片文件
      const files = fs.readdirSync(sourceDir);
      const imageFiles = files.filter(f => f.endsWith('.png') || f.endsWith('.jpg') || f.endsWith('.jpeg'));
      const videoFiles = files.filter(f => f.endsWith('.mp4') || f.endsWith('.avi') || f.endsWith('.mov'));
      
      console.log(`  发现 ${imageFiles.length} 个图片文件，${videoFiles.length} 个视频文件`);
      
      imageFiles.forEach(file => {
        const src = path.join(sourceDir, file);
        const dest = path.join(imageDir, file);
        fs.copyFileSync(src, dest);
      });
      
      videoFiles.forEach(file => {
        const src = path.join(sourceDir, file);
        const dest = path.join(videoDir, file);
        fs.copyFileSync(src, dest);
      });
      
      // 构建产品数据
      const productData = {
        id: product.id,
        name: product.name,
        name_en: product.nameEn,
        slug: product.slug,
        description: product.description,
        description_en: `${product.nameEn} provides an innovative interactive experience combining advanced technology with engaging content for immersive entertainment and education.`,
        type: 'interactive_equipment',
        category: product.category,
        category_en: product.categoryEn,
        style: '互动,高科技,娱乐',
        style_en: 'Interactive,High-tech,Entertainment',
        features: [
          '先进交互技术',
          '高清视觉效果',
          '沉浸式体验',
          '多用户支持',
          '智能控制系统'
        ],
        features_en: [
          'Advanced Interactive Technology',
          'HD Visual Effects',
          'Immersive Experience',
          'Multi-user Support',
          'Smart Control System'
        ],
        images: imageFiles.map(f => `/images/products/${product.slug}/${f}`),
        videos: videoFiles.map(f => `/videos/products/${product.slug}/${f}`),
        in_stock: true,
        is_featured: true,
        price: product.price,
        created_at: `2025-01-26T${index}:00:00.000Z`,
        updated_at: `2025-01-26T${index}:00:00.000Z`
      };
      
      currentData.push(productData);
      console.log(`✓ 已添加产品: ${product.name} (ID: ${product.id}) - ${imageFiles.length} 图片, ${videoFiles.length} 视频`);
      
    } catch (error) {
      console.error(`处理产品 ${product.name} 时出错:`, error.message);
    }
  } else {
    console.log(`⚠ 源目录不存在: ${sourceDir}`);
  }
});

// 保存更新的JSON
fs.writeFileSync(jsonPath, JSON.stringify(currentData, null, 2));
console.log(`\n第三批处理完成！已添加 ${batch3Products.length} 个产品到系统。`);
console.log(`当前总产品数量: ${currentData.length}`);