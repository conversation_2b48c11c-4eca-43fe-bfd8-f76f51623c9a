const fs = require('fs');
const path = require('path');

// 第四批产品数据 (ID 63-69) - 最后一批
const batch4Products = [
  {
    id: 64,
    folder: '32.互动鼓',
    name: '互动鼓',
    nameEn: 'Interactive Drum',
    slug: 'interactive-drum',
    category: '音乐互动',
    categoryEn: 'Music Interactive',
    description: '互动鼓通过体感技术识别击鼓动作，在虚拟环境中进行音乐演奏，提供真实的打鼓体验和音乐教学功能。',
    price: 35999
  },
  {
    id: 65,
    folder: '33.互动跳一跳',
    name: '互动跳一跳',
    nameEn: 'Interactive Jump Game',
    slug: 'interactive-jump-game',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '互动跳一跳将经典手机游戏搬到现实中，玩家通过真实跳跃动作控制游戏角色，体验全新的体感游戏乐趣。',
    price: 28999
  },
  {
    id: 66,
    folder: '34.3D 合影拍照',
    name: '3D合影拍照',
    nameEn: '3D Photo Booth',
    slug: '3d-photo-booth',
    category: '互动展示',
    categoryEn: 'Interactive Display',
    description: '3D合影拍照系统通过多角度摄像头捕捉，生成立体合影效果，为用户提供独特的拍照体验和纪念品制作。',
    price: 42999
  },
  {
    id: 67,
    folder: '35.AR智慧教室',
    name: 'AR智慧教室',
    nameEn: 'AR Smart Classroom',
    slug: 'ar-smart-classroom',
    category: '科普教育',
    categoryEn: 'Science Education',
    description: 'AR智慧教室将增强现实技术融入教学环境，为学生提供沉浸式的学习体验，让抽象概念变得生动具体。',
    price: 149999
  },
  {
    id: 68,
    folder: '36.墙地感统教室',
    name: '墙地感统教室',
    nameEn: 'Wall-Floor Sensory Classroom',
    slug: 'wall-floor-sensory-classroom',
    category: '科普教育',
    categoryEn: 'Science Education',
    description: '墙地感统教室通过墙面和地面的互动投影，为儿童提供全方位的感统训练环境，促进身心协调发展。',
    price: 89999
  }
];

// 读取当前JSON
const jsonPath = './public/mock-products.json';
let currentData = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));

console.log('开始处理第四批产品 (ID 64-68) - 最后一批...');

// 处理每个产品
batch4Products.forEach((product, index) => {
  console.log(`处理产品 ${index + 1}/${batch4Products.length}: ${product.name}`);
  
  // 创建目录
  const imageDir = `./public/images/products/${product.slug}`;
  const videoDir = `./public/videos/products/${product.slug}`;
  
  if (!fs.existsSync(imageDir)) {
    fs.mkdirSync(imageDir, { recursive: true });
  }
  if (!fs.existsSync(videoDir)) {
    fs.mkdirSync(videoDir, { recursive: true });
  }
  
  // 复制文件
  const sourceDir = `./产品更新/${product.folder}`;
  if (fs.existsSync(sourceDir)) {
    try {
      // 复制图片文件
      const files = fs.readdirSync(sourceDir);
      const imageFiles = files.filter(f => f.endsWith('.png') || f.endsWith('.jpg') || f.endsWith('.jpeg'));
      const videoFiles = files.filter(f => f.endsWith('.mp4') || f.endsWith('.avi') || f.endsWith('.mov'));
      
      console.log(`  发现 ${imageFiles.length} 个图片文件，${videoFiles.length} 个视频文件`);
      
      imageFiles.forEach(file => {
        const src = path.join(sourceDir, file);
        const dest = path.join(imageDir, file);
        fs.copyFileSync(src, dest);
      });
      
      videoFiles.forEach(file => {
        const src = path.join(sourceDir, file);
        const dest = path.join(videoDir, file);
        fs.copyFileSync(src, dest);
      });
      
      // 构建产品数据
      const productData = {
        id: product.id,
        name: product.name,
        name_en: product.nameEn,
        slug: product.slug,
        description: product.description,
        description_en: `${product.nameEn} provides an innovative interactive experience combining advanced technology with engaging content for immersive entertainment and education.`,
        type: 'interactive_equipment',
        category: product.category,
        category_en: product.categoryEn,
        style: '互动,高科技,娱乐',
        style_en: 'Interactive,High-tech,Entertainment',
        features: [
          '先进交互技术',
          '高清视觉效果',
          '沉浸式体验',
          '多用户支持',
          '智能控制系统'
        ],
        features_en: [
          'Advanced Interactive Technology',
          'HD Visual Effects',
          'Immersive Experience',
          'Multi-user Support',
          'Smart Control System'
        ],
        images: imageFiles.map(f => `/images/products/${product.slug}/${f}`),
        videos: videoFiles.map(f => `/videos/products/${product.slug}/${f}`),
        in_stock: true,
        is_featured: true,
        price: product.price,
        created_at: `2025-01-26T${10 + index}:00:00.000Z`,
        updated_at: `2025-01-26T${10 + index}:00:00.000Z`
      };
      
      currentData.push(productData);
      console.log(`✓ 已添加产品: ${product.name} (ID: ${product.id}) - ${imageFiles.length} 图片, ${videoFiles.length} 视频`);
      
    } catch (error) {
      console.error(`处理产品 ${product.name} 时出错:`, error.message);
    }
  } else {
    console.log(`⚠ 源目录不存在: ${sourceDir}`);
  }
});

// 保存更新的JSON
fs.writeFileSync(jsonPath, JSON.stringify(currentData, null, 2));
console.log(`\n第四批处理完成！已添加 ${batch4Products.length} 个产品到系统。`);
console.log(`当前总产品数量: ${currentData.length}`);
console.log('\n🎉 所有37个产品批量添加完成！');