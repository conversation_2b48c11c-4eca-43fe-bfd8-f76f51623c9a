const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');

// 批处理产品数据
const batchProducts = [
  {
    id: 38,
    folder: '7.神笔绘画',
    name: '神笔绘画',
    nameEn: 'Magic Painting',
    slug: 'magic-painting',
    category: '创意互动',
    categoryEn: 'Creative Interactive',
    description: '神笔绘画系统让孩子们在纸上自由绘画，通过先进的图像识别技术，将画作实时投影到大屏幕上，并为画中的角色赋予生命力，创造出奇幻的互动体验。',
    price: 35999
  },
  {
    id: 39,
    folder: '8.互动沙池',
    name: '互动沙池',
    nameEn: 'Interactive Sandbox',
    slug: 'interactive-sandbox',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '互动沙池结合AR技术，当玩家在沙池中堆积沙子时，投影系统会在沙子表面呈现出山脉、河流、海洋等地形效果，创造出真实的地理环境模拟。',
    price: 38999
  },
  {
    id: 40,
    folder: '9.AR沙桌',
    name: 'AR沙桌',
    nameEn: 'AR Sand Table',
    slug: 'ar-sand-table',
    category: '科普教育',
    categoryEn: 'Science Education',
    description: 'AR沙桌采用增强现实技术，玩家可以通过手势和工具在沙桌上塑造地形，系统会实时显示高程线、水流、植被等地理信息，是地理教学的理想工具。',
    price: 46999
  },
  {
    id: 41,
    folder: '10.互动滑板',
    name: '互动滑板',
    nameEn: 'Interactive Skateboard',
    slug: 'interactive-skateboard',
    category: '体感游戏',
    categoryEn: 'Motion Gaming',
    description: '互动滑板通过体感技术捕捉玩家的滑板动作，在屏幕上模拟真实的滑板运动，玩家可以执行各种滑板技巧，享受极限运动的刺激。',
    price: 41999
  },
  {
    id: 42,
    folder: '11.益智流水墙',
    name: '益智流水墙',
    nameEn: 'Educational Water Wall',
    slug: 'educational-water-wall',
    category: '科普教育',
    categoryEn: 'Science Education',
    description: '益智流水墙通过水流的物理原理展示重力、流体力学等科学概念，孩子们可以通过调节水道、阀门等装置，观察水流的变化，学习科学知识。',
    price: 28999
  }
];

// 读取当前JSON
const jsonPath = './public/mock-products.json';
let currentData = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));

console.log('开始批量处理产品...');

// 处理每个产品
batchProducts.forEach((product, index) => {
  console.log(`处理产品 ${index + 1}/${batchProducts.length}: ${product.name}`);
  
  // 创建目录
  const imageDir = `./public/images/products/${product.slug}`;
  const videoDir = `./public/videos/products/${product.slug}`;
  
  if (!fs.existsSync(imageDir)) {
    fs.mkdirSync(imageDir, { recursive: true });
  }
  if (!fs.existsSync(videoDir)) {
    fs.mkdirSync(videoDir, { recursive: true });
  }
  
  // 复制文件
  const sourceDir = `./产品更新/${product.folder}`;
  if (fs.existsSync(sourceDir)) {
    try {
      // 复制图片文件
      const files = fs.readdirSync(sourceDir);
      const imageFiles = files.filter(f => f.endsWith('.png') || f.endsWith('.jpg'));
      const videoFiles = files.filter(f => f.endsWith('.mp4'));
      
      imageFiles.forEach(file => {
        const src = path.join(sourceDir, file);
        const dest = path.join(imageDir, file);
        fs.copyFileSync(src, dest);
      });
      
      videoFiles.forEach(file => {
        const src = path.join(sourceDir, file);
        const dest = path.join(videoDir, file);
        fs.copyFileSync(src, dest);
      });
      
      // 构建产品数据
      const productData = {
        id: product.id,
        name: product.name,
        name_en: product.nameEn,
        slug: product.slug,
        description: product.description,
        description_en: `${product.nameEn} provides an innovative interactive experience combining advanced technology with engaging gameplay.`,
        type: 'interactive_equipment',
        category: product.category,
        category_en: product.categoryEn,
        style: '互动,体感,娱乐',
        style_en: 'Interactive,Motion Sensing,Entertainment',
        features: [
          '先进体感技术',
          '实时互动反馈',
          '高清投影显示',
          '多人同时游戏',
          '安全防护设计'
        ],
        features_en: [
          'Advanced Motion Technology',
          'Real-time Interactive Feedback',
          'HD Projection Display',
          'Multiplayer Gaming',
          'Safety Protection Design'
        ],
        images: imageFiles.map(f => `/images/products/${product.slug}/${f}`),
        videos: videoFiles.map(f => `/videos/products/${product.slug}/${f}`),
        in_stock: true,
        is_featured: true,
        price: product.price,
        created_at: `2025-01-25T${16 + index}:00:00.000Z`,
        updated_at: `2025-01-25T${16 + index}:00:00.000Z`
      };
      
      currentData.push(productData);
      console.log(`✓ 已添加产品: ${product.name} (ID: ${product.id})`);
      
    } catch (error) {
      console.error(`处理产品 ${product.name} 时出错:`, error.message);
    }
  }
});

// 保存更新的JSON
fs.writeFileSync(jsonPath, JSON.stringify(currentData, null, 2));
console.log(`\n批量处理完成！已添加 ${batchProducts.length} 个产品到系统。`);
console.log(`当前总产品数量: ${currentData.length}`);