const fs = require('fs');

// 读取产品数据
const jsonPath = './public/mock-products.json';
const products = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));

// 筛选新添加的产品 (ID >= 32)
const newProducts = products.filter(p => p.id >= 32);

console.log('=== 检查新添加产品的视频情况 ===\n');

let noVideoProducts = [];
let hasVideoProducts = [];

newProducts.forEach(product => {
  if (!product.videos || product.videos.length === 0) {
    noVideoProducts.push({
      id: product.id,
      name: product.name,
      slug: product.slug
    });
    console.log(`❌ 产品 ${product.id}: ${product.name} - 没有视频`);
  } else {
    hasVideoProducts.push({
      id: product.id,
      name: product.name,
      videoCount: product.videos.length,
      videos: product.videos
    });
    console.log(`✅ 产品 ${product.id}: ${product.name} - ${product.videos.length}个视频`);
  }
});

console.log('\n=== 汇总统计 ===');
console.log(`新产品总数: ${newProducts.length}`);
console.log(`有视频的产品: ${hasVideoProducts.length}`);
console.log(`没有视频的产品: ${noVideoProducts.length}`);

if (noVideoProducts.length > 0) {
  console.log('\n=== 没有视频的产品列表 ===');
  noVideoProducts.forEach(p => {
    console.log(`- ID ${p.id}: ${p.name} (${p.slug})`);
  });
}

// 检查视频文件是否实际存在
console.log('\n=== 检查视频文件是否存在 ===');
let missingVideoFiles = [];

hasVideoProducts.forEach(product => {
  product.videos.forEach(videoPath => {
    const fullPath = `./public${videoPath}`;
    if (!fs.existsSync(fullPath)) {
      missingVideoFiles.push({
        productId: product.id,
        productName: product.name,
        videoPath: videoPath
      });
      console.log(`⚠️  缺失文件: ${product.name} - ${videoPath}`);
    }
  });
});

if (missingVideoFiles.length === 0) {
  console.log('✅ 所有视频文件都存在');
} else {
  console.log(`\n❌ 发现 ${missingVideoFiles.length} 个缺失的视频文件`);
}