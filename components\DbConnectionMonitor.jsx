'use client';

import { useState, useEffect } from 'react';

/**
 * 数据库连接监控组件
 * 
 * 监控数据库连接状态并显示状态指示器
 * 在连接失败时自动尝试重新连接
 */
const DbConnectionMonitor = () => {
  const [connectionStatus, setConnectionStatus] = useState('checking');
  const [lastError, setLastError] = useState(null);
  const [isVisible, setIsVisible] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  
  // 是否为开发环境
  const isDev = process.env.NODE_ENV === 'development';
  
  // 检查数据库健康状态
  const checkDbHealth = async () => {
    try {
      const response = await fetch('/api/db-health', {
        method: 'GET',
        headers: { 'Cache-Control': 'no-cache' }
      });
      
      if (response.ok) {
        const data = await response.json();
        setConnectionStatus(data.status);
        setLastError(data.error || null);
        
        // 如果连接恢复，重置重试计数
        if (data.status === 'healthy') {
          setRetryCount(0);
        }
        
        return data.status === 'healthy';
      } else {
        setConnectionStatus('error');
        setLastError(`HTTP错误: ${response.status}`);
        return false;
      }
    } catch (error) {
      setConnectionStatus('error');
      setLastError(error.message);
      return false;
    }
  };
  
  // 初始检查和定时轮询
  useEffect(() => {
    // 初始检查
    checkDbHealth();
    
    // 设置轮询间隔
    const interval = setInterval(() => {
      checkDbHealth().then(isHealthy => {
        // 如果不健康，增加重试计数
        if (!isHealthy) {
          setRetryCount(prev => prev + 1);
        }
      });
    }, 30000); // 30秒检查一次
    
    return () => clearInterval(interval);
  }, []);
  
  // 控制组件可见性
  useEffect(() => {
    // 只在开发环境或连接出错时显示
    setIsVisible(isDev || connectionStatus !== 'healthy');
  }, [connectionStatus, isDev]);
  
  // 如果组件不可见，不渲染任何内容
  if (!isVisible) return null;
  
  // 状态颜色映射
  const statusColors = {
    'healthy': 'bg-green-500',
    'unhealthy': 'bg-red-500',
    'checking': 'bg-yellow-500',
    'error': 'bg-red-500'
  };
  
  // 状态文本映射
  const statusText = {
    'healthy': '数据库连接正常',
    'unhealthy': '数据库连接异常',
    'checking': '正在检查数据库连接...',
    'error': '数据库连接失败'
  };
  
  return (
    <div className="fixed bottom-4 right-4 z-50 shadow-lg rounded-lg bg-white dark:bg-gray-800 p-3 text-sm">
      <div className="flex items-center space-x-2">
        <div className={`w-3 h-3 rounded-full ${statusColors[connectionStatus] || 'bg-gray-500'}`}></div>
        <span>{statusText[connectionStatus] || '未知状态'}</span>
        
        {/* 重试计数器 */}
        {retryCount > 0 && (
          <span className="text-xs text-gray-500">
            (重试: {retryCount})
          </span>
        )}
        
        {/* 重试按钮 */}
        <button 
          onClick={() => checkDbHealth()} 
          className="ml-2 px-2 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded"
        >
          重试
        </button>
        
        {/* 切换显示详情按钮 */}
        <button 
          onClick={() => setIsVisible(false)} 
          className="ml-2 px-2 py-1 text-xs bg-gray-300 hover:bg-gray-400 text-gray-800 rounded"
        >
          隐藏
        </button>
      </div>
      
      {/* 错误详情 */}
      {lastError && (
        <div className="mt-2 text-xs text-red-500">
          <div>错误: {lastError}</div>
          <div className="mt-1 text-gray-500">
            使用模拟数据作为降级措施，数据可能不是最新的。
          </div>
        </div>
      )}
    </div>
  );
};

export default DbConnectionMonitor; 