'use client';

import { useState } from 'react';

/**
 * 悬浮缓存清理按钮
 * 方便开发人员快速清除缓存
 */
const FloatingCacheCleaner = ({ onlyInDev = true }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState(null);
  
  // 是否显示组件
  const isDev = process.env.NODE_ENV === 'development';
  if (onlyInDev && !isDev) return null;
  
  // 清除缓存
  const clearCache = async (type = 'all') => {
    setIsLoading(true);
    setResult(null);
    
    try {
      const response = await fetch(`/api/cache/clear?type=${type}`, {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setResult({
          success: true,
          message: data.message || `缓存已清除 (${type})`
        });
        
        // 如果是客户端缓存，刷新页面
        if (type === 'client' || type === 'all') {
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        }
      } else {
        setResult({
          success: false,
          message: `清除失败: ${response.status}`
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: error.message || '发生错误'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // 清除localStorage
  const clearLocalStorage = () => {
    try {
      localStorage.clear();
      setResult({
        success: true,
        message: '浏览器存储已清除'
      });
      
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (error) {
      setResult({
        success: false,
        message: error.message || '清除失败'
      });
    }
  };
  
  return (
    <div className="fixed bottom-4 left-4 z-50 flex flex-col items-start">
      {/* 主按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-full shadow-lg flex items-center justify-center"
        title="缓存管理"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M18 6V4H6v2"></path>
          <path d="M12 2v6"></path>
          <rect x="2" y="8" width="20" height="14" rx="2"></rect>
          <path d="M6 16h.01"></path>
          <path d="M10 16h.01"></path>
          <path d="M14 16h.01"></path>
          <path d="M18 16h.01"></path>
        </svg>
      </button>
      
      {/* 展开菜单 */}
      {isOpen && (
        <div className="mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 min-w-[200px]">
          <div className="text-sm font-medium mb-2">缓存清理</div>
          
          <div className="space-y-2">
            <button
              onClick={() => clearCache('all')}
              disabled={isLoading}
              className="w-full text-left px-3 py-2 text-sm rounded bg-red-100 hover:bg-red-200 text-red-800 disabled:opacity-50"
            >
              清除所有缓存
            </button>
            
            <button
              onClick={() => clearCache('client')}
              disabled={isLoading}
              className="w-full text-left px-3 py-2 text-sm rounded bg-blue-100 hover:bg-blue-200 text-blue-800 disabled:opacity-50"
            >
              清除客户端缓存
            </button>
            
            <button
              onClick={clearLocalStorage}
              disabled={isLoading}
              className="w-full text-left px-3 py-2 text-sm rounded bg-yellow-100 hover:bg-yellow-200 text-yellow-800 disabled:opacity-50"
            >
              清除浏览器存储
            </button>
          </div>
          
          {/* 结果显示 */}
          {result && (
            <div className={`mt-2 p-2 text-xs rounded ${result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
              {result.message}
              {(result.success && (result.message.includes('客户端') || result.message.includes('浏览器'))) && (
                <div className="italic mt-1">页面将在1.5秒后刷新...</div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FloatingCacheCleaner; 