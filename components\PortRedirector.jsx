'use client';

import { useEffect } from 'react';

/**
 * 端口重定向组件
 * 
 * 解决Next.js服务器在端口3000被占用时自动切换到3001的问题
 * 自动检测URLs中硬编码的端口号并进行适当的修复
 */
const PortRedirector = () => {
  useEffect(() => {
    // 只在客户端执行
    if (typeof window === 'undefined') return;

    // 获取当前端口
    const currentPort = window.location.port;
    
    // 记录当前使用的端口
    console.log(`[端口重定向] 当前页面端口: ${currentPort || '默认80/443'}`);
    
    // 如果当前URL中包含错误的端口，自动重定向
    const url = window.location.href;
    
    // 3000端口被占用时，站点运行在3001上
    if (currentPort === '3001' && url.includes(':3000/')) {
      const correctedUrl = url.replace(':3000/', ':3001/');
      console.log(`[端口重定向] 从 ${url} 重定向到 ${correctedUrl}`);
      window.location.href = correctedUrl;
    }
    
    // 3001端口被释放后，站点又运行在3000上
    if (currentPort === '3000' && url.includes(':3001/')) {
      const correctedUrl = url.replace(':3001/', ':3000/');
      console.log(`[端口重定向] 从 ${url} 重定向到 ${correctedUrl}`);
      window.location.href = correctedUrl;
    }
    
    // 修复绝对URL链接
    function fixLinks() {
      // 延迟执行，确保页面已加载完成
      setTimeout(() => {
        // 获取所有a标签
        const links = document.querySelectorAll('a[href]');
        
        links.forEach(link => {
          const href = link.getAttribute('href');
          
          // 检查是否为同域链接且包含端口号
          if (href && (
            (href.includes(':3000') && currentPort === '3001') ||
            (href.includes(':3001') && currentPort === '3000')
          )) {
            // 修复链接
            let fixedHref = href;
            if (currentPort === '3001' && href.includes(':3000')) {
              fixedHref = href.replace(':3000', ':3001');
            } else if (currentPort === '3000' && href.includes(':3001')) {
              fixedHref = href.replace(':3001', ':3000');
            }
            
            // 更新链接
            if (fixedHref !== href) {
              console.log(`[端口重定向] 修复链接: ${href} -> ${fixedHref}`);
              link.setAttribute('href', fixedHref);
            }
          }
        });
      }, 1000);
    }
    
    // 首次执行
    fixLinks();
    
    // 监听路由变化
    const observer = new MutationObserver(fixLinks);
    observer.observe(document.body, { childList: true, subtree: true });
    
    // 清理函数
    return () => {
      observer.disconnect();
    };
  }, []);
  
  // 此组件不渲染任何内容
  return null;
};

export default PortRedirector; 