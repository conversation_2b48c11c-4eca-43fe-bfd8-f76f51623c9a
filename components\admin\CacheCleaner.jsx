'use client';

import { useState } from 'react';
import { getApiUrl } from '@/lib/apiUtils';

/**
 * 缓存清理组件
 * 用于管理界面清除各种缓存
 */
const CacheCleaner = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  
  // 清除缓存的函数
  const clearCache = async (type = 'all') => {
    setIsLoading(true);
    setError(null);
    setResult(null);
    
    try {
      // 构建API URL
      const apiUrl = getApiUrl(`cache/clear?type=${type}`);
      
      // 发送请求
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setResult(data);
        
        // 如果清除的是客户端缓存，刷新页面
        if (type === 'client' || type === 'all') {
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        setError(errorData.error || `请求失败: ${response.status}`);
      }
    } catch (err) {
      setError(err.message || '清除缓存时发生错误');
    } finally {
      setIsLoading(false);
    }
  };
  
  // 清除浏览器本地存储
  const clearLocalStorage = () => {
    try {
      localStorage.clear();
      setResult({
        success: true,
        type: 'browser',
        message: '已清除浏览器本地存储'
      });
      
      // 1.5秒后刷新页面
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (err) {
      setError(err.message || '清除浏览器存储时发生错误');
    }
  };
  
  // 格式化结果消息
  const formatResultMessage = (result) => {
    if (!result) return null;
    
    if (result.results) {
      // 如果是组合结果
      return (
        <div>
          <p className="font-medium">{result.message}</p>
          <ul className="mt-2 text-sm">
            {result.results.memory && (
              <li>内存缓存: {result.results.memory.message}</li>
            )}
            {result.results.client && (
              <li>客户端缓存: {result.results.client.message}</li>
            )}
            {result.results.next && (
              <li>Next.js缓存: {result.results.next.message}</li>
            )}
          </ul>
          {(result.results.client?.success || result.type === 'client') && (
            <p className="text-sm mt-2 italic">页面将在1.5秒后刷新...</p>
          )}
        </div>
      );
    }
    
    return (
      <div>
        <p>{result.message}</p>
        {(result.type === 'client' || result.type === 'browser') && (
          <p className="text-sm mt-2 italic">页面将在1.5秒后刷新...</p>
        )}
      </div>
    );
  };
  
  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-4">缓存管理</h2>
      
      <div className="space-y-4">
        {/* 按钮组 */}
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => clearCache('all')}
            disabled={isLoading}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md disabled:opacity-50"
          >
            {isLoading ? '清除中...' : '清除所有缓存'}
          </button>
          
          <button
            onClick={() => clearCache('memory')}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md disabled:opacity-50"
          >
            {isLoading ? '清除中...' : '清除内存缓存'}
          </button>
          
          <button
            onClick={() => clearCache('client')}
            disabled={isLoading}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md disabled:opacity-50"
          >
            {isLoading ? '清除中...' : '清除客户端缓存'}
          </button>
          
          <button
            onClick={() => clearCache('next')}
            disabled={isLoading}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md disabled:opacity-50"
          >
            {isLoading ? '清除中...' : '清除Next.js缓存'}
          </button>
          
          <button
            onClick={clearLocalStorage}
            disabled={isLoading}
            className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-md disabled:opacity-50"
          >
            {isLoading ? '清除中...' : '清除浏览器存储'}
          </button>
        </div>
        
        {/* 结果显示 */}
        {error && (
          <div className="mt-4 p-3 bg-red-100 text-red-800 rounded-md">
            <p className="font-medium">错误</p>
            <p className="text-sm">{error}</p>
          </div>
        )}
        
        {result && (
          <div className={`mt-4 p-3 rounded-md ${result.success ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
            {formatResultMessage(result)}
          </div>
        )}
        
        {/* 缓存说明 */}
        <div className="mt-6 text-sm text-gray-600 dark:text-gray-400">
          <h3 className="font-medium mb-2">缓存类型说明:</h3>
          <ul className="list-disc list-inside space-y-1">
            <li><span className="font-medium">内存缓存</span> - 服务器内存中的数据缓存，重启服务器后会自动清除</li>
            <li><span className="font-medium">客户端缓存</span> - 浏览器中的API响应缓存，用于提高前端性能</li>
            <li><span className="font-medium">Next.js缓存</span> - Next.js框架的数据缓存和页面缓存</li>
            <li><span className="font-medium">浏览器存储</span> - 浏览器的localStorage存储，包含用户设置和会话信息</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CacheCleaner; 