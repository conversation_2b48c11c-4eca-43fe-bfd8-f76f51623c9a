'use client';

import { ReactNode, useState, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import Head from 'next/head';

// Custom session types
interface CustomUser {
  name?: string | null;
  email?: string | null;
  image?: string | null;
  role?: 'admin' | 'editor';
  id?: string;
}

interface CustomSession {
  user?: CustomUser;
  expires: string;
}

interface LayoutProps {
  children: ReactNode;
  title: string;
}

export default function AdminLayout({ children, title }: LayoutProps) {
  const { data: session, status } = useSession() as { data: CustomSession | null; status: string };
  const router = useRouter();
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [language, setLanguage] = useState<string>('zh'); // 默认中文
  const [isClientSide, setIsClientSide] = useState(false);
  const [localSession, setLocalSession] = useState<any>(null);

  // 从路径中提取语言参数
  const currentLang = pathname?.split('/')[1] || 'zh';

  // 确保所有浏览器API调用都在客户端执行
  useEffect(() => {
    setIsClientSide(true);

    if (typeof window !== 'undefined') {
      const storedLang = localStorage.getItem('ADMIN_LANG') || 'zh';
      setLanguage(storedLang);

      // 检查本地会话
      const adminSession = localStorage.getItem('admin_session');
      if (adminSession) {
        try {
          const sessionData = JSON.parse(adminSession);
          if (new Date(sessionData.expires) > new Date()) {
            setLocalSession(sessionData);
          } else {
            localStorage.removeItem('admin_session');
          }
        } catch (error) {
          localStorage.removeItem('admin_session');
        }
      }
    }
  }, []);

  // 切换语言
  const toggleLanguage = () => {
    const newLang = language === 'zh' ? 'en' : 'zh';
    setLanguage(newLang);
    if (typeof window !== 'undefined') {
      localStorage.setItem('ADMIN_LANG', newLang);
    }
  };

  // 获取翻译
  const t = (key: string) => {
    const translations: { [key: string]: { [key: string]: string } } = {
      dashboard: { zh: '控制面板', en: 'Dashboard' },
      products: { zh: '产品管理', en: 'Products' },
      content: { zh: '内容管理', en: 'Content' },
      form_notifications: { zh: '表单通知', en: 'Form Notifications' },
      email_settings: { zh: '邮件设置', en: 'Email Settings' },
      users: { zh: '用户管理', en: 'Users' },
      settings: { zh: '设置', en: 'Settings' },
      admin_dashboard: { zh: '管理员仪表板', en: 'Admin Dashboard' },
      sign_out: { zh: '退出登录', en: 'Sign Out' },
      logout: { zh: '退出登录', en: 'Logout' },
      site_preview: { zh: '网站预览', en: 'Site Preview' },
      back_to_site: { zh: '返回网站', en: 'Back to Site' },
      account: { zh: '账号', en: 'Account' },
      admin: { zh: '管理员', en: 'Admin' },
      language: { zh: '语言', en: 'Language' },
      chinese: { zh: '中文', en: 'Chinese' },
      english: { zh: '英文', en: 'English' },
      navigation_editor: { zh: '导航菜单编辑', en: 'Navigation Menu' },
    };

    return translations[key]?.[language] || key;
  };

  if (!isClientSide) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-xl text-gray-600">加载中...</div>
      </div>
    );
  }

  // 检查是否已认证（NextAuth会话或本地会话）
  const isAuthenticated = localSession || (status === 'authenticated' && session);

  // If not authenticated, redirect to login
  if (!isAuthenticated && status === 'unauthenticated') {
    router.push(`/${currentLang}/admin/login`);
    return null;
  }

  // If still loading authentication and no local session, show loading state
  if (status === 'loading' && !localSession) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-xl">{language === 'zh' ? '加载中...' : 'Loading...'}</div>
      </div>
    );
  }

  // 获取当前用户信息（优先使用本地会话）
  const currentUser = localSession?.user || session?.user;
  const isAdmin = currentUser?.role === 'admin' || currentUser?.role?.includes('admin');

  const handleSignOut = async () => {
    // 清除本地会话
    if (typeof window !== 'undefined') {
      localStorage.removeItem('admin_session');
    }
    
    // 如果是NextAuth会话，也要退出
    if (session) {
      await signOut({ redirect: false });
    }
    
    router.push(`/${currentLang}/admin/login`);
  };

  // 检查路径是否匹配某个模式
  const isActive = (pattern: string) => {
    return pathname ? pathname.startsWith(pattern) : false;
  };

  const menuItems = [
    {
      name: t('dashboard'),
      icon: <i className="fas fa-tachometer-alt"></i>,
      href: `/${currentLang}/admin`,
      active: isActive(`/${currentLang}/admin`) && pathname === `/${currentLang}/admin`,
    },
    {
      name: t('navigation_editor'),
      icon: <i className="fas fa-bars"></i>,
      href: `/${currentLang}/admin/navigation-editor`,
      active: isActive(`/${currentLang}/admin/navigation-editor`),
    },
    {
      name: t('products'),
      icon: <i className="fas fa-box"></i>,
      href: `/${currentLang}/admin/products`,
      active: isActive(`/${currentLang}/admin/products`),
    },

    {
      name: t('content'),
      icon: <i className="fas fa-file-alt"></i>,
      href: `/${currentLang}/admin/content`,
      active: isActive(`/${currentLang}/admin/content`),
    },
    {
      name: t('form_notifications'),
      icon: <i className="fas fa-bell"></i>,
      href: `/${currentLang}/admin/form-notifications`,
      active: isActive(`/${currentLang}/admin/form-notifications`),
    },
    {
      name: t('email_settings'),
      icon: <i className="fas fa-envelope"></i>,
      href: `/${currentLang}/admin/email-settings`,
      active: isActive(`/${currentLang}/admin/email-settings`),
    },
    {
      name: t('users'),
      icon: <i className="fas fa-users"></i>,
      href: `/${currentLang}/admin/users`,
      active: isActive(`/${currentLang}/admin/users`),
      adminOnly: true,
    },
    {
      name: t('settings'),
      icon: <i className="fas fa-cog"></i>,
      href: `/${currentLang}/admin/settings`,
      active: isActive(`/${currentLang}/admin/settings`),
    },
  ];

  return (
    <>
      <Head>
        <title>
          {title} | {t('admin_dashboard')}
        </title>
      </Head>

      <div className="flex h-screen bg-gray-100">
        {/* Sidebar for desktop */}
        <div className="hidden md:flex md:flex-shrink-0">
          <div className="flex flex-col w-64 bg-gray-800">
            <div className="flex items-center justify-center h-16 bg-gray-900">
              <span className="text-white font-bold text-lg">{t('admin_dashboard')}</span>
            </div>
            <div className="flex flex-col flex-grow overflow-y-auto">
              <nav className="flex-1 px-2 py-4 space-y-1">
                {menuItems.map(item => {
                  // Skip admin-only items for non-admin users
                  if (item.adminOnly && !isAdmin) return null;

                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`flex items-center px-4 py-2 text-sm font-medium rounded-md ${
                        item.active
                          ? 'bg-gray-900 text-white'
                          : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                      }`}
                    >
                      <span className="mr-3">{item.icon}</span>
                      {item.name}
                    </Link>
                  );
                })}
              </nav>
              <div className="p-4 border-t border-gray-700">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center text-white">
                      {currentUser?.name?.charAt(0).toUpperCase() || 'U'}
                    </div>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-white">{currentUser?.name}</p>
                    <p className="text-xs font-medium text-gray-400">{currentUser?.role}</p>
                  </div>
                </div>
                <button
                  onClick={handleSignOut}
                  className="mt-3 flex items-center w-full px-4 py-2 text-sm text-gray-300 rounded-md hover:bg-gray-700 hover:text-white"
                >
                  <i className="fas fa-sign-out-alt mr-3"></i>
                  {t('sign_out')}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile sidebar */}
        <div className={`fixed inset-0 z-40 md:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
          <div
            className="fixed inset-0 bg-gray-600 bg-opacity-75"
            onClick={() => setSidebarOpen(false)}
          ></div>
          <div className="fixed inset-y-0 left-0 flex flex-col w-full max-w-xs bg-gray-800">
            <div className="flex items-center justify-between h-16 px-4 bg-gray-900">
              <span className="text-white font-bold text-lg">{t('admin_dashboard')}</span>
              <button
                onClick={() => setSidebarOpen(false)}
                className="text-gray-300 hover:text-white"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            <div className="flex-1 overflow-y-auto">
              <nav className="px-2 py-4 space-y-1">
                {menuItems.map(item => {
                  // Skip admin-only items for non-admin users
                  if (item.adminOnly && !isAdmin) return null;

                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`flex items-center px-4 py-2 text-sm font-medium rounded-md ${
                        item.active
                          ? 'bg-gray-900 text-white'
                          : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                      }`}
                      onClick={() => setSidebarOpen(false)}
                    >
                      <span className="mr-3">{item.icon}</span>
                      {item.name}
                    </Link>
                  );
                })}
              </nav>
              <div className="p-4 border-t border-gray-700">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center text-white">
                      {currentUser?.name?.charAt(0).toUpperCase() || 'U'}
                    </div>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-white">{currentUser?.name}</p>
                    <p className="text-xs font-medium text-gray-400">{currentUser?.role}</p>
                  </div>
                </div>
                <button
                  onClick={handleSignOut}
                  className="mt-3 flex items-center w-full px-4 py-2 text-sm text-gray-300 rounded-md hover:bg-gray-700 hover:text-white"
                >
                  <i className="fas fa-sign-out-alt mr-3"></i>
                  {t('sign_out')}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex flex-col flex-1 overflow-hidden">
          <header className="bg-white shadow-sm">
            <div className="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
              <button
                className="md:hidden text-gray-500 hover:text-gray-700"
                onClick={() => setSidebarOpen(true)}
              >
                <i className="fas fa-bars"></i>
              </button>
              <h1 className="text-xl font-bold text-gray-900">{title}</h1>
              <button
                onClick={toggleLanguage}
                className="flex items-center px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md"
              >
                {language === 'zh' ? '🇬🇧 English' : '🇨🇳 中文'}
              </button>
            </div>
          </header>
          <main className="flex-1 overflow-y-auto p-4 sm:p-6 lg:p-8 bg-gray-100">{children}</main>
        </div>
      </div>
    </>
  );
}
