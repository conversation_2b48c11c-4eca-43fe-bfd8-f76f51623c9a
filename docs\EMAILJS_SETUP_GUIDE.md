# EmailJS 完整配置指南

## 🎯 目标
配置EmailJS直接发送邮件到 <EMAIL>，无需后端SMTP配置

## 📧 第1步: 注册EmailJS账号

1. 访问：https://www.emailjs.com
2. 点击 **"Sign Up"** 注册
3. 使用邮箱：<EMAIL>
4. 验证邮箱并登录

## ⚙️ 第2步: 创建邮件服务 (Email Service)

### 2.1 添加邮件服务
1. 登录后，点击 **"Email Services"**
2. 点击 **"Add New Service"**
3. 选择邮件服务商：

#### 推荐选择：Gmail
- 选择 **"Gmail"**
- 点击 **"Connect Account"**
- 使用您的Gmail账号授权
- 服务ID会自动生成（如：service_abc123）

#### 或选择：Outlook
- 选择 **"Outlook"**
- 使用Microsoft账号授权

### 2.2 记录服务ID
- 创建成功后，记录 **Service ID**（如：service_abc123）

## 📝 第3步: 创建邮件模板 (Email Template)

### 3.1 添加模板
1. 点击 **"Email Templates"**
2. 点击 **"Create New Template"**
3. 选择刚创建的服务

### 3.2 配置模板内容
```html
<!-- 邮件主题 -->
Subject: {{subject}}

<!-- 收件人 -->
To: <EMAIL>

<!-- 邮件内容 -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>新的表单提交</title>
</head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
        <h1 style="margin: 0; font-size: 28px;">🔔 新的表单提交</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px;">来自跨境电商网站</p>
    </div>
    
    <div style="background-color: #ffffff; padding: 30px; border: 1px solid #e1e5e9; border-top: none;">
        <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
            <h2 style="color: #495057; margin-top: 0; margin-bottom: 20px;">👤 客户信息</h2>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 10px 0; font-weight: bold; width: 120px;">姓名:</td>
                    <td style="padding: 10px 0;">{{customer_name}}</td>
                </tr>
                <tr>
                    <td style="padding: 10px 0; font-weight: bold;">邮箱:</td>
                    <td style="padding: 10px 0;">{{customer_email}}</td>
                </tr>
                <tr>
                    <td style="padding: 10px 0; font-weight: bold;">电话:</td>
                    <td style="padding: 10px 0;">{{customer_phone}}</td>
                </tr>
                <tr>
                    <td style="padding: 10px 0; font-weight: bold;">国家:</td>
                    <td style="padding: 10px 0;">{{customer_country}}</td>
                </tr>
                <tr>
                    <td style="padding: 10px 0; font-weight: bold;">场地大小:</td>
                    <td style="padding: 10px 0;">{{playground_size}}</td>
                </tr>
                <tr>
                    <td style="padding: 10px 0; font-weight: bold;">感兴趣的产品:</td>
                    <td style="padding: 10px 0;">{{product_interest}}</td>
                </tr>
            </table>
        </div>

        <div style="background-color: #fff; padding: 25px; border: 2px solid #e9ecef; border-radius: 8px; margin-bottom: 25px;">
            <h2 style="color: #495057; margin-top: 0; margin-bottom: 15px;">💬 客户留言</h2>
            <p style="line-height: 1.8; color: #495057; margin: 0;">{{customer_message}}</p>
        </div>

        <div style="background-color: #f1f3f4; padding: 20px; border-radius: 8px;">
            <p style="margin: 0; color: #6c757d; font-size: 14px;">
                <strong>📅 提交时间:</strong> {{submission_time}}<br>
                <strong>🆔 提交ID:</strong> #{{submission_id}}<br>
                <strong>📧 邮件服务:</strong> EmailJS
            </p>
        </div>
    </div>

    <div style="background-color: #6c757d; color: white; padding: 20px; border-radius: 0 0 10px 10px; text-align: center;">
        <p style="margin: 0; font-size: 14px;">此邮件由跨境电商网站系统通过 EmailJS 服务自动发送</p>
    </div>
</body>
</html>
```

### 3.3 保存模板
- 点击 **"Save"**
- 记录 **Template ID**（如：template_xyz789）

## 🔑 第4步: 获取Public Key

1. 点击 **"Account"** → **"General"**
2. 找到 **"Public Key"**
3. 记录Public Key（如：user_abcdefghijk）

## 📋 第5步: 记录配置信息

请记录以下信息：
- **Service ID**: service_abc123
- **Template ID**: template_xyz789  
- **Public Key**: user_abcdefghijk

## 💻 第6步: 代码集成

### 6.1 安装EmailJS包
```bash
npm install @emailjs/browser
```

### 6.2 更新环境变量
在 `.env.local` 中添加：
```
NEXT_PUBLIC_EMAILJS_SERVICE_ID=service_abc123
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=template_xyz789
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=user_abcdefghijk
```

## 🧪 第7步: 测试发送

使用测试API：
```
POST http://localhost:3000/api/emailjs-test
```

## ⚠️ 重要提醒

### 免费额度限制
- **免费版**: 200封邮件/月
- **付费版**: $15/月起，无限制

### 模板变量
确保模板中的变量名与代码中一致：
- `{{customer_name}}` - 客户姓名
- `{{customer_email}}` - 客户邮箱
- `{{customer_phone}}` - 客户电话
- `{{customer_message}}` - 客户留言
- 等等...

### 收件人设置
在模板的 "To" 字段中直接写：<EMAIL>

## 🔍 故障排除

### 常见问题
1. **邮件未收到**: 检查垃圾邮件文件夹
2. **发送失败**: 检查Service ID、Template ID、Public Key是否正确
3. **模板错误**: 确保所有变量都有对应值

### 调试步骤
1. 在EmailJS控制台查看发送日志
2. 检查浏览器控制台错误信息
3. 验证所有配置参数

## 🎯 优势总结

✅ **无需后端配置** - 前端直接发送
✅ **支持任何邮箱** - 可发送到QQ、Gmail等
✅ **免费额度充足** - 200封/月
✅ **配置简单** - 图形化界面
✅ **模板丰富** - 支持HTML格式
✅ **实时监控** - 发送状态可查看
