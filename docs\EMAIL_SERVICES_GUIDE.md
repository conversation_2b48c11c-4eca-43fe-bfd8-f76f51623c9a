# 邮件服务配置指南

## 🚀 推荐的免费邮件服务

### 1. SendGrid (最推荐)
- **免费额度**: 每月100封邮件
- **优势**: 99%送达率，专业服务，详细统计
- **官网**: https://sendgrid.com

**配置步骤**:
1. 注册SendGrid账号
2. 验证邮箱地址
3. 创建API密钥：Settings → API Keys → Create API Key
4. 在`.env.local`中设置：
   ```
   SENDGRID_API_KEY=SG.your-api-key-here
   ```

### 2. Resend (开发者友好)
- **免费额度**: 每月3000封邮件
- **优势**: 现代化API，React邮件模板支持
- **官网**: https://resend.com

**配置步骤**:
1. 注册Resend账号
2. 创建API密钥
3. 在`.env.local`中设置：
   ```
   RESEND_API_KEY=re_your-api-key-here
   ```

### 3. Mailgun
- **免费额度**: 前3个月每月5000封邮件
- **优势**: 强大的API，邮件验证功能
- **官网**: https://www.mailgun.com

### 4. EmailJS (前端直接发送)
- **免费额度**: 每月200封邮件
- **优势**: 无需后端，直接从前端发送
- **官网**: https://www.emailjs.com

## 🔧 快速配置

### 方案1: SendGrid (推荐)
```bash
# 1. 注册SendGrid账号
# 2. 获取API密钥
# 3. 在.env.local中添加：
SENDGRID_API_KEY=SG.your-sendgrid-api-key
```

### 方案2: Resend
```bash
# 1. 注册Resend账号
# 2. 获取API密钥
# 3. 在.env.local中添加：
RESEND_API_KEY=re_your-resend-api-key
```

### 方案3: 继续使用SMTP (当前)
```bash
# 当前163邮箱配置
SMTP_HOST=smtp.163.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=DYa2xepY9Gk7g3Zn
FROM_EMAIL=<EMAIL>
```

## 🧪 测试邮件发送

### 测试专业邮件服务
```bash
# 访问测试API
GET http://localhost:3000/api/professional-email-test

# 发送测试邮件
POST http://localhost:3000/api/professional-email-test
```

### 测试传统SMTP
```bash
# 发送测试邮件
POST http://localhost:3000/api/simple-email-test
```

## 📊 服务对比

| 服务 | 免费额度 | 送达率 | 配置难度 | 推荐指数 |
|------|----------|--------|----------|----------|
| SendGrid | 100封/月 | 99% | 简单 | ⭐⭐⭐⭐⭐ |
| Resend | 3000封/月 | 98% | 简单 | ⭐⭐⭐⭐⭐ |
| Mailgun | 5000封/月(3个月) | 98% | 中等 | ⭐⭐⭐⭐ |
| EmailJS | 200封/月 | 95% | 简单 | ⭐⭐⭐ |
| 163邮箱 | 无限制 | 85% | 复杂 | ⭐⭐ |

## 🔍 故障排除

### 常见问题
1. **API密钥无效**: 检查密钥是否正确复制
2. **发件人地址未验证**: 在服务商后台验证发件人邮箱
3. **超出免费额度**: 升级到付费计划或更换服务
4. **邮件进入垃圾箱**: 配置SPF、DKIM记录

### 调试步骤
1. 检查环境变量配置
2. 查看服务器日志
3. 测试API连接
4. 验证邮件模板格式

## 🎯 生产环境建议

1. **小型项目**: 使用Resend (3000封/月免费)
2. **中型项目**: 使用SendGrid (稳定可靠)
3. **大型项目**: 使用Mailgun或企业邮件服务
4. **国内项目**: 考虑阿里云邮件推送

## 📞 技术支持

如果遇到配置问题，可以：
1. 查看服务商官方文档
2. 检查API密钥权限设置
3. 联系服务商技术支持
4. 使用备用邮件服务
