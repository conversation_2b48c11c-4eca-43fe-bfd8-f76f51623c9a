# 跨境电商网站

基于Next.js构建的多语言跨境电商平台，提供产品展示、博客、内容管理和管理后台功能。

## 项目特点

- 🌐 多语言支持 (中文、英文)
- 📱 响应式设计，适配各种设备
- 🚀 基于Next.js App Router架构
- 🔒 集成用户认证和权限管理
- 📝 内容管理系统
- 🛒 产品展示和分类功能
- 📊 管理员后台

## 技术栈

- **前端框架**: Next.js (App Router)
- **样式解决方案**: Tailwind CSS
- **数据库**: PostgreSQL (通过Neon Serverless)
- **认证**: NextAuth.js
- **部署**: Vercel/自托管

## 开始使用

### 环境要求

- Node.js 18.x 或更高版本
- npm 或 yarn

### 安装步骤

1. 克隆仓库

```bash
git clone [仓库URL]
cd nextjs-app
```

2. 安装依赖

```bash
npm install
# 或
yarn install
```

3. 配置环境变量

复制环境变量示例文件并进行配置：

```bash
cp .env.example .env.local
```

在`.env.local`文件中填写必要的环境变量。

4. 初始化数据库

```bash
npm run init-db
```

5. 启动开发服务器

```bash
npm run dev
```

应用将在 [http://localhost:3000](http://localhost:3000) 运行。

## 项目结构

详细的项目结构说明请参见[项目结构文档](docs/project-structure.md)。

## 常用命令

- `npm run dev` - 启动开发服务器
- `npm run build` - 构建生产版本
- `npm run start` - 启动生产服务器
- `npm run lint` - 运行ESLint检查
- `npm run migrate-api` - 迁移API路由
- `npm run migrate-pages` - 迁移页面路由
- `npm run check-routes` - 检查路由冲突
- `npm run reorganize-assets` - 重组产品资源

## 开发指南

### 添加新页面

在`app/[lang]/`目录下创建相应的目录和`page.tsx`文件。例如，添加"关于我们"页面：

```tsx
// app/[lang]/about-us/page.tsx
export default function AboutUs() {
  return (
    <div>
      <h1>关于我们</h1>
      {/* 页面内容 */}
    </div>
  );
}
```

### 添加新API端点

在`app/api/`目录下创建相应的目录和`route.ts`文件。例如，添加产品API：

```tsx
// app/api/products/route.ts
import { NextResponse } from 'next/server';

export async function GET() {
  // 获取产品列表
  return NextResponse.json({ products: [] });
}
```

### 添加新组件

共享组件应放在`components/`目录中。例如：

```tsx
// components/ProductCard.tsx
export default function ProductCard({ product }) {
  return (
    <div className="card">
      <h3>{product.name}</h3>
      <p>{product.description}</p>
    </div>
  );
}
```

## 部署

### Vercel部署

1. 将代码推送到GitHub仓库
2. 在Vercel上导入项目
3. 配置环境变量
4. 部署

### 自托管部署

```bash
npm run build
npm run start
```

## 贡献指南

1. Fork仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送分支 (`git push origin feature/amazing-feature`)
5. 提交Pull Request

## 许可证

[MIT License](LICENSE)

## 项目重构

该项目已经完成了从混合路由（Pages Router + App Router）到纯App Router的重构：

1. 解决了路由冗余问题
   - 移除了所有pages/api目录下的API路由
   - 使用App Router的Route Handlers替代旧的API路由

2. 优化了项目结构
   - 所有API路由统一在app/api目录下
   - 所有页面路由统一在app/[lang]目录下
   - 规范化了静态资源目录

3. 简化了开发流程
   - 统一使用App Router的路由和API处理方式
   - 减少了维护两套路由系统的复杂性

详细迁移信息请参阅 [路由迁移总结](./docs/route-migration-summary.md)。
