# 🛒 香港服务器购买指南

## 🎯 推荐购买顺序

### 第一选择：阿里云香港 (最推荐)
**购买链接**: https://ecs-buy.aliyun.com/

#### 配置选择：
1. **地域**: 香港
2. **实例规格**: 
   - 入门: ecs.c6.large (2核4GB) ¥280/月
   - 推荐: ecs.c6.xlarge (4核8GB) ¥400/月
3. **镜像**: Ubuntu 22.04 LTS
4. **存储**: 80GB SSD
5. **网络**: 按固定带宽 5Mbps

#### 购买步骤：
```
1. 注册阿里云账户
2. 实名认证 (企业/个人)
3. 选择香港地域
4. 配置服务器规格
5. 设置密码和安全组
6. 支付 (支持支付宝/微信)
```

### 第二选择：腾讯云香港
**购买链接**: https://buy.cloud.tencent.com/cvm

#### 优势：
- 价格稍便宜 (¥350/月)
- 同样免备案
- 中文支持

### 第三选择：DigitalOcean 新加坡
**购买链接**: https://www.digitalocean.com/

#### 优势：
- 价格便宜 ($40/月)
- 国际化平台
- 按小时计费

## 💳 支付方式

### 阿里云支付：
- ✅ 支付宝
- ✅ 微信支付
- ✅ 银行卡
- ✅ 企业转账

### 国际平台支付：
- ✅ 信用卡 (Visa/MasterCard)
- ✅ PayPal
- ✅ 银行转账

## 🔧 购买后立即配置

### 1. 安全组配置
```bash
# 开放端口
- 22 (SSH)
- 80 (HTTP)
- 443 (HTTPS)
- 3000 (Next.js开发)
```

### 2. 域名配置
```bash
# 购买域名 (推荐)
- .com (国际通用)
- .net (技术类)
- .shop (电商类)
```

### 3. SSL证书
```bash
# 免费SSL证书
- Let's Encrypt (免费)
- 阿里云SSL (免费版)
- Cloudflare SSL (免费)
```

## 📞 技术支持

### 阿里云技术支持：
- **工单系统**: 24小时响应
- **电话支持**: 400-601-0101
- **在线客服**: 实时聊天
- **文档中心**: 详细中文文档

### 社区支持：
- **阿里云开发者社区**
- **Stack Overflow**
- **GitHub Issues**

## 🚀 快速部署脚本

购买服务器后，使用此脚本快速部署：

```bash
#!/bin/bash
# 香港服务器快速部署脚本

echo "🚀 开始配置香港服务器..."

# 更新系统
apt update && apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com | sh
systemctl start docker
systemctl enable docker

# 安装Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# 配置防火墙
ufw allow 22
ufw allow 80
ufw allow 443
ufw --force enable

# 创建项目目录
mkdir -p /var/www/ecommerce
cd /var/www/ecommerce

echo "✅ 服务器配置完成！"
echo "📋 下一步："
echo "1. 克隆您的项目代码"
echo "2. 配置域名解析"
echo "3. 申请SSL证书"
echo "4. 启动应用"
```

## 💰 费用优化建议

### 节省成本的方法：
1. **按量付费**: 选择按量付费模式
2. **预付费折扣**: 年付可享受折扣
3. **资源包**: 购买流量包更便宜
4. **监控用量**: 设置费用告警

### 费用预算 (月费用)：
```
💰 最低配置: ¥280/月
   - 2核4GB香港服务器
   - 基础带宽
   - 无数据库 (使用JSON)

💰 推荐配置: ¥620/月
   - 4核8GB香港服务器
   - PostgreSQL数据库
   - CDN加速
   - 对象存储

💰 高配版本: ¥1200/月
   - 8核16GB服务器
   - 高可用数据库
   - 全球CDN
   - 负载均衡
```

## 🎯 立即行动

### 今天就可以开始：
1. **注册阿里云账户**: https://account.aliyun.com/
2. **完成实名认证**: 上传身份证/营业执照
3. **购买香港服务器**: 选择4核8GB配置
4. **配置安全组**: 开放必要端口
5. **部署您的项目**: 使用Docker部署

### 预计时间：
- **购买服务器**: 30分钟
- **配置环境**: 1小时
- **部署应用**: 2小时
- **域名配置**: 1小时
- **总计**: 半天内完成上线

**您的跨境电商独立站很快就能面向全球用户了！** 🌍
