# 路由迁移计划

## 概述

该计划旨在将项目从混合的 Pages Router 和 App Router 路由系统迁移到统一的 App Router 架构。同时，处理数据库连接问题和其他架构问题。

## 1. 数据库相关改进

### 完成的任务

- [x] 删除 `lib/mongodb.ts` - MongoDB 连接文件
- [x] 创建 `models/CategoryPg.ts` - PostgreSQL 版本的分类模型

### 待处理任务

- [ ] 删除其余 MongoDB 模型文件 (Product.ts, User.ts, Content.ts)
- [ ] 创建对应的 PostgreSQL 模型文件 (UserPg.ts, ContentPg.ts)
- [ ] 更新数据库连接超时设置，解决连接超时问题

## 2. 路由迁移

### API 路由迁移计划

- [ ] 将 `pages/api/products/index.ts` 迁移到 `app/api/products/route.ts`
- [ ] 将 `pages/api/products/[id].ts` 迁移到 `app/api/products/[id]/route.ts`
- [ ] 将 `pages/api/products/by-slug` 目录下内容迁移到 `app/api/products/by-slug`
- [ ] 将 `pages/api/categories` 目录下内容迁移到 `app/api/categories`
- [ ] 将 `pages/api/auth` 目录下内容迁移到 `app/api/auth`
- [ ] 将 `pages/api/users` 目录下内容迁移到 `app/api/users`
- [ ] 将 `pages/api/content` 目录下内容迁移到 `app/api/content`
- [ ] 将 `pages/api/admin` 目录下内容迁移到 `app/api/admin`

### 页面迁移计划

- [ ] 将 `pages/admin` 目录下内容迁移到 `app/admin` 或 `app/[lang]/admin`
- [ ] 删除 `pages/_app.tsx` 和 `pages/_document.tsx`

## 3. 文件组织优化

### 待处理任务

- [ ] 将中文目录 `产品/` 重命名为 `products-assets/` 并适当重组
- [ ] 简化目录结构，减少嵌套层级
  - [ ] 整合 `app/[lang]/pages` 下的内容到更合理的结构
  - [ ] 移除不必要的嵌套目录

## 4. 国际化改进

### 待处理任务

- [ ] 审查并移除硬编码的文本
  - [ ] HeroSlider 组件中的"了解更多"和"Learn More"按钮文本
  - [ ] 其他组件中的硬编码文本
- [ ] 确保所有文本都使用i18n字典

## 5. Webpack配置优化

### 待处理任务

- [ ] 研究并修复 cloudflare:sockets 相关的问题
- [ ] 优化 webpack 配置，提高构建性能

## 实施顺序

1. 首先解决数据库连接问题，确保应用能够稳定连接到PostgreSQL
2. 迁移API路由，确保后端功能正常
3. 迁移页面组件，完成前端转换
4. 优化文件组织和目录结构
5. 改进国际化实现
6. 优化webpack配置
