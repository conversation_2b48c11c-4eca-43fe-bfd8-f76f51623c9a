# 项目结构说明文档

## 概述

本项目是一个基于Next.js的多语言电商网站，使用App Router架构。项目经过重构，现在具有更加清晰和标准化的目录结构。

## 主要目录结构

```
nextjs-app/
├── app/                  # Next.js App Router目录
│   ├── [lang]/           # 多语言路由（动态参数）
│   ├── api/              # API路由（全部迁移自pages/api）
│   ├── components/       # 应用级组件
│   └── dictionaries/     # 多语言词典文件
├── backup/               # 备份文件目录
│   └── pages-api/        # 原始Pages Router API文件备份
├── components/           # 共享组件
├── lib/                  # 工具库和辅助函数
├── models/               # 数据模型
├── pages/                # 旧Pages Router（API已迁移到app/api）
├── public/               # 静态资源（图片、字体等）
├── products-assets/      # 产品资源（英文目录，由"产品"目录重构而来）
├── 产品/                 # 原始产品素材（中文目录，作为素材保留）
├── scripts/              # 工具脚本和辅助程序
└── styles/               # 全局样式
```

## 重要文件

- `next.config.js`: Next.js配置文件
- `package.json`: 项目依赖和脚本
- `tailwind.config.js`: Tailwind CSS配置
- `middleware.ts`: Next.js中间件（处理国际化等）

## 路由结构

### 页面路由

- `/[lang]/...`: 所有公开页面，支持多语言
- `/[lang]/admin/...`: 管理后台，已从Pages Router迁移

### API路由

**注意：所有API路由已经完全迁移到App Router，并且原始Pages Router API文件已移动到backup/pages-api目录。**

- `/api/products/...`: 产品相关API
- `/api/categories/...`: 分类相关API
- `/api/users/...`: 用户相关API
- `/api/content/...`: 内容相关API
- `/api/auth/...`: 认证相关API
- `/api/admin/...`: 管理相关API
- `/api/db-test/...`: 数据库测试相关API
- `/api/upload/`: 文件上传API

## 资源管理

- `public/`: 所有静态资源，遵循Next.js约定
- `products-assets/`: 结构化的产品资源库（英文目录名）
  - `interactive/`: 互动类产品
  - `entertainment/`: 娱乐类产品
  - `virtual-reality/`: 虚拟现实类产品
- `产品/`: 原始产品素材（保留作为参考）

## 国际化实现

项目使用`[lang]`动态路由参数实现多语言支持:

- 支持的语言: `en`(英文), `zh`(中文)
- 语言文件位于: `app/dictionaries/`目录

## 脚本工具

项目包含多个有用的辅助脚本:

- `npm run migrate-api`: 迁移API路由
- `npm run migrate-pages`: 迁移页面路由
- `npm run check-routes`: 检查路由冲突
- `npm run organize-scripts`: 整理脚本文件
- `npm run reorganize-assets`: 重组产品资源
- `npm run normalize-public`: 规范化公共资源目录
- `npm run setup-linting`: 设置ESLint和Prettier
- `npm run remove-duplicates`: 移除重复的API路由文件
- `npm run format`: 格式化代码

## 开发指南

### 添加新页面

添加新页面时，请在`app/[lang]/`下创建相应的目录和`page.tsx`文件。

### 添加新API

添加新API端点时，请在`app/api/`下创建相应的目录和`route.ts`文件。不要在`pages/api`目录下创建新的API文件。

### 添加新组件

共享组件应放在`components/`目录中，而特定于页面的组件应放在对应页面目录下的`components/`中。

### 添加新静态资源

所有静态资源应放在`public/`目录中，遵循Next.js的约定。

## 部署说明

项目支持两种部署方式:

1. **服务器渲染模式**: 使用`next build`和`next start`
2. **静态导出模式**: 使用`next build`和`next export`（配置见`next-static-export.config.js`）

## 注意事项

- 项目已完全迁移到App Router架构：
  - 页面已迁移到`app/[lang]/`目录下
  - API路由已迁移到`app/api/`目录下
  - 原Pages Router API文件已备份到`backup/pages-api/`目录
- 原始中文`产品`目录仍作为素材保留，但新内容应添加到`products-assets`英文目录
- 所有静态资源均已移至标准的`public`目录
