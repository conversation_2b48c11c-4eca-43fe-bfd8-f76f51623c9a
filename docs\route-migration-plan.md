# Next.js路由迁移计划

## 当前状态

项目当前同时使用两套路由系统:

1. **App Router** (`app/` 目录): 新版Next.js路由系统，支持服务器组件、嵌套布局等
2. **Pages Router** (`pages/` 目录): 传统Next.js路由系统，主要用于管理后台和部分API

API路由分散在两个地方:

- `app/api/`: App Router的Route Handlers
- `pages/api/`: Pages Router的API Routes

## 迁移目标

将所有路由统一到**App Router**系统，使项目架构更加一致、维护更简单。

## 迁移步骤

### 1. API路由迁移

| 原路径                               | 目标路径                                 | 状态         |
| ------------------------------------ | ---------------------------------------- | ------------ |
| `/pages/api/categories/*`            | `/app/api/categories/*`                  | 待迁移       |
| `/pages/api/users/*`                 | `/app/api/users/*`                       | 待迁移       |
| `/pages/api/products/*`              | `/app/api/products/*`                    | 待迁移       |
| `/pages/api/content/*`               | `/app/api/content/*`                     | 待迁移       |
| `/pages/api/auth/*`                  | `/app/api/auth/*`                        | 已存在(合并) |
| `/pages/api/admin/*`                 | `/app/api/admin/*`                       | 已存在(合并) |
| `/pages/api/create-default-admin.ts` | `/app/api/admin/create-default/route.ts` | 待迁移       |
| `/pages/api/upload.ts`               | `/app/api/upload/route.ts`               | 待迁移       |
| `/pages/api/test-mongodb.ts`         | `/app/api/db-test/mongodb/route.ts`      | 待迁移       |
| `/pages/api/products-import.ts`      | `/app/api/products/import/route.ts`      | 待迁移       |
| `/pages/api/raw-file.ts`             | `/app/api/content/raw-file/route.ts`     | 待迁移       |
| `/pages/api/check-db.ts`             | `/app/api/db-test/check/route.ts`        | 待迁移       |
| `/pages/api/test-db.ts`              | `/app/api/db-test/route.ts`              | 待迁移       |

### 2. 页面路由迁移

| 原路径           | 目标路径              | 状态   |
| ---------------- | --------------------- | ------ |
| `/pages/admin/*` | `/app/[lang]/admin/*` | 待迁移 |

### 3. 迁移注意事项

1. **API处理差异**:

   - Pages API: 导出函数处理请求
   - App API: 使用命名HTTP方法函数(GET, POST等)

2. **数据获取方式**:

   - Pages: `getServerSideProps`, `getStaticProps`
   - App: React Server Components, `fetch` 原生支持

3. **布局处理**:

   - Pages: 使用`_app.js`和组件嵌套
   - App: 使用`layout.tsx`文件

4. **国际化路由**:
   - 统一使用`app/[lang]/`动态路由参数方式
   - 移除`app/en/`和`app/zh/`的冗余实现

## 迁移优先级

1. 首先迁移API路由(保证数据访问一致性)
2. 然后迁移管理后台页面
3. 最后清理冗余文件和代码

## 迁移验证

每完成一部分迁移，确保:

1. 功能测试通过
2. 性能没有下降
3. 路由能正确解析和导航
4. 多语言支持正常工作

## 完成后操作

1. 移除`pages`目录
2. 更新`next.config.js`中的相关配置
3. 更新项目文档
