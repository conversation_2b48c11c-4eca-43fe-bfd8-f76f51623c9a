const fs = require('fs');

console.log('=== 修复产品数据语言一致性问题 ===\n');

// 读取产品数据
const jsonPath = './public/mock-products.json';
let products = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));

console.log(`开始修复 ${products.length} 个产品的语言一致性问题...\n`);

let fixedCount = 0;

// 修复每个产品的语言问题
products.forEach((product, index) => {
  let hasChanges = false;
  
  // 修复儿童产品的英文特性列表 (产品ID: 19, 20, 26, 27)
  if ([19, 20, 26, 27].includes(product.id)) {
    if (product.features_en && product.features_en.some(f => /[\u4e00-\u9fa5]/.test(f))) {
      product.features_en = [
        'Child-Friendly Design',
        'Safety Features',
        'Educational Games',
        'Growth Tracking',
        'Parent-Child Interaction'
      ];
      hasChanges = true;
      console.log(`✓ 修复产品 ${product.id} (${product.name}) 的英文特性列表`);
    }
  }
  
  // 修复全息产品的英文特性列表 (产品ID: 21, 22, 23, 24, 28, 29, 30, 31)
  if ([21, 22, 23, 24, 28, 29, 30, 31].includes(product.id)) {
    if (product.features_en && product.features_en.some(f => /[\u4e00-\u9fa5]/.test(f))) {
      product.features_en = [
        'Holographic Projection',
        'Stunning Visual Effects', 
        'HD Display',
        'Remote Control',
        'Multi-scenario Application'
      ];
      hasChanges = true;
      console.log(`✓ 修复产品 ${product.id} (${product.name}) 的英文特性列表`);
    }
  }
  
  // 修复AR产品的中文特性列表 (产品ID: 5, 13)
  if ([5, 13].includes(product.id)) {
    if (product.features && product.features.includes('AR技术')) {
      const newFeatures = [...product.features];
      const arIndex = newFeatures.indexOf('AR技术');
      if (arIndex !== -1) {
        newFeatures[arIndex] = '增强现实技术';
        product.features = newFeatures;
        hasChanges = true;
        console.log(`✓ 修复产品 ${product.id} (${product.name}) 的中文特性列表 - 将"AR技术"改为"增强现实技术"`);
      }
    }
  }
  
  if (hasChanges) {
    fixedCount++;
  }
});

// 保存修复后的数据
if (fixedCount > 0) {
  fs.writeFileSync(jsonPath, JSON.stringify(products, null, 2));
  console.log(`\n✅ 修复完成！共修复了 ${fixedCount} 个产品的语言一致性问题。`);
} else {
  console.log(`\n✅ 所有产品的语言一致性都正确，无需修复。`);
}

console.log('\n=== 剩余的描述问题需要手动检查 ===');
console.log('以下产品的描述开头包含英文缩写，需要确认是否合理:');

const descriptionIssues = [
  { id: 2, name: 'AR体感蹦床', issue: '以"AR"开头' },
  { id: 5, name: 'AR教育系统', issue: '包含"AR"' },
  { id: 6, name: 'KTV互动系统', issue: '以"KTV"开头' },
  { id: 10, name: 'AR体感蹦床专业版', issue: '以"AR"开头' },
  { id: 13, name: 'AR教育专业版', issue: '包含"AR"' },
  { id: 14, name: 'KTV系统专业版', issue: '以"KTV"开头' },
  { id: 40, name: 'AR沙桌', issue: '以"AR"开头' },
  { id: 59, name: 'CAVE空间', issue: '以"CAVE"开头' },
  { id: 63, name: 'AR粒子人', issue: '以"AR"开头' },
  { id: 67, name: 'AR智慧教室', issue: '以"AR"开头' }
];

descriptionIssues.forEach(item => {
  console.log(`- 产品 ${item.id}: ${item.name} (${item.issue})`);
});

console.log('\n这些英文缩写通常是技术术语，在中文描述中使用是合理的:');
console.log('- AR = 增强现实 (Augmented Reality)');
console.log('- KTV = 卡拉OK');
console.log('- CAVE = 洞穴型虚拟现实系统');
console.log('\n建议保持现状，这些缩写在行业内是标准用法。');