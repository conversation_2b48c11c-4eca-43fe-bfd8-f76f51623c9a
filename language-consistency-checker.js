const fs = require('fs');
const path = require('path');

console.log('=== 语言一致性检查器 ===\n');

// 读取产品数据
const jsonPath = './public/mock-products.json';
const products = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));

console.log(`检查 ${products.length} 个产品的语言一致性...\n`);

let issues = [];

// 检查产品数据中的语言问题
products.forEach(product => {
  const productIssues = [];
  
  // 检查中文字段是否包含英文内容
  if (product.name && /[a-zA-Z]/.test(product.name) && !/^[a-zA-Z0-9\s\-_]+$/.test(product.name)) {
    if (product.name.includes('V2') || product.name.includes('AR') || product.name.includes('3D') || product.name.includes('KTV') || product.name.includes('CAVE')) {
      // 这些是合理的英文缩写，跳过
    } else {
      productIssues.push(`中文名称包含英文: "${product.name}"`);
    }
  }
  
  if (product.description && /^[a-zA-Z]/.test(product.description)) {
    productIssues.push(`中文描述以英文开头: "${product.description.substring(0, 50)}..."`);
  }
  
  if (product.category && /^[a-zA-Z]/.test(product.category)) {
    productIssues.push(`中文分类是英文: "${product.category}"`);
  }
  
  // 检查英文字段是否包含中文内容
  if (product.name_en && /[\u4e00-\u9fa5]/.test(product.name_en)) {
    productIssues.push(`英文名称包含中文: "${product.name_en}"`);
  }
  
  if (product.description_en && /[\u4e00-\u9fa5]/.test(product.description_en)) {
    productIssues.push(`英文描述包含中文: "${product.description_en.substring(0, 50)}..."`);
  }
  
  if (product.category_en && /[\u4e00-\u9fa5]/.test(product.category_en)) {
    productIssues.push(`英文分类包含中文: "${product.category_en}"`);
  }
  
  // 检查features数组
  if (product.features) {
    product.features.forEach((feature, index) => {
      if (/^[a-zA-Z]/.test(feature)) {
        productIssues.push(`中文特性 ${index + 1} 是英文: "${feature}"`);
      }
    });
  }
  
  if (product.features_en) {
    product.features_en.forEach((feature, index) => {
      if (/[\u4e00-\u9fa5]/.test(feature)) {
        productIssues.push(`英文特性 ${index + 1} 包含中文: "${feature}"`);
      }
    });
  }
  
  // 检查style字段
  if (product.style && /^[a-zA-Z]/.test(product.style)) {
    productIssues.push(`中文风格是英文: "${product.style}"`);
  }
  
  if (product.style_en && /[\u4e00-\u9fa5]/.test(product.style_en)) {
    productIssues.push(`英文风格包含中文: "${product.style_en}"`);
  }
  
  // 检查是否缺少对应的语言字段
  if (product.name && !product.name_en) {
    productIssues.push('缺少英文名称 (name_en)');
  }
  
  if (product.description && !product.description_en) {
    productIssues.push('缺少英文描述 (description_en)');
  }
  
  if (product.category && !product.category_en) {
    productIssues.push('缺少英文分类 (category_en)');
  }
  
  if (product.features && !product.features_en) {
    productIssues.push('缺少英文特性列表 (features_en)');
  }
  
  if (product.style && !product.style_en) {
    productIssues.push('缺少英文风格 (style_en)');
  }
  
  if (productIssues.length > 0) {
    issues.push({
      id: product.id,
      name: product.name || product.name_en || 'Unknown',
      slug: product.slug,
      issues: productIssues
    });
  }
});

// 输出检查结果
if (issues.length === 0) {
  console.log('✅ 所有产品的语言一致性都正确！');
} else {
  console.log(`❌ 发现 ${issues.length} 个产品存在语言一致性问题:\n`);
  
  issues.forEach(item => {
    console.log(`产品 ID ${item.id}: ${item.name} (${item.slug})`);
    item.issues.forEach(issue => {
      console.log(`  - ${issue}`);
    });
    console.log();
  });
}

// 生成修复建议
console.log('\n=== 修复建议 ===');
console.log('1. 确保中文字段（name, description, category, features, style）只包含中文内容');
console.log('2. 确保英文字段（name_en, description_en, category_en, features_en, style_en）只包含英文内容');
console.log('3. 补全缺失的对应语言字段');
console.log('4. 检查特殊缩写（如AR、3D、VR、KTV）的使用是否合理');

// 检查组件文件中的硬编码文本
console.log('\n=== 检查组件文件中的硬编码文本 ===');

const componentsDir = './app/components';
const pagesDir = './app';

function checkFileForMixedLanguage(filePath, relativePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const fileIssues = [];
    
    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      
      // 跳过注释行
      if (trimmedLine.startsWith('//') || trimmedLine.startsWith('/*') || trimmedLine.startsWith('*')) {
        return;
      }
      
      // 检查是否同时包含中英文
      const hasChinese = /[\u4e00-\u9fa5]/.test(line);
      const hasEnglish = /[a-zA-Z]/.test(line);
      
      if (hasChinese && hasEnglish) {
        // 排除一些常见的合理情况
        if (line.includes('className') || 
            line.includes('import') || 
            line.includes('export') || 
            line.includes('console.log') ||
            line.includes('useState') ||
            line.includes('useEffect') ||
            line.includes('src=') ||
            line.includes('href=') ||
            line.includes('alt=') ||
            line.includes('key=') ||
            line.includes('id=') ||
            line.includes('onClick') ||
            line.includes('onChange') ||
            line.includes('onSubmit') ||
            line.includes('type=') ||
            line.includes('value=') ||
            line.includes('placeholder=') ||
            line.includes('props.') ||
            line.includes('const ') ||
            line.includes('let ') ||
            line.includes('var ') ||
            line.includes('function ') ||
            line.includes('return ') ||
            line.includes('if (') ||
            line.includes('else ') ||
            line.includes('API') ||
            line.includes('JSON') ||
            line.includes('URL') ||
            line.includes('HTTP')) {
          return;
        }
        
        fileIssues.push(`第 ${index + 1} 行: ${trimmedLine}`);
      }
    });
    
    if (fileIssues.length > 0) {
      console.log(`\n文件: ${relativePath}`);
      fileIssues.slice(0, 5).forEach(issue => { // 只显示前5个问题
        console.log(`  ${issue}`);
      });
      if (fileIssues.length > 5) {
        console.log(`  ... 还有 ${fileIssues.length - 5} 个类似问题`);
      }
    }
    
  } catch (error) {
    // 忽略读取错误
  }
}

function scanDirectory(dir, baseDir = '') {
  try {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const fullPath = path.join(dir, file);
      const relativePath = path.join(baseDir, file);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // 跳过一些目录
        if (!file.startsWith('.') && file !== 'node_modules' && file !== 'build') {
          scanDirectory(fullPath, relativePath);
        }
      } else if (file.endsWith('.tsx') || file.endsWith('.jsx') || file.endsWith('.ts') || file.endsWith('.js')) {
        checkFileForMixedLanguage(fullPath, relativePath);
      }
    });
  } catch (error) {
    // 忽略目录扫描错误
  }
}

if (fs.existsSync(componentsDir)) {
  scanDirectory(componentsDir, 'app/components');
}

// 检查一些关键页面文件
const keyFiles = [
  './app/layout.tsx',
  './app/page.tsx',
  './app/[lang]/layout.tsx',
  './app/[lang]/page.tsx',
  './app/[lang]/products/page.tsx',
  './app/components/Header.tsx',
  './app/components/Footer.tsx',
  './app/components/ProductGrid.tsx',
  './app/components/ProductCard.tsx'
];

keyFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    checkFileForMixedLanguage(filePath, filePath);
  }
});

console.log('\n=== 检查完成 ===');
console.log('建议:');
console.log('1. 使用国际化(i18n)系统来管理所有用户界面文本');
console.log('2. 将硬编码的中文文本移动到字典文件中');
console.log('3. 确保组件根据当前语言设置显示相应的文本');
console.log('4. 检查语言切换功能是否正常工作');