/**
 * 缓存清理工具
 * 用于清除各种缓存数据
 */
import { memoryCache, clientCache } from '@/lib/cache';

/**
 * 清除所有内存缓存
 * @returns {Object} 清除结果
 */
export function clearMemoryCache() {
  console.log('[缓存清理] 开始清除内存缓存...');
  
  // 清除前的缓存键数量
  const beforeCount = memoryCache ? memoryCache.size() : 0;
  
  try {
    // 清除内存缓存
    if (memoryCache && typeof memoryCache.clear === 'function') {
      memoryCache.clear();
      console.log('[缓存清理] 已清除内存缓存');
    } else {
      console.warn('[缓存清理] 内存缓存对象不可用');
    }
    
    return {
      success: true,
      type: 'memory',
      clearedItems: beforeCount,
      message: `已清除${beforeCount}项内存缓存`
    };
  } catch (error) {
    console.error('[缓存清理] 清除内存缓存时出错:', error);
    return {
      success: false,
      type: 'memory',
      error: error.message
    };
  }
}

/**
 * 清除客户端缓存（localStorage）
 * @returns {Object} 清除结果
 */
export function clearClientCache() {
  console.log('[缓存清理] 开始清除客户端缓存...');
  
  try {
    // 清除客户端缓存
    if (clientCache && typeof clientCache.clear === 'function') {
      clientCache.clear();
      console.log('[缓存清理] 已清除客户端缓存');
    } else if (typeof localStorage !== 'undefined') {
      // 如果clientCache不可用，尝试直接清除localStorage中的缓存项
      const cachePrefix = 'app_cache_';
      const cacheKeys = Object.keys(localStorage)
        .filter(key => key.startsWith(cachePrefix));
      
      cacheKeys.forEach(key => localStorage.removeItem(key));
      console.log(`[缓存清理] 已清除${cacheKeys.length}项客户端缓存`);
      
      return {
        success: true,
        type: 'client',
        clearedItems: cacheKeys.length,
        message: `已清除${cacheKeys.length}项客户端缓存`
      };
    } else {
      console.warn('[缓存清理] 客户端缓存对象不可用');
      return {
        success: false,
        type: 'client',
        error: '客户端缓存对象不可用'
      };
    }
    
    return {
      success: true,
      type: 'client',
      message: '已清除客户端缓存'
    };
  } catch (error) {
    console.error('[缓存清理] 清除客户端缓存时出错:', error);
    return {
      success: false,
      type: 'client',
      error: error.message
    };
  }
}

/**
 * 清除Next.js数据缓存
 * @returns {Promise<Object>} 清除结果
 */
export async function clearNextCache() {
  console.log('[缓存清理] 开始清除Next.js数据缓存...');
  
  try {
    // 尝试通过重新验证API路径来清除Next.js缓存
    const revalidateEndpoints = [
      '/api/products',
      '/api/categories/featured',
      '/api/products/by-slug/ktv',
      '/api/products/by-slug/ar-trampoline',
      '/api/products/by-slug/3d-sandbox'
    ];
    
    const results = await Promise.allSettled(
      revalidateEndpoints.map(async endpoint => {
        try {
          // 添加时间戳参数避免浏览器缓存
          const response = await fetch(`${endpoint}?t=${Date.now()}`, {
            method: 'GET',
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });
          
          return {
            endpoint,
            status: response.status,
            ok: response.ok
          };
        } catch (err) {
          return {
            endpoint,
            error: err.message
          };
        }
      })
    );
    
    const successCount = results.filter(r => r.status === 'fulfilled' && r.value.ok).length;
    
    console.log(`[缓存清理] 已刷新${successCount}/${revalidateEndpoints.length}个Next.js数据缓存`);
    
    return {
      success: true,
      type: 'next',
      refreshedEndpoints: successCount,
      totalEndpoints: revalidateEndpoints.length,
      message: `已刷新${successCount}/${revalidateEndpoints.length}个Next.js数据缓存`
    };
  } catch (error) {
    console.error('[缓存清理] 清除Next.js数据缓存时出错:', error);
    return {
      success: false,
      type: 'next',
      error: error.message
    };
  }
}

/**
 * 清除所有缓存
 * @returns {Promise<Object>} 清除结果
 */
export async function clearAllCaches() {
  console.log('[缓存清理] 开始清除所有缓存...');
  
  const memoryResult = clearMemoryCache();
  const clientResult = clearClientCache();
  const nextResult = await clearNextCache();
  
  const success = memoryResult.success && clientResult.success && nextResult.success;
  
  return {
    success,
    results: {
      memory: memoryResult,
      client: clientResult,
      next: nextResult
    },
    message: success ? '已成功清除所有缓存' : '清除缓存过程中出现错误'
  };
}

// 导出默认清除所有缓存的函数
export default clearAllCaches; 