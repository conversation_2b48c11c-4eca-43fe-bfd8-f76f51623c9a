/**
 * 缓存模块 - 提供服务器端和客户端缓存功能
 */

// 服务器端内存缓存对象
class MemoryCache {
  constructor() {
    this.cache = new Map();
    this.defaultTTL = 10 * 60 * 1000; // 默认缓存10分钟
    this.lruList = []; // 新增: LRU列表，按使用顺序存储键
    this.maxSize = 1000; // 新增: 最大缓存项数量
    this.useLRU = true; // 新增: 使用LRU算法
    
    // 新增: 性能统计
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      lastPrune: Date.now()
    };
    
    // 新增: 自动定期清理过期缓存
    if (typeof setInterval !== 'undefined') {
      this.pruneIntervalId = setInterval(() => {
        this.prune();
      }, 60000); // 每分钟清理一次
    }
  }

  /**
   * 设置缓存项
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {number} ttl - 过期时间(毫秒)，默认10分钟
   */
  set(key, value, ttl = this.defaultTTL) {
    const expiry = Date.now() + ttl;
    this.cache.set(key, { value, expiry });
    this.stats.sets++;
    
    // 处理LRU
    if (this.useLRU) {
      // 如果键已存在于LRU列表中，先移除
      const existingIndex = this.lruList.indexOf(key);
      if (existingIndex >= 0) {
        this.lruList.splice(existingIndex, 1);
      }
      
      // 添加到LRU列表末尾（表示最近使用）
      this.lruList.push(key);
      
      // 如果超出最大大小，删除最少使用的项
      if (this.cache.size > this.maxSize) {
        const oldestKey = this.lruList.shift(); // 移除并获取第一个元素
        if (oldestKey) {
          this.cache.delete(oldestKey);
          this.stats.evictions++;
        }
      }
    }
    
    return true;
  }

  /**
   * 获取缓存项
   * @param {string} key - 缓存键
   * @returns {any} 缓存值，如果不存在或已过期则返回undefined
   */
  get(key) {
    const item = this.cache.get(key);
    if (!item) {
      this.stats.misses++;
      return undefined;
    }
    
    // 检查是否过期
    if (Date.now() > item.expiry) {
      this.delete(key);
      this.stats.misses++;
      return undefined;
    }
    
    this.stats.hits++;
    
    // 更新LRU状态
    if (this.useLRU) {
      // 移动到列表末尾以表示最近使用
      const existingIndex = this.lruList.indexOf(key);
      if (existingIndex >= 0) {
        this.lruList.splice(existingIndex, 1);
        this.lruList.push(key);
      }
    }
    
    return item.value;
  }

  /**
   * 删除缓存项
   * @param {string} key - 缓存键
   * @returns {boolean} 是否成功删除
   */
  delete(key) {
    const result = this.cache.delete(key);
    if (result) {
      this.stats.deletes++;
      
      // 从LRU列表中移除
      if (this.useLRU) {
        const existingIndex = this.lruList.indexOf(key);
        if (existingIndex >= 0) {
          this.lruList.splice(existingIndex, 1);
        }
      }
    }
    return result;
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear();
    this.lruList = [];
  }

  /**
   * 获取所有缓存键
   * @returns {string[]} 缓存键数组
   */
  keys() {
    return Array.from(this.cache.keys());
  }

  /**
   * 获取缓存项数量
   * @returns {number} 缓存项数量
   */
  size() {
    return this.cache.size;
  }
  
  /**
   * 新增: 批量获取缓存项
   * @param {string[]} keys - 要获取的键数组
   * @returns {Object} 包含找到的缓存项的对象
   */
  getMulti(keys) {
    if (!Array.isArray(keys)) return {};
    
    const result = {};
    for (const key of keys) {
      const value = this.get(key);
      if (value !== undefined) {
        result[key] = value;
      }
    }
    return result;
  }
  
  /**
   * 新增: 批量设置缓存项
   * @param {Object} entries - 要设置的键值对
   * @param {number} ttl - 过期时间(毫秒)
   * @returns {boolean} 是否全部设置成功
   */
  setMulti(entries, ttl = this.defaultTTL) {
    if (typeof entries !== 'object' || entries === null) return false;
    
    for (const [key, value] of Object.entries(entries)) {
      this.set(key, value, ttl);
    }
    return true;
  }
  
  /**
   * 新增: 清除过期缓存项
   * @returns {number} 清除的项目数
   */
  prune() {
    const now = Date.now();
    let prunedCount = 0;
    
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.delete(key);
        prunedCount++;
      }
    }
    
    this.stats.lastPrune = now;
    return prunedCount;
  }
  
  /**
   * 新增: 获取缓存统计信息
   * @returns {Object} 统计信息对象
   */
  getStats() {
    return {
      ...this.stats,
      size: this.cache.size,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses || 1)
    };
  }
}

// 客户端缓存 - 使用localStorage持久化
class ClientCache {
  constructor() {
    this.prefix = 'app_cache_';
    this.defaultTTL = 30 * 60 * 1000; // 默认30分钟
  }

  /**
   * 设置缓存项
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {number} ttl - 过期时间(毫秒)
   */
  set(key, value, ttl = this.defaultTTL) {
    if (typeof window === 'undefined') return false;
    
    try {
      const item = {
        value,
        expiry: Date.now() + ttl
      };
      
      localStorage.setItem(this.prefix + key, JSON.stringify(item));
      return true;
    } catch (error) {
      console.error('客户端缓存错误:', error);
      return false;
    }
  }

  /**
   * 获取缓存项
   * @param {string} key - 缓存键
   * @returns {any} 缓存值，如果不存在或已过期则返回undefined
   */
  get(key) {
    if (typeof window === 'undefined') return undefined;
    
    try {
      const itemStr = localStorage.getItem(this.prefix + key);
      if (!itemStr) return undefined;
      
      const item = JSON.parse(itemStr);
      
      // 检查是否过期
      if (Date.now() > item.expiry) {
        this.delete(key);
        return undefined;
      }
      
      return item.value;
    } catch (error) {
      console.error('客户端缓存读取错误:', error);
      return undefined;
    }
  }

  /**
   * 删除缓存项
   * @param {string} key - 缓存键
   */
  delete(key) {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.removeItem(this.prefix + key);
    } catch (error) {
      console.error('客户端缓存删除错误:', error);
    }
  }

  /**
   * 清空所有缓存
   */
  clear() {
    if (typeof window === 'undefined') return;
    
    try {
      Object.keys(localStorage)
        .filter(key => key.startsWith(this.prefix))
        .forEach(key => localStorage.removeItem(key));
    } catch (error) {
      console.error('客户端缓存清理错误:', error);
    }
  }
}

// 导出服务器端缓存单例
const memoryCache = new MemoryCache();

// 导出客户端缓存单例
const clientCache = new ClientCache();

// 导出模块
module.exports = { 
  memoryCache,
  clientCache
}; 