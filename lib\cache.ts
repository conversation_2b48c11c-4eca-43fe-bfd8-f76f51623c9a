/**
 * 简单的内存缓存实现，用于改善数据获取性能
 */

type CacheItem<T> = {
  data: T;
  timestamp: number;
};

type CacheOptions = {
  ttl?: number; // 缓存生存时间（毫秒）
};

class MemoryCache {
  private cache: Map<string, CacheItem<any>>;
  private defaultTTL: number = 10 * 60 * 1000; // 默认10分钟

  constructor() {
    this.cache = new Map();
  }

  /**
   * 从缓存获取数据
   * @param key 缓存键
   * @param options 缓存选项
   * @returns 如果缓存有效返回数据，否则返回undefined
   */
  get<T>(key: string, options: CacheOptions = {}): T | undefined {
    const ttl = options.ttl || this.defaultTTL;
    const item = this.cache.get(key);

    if (!item) {
      return undefined;
    }

    // 检查缓存是否过期
    if (Date.now() - item.timestamp > ttl) {
      this.cache.delete(key);
      return undefined;
    }

    return item.data as T;
  }

  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 要缓存的数据
   */
  set<T>(key: string, data: T): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  /**
   * 删除缓存
   * @param key 缓存键
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * 获取所有缓存键
   * @returns 所有缓存键数组
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * 获取缓存大小
   * @returns 缓存项数量
   */
  size(): number {
    return this.cache.size;
  }
}

// 创建一个全局缓存实例
export const memoryCache = new MemoryCache();

// 为客户端缓存创建辅助函数
export const clientCache = {
  /**
   * 从sessionStorage获取缓存数据
   * @param key 缓存键
   * @param ttl 缓存生存时间（毫秒）
   * @returns 如果缓存有效返回数据，否则返回undefined
   */
  get<T>(key: string, ttl: number = 10 * 60 * 1000): T | undefined {
    if (typeof window === 'undefined') {
      return undefined;
    }

    try {
      const item = sessionStorage.getItem(key);
      if (!item) {
        return undefined;
      }

      const { data, timestamp } = JSON.parse(item);

      // 检查缓存是否过期
      if (Date.now() - timestamp > ttl) {
        sessionStorage.removeItem(key);
        return undefined;
      }

      return data as T;
    } catch (error) {
      console.error('Client cache error:', error);
      return undefined;
    }
  },

  /**
   * 设置sessionStorage缓存
   * @param key 缓存键
   * @param data 要缓存的数据
   */
  set<T>(key: string, data: T): void {
    if (typeof window === 'undefined') {
      return;
    }

    try {
      sessionStorage.setItem(
        key,
        JSON.stringify({
          data,
          timestamp: Date.now(),
        })
      );
    } catch (error) {
      console.error('Client cache set error:', error);
    }
  },
};

export default memoryCache;
