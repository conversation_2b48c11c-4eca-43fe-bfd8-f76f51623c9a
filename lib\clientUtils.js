/**
 * 客户端工具函数
 * 用于处理API请求和错误
 */
import { getApiUrl } from './apiUtils';

/**
 * 封装的API请求函数
 * 自动处理错误和降级到模拟数据
 * 
 * @param {string} path - API路径
 * @param {Object} options - 请求选项
 * @param {Object} fallback - 请求失败时的降级数据
 * @returns {Promise<Object>} 响应数据
 */
export async function fetchWithFallback(path, options = {}, fallback = null) {
  const url = getApiUrl(path);
  
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // 检查API返回的数据是否已是模拟数据
    if (data.isMock) {
      console.log(`[API] 返回了模拟数据: ${path}`);
    }
    
    return data;
  } catch (error) {
    console.error(`[API] 请求失败 (${path}):`, error);
    
    // 如果提供了降级数据，使用它
    if (fallback) {
      console.log(`[API] 使用降级数据: ${path}`);
      return {
        success: true,
        data: fallback,
        isFallback: true,
        error: error.message
      };
    }
    
    // 否则抛出错误
    throw error;
  }
}

/**
 * 并行获取多个API资源
 * 单个失败不会影响整体请求
 * 
 * @param {Array<Object>} requests - 请求配置数组
 * @returns {Promise<Object>} 包含所有请求结果的对象
 */
export async function fetchMultiple(requests) {
  // 处理每个请求，使用Promise.allSettled确保部分失败不影响其他请求
  const results = await Promise.allSettled(
    requests.map(async (req) => {
      const { key, path, options, fallback } = req;
      try {
        const data = await fetchWithFallback(path, options, fallback);
        return { key, data, success: true };
      } catch (error) {
        return { 
          key, 
          data: fallback || null, 
          success: false, 
          error: error.message,
          isFallback: !!fallback
        };
      }
    })
  );
  
  // 转换结果为对象
  const responseData = {};
  results.forEach((result, index) => {
    const key = requests[index].key;
    if (result.status === 'fulfilled') {
      responseData[key] = result.value.data;
    } else {
      // 请求失败，使用空数据或降级数据
      responseData[key] = requests[index].fallback || null;
      console.error(`[API] 获取 ${key} 失败:`, result.reason);
    }
  });
  
  return responseData;
}

/**
 * 模拟产品数据
 * 在API请求失败时使用
 */
export const mockProducts = [];

/**
 * 模拟分类数据
 * 在API请求失败时使用
 */
export const mockCategories = [
  { id: '1', name: '室内游乐场', slug: 'indoor-playground' },
  { id: '2', name: '蹦床公园', slug: 'trampoline-park' },
  { id: '3', name: '互动设备', slug: 'interactive-equipment' }
]; 