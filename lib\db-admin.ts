import sql from './db';
import bcrypt from 'bcryptjs';

// 创建统一的用户表（包含管理员）
export async function createUsersTable() {
  try {
    const result = await sql`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(255) NOT NULL UNIQUE,
        email VARCHAR(255) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'editor', 'admin', 'super_admin')),
        is_active BOOLEAN DEFAULT true,
        last_login TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    return { success: true, message: 'Users table created successfully', data: result };
  } catch (error) {
    console.error('Error creating users table:', error);
    return { success: false, message: 'Failed to create users table', error };
  }
}

// 创建用户（包括管理员）
export async function createUser(user: {
  username: string;
  email: string;
  password: string;
  role?: 'user' | 'editor' | 'admin' | 'super_admin';
}) {
  try {
    // 检查用户名是否已存在
    const existingUser = await sql`
      SELECT * FROM users WHERE username = ${user.username} OR email = ${user.email}
    `;

    if (existingUser.length > 0) {
      return {
        success: false,
        message: 'Username or email already exists',
        data: null,
      };
    }

    // 加密密码
    const salt = await bcrypt.genSalt(10);
    const passwordHash = await bcrypt.hash(user.password, salt);

    // 插入用户
    const result = await sql`
      INSERT INTO users (
        username,
        email,
        password_hash,
        role
      )
      VALUES (
        ${user.username},
        ${user.email},
        ${passwordHash},
        ${user.role || 'user'}
      )
      RETURNING id, username, email, role, created_at
    `;

    return {
      success: true,
      message: 'User created successfully',
      data: result[0],
    };
  } catch (error) {
    console.error('Error creating user:', error);
    return {
      success: false,
      message: 'Failed to create user',
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// 创建管理员用户的便捷函数
export async function createAdminUser(admin: {
  username: string;
  email: string;
  password: string;
  is_super_admin?: boolean;
}) {
  return createUser({
    ...admin,
    role: admin.is_super_admin ? 'super_admin' : 'admin'
  });
}

// 验证用户登录
export async function verifyUserLogin(username: string, password: string) {
  try {
    // 查找用户
    const users = await sql`
      SELECT * FROM users WHERE username = ${username} AND is_active = true
    `;

    if (users.length === 0) {
      return { success: false, message: 'Invalid username or password', data: null };
    }

    const user = users[0];

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);

    if (!isPasswordValid) {
      return { success: false, message: 'Invalid username or password', data: null };
    }

    // 更新最后登录时间
    await sql`
      UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ${user.id}
    `;

    // 返回用户信息（不包含密码）
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password_hash, ...userWithoutPassword } = user;

    return {
      success: true,
      message: 'Login successful',
      data: userWithoutPassword,
    };
  } catch (error) {
    console.error('Error verifying user login:', error);
    return {
      success: false,
      message: 'Login verification failed',
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// 验证管理员登录（向后兼容）
export async function verifyAdminLogin(username: string, password: string) {
  const result = await verifyUserLogin(username, password);

  if (result.success && result.data) {
    // 检查是否有管理员权限
    if (!['admin', 'super_admin'].includes(result.data.role)) {
      return { success: false, message: 'Insufficient permissions', data: null };
    }
  }

  return result;
}

// 获取所有用户
export async function getAllUsers() {
  try {
    const users = await sql`
      SELECT id, username, email, role, is_active, last_login, created_at, updated_at
      FROM users
      ORDER BY created_at DESC
    `;

    return {
      success: true,
      message: 'Users retrieved successfully',
      data: users,
    };
  } catch (error) {
    console.error('Error getting users:', error);
    return {
      success: false,
      message: 'Failed to get users',
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// 获取用户统计信息
export async function getUserStats() {
  try {
    const stats = await sql`
      SELECT
        COUNT(*) as total_users,
        COUNT(CASE WHEN role = 'admin' OR role = 'super_admin' THEN 1 END) as admin_users,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active_users,
        COUNT(CASE WHEN last_login > CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as recent_logins
      FROM users
    `;

    return {
      success: true,
      message: 'User stats retrieved successfully',
      data: stats[0],
    };
  } catch (error) {
    console.error('Error getting user stats:', error);
    return {
      success: false,
      message: 'Failed to get user stats',
      error: error instanceof Error ? error.message : String(error),
    };
  }
}
