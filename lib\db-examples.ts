import sql from './db';

// 示例1: 创建表
export async function createProductsTable() {
  try {
    const result = await sql`
      CREATE TABLE IF NOT EXISTS products (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        price DECIMAL(10, 2) NOT NULL,
        image_url VARCHAR(255),
        category_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    return { success: true, message: 'Products table created successfully', data: result };
  } catch (error: unknown) {
    console.error('Error creating products table:', error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    return { success: false, message: 'Failed to create products table', error: { message } };
  }
}

// 示例2: 插入数据
export async function insertProduct(product: {
  name: string;
  description?: string;
  price: number;
  image_url?: string;
  category_id?: number;
}) {
  try {
    const result = await sql`
      INSERT INTO products (name, description, price, image_url, category_id)
      VALUES (
        ${product.name},
        ${product.description || null},
        ${product.price},
        ${product.image_url || null},
        ${product.category_id || null}
      )
      RETURNING *
    `;
    return { success: true, message: 'Product inserted successfully', data: result[0] };
  } catch (error: unknown) {
    console.error('Error inserting product:', error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    return { success: false, message: 'Failed to insert product', error: { message } };
  }
}

// 示例3: 查询数据
export async function getAllProducts() {
  try {
    const result = await sql`SELECT * FROM products ORDER BY created_at DESC`;
    return { success: true, message: 'Products retrieved successfully', data: result };
  } catch (error: unknown) {
    console.error('Error retrieving products:', error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    return { success: false, message: 'Failed to retrieve products', error: { message } };
  }
}

// 示例4: 按ID查询
export async function getProductById(id: number) {
  try {
    const result = await sql`SELECT * FROM products WHERE id = ${id}`;
    if (result.length === 0) {
      return { success: false, message: 'Product not found', data: null };
    }
    return { success: true, message: 'Product retrieved successfully', data: result[0] };
  } catch (error: unknown) {
    console.error(`Error retrieving product with ID ${id}:`, error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    return { success: false, message: 'Failed to retrieve product', error: { message } };
  }
}

// 示例5: 更新数据
export async function updateProduct(
  id: number,
  updates: {
    name?: string;
    description?: string;
    price?: number;
    image_url?: string;
    category_id?: number;
  }
) {
  try {
    // 构建动态SET子句
    const updateFields: string[] = [];
    const values: Array<string | number> = [];

    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        updateFields.push(`${key} = $${updateFields.length + 1}`);
        values.push(value);
      }
    });

    // 添加更新时间
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);

    // 如果没有字段要更新，则返回
    if (updateFields.length === 1) {
      // 只有updated_at
      return { success: false, message: 'No fields to update', data: null };
    }

    const setClause = updateFields.join(', ');
    values.push(id); // 添加WHERE条件的值

    const query = `
      UPDATE products 
      SET ${setClause} 
      WHERE id = $${values.length} 
      RETURNING *
    `;

    const result = await sql.query(query, values);

    if (result.length === 0) {
      return { success: false, message: 'Product not found or no changes made', data: null };
    }

    return { success: true, message: 'Product updated successfully', data: result[0] };
  } catch (error: unknown) {
    console.error(`Error updating product with ID ${id}:`, error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    return { success: false, message: 'Failed to update product', error: { message } };
  }
}

// 示例6: 删除数据
export async function deleteProduct(id: number) {
  try {
    const result = await sql`DELETE FROM products WHERE id = ${id} RETURNING *`;

    if (result.length === 0) {
      return { success: false, message: 'Product not found', data: null };
    }

    return { success: true, message: 'Product deleted successfully', data: result[0] };
  } catch (error: unknown) {
    console.error(`Error deleting product with ID ${id}:`, error);
    const message = error instanceof Error ? error.message : 'Unknown error';
    return { success: false, message: 'Failed to delete product', error: { message } };
  }
}
