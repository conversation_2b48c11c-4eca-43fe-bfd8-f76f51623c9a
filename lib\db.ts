import { neon, neonConfig } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import { QueryResult, QueryResultRow, PoolClient } from 'pg';
import dbConnect from './postgresql';

// 加载环境变量
dotenv.config();

// 设置NeonDB配置
neonConfig.fetchConnectionCache = true;

// 获取数据库连接字符串
let connectionString = process.env.DATABASE_URL;

// 添加详细日志
console.log('DATABASE_URL environment variable exists:', !!connectionString);

// 如果环境变量不存在，使用硬编码的连接字符串（生产环境不建议这样做）
if (!connectionString) {
  console.log('Using hardcoded connection string as fallback');
  connectionString =
    'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';
}

// 创建SQL查询客户端
const sql = neon(connectionString);

export default sql;

// 模拟数据缓存
const mockDataCache: { [key: string]: unknown } = {};

// 设置模拟数据
export function setMockData(key: string, data: unknown) {
  mockDataCache[key] = data;
}

// 获取模拟数据
export function getMockData<T>(key: string, defaultValue: T): T {
  const cachedData = mockDataCache[key];
  if (cachedData !== undefined) {
    // Assuming the user of getMockData knows the type T and it matches what was set.
    // A more robust solution might involve runtime type checks or schema validation if types are complex.
    return cachedData as T;
  }
  return defaultValue;
}

// 测试连接函数
export async function testConnection() {
  try {
    console.log('Attempting to connect to database...');
    const result = await sql`SELECT 1 as test`;
    console.log('Database connection successful:', result);
    return { success: true, message: 'Database connection successful', data: result };
  } catch (error) {
    console.error('Database connection error:', error);
    // 返回更详细的错误信息
    return {
      success: false,
      message: 'Database connection failed',
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    };
  }
}

/**
 * Execute a SQL query with optional parameters
 * @param text SQL query text
 * @param params Query parameters
 * @returns Query result
 */
export async function query<T extends QueryResultRow = QueryResultRow>(
  text: string,
  params?: (string | number | boolean | null)[],
  mockDataKey?: string,
  mockDataValue?: unknown
): Promise<QueryResult<T>> {
  try {
    // 尝试连接到数据库
    const pool = await dbConnect();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- pg type definition expects any[]
    return await pool.query<T>(text, params as any[]);
  } catch (error) {
    // 数据库连接失败，检查是否有模拟数据
    if (mockDataKey && mockDataValue) {
      console.warn(`Database query failed, using mock data for key: ${mockDataKey}`);
      setMockData(mockDataKey, mockDataValue);

      // 返回模拟的QueryResult对象
      return {
        rows: Array.isArray(mockDataValue) ? mockDataValue : [mockDataValue],
        command: 'SELECT',
        rowCount: Array.isArray(mockDataValue) ? mockDataValue.length : 1,
        oid: 0,
        fields: [],
      } as QueryResult<T>;
    }

    // 检查是否有缓存的模拟数据
    if (mockDataKey && mockDataCache[mockDataKey]) {
      console.warn(`Using cached mock data for key: ${mockDataKey}`);
      const mockData = mockDataCache[mockDataKey];

      return {
        rows: Array.isArray(mockData) ? mockData : [mockData],
        command: 'SELECT',
        rowCount: Array.isArray(mockData) ? mockData.length : 1,
        oid: 0,
        fields: [],
      } as QueryResult<T>;
    }

    // 如果没有模拟数据，则抛出原始错误
    throw error;
  }
}

/**
 * Execute a SQL query within a transaction
 * @param callback Function that accepts a transaction client and executes queries
 * @returns Result of the callback
 */
export async function withTransaction<T>(
  callback: (client: PoolClient) => Promise<T>,
  mockResult?: T
): Promise<T> {
  try {
    const pool = await dbConnect();
    const client = await pool.connect();

    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    // 如果数据库连接失败且提供了模拟结果，则返回模拟结果
    if (mockResult !== undefined) {
      console.warn('Database transaction failed, using mock result');
      return mockResult;
    }
    throw error;
  }
}

/**
 * Initialize the database by running the schema.sql file
 * 仅在服务器端执行
 */
export async function initializeDatabase(): Promise<void> {
  // 确保只在服务器端执行
  if (typeof window !== 'undefined') {
    console.error('initializeDatabase should only be called on the server side');
    return;
  }

  // 动态导入fs和path模块
  const fs = await import('fs');
  const path = await import('path');
  const schemaPath = path.join(process.cwd(), 'lib', 'schema.sql');

  try {
    const schema = fs.readFileSync(schemaPath, 'utf8');
    const pool = await dbConnect();
    await pool.query(schema);
    console.log('Database schema initialized successfully');
  } catch (error: unknown) {
    const message =
      error instanceof Error ? error.message : 'Unknown error during schema initialization';
    console.error('Failed to initialize database schema:', message);
    // Re-throw the original error or a new one wrapping it, depending on desired behavior
    if (error instanceof Error) throw error;
    throw new Error(message);
  }
}
