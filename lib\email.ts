// 邮件服务配置和发送功能
interface EmailConfig {
  smtp_host: string;
  smtp_port: number;
  smtp_user: string;
  smtp_pass: string;
  from_email: string;
  from_name: string;
  admin_email: string;
}

interface FormSubmissionData {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  country?: string;
  playground_size?: string;
  product?: string;
  message: string;
  created_at: string;
}

// 获取邮件配置
function getEmailConfig(): EmailConfig {
  return {
    smtp_host: process.env.SMTP_HOST || 'smtp.gmail.com',
    smtp_port: parseInt(process.env.SMTP_PORT || '587'),
    smtp_user: process.env.SMTP_USER || '',
    smtp_pass: process.env.SMTP_PASS || '',
    from_email: process.env.FROM_EMAIL || process.env.SMTP_USER || '',
    from_name: process.env.FROM_NAME || '跨境电商网站',
    admin_email: process.env.ADMIN_EMAIL || '<EMAIL>',
  };
}

// 生成表单提交通知邮件HTML
function generateFormNotificationHTML(data: FormSubmissionData): string {
  return `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>新的表单提交通知</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #f4f4f4;
        }
        .container {
          background-color: #ffffff;
          padding: 30px;
          border-radius: 10px;
          box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 20px;
          border-radius: 10px 10px 0 0;
          margin: -30px -30px 30px -30px;
          text-align: center;
        }
        .header h1 {
          margin: 0;
          font-size: 24px;
        }
        .info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 15px;
          margin: 20px 0;
        }
        .info-item {
          background-color: #f8f9fa;
          padding: 15px;
          border-radius: 8px;
          border-left: 4px solid #667eea;
        }
        .info-label {
          font-weight: bold;
          color: #555;
          font-size: 14px;
          margin-bottom: 5px;
        }
        .info-value {
          color: #333;
          font-size: 16px;
        }
        .message-section {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
          border-left: 4px solid #28a745;
        }
        .message-label {
          font-weight: bold;
          color: #555;
          margin-bottom: 10px;
        }
        .message-content {
          background-color: white;
          padding: 15px;
          border-radius: 5px;
          white-space: pre-wrap;
          border: 1px solid #e9ecef;
        }
        .footer {
          margin-top: 30px;
          padding-top: 20px;
          border-top: 1px solid #eee;
          text-align: center;
          color: #666;
          font-size: 14px;
        }
        .action-button {
          display: inline-block;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 12px 25px;
          text-decoration: none;
          border-radius: 5px;
          margin: 20px 0;
          font-weight: bold;
        }
        @media (max-width: 600px) {
          .info-grid {
            grid-template-columns: 1fr;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🔔 新的表单提交通知</h1>
          <p>您收到了一个新的客户咨询</p>
        </div>
        
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">👤 客户姓名</div>
            <div class="info-value">${data.name}</div>
          </div>
          
          <div class="info-item">
            <div class="info-label">📧 邮箱地址</div>
            <div class="info-value">${data.email || '未提供'}</div>
          </div>
          
          <div class="info-item">
            <div class="info-label">📱 联系电话</div>
            <div class="info-value">${data.phone || '未提供'}</div>
          </div>
          
          <div class="info-item">
            <div class="info-label">🌍 国家/地区</div>
            <div class="info-value">${data.country || '未提供'}</div>
          </div>
          
          <div class="info-item">
            <div class="info-label">📏 场地大小</div>
            <div class="info-value">${data.playground_size || '未提供'}</div>
          </div>
          
          <div class="info-item">
            <div class="info-label">🎯 感兴趣的产品</div>
            <div class="info-value">${data.product || '未指定'}</div>
          </div>
        </div>
        
        <div class="message-section">
          <div class="message-label">💬 客户留言</div>
          <div class="message-content">${data.message}</div>
        </div>
        
        <div style="text-align: center;">
          <a href="${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/zh/admin/form-notifications" class="action-button">
            查看管理后台
          </a>
        </div>
        
        <div class="footer">
          <p><strong>提交时间:</strong> ${new Date(data.created_at).toLocaleString('zh-CN')}</p>
          <p><strong>提交ID:</strong> #${data.id}</p>
          <hr style="margin: 15px 0; border: none; border-top: 1px solid #eee;">
          <p>此邮件由跨境电商网站系统自动发送</p>
          <p>请及时回复客户咨询以提供更好的服务体验</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

// 生成纯文本版本
function generateFormNotificationText(data: FormSubmissionData): string {
  return `
新的表单提交通知

客户信息:
- 姓名: ${data.name}
- 邮箱: ${data.email || '未提供'}
- 电话: ${data.phone || '未提供'}
- 国家: ${data.country || '未提供'}
- 场地大小: ${data.playground_size || '未提供'}
- 感兴趣的产品: ${data.product || '未指定'}

客户留言:
${data.message}

提交时间: ${new Date(data.created_at).toLocaleString('zh-CN')}
提交ID: #${data.id}

请登录管理后台查看详情: ${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/zh/admin/form-notifications

---
此邮件由跨境电商网站系统自动发送
  `.trim();
}

// 使用 SMTP 发送邮件 (需要 nodemailer)
export async function sendFormNotificationEmail(data: FormSubmissionData): Promise<boolean> {
  const config = getEmailConfig();
  
  // 检查是否配置了邮件服务
  if (!config.smtp_user || !config.smtp_pass || !config.admin_email) {
    console.warn('邮件服务未配置，跳过邮件发送');
    return false;
  }

  try {
    // 如果安装了 nodemailer，使用它发送邮件
    try {
      const nodemailer = require('nodemailer');
      
      const transporter = nodemailer.createTransport({
        host: config.smtp_host,
        port: config.smtp_port,
        secure: config.smtp_port === 465,
        auth: {
          user: config.smtp_user,
          pass: config.smtp_pass,
        },
      });

      const mailOptions = {
        from: `"${config.from_name}" <${config.from_email}>`,
        to: config.admin_email,
        subject: `🔔 新的表单提交 - ${data.name}`,
        text: generateFormNotificationText(data),
        html: generateFormNotificationHTML(data),
      };

      const result = await transporter.sendMail(mailOptions);
      console.log('邮件发送成功:', result.messageId);
      return true;
    } catch (nodemailerError) {
      console.log('Nodemailer 不可用，尝试其他方式发送邮件');
      throw nodemailerError;
    }
  } catch (error) {
    console.error('邮件发送失败:', error);
    return false;
  }
}

// 使用第三方邮件服务 API 发送邮件 (备用方案)
export async function sendEmailViaAPI(data: FormSubmissionData): Promise<boolean> {
  const config = getEmailConfig();
  
  // 这里可以集成其他邮件服务，如 SendGrid, Mailgun 等
  // 示例：使用 SendGrid API
  if (process.env.SENDGRID_API_KEY) {
    try {
      const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.SENDGRID_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          personalizations: [{
            to: [{ email: config.admin_email }],
            subject: `🔔 新的表单提交 - ${data.name}`,
          }],
          from: { email: config.from_email, name: config.from_name },
          content: [
            {
              type: 'text/plain',
              value: generateFormNotificationText(data),
            },
            {
              type: 'text/html',
              value: generateFormNotificationHTML(data),
            },
          ],
        }),
      });

      if (response.ok) {
        console.log('邮件通过 SendGrid 发送成功');
        return true;
      } else {
        console.error('SendGrid 邮件发送失败:', await response.text());
        return false;
      }
    } catch (error) {
      console.error('SendGrid 邮件发送错误:', error);
      return false;
    }
  }

  return false;
}

// 主要的邮件发送函数
export async function sendFormNotification(data: FormSubmissionData): Promise<boolean> {
  console.log('准备发送表单提交通知邮件...');

  // 首先尝试使用 nodemailer (如果可用)
  try {
    const nodemailerResult = await sendFormNotificationEmail(data);
    if (nodemailerResult) {
      return true;
    }
  } catch (error) {
    console.log('Nodemailer 不可用，尝试其他方式');
  }

  // 如果 nodemailer 失败，尝试第三方 API
  const apiResult = await sendEmailViaAPI(data);
  if (apiResult) {
    return true;
  }

  // 如果都失败了，记录到控制台作为备用
  console.log('邮件发送失败，将通知信息记录到控制台:');
  console.log('='.repeat(50));
  console.log(generateFormNotificationText(data));
  console.log('='.repeat(50));

  return false;
}
