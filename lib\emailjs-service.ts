// EmailJS 邮件发送服务
// 优势：前端直接发送，无需后端配置，可发送到任何邮箱

interface EmailData {
  id: number;
  name: string;
  email: string;
  phone: string;
  country: string;
  playground_size: string;
  product: string;
  message: string;
  created_at: string;
}

export async function sendEmailWithEmailJS(data: EmailData) {
  // EmailJS 配置
  const serviceId = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID || 'your_service_id';
  const templateId = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID || 'your_template_id';
  const publicKey = process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY || 'your_public_key';
  const targetEmail = process.env.ADMIN_EMAIL || '<EMAIL>';

  // 邮件参数
  const templateParams = {
    to_email: targetEmail,
    from_name: '跨境电商网站',
    customer_name: data.name,
    customer_email: data.email,
    customer_phone: data.phone,
    customer_country: data.country,
    playground_size: data.playground_size,
    product_interest: data.product,
    customer_message: data.message,
    submission_time: new Date(data.created_at).toLocaleString('zh-CN'),
    submission_id: data.id,
    subject: `🔔 新的表单提交 - ${data.name}`
  };

  try {
    // 动态导入 EmailJS
    const emailjs = await import('@emailjs/browser');
    
    const result = await emailjs.send(
      serviceId,
      templateId,
      templateParams,
      publicKey
    );

    return {
      success: true,
      messageId: result.text,
      service: 'EmailJS',
      sentTo: targetEmail
    };
  } catch (error) {
    console.error('EmailJS发送失败:', error);
    throw error;
  }
}

// 使用 Fetch API 发送邮件 (备用方案)
export async function sendEmailWithFetch(data: EmailData) {
  const targetEmail = '<EMAIL>';
  
  try {
    // 使用免费的邮件发送API服务
    const response = await fetch('https://formspree.io/f/your-form-id', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: targetEmail,
        subject: `🔔 新的表单提交 - ${data.name}`,
        message: `
新的表单提交通知

客户信息:
- 姓名: ${data.name}
- 邮箱: ${data.email}
- 电话: ${data.phone}
- 国家: ${data.country}
- 场地大小: ${data.playground_size}
- 感兴趣的产品: ${data.product}

客户留言:
${data.message}

提交时间: ${new Date(data.created_at).toLocaleString('zh-CN')}
提交ID: #${data.id}
        `,
        _replyto: data.email,
        _subject: `新客户咨询 - ${data.name}`
      })
    });

    if (response.ok) {
      return {
        success: true,
        messageId: 'formspree-sent',
        service: 'Formspree',
        sentTo: targetEmail
      };
    } else {
      throw new Error(`HTTP ${response.status}`);
    }
  } catch (error) {
    console.error('Formspree发送失败:', error);
    throw error;
  }
}
