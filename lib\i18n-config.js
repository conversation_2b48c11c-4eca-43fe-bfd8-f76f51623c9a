// i18n-config.js - Configuration for internationalization

export const i18n = {
  locales: ['en', 'zh'],
  defaultLocale: 'en',
};

// Create a simple i18n instance (without i18next)
export function createI18nInstance(locale = i18n.defaultLocale) {
  return {
    locale,
    t: key => {
      return key; // This is a placeholder; actual translation will be done in components
    },
  };
}

// Load dictionary helper (similar to app router implementation)
export async function getDictionary(locale) {
  try {
    return import(`../app/dictionaries/${locale}.json`).then(module => module.default);
  } catch (error) {
    console.error('Error loading dictionary:', error);
    return import(`../app/dictionaries/${i18n.defaultLocale}.json`).then(module => module.default);
  }
}
