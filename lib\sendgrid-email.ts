// SendGrid邮件发送服务
// 使用方法：
// 1. 注册SendGrid账号：https://sendgrid.com
// 2. 获取API密钥
// 3. 在.env.local中设置SENDGRID_API_KEY

interface EmailData {
  id: number;
  name: string;
  email: string;
  phone: string;
  country: string;
  playground_size: string;
  product: string;
  message: string;
  created_at: string;
}

export async function sendEmailWithSendGrid(data: EmailData) {
  const apiKey = process.env.SENDGRID_API_KEY;
  const fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
  const fromName = process.env.FROM_NAME || '跨境电商网站';
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';

  if (!apiKey) {
    throw new Error('SendGrid API密钥未配置');
  }

  const emailContent = {
    personalizations: [
      {
        to: [{ email: adminEmail }],
        subject: `🔔 新的表单提交 - ${data.name}`
      }
    ],
    from: {
      email: fromEmail,
      name: fromN<PERSON>
    },
    content: [
      {
        type: 'text/html',
        value: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
              新的表单提交通知
            </h2>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <h3 style="color: #007bff; margin-top: 0;">客户信息</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; width: 120px;">姓名:</td>
                  <td style="padding: 8px 0;">${data.name}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold;">邮箱:</td>
                  <td style="padding: 8px 0;">${data.email}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold;">电话:</td>
                  <td style="padding: 8px 0;">${data.phone}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold;">国家:</td>
                  <td style="padding: 8px 0;">${data.country}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold;">场地大小:</td>
                  <td style="padding: 8px 0;">${data.playground_size}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold;">感兴趣的产品:</td>
                  <td style="padding: 8px 0;">${data.product}</td>
                </tr>
              </table>
            </div>

            <div style="background-color: #fff; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px;">
              <h3 style="color: #007bff; margin-top: 0;">客户留言</h3>
              <p style="line-height: 1.6; color: #333;">${data.message}</p>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; font-size: 14px;">
              <p><strong>提交时间:</strong> ${new Date(data.created_at).toLocaleString('zh-CN')}</p>
              <p><strong>提交ID:</strong> #${data.id}</p>
              <p style="margin-top: 20px;">此邮件由跨境电商网站系统自动发送</p>
            </div>
          </div>
        `
      },
      {
        type: 'text/plain',
        value: `
新的表单提交通知

客户信息:
- 姓名: ${data.name}
- 邮箱: ${data.email}
- 电话: ${data.phone}
- 国家: ${data.country}
- 场地大小: ${data.playground_size}
- 感兴趣的产品: ${data.product}

客户留言:
${data.message}

提交时间: ${new Date(data.created_at).toLocaleString('zh-CN')}
提交ID: #${data.id}

此邮件由跨境电商网站系统自动发送
        `
      }
    ]
  };

  try {
    const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(emailContent)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`SendGrid API错误: ${response.status} - ${errorText}`);
    }

    return {
      success: true,
      messageId: response.headers.get('x-message-id') || 'sendgrid-sent',
      service: 'SendGrid'
    };
  } catch (error) {
    console.error('SendGrid发送失败:', error);
    throw error;
  }
}

// Resend邮件发送服务
export async function sendEmailWithResend(data: EmailData) {
  const apiKey = process.env.RESEND_API_KEY;
  const fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
  const fromName = process.env.FROM_NAME || '跨境电商网站';
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';

  if (!apiKey) {
    throw new Error('Resend API密钥未配置');
  }

  const emailContent = {
    from: `${fromName} <${fromEmail}>`,
    to: [adminEmail],
    subject: `🔔 新的表单提交 - ${data.name}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">新的表单提交通知</h2>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px;">
          <h3>客户信息</h3>
          <p><strong>姓名:</strong> ${data.name}</p>
          <p><strong>邮箱:</strong> ${data.email}</p>
          <p><strong>电话:</strong> ${data.phone}</p>
          <p><strong>国家:</strong> ${data.country}</p>
          <p><strong>场地大小:</strong> ${data.playground_size}</p>
          <p><strong>感兴趣的产品:</strong> ${data.product}</p>
        </div>
        <div style="margin-top: 20px;">
          <h3>客户留言</h3>
          <p>${data.message}</p>
        </div>
        <div style="margin-top: 20px; color: #666; font-size: 14px;">
          <p>提交时间: ${new Date(data.created_at).toLocaleString('zh-CN')}</p>
          <p>提交ID: #${data.id}</p>
        </div>
      </div>
    `,
    text: `
新的表单提交通知

客户信息:
- 姓名: ${data.name}
- 邮箱: ${data.email}
- 电话: ${data.phone}
- 国家: ${data.country}
- 场地大小: ${data.playground_size}
- 感兴趣的产品: ${data.product}

客户留言:
${data.message}

提交时间: ${new Date(data.created_at).toLocaleString('zh-CN')}
提交ID: #${data.id}
    `
  };

  try {
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(emailContent)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Resend API错误: ${response.status} - ${JSON.stringify(errorData)}`);
    }

    const result = await response.json();
    return {
      success: true,
      messageId: result.id,
      service: 'Resend'
    };
  } catch (error) {
    console.error('Resend发送失败:', error);
    throw error;
  }
}

// 邮件发送主函数 - 支持多种服务
export async function sendNotificationEmail(data: EmailData) {
  const services = [
    { name: 'SendGrid', fn: sendEmailWithSendGrid, enabled: !!process.env.SENDGRID_API_KEY },
    { name: 'Resend', fn: sendEmailWithResend, enabled: !!process.env.RESEND_API_KEY }
  ];

  // 尝试可用的服务
  for (const service of services) {
    if (service.enabled) {
      try {
        console.log(`尝试使用 ${service.name} 发送邮件...`);
        const result = await service.fn(data);
        console.log(`${service.name} 发送成功:`, result.messageId);
        return result;
      } catch (error) {
        console.error(`${service.name} 发送失败:`, error);
        continue;
      }
    }
  }

  // 如果所有服务都失败，抛出错误
  throw new Error('所有邮件服务都不可用');
}
