// 简化的邮件发送功能，使用 fetch API 调用第三方邮件服务

interface FormSubmissionData {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  country?: string;
  playground_size?: string;
  product?: string;
  message: string;
  created_at: string;
}

// 生成邮件内容
function generateEmailContent(data: FormSubmissionData) {
  const subject = `🔔 新的表单提交 - ${data.name}`;
  
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4;">
      <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1);">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: -30px -30px 30px -30px; text-align: center;">
          <h1 style="margin: 0; font-size: 24px;">🔔 新的表单提交通知</h1>
          <p style="margin: 10px 0 0 0;">您收到了一个新的客户咨询</p>
        </div>
        
        <div style="margin: 20px 0;">
          <h3 style="color: #333; margin-bottom: 15px;">客户信息</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">👤 客户姓名:</td>
              <td style="padding: 8px; border-bottom: 1px solid #eee;">${data.name}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">📧 邮箱地址:</td>
              <td style="padding: 8px; border-bottom: 1px solid #eee;">${data.email || '未提供'}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">📱 联系电话:</td>
              <td style="padding: 8px; border-bottom: 1px solid #eee;">${data.phone || '未提供'}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">🌍 国家/地区:</td>
              <td style="padding: 8px; border-bottom: 1px solid #eee;">${data.country || '未提供'}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">📏 场地大小:</td>
              <td style="padding: 8px; border-bottom: 1px solid #eee;">${data.playground_size || '未提供'}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border-bottom: 1px solid #eee; font-weight: bold; color: #555;">🎯 感兴趣的产品:</td>
              <td style="padding: 8px; border-bottom: 1px solid #eee;">${data.product || '未指定'}</td>
            </tr>
          </table>
        </div>
        
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
          <h3 style="color: #333; margin-top: 0;">💬 客户留言</h3>
          <div style="background-color: white; padding: 15px; border-radius: 5px; white-space: pre-wrap; border: 1px solid #e9ecef;">
${data.message}
          </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/zh/admin/form-notifications" 
             style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold;">
            查看管理后台
          </a>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #666; font-size: 14px;">
          <p><strong>提交时间:</strong> ${new Date(data.created_at).toLocaleString('zh-CN')}</p>
          <p><strong>提交ID:</strong> #${data.id}</p>
          <hr style="margin: 15px 0; border: none; border-top: 1px solid #eee;">
          <p>此邮件由跨境电商网站系统自动发送</p>
          <p>请及时回复客户咨询以提供更好的服务体验</p>
        </div>
      </div>
    </div>
  `;

  const textContent = `
新的表单提交通知

客户信息:
- 姓名: ${data.name}
- 邮箱: ${data.email || '未提供'}
- 电话: ${data.phone || '未提供'}
- 国家: ${data.country || '未提供'}
- 场地大小: ${data.playground_size || '未提供'}
- 感兴趣的产品: ${data.product || '未指定'}

客户留言:
${data.message}

提交时间: ${new Date(data.created_at).toLocaleString('zh-CN')}
提交ID: #${data.id}

请登录管理后台查看详情: ${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/zh/admin/form-notifications

---
此邮件由跨境电商网站系统自动发送
  `.trim();

  return { subject, htmlContent, textContent };
}

// 使用 SendGrid API 发送邮件
async function sendViaSendGrid(data: FormSubmissionData): Promise<boolean> {
  const apiKey = process.env.SENDGRID_API_KEY;
  if (!apiKey) {
    console.log('SendGrid API Key 未配置');
    return false;
  }

  const adminEmail = process.env.ADMIN_EMAIL;
  const fromEmail = process.env.FROM_EMAIL || process.env.SMTP_USER;
  const fromName = process.env.FROM_NAME || '跨境电商网站';

  if (!adminEmail || !fromEmail) {
    console.log('邮件地址配置不完整');
    return false;
  }

  const { subject, htmlContent, textContent } = generateEmailContent(data);

  try {
    const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        personalizations: [{
          to: [{ email: adminEmail }],
          subject: subject,
        }],
        from: { email: fromEmail, name: fromName },
        content: [
          {
            type: 'text/plain',
            value: textContent,
          },
          {
            type: 'text/html',
            value: htmlContent,
          },
        ],
      }),
    });

    if (response.ok) {
      console.log('邮件通过 SendGrid 发送成功');
      return true;
    } else {
      const errorText = await response.text();
      console.error('SendGrid 邮件发送失败:', errorText);
      return false;
    }
  } catch (error) {
    console.error('SendGrid 邮件发送错误:', error);
    return false;
  }
}

// 使用其他邮件服务 API (例如 Mailgun)
async function sendViaMailgun(data: FormSubmissionData): Promise<boolean> {
  const apiKey = process.env.MAILGUN_API_KEY;
  const domain = process.env.MAILGUN_DOMAIN;
  
  if (!apiKey || !domain) {
    console.log('Mailgun 配置不完整');
    return false;
  }

  const adminEmail = process.env.ADMIN_EMAIL;
  const fromEmail = process.env.FROM_EMAIL || process.env.SMTP_USER;
  const fromName = process.env.FROM_NAME || '跨境电商网站';

  if (!adminEmail || !fromEmail) {
    console.log('邮件地址配置不完整');
    return false;
  }

  const { subject, htmlContent, textContent } = generateEmailContent(data);

  try {
    const formData = new FormData();
    formData.append('from', `${fromName} <${fromEmail}>`);
    formData.append('to', adminEmail);
    formData.append('subject', subject);
    formData.append('text', textContent);
    formData.append('html', htmlContent);

    const response = await fetch(`https://api.mailgun.net/v3/${domain}/messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${Buffer.from(`api:${apiKey}`).toString('base64')}`,
      },
      body: formData,
    });

    if (response.ok) {
      console.log('邮件通过 Mailgun 发送成功');
      return true;
    } else {
      const errorText = await response.text();
      console.error('Mailgun 邮件发送失败:', errorText);
      return false;
    }
  } catch (error) {
    console.error('Mailgun 邮件发送错误:', error);
    return false;
  }
}

// 主要的邮件发送函数
export async function sendSimpleFormNotification(data: FormSubmissionData): Promise<boolean> {
  console.log('准备发送表单提交通知邮件...');
  
  // 首先尝试 SendGrid
  const sendGridResult = await sendViaSendGrid(data);
  if (sendGridResult) {
    return true;
  }

  // 如果 SendGrid 失败，尝试 Mailgun
  const mailgunResult = await sendViaMailgun(data);
  if (mailgunResult) {
    return true;
  }

  // 如果都失败了，记录到控制台作为备用
  console.log('邮件发送失败，将通知信息记录到控制台:');
  console.log('='.repeat(50));
  const { textContent } = generateEmailContent(data);
  console.log(textContent);
  console.log('='.repeat(50));
  
  return false;
}
