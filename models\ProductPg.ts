import { query, withTransaction } from '../lib/db';
import { QueryResultRow } from 'pg';

// 产品数据接口
export interface IProduct {
  id?: number;
  slug: string;
  title: string;
  description: string;
  size: string;
  style?: string;
  type: string;
  features?: string[];
  images?: string[];

  isPublished?: boolean;
  translations?: {
    [locale: string]: {
      title?: string;
      description?: string;
      features?: string[];
    };
  };
  createdAt?: Date;
  updatedAt?: Date;
}

// 产品响应接口
export interface ProductWithRelations extends IProduct {
  features: string[];
  images: string[];

}

// PostgreSQL 产品记录接口
interface ProductRow extends QueryResultRow {
  id: number;
  slug: string;
  title: string;
  description: string;
  size: string;
  style: string | null;
  type: string;
  is_published: boolean;
  created_at: Date;
  updated_at: Date;
}

// 创建产品
export async function createProduct(product: IProduct): Promise<ProductWithRelations> {
  return withTransaction(async client => {
    // 1. 插入产品基本信息
    const { rows } = await client.query(
      `INSERT INTO products
        (slug, title, description, size, style, type, is_published)
       VALUES
        ($1, $2, $3, $4, $5, $6, $7)
       RETURNING *`,
      [
        product.slug,
        product.title,
        product.description,
        product.size,
        product.style || null,
        product.type,
        product.isPublished || false,
      ]
    );

    const newProduct = rows[0] as ProductRow;

    // 2. 插入产品特性
    if (product.features && product.features.length > 0) {
      for (const feature of product.features) {
        await client.query(`INSERT INTO product_features (product_id, feature) VALUES ($1, $2)`, [
          newProduct.id,
          feature,
        ]);
      }
    }

    // 3. 插入产品图片
    if (product.images && product.images.length > 0) {
      for (const imageUrl of product.images) {
        await client.query(`INSERT INTO product_images (product_id, image_url) VALUES ($1, $2)`, [
          newProduct.id,
          imageUrl,
        ]);
      }
    }



    // 5. 插入翻译内容
    if (product.translations) {
      for (const [locale, translation] of Object.entries(product.translations)) {
        await client.query(
          `INSERT INTO product_translations (product_id, locale, title, description)
           VALUES ($1, $2, $3, $4)`,
          [newProduct.id, locale, translation.title || null, translation.description || null]
        );

        // 插入特性翻译
        if (translation.features && translation.features.length > 0) {
          // 获取所有该产品的特性
          const { rows: features } = await client.query(
            `SELECT id, feature FROM product_features WHERE product_id = $1`,
            [newProduct.id]
          );

          // 遍历翻译特性并插入
          for (let i = 0; i < Math.min(features.length, translation.features.length); i++) {
            await client.query(
              `INSERT INTO feature_translations (feature_id, locale, feature)
               VALUES ($1, $2, $3)`,
              [features[i].id, locale, translation.features[i]]
            );
          }
        }
      }
    }

    // 获取完整的产品信息并返回
    const result = await getProductBySlug(product.slug);
    if (!result) {
      throw new Error(`Failed to retrieve created product: ${product.slug}`);
    }
    return result;
  });
}

// 根据slug获取产品
export async function getProductBySlug(slug: string): Promise<ProductWithRelations | null> {
  // 获取产品基础信息
  const { rows: products } = await query(`SELECT * FROM products WHERE slug = $1`, [slug]);

  if (products.length === 0) {
    return null;
  }

  const product = products[0] as ProductRow;

  // 获取产品特性
  const { rows: featureRows } = await query(
    `SELECT feature FROM product_features WHERE product_id = $1`,
    [product.id]
  );

  // 获取产品图片
  const { rows: imageRows } = await query(
    `SELECT image_url FROM product_images WHERE product_id = $1`,
    [product.id]
  );



  // 获取产品翻译
  const { rows: translationRows } = await query(
    `SELECT locale, title, description
     FROM product_translations
     WHERE product_id = $1`,
    [product.id]
  );

  // 构建翻译对象
  const translations: {
    [locale: string]: { title?: string; description?: string; features?: string[] };
  } = {};

  for (const translation of translationRows) {
    translations[translation.locale] = {
      title: translation.title,
      description: translation.description,
    };

    // 获取特性翻译
    const { rows: featureTranslations } = await query(
      `SELECT ft.feature
       FROM feature_translations ft
       JOIN product_features pf ON ft.feature_id = pf.id
       WHERE pf.product_id = $1 AND ft.locale = $2`,
      [product.id, translation.locale]
    );

    if (featureTranslations.length > 0) {
      translations[translation.locale].features = featureTranslations.map(ft => ft.feature);
    }
  }

  // 组装并返回产品数据
  return {
    id: product.id,
    slug: product.slug,
    title: product.title,
    description: product.description,
    size: product.size,
    style: product.style || undefined,
    type: product.type,
    features: featureRows.map(row => row.feature),
    images: imageRows.map(row => row.image_url),

    isPublished: product.is_published,
    translations: Object.keys(translations).length > 0 ? translations : undefined,
    createdAt: product.created_at,
    updatedAt: product.updated_at,
  };
}

// 获取所有产品
export async function getAllProducts(options?: {
  limit?: number;
  offset?: number;
  isPublished?: boolean;
  type?: string;

}): Promise<ProductWithRelations[]> {
  let queryText = `
    SELECT p.*
    FROM products p
  `;

  const params: any[] = [];
  const whereClause: string[] = [];



  // 如果指定了发布状态
  if (options?.isPublished !== undefined) {
    whereClause.push(`p.is_published = $${params.length + 1}`);
    params.push(options.isPublished);
  }

  // 如果指定了产品类型
  if (options?.type) {
    whereClause.push(`p.type = $${params.length + 1}`);
    params.push(options.type);
  }

  // 添加WHERE子句
  if (whereClause.length > 0) {
    queryText += ` WHERE ${whereClause.join(' AND ')}`;
  }

  // 添加排序
  queryText += ` ORDER BY p.created_at DESC`;

  // 添加分页
  if (options?.limit) {
    queryText += ` LIMIT $${params.length + 1}`;
    params.push(options.limit);

    if (options?.offset) {
      queryText += ` OFFSET $${params.length + 1}`;
      params.push(options.offset);
    }
  }

  const { rows: products } = await query(queryText, params);

  // 获取每个产品的相关数据（特性、图片、分类）
  const result: ProductWithRelations[] = [];

  for (const product of products) {
    const productWithRelations = await getProductBySlug((product as ProductRow).slug);
    if (productWithRelations) {
      result.push(productWithRelations);
    }
  }

  return result;
}

// 更新产品
export async function updateProduct(
  slug: string,
  updates: Partial<IProduct>
): Promise<ProductWithRelations | null> {
  return withTransaction(async client => {
    // 首先检查产品是否存在
    const { rows: products } = await client.query(`SELECT * FROM products WHERE slug = $1`, [slug]);

    if (products.length === 0) {
      return null;
    }

    const product = products[0] as ProductRow;

    // 构建更新SQL
    const updateFields: string[] = [];
    const values: any[] = [];
    let paramCounter = 1;

    if (updates.title !== undefined) {
      updateFields.push(`title = $${paramCounter++}`);
      values.push(updates.title);
    }

    if (updates.description !== undefined) {
      updateFields.push(`description = $${paramCounter++}`);
      values.push(updates.description);
    }

    if (updates.size !== undefined) {
      updateFields.push(`size = $${paramCounter++}`);
      values.push(updates.size);
    }

    if (updates.style !== undefined) {
      updateFields.push(`style = $${paramCounter++}`);
      values.push(updates.style);
    }

    if (updates.type !== undefined) {
      updateFields.push(`type = $${paramCounter++}`);
      values.push(updates.type);
    }

    if (updates.isPublished !== undefined) {
      updateFields.push(`is_published = $${paramCounter++}`);
      values.push(updates.isPublished);
    }

    if (updates.slug !== undefined && updates.slug !== slug) {
      updateFields.push(`slug = $${paramCounter++}`);
      values.push(updates.slug);
    }

    // 更新时间戳
    updateFields.push(`updated_at = NOW()`);

    // 如果有字段需要更新
    if (updateFields.length > 0) {
      values.push(product.id);
      await client.query(
        `UPDATE products SET ${updateFields.join(', ')} WHERE id = $${paramCounter}`,
        values
      );
    }

    // 更新特性（如果提供了）
    if (updates.features !== undefined) {
      // 删除旧特性
      await client.query(`DELETE FROM product_features WHERE product_id = $1`, [product.id]);

      // 添加新特性
      if (updates.features && updates.features.length > 0) {
        for (const feature of updates.features) {
          await client.query(`INSERT INTO product_features (product_id, feature) VALUES ($1, $2)`, [
            product.id,
            feature,
          ]);
        }
      }
    }

    // 更新图片（如果提供了）
    if (updates.images !== undefined) {
      // 删除旧图片
      await client.query(`DELETE FROM product_images WHERE product_id = $1`, [product.id]);

      // 添加新图片
      if (updates.images && updates.images.length > 0) {
        for (const imageUrl of updates.images) {
          await client.query(`INSERT INTO product_images (product_id, image_url) VALUES ($1, $2)`, [
            product.id,
            imageUrl,
          ]);
        }
      }
    }



    // 更新翻译（如果提供了）
    if (updates.translations !== undefined) {
      for (const [locale, translation] of Object.entries(updates.translations)) {
        // 检查此语言的翻译是否已存在
        const { rows: existingTranslations } = await client.query(
          `SELECT id FROM product_translations WHERE product_id = $1 AND locale = $2`,
          [product.id, locale]
        );

        if (existingTranslations.length > 0) {
          // 更新现有翻译
          await client.query(
            `UPDATE product_translations
             SET title = $1, description = $2, updated_at = NOW()
             WHERE product_id = $3 AND locale = $4`,
            [translation.title || null, translation.description || null, product.id, locale]
          );
        } else {
          // 创建新翻译
          await client.query(
            `INSERT INTO product_translations (product_id, locale, title, description)
             VALUES ($1, $2, $3, $4)`,
            [product.id, locale, translation.title || null, translation.description || null]
          );
        }

        // 处理特性翻译
        if (translation.features !== undefined) {
          // 获取产品特性
          const { rows: features } = await client.query(
            `SELECT id FROM product_features WHERE product_id = $1`,
            [product.id]
          );

          // 删除现有的特性翻译
          for (const feature of features) {
            await client.query(
              `DELETE FROM feature_translations
               WHERE feature_id = $1 AND locale = $2`,
              [feature.id, locale]
            );
          }

          // 添加新的特性翻译
          if (translation.features && translation.features.length > 0) {
            for (let i = 0; i < Math.min(features.length, translation.features.length); i++) {
              await client.query(
                `INSERT INTO feature_translations (feature_id, locale, feature)
                 VALUES ($1, $2, $3)`,
                [features[i].id, locale, translation.features[i]]
              );
            }
          }
        }
      }
    }

    // 返回更新后的产品
    return getProductBySlug(updates.slug || slug);
  });
}

// 删除产品
export async function deleteProduct(slug: string): Promise<boolean> {
  const result = await query(`DELETE FROM products WHERE slug = $1`, [slug]);

  return result.rowCount ? result.rowCount > 0 : false;
}

// 搜索产品
export async function searchProducts(
  searchTerm: string,
  options?: {
    limit?: number;
    offset?: number;
    isPublished?: boolean;
  }
): Promise<ProductWithRelations[]> {
  const params: any[] = [searchTerm];
  let whereClause = `to_tsvector('english', p.title || ' ' || p.description) @@ plainto_tsquery('english', $1)`;

  if (options?.isPublished !== undefined) {
    whereClause += ` AND p.is_published = $${params.length + 1}`;
    params.push(options.isPublished);
  }

  let queryText = `
    SELECT p.*
    FROM products p
    WHERE ${whereClause}
    ORDER BY ts_rank(to_tsvector('english', p.title || ' ' || p.description), plainto_tsquery('english', $1)) DESC
  `;

  if (options?.limit) {
    queryText += ` LIMIT $${params.length + 1}`;
    params.push(options.limit);

    if (options?.offset) {
      queryText += ` OFFSET $${params.length + 1}`;
      params.push(options.offset);
    }
  }

  const { rows: products } = await query(queryText, params);

  // 获取每个产品的相关数据
  const result: ProductWithRelations[] = [];

  for (const product of products) {
    const productWithRelations = await getProductBySlug((product as ProductRow).slug);
    if (productWithRelations) {
      result.push(productWithRelations);
    }
  }

  return result;
}
