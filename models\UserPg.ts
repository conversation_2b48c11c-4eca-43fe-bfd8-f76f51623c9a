import { query, withTransaction } from '../lib/db';
import { QueryResultRow } from 'pg';
import bcrypt from 'bcryptjs';

// 用户数据接口
export interface IUser {
  id?: number;
  username: string;
  email: string;
  password: string; // 在应用层面仍使用 password 作为字段名，但存储时会用 password_hash
  role: 'admin' | 'editor' | 'user';
  createdAt?: Date;
  updatedAt?: Date;
}

// PostgreSQL 用户记录接口
interface UserRow extends QueryResultRow {
  id: number;
  username: string;
  email: string;
  password_hash: string; // 数据库中的字段名是 password_hash
  role: string;
  created_at: Date;
  updated_at: Date;
}

// 创建用户
export async function createUser(user: IUser): Promise<IUser> {
  return withTransaction(async client => {
    const { rows } = await client.query(
      `INSERT INTO users
        (username, email, password_hash, role)
       VALUES
        ($1, $2, $3, $4)
       RETURNING *`,
      [user.username, user.email, await hashPassword(user.password), user.role]
    );

    const newUser = rows[0] as UserRow;
    return mapUserRow(newUser);
  });
}

// 查找用户（用于登录）
export async function findUserByCredentials(credentials: {
  username?: string;
  email?: string;
}): Promise<IUser | null> {
  // 构建查询条件 - 优先使用用户名，如果没有则使用邮箱
  let sqlQuery = 'SELECT * FROM users WHERE ';
  let params = [];

  if (credentials.username) {
    sqlQuery += 'username = $1';
    params.push(credentials.username);
  } else if (credentials.email) {
    sqlQuery += 'email = $1';
    params.push(credentials.email);
  } else {
    return null;
  }

  const { rows } = await query(sqlQuery, params);

  if (rows.length === 0) {
    return null;
  }

  return mapUserRow(rows[0] as UserRow);
}

// 根据ID获取用户
export async function getUserById(id: number): Promise<IUser | null> {
  const { rows } = await query(`SELECT * FROM users WHERE id = $1`, [id]);

  if (rows.length === 0) {
    return null;
  }

  return mapUserRow(rows[0] as UserRow);
}

// 获取所有用户
export async function getAllUsers(): Promise<IUser[]> {
  const { rows } = await query(`SELECT * FROM users ORDER BY username`);

  return rows.map(row => mapUserRow(row as UserRow));
}

// 更新用户
export async function updateUser(id: number, updates: Partial<IUser>): Promise<IUser | null> {
  return withTransaction(async client => {
    // 1. 获取当前用户
    const { rows: existingRows } = await client.query(`SELECT * FROM users WHERE id = $1`, [id]);

    if (existingRows.length === 0) {
      return null;
    }

    // 2. 更新用户信息
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramCounter = 1;

    if (updates.username !== undefined) {
      updateFields.push(`username = $${paramCounter++}`);
      updateValues.push(updates.username);
    }

    if (updates.email !== undefined) {
      updateFields.push(`email = $${paramCounter++}`);
      updateValues.push(updates.email);
    }

    if (updates.password !== undefined) {
      updateFields.push(`password_hash = $${paramCounter++}`);
      updateValues.push(await hashPassword(updates.password));
    }

    if (updates.role !== undefined) {
      updateFields.push(`role = $${paramCounter++}`);
      updateValues.push(updates.role);
    }

    if (updateFields.length > 0) {
      updateFields.push(`updated_at = NOW()`);
      const sql = `
        UPDATE users
        SET ${updateFields.join(', ')}
        WHERE id = $${paramCounter}
        RETURNING *
      `;
      updateValues.push(id);

      const { rows } = await client.query(sql, updateValues);
      return mapUserRow(rows[0] as UserRow);
    }

    // 如果没有更新，返回现有用户
    return mapUserRow(existingRows[0] as UserRow);
  });
}

// 删除用户
export async function deleteUser(id: number): Promise<boolean> {
  const { rowCount } = await query(`DELETE FROM users WHERE id = $1`, [id]);

  return rowCount !== null && rowCount > 0;
}

// 辅助函数：比较密码
export async function comparePassword(
  inputPassword: string,
  hashedPassword: string
): Promise<boolean> {
  if (!inputPassword || !hashedPassword) {
    throw new Error('比较密码时缺少必要参数');
  }

  try {
    return await bcrypt.compare(inputPassword, hashedPassword);
  } catch (error) {
    console.error('Password comparison error:', error);
    throw new Error('密码比较过程中出现错误');
  }
}

// 辅助函数：哈希密码
async function hashPassword(password: string): Promise<string> {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
}

// 辅助函数：转换数据库行到业务对象
function mapUserRow(row: UserRow): IUser {
  return {
    id: row.id,
    username: row.username,
    email: row.email,
    password: row.password_hash, // 数据库中的 password_hash 转换为应用层的 password
    role: row.role as 'admin' | 'editor' | 'user',
    createdAt: row.created_at,
    updatedAt: row.updated_at,
  };
}
