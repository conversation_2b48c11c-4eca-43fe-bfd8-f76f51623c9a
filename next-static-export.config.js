/**
 * Next.js Static Export Configuration
 *
 * This file contains configuration for the `next export` command
 * to exclude certain pages from static generation.
 */

module.exports = {
  // Pages that should be excluded from static export
  excludedPages: [
    // Admin pages - these use client-side authentication
    '/admin',
    '/admin/login',
    '/admin/users',
    '/admin/users/[id]',
    '/admin/products',
    '/admin/products/[id]',
    '/admin/categories',
    '/admin/categories/[id]',
    '/admin/settings',
    '/admin/content',
    '/admin/content/[id]',

    // Dynamic pages that should be rendered at runtime
    '/[lang]/products',
    '/products',

    // API routes (these are automatically excluded, but listed for clarity)
    '/api/auth/[...nextauth]',
    '/api/products',
    '/api/products/[id]',
    '/api/categories',
    '/api/categories/[id]',
    '/api/users',
    '/api/users/[id]',
  ],
};
