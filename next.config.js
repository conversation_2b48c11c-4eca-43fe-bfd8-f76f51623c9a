/** @type {import('next').NextConfig} */
const { existsSync, mkdirSync, cpSync } = require('fs');
const { join } = require('path');
const { excludedPages } = require('./next-static-export.config');

// 自定义构建后脚本，将public目录复制到standalone目录
const copyPublicFolder = () => {
  const publicDir = join(__dirname, 'public');
  const standalonePubDir = join(__dirname, '.next/standalone/public');

  if (existsSync(publicDir)) {
    // 确保目标目录存在
    if (!existsSync(standalonePubDir)) {
      mkdirSync(standalonePubDir, { recursive: true });
    }

    // 复制public目录到standalone目录
    cpSync(publicDir, standalonePubDir, { recursive: true });
    console.log('✅ Public folder copied to standalone directory');
  }
};

const nextConfig = {
  reactStrictMode: false,
  eslint: {
    // TODO: Fix ESLint errors and enable during build for better code quality
    // Currently disabled due to configuration migration needed
    ignoreDuringBuilds: true,
  },
  typescript: {
    // TODO: Fix TypeScript errors and enable during build for better type safety
    // Currently disabled due to existing type errors in codebase
    ignoreBuildErrors: true,
  },
  // 修改静态导出配置，使其与App Router兼容
  // App Router项目中使用generateStaticParams()替代exportPathMap
  // 图片优化配置
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '',
        pathname: '/**',
      },
    ],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60 * 60 * 24, // 24小时缓存
    // 启用图片尺寸优化
    dangerouslyAllowSVG: true,
    // 在standalone模式下，需要指定图片加载器和路径前缀
    unoptimized: true, // 在standalone模式下禁用图片优化，避免路径问题
  },
  // 启用压缩，减小资源大小
  compress: true,
  // 注意: export模式不支持自定义headers，缓存控制将通过部署平台配置
  // 优化实验性功能配置
  experimental: {
    // 确保CSS优化配置与已安装的依赖一致
    optimizeCss: true, // 已安装critters，可以启用CSS优化
    // 启用内存优化
    optimizeServerReact: true,
    // 启用服务端组件优化
    serverComponentsExternalPackages: [],
    // 禁用数据预取
    workerThreads: false,
    cpus: 1,
    // 优化开发环境性能
    ...(process.env.NODE_ENV === 'development' && {
      // 启用 Turbo 模式以提升性能
      turbo: {
        rules: {
          '*.css': ['css'],
          '*.scss': ['sass'],
        },
      },
    }),
  },
  // 环境变量配置
  env: {
    NEXT_PUBLIC_API_URL: process.env.NODE_ENV === 'production' ? '/api' : '/api', // 使用相对路径，让浏览器自动使用当前域名和端口
    NEXT_PUBLIC_API_HOST: '',
  },
  // 更新Webpack配置已移至下方合并
  // 部署配置 - 使用standalone模式以支持API路由
  output: 'standalone',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,

  // 添加自定义构建后脚本
  webpack: (config, { dev, isServer }) => {
    // 开发环境优化
    if (dev) {
      // 减少文件监听的敏感度，降低 Fast Refresh 频率
      config.watchOptions = {
        poll: 1000, // 每秒检查一次文件变化
        aggregateTimeout: 300, // 文件变化后等待300ms再重新构建
        ignored: [
          '**/node_modules/**',
          '**/.git/**',
          '**/.next/**',
          '**/public/**',
          '**/*.log',
          '**/.env*',
        ],
      };

      // 优化模块热替换
      config.optimization = {
        ...config.optimization,
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false,
      };
    }

    // 处理cloudflare:sockets模块
    config.resolve.fallback = {
      ...config.resolve.fallback,
      'cloudflare:sockets': false,
    };

    // 非开发环境启用模块压缩
    if (!dev) {
      config.optimization.minimize = true;

      // 添加更多生产环境优化
      if (!isServer) {
        // 客户端构建优化
        config.optimization.splitChunks = {
          chunks: 'all',
          cacheGroups: {
            default: false,
            vendors: false,
            // 将React相关库打包到一起
            framework: {
              name: 'framework',
              test: /[\\/]node_modules[\\/](react|react-dom|scheduler)[\\/]/,
              priority: 40,
              chunks: 'all',
            },
            // 将常用库打包到一起
            lib: {
              test: /[\\/]node_modules[\\/]/,
              priority: 30,
              chunks: 'all',
            },
            // 将共享组件打包到一起
            commons: {
              name: 'commons',
              minChunks: 2,
              priority: 20,
              chunks: 'all',
              reuseExistingChunk: true,
            },
            // 样式单独打包
            styles: {
              name: 'styles',
              test: /\.css$/,
              chunks: 'all',
              enforce: true,
            },
          },
        };
      }
    }

    // 在服务器构建完成后复制public文件夹到standalone目录
    if (isServer && !dev) {
      config.plugins.push({
        apply: compiler => {
          compiler.hooks.afterEmit.tap('CopyPublicToStandalone', () => {
            setTimeout(() => {
              try {
                copyPublicFolder();
              } catch (err) {
                console.error('Error copying public folder:', err);
              }
            }, 1000); // 延迟1秒执行，确保构建完成
          });
        },
      });
    }

    return config;
  },
};

// 导出配置
const config = nextConfig;

// 在生产环境下，我们已经在webpack配置中添加了afterEmit钩子来复制public文件夹
// 不需要额外的onPostBuild钩子，因为Next.js不支持这个选项
if (process.env.NODE_ENV === 'production') {
  console.log('🔄 Production build detected, will copy public folder after build');
  // 注意：实际的复制操作已经在webpack配置的afterEmit钩子中处理
}

module.exports = config;
