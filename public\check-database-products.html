<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库产品检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-weight: bold;
            white-space: pre-wrap;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn.success {
            background-color: #28a745;
        }
        .btn.success:hover {
            background-color: #218838;
        }
        .controls {
            text-align: center;
            margin-bottom: 30px;
        }
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 14px;
        }
        .products-table th,
        .products-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        .products-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        .products-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .products-table tr:hover {
            background-color: #f5f5f5;
        }
        .product-id {
            font-weight: bold;
            color: #007bff;
        }
        .product-name {
            font-weight: bold;
            color: #333;
        }
        .product-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-published {
            background-color: #d4edda;
            color: #155724;
        }
        .status-draft {
            background-color: #f8d7da;
            color: #721c24;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }
        .table-container {
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .links {
            text-align: center;
            margin-top: 30px;
        }
        .links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 15px;
            padding: 8px 16px;
            border: 1px solid #007bff;
            border-radius: 5px;
            display: inline-block;
        }
        .links a:hover {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 数据库产品检查</h1>

        <div class="controls">
            <button class="btn" onclick="checkDatabaseDirect()">🗄️ 检查数据库产品</button>
            <button class="btn success" onclick="checkProductsAPI()">📡 检查产品API</button>
        </div>

        <div id="status" class="status info">正在检查数据库产品...</div>

        <div id="summary-stats" class="summary-stats" style="display: none;"></div>

        <div id="products-container" style="display: none;">
            <h3>产品列表</h3>
            <div class="table-container">
                <table class="products-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>名称</th>
                            <th>Slug</th>
                            <th>类型</th>
                            <th>尺寸</th>
                            <th>状态</th>
                            <th>特色</th>
                            <th>创建时间</th>
                            <th>更新时间</th>
                        </tr>
                    </thead>
                    <tbody id="products-tbody">
                    </tbody>
                </table>
            </div>
        </div>

        <div class="links">
            <a href="/zh/admin/products">📦 产品管理页面</a>
            <a href="/zh/admin">🏠 管理员首页</a>
            <a href="/zh">🌐 网站首页</a>
        </div>
    </div>

    <script>


        async function checkProductsAPI() {
            const statusEl = document.getElementById('status');

            statusEl.className = 'status info';
            statusEl.textContent = '正在检查产品API...';

            try {
                console.log('检查产品API...');

                // 检查API响应时间和状态
                const startTime = Date.now();
                const response = await fetch('/api/products?limit=5');
                const endTime = Date.now();
                const responseTime = endTime - startTime;

                const data = await response.json();
                console.log('API响应:', data);

                if (response.ok && data.success) {
                    statusEl.className = 'status success';
                    statusEl.textContent = `✅ 产品API正常\n响应时间: ${responseTime}ms\n状态码: ${response.status}\n产品数量: ${data.products?.length || 0}`;
                } else {
                    statusEl.className = 'status error';
                    statusEl.textContent = `❌ 产品API异常\n状态码: ${response.status}\n错误: ${data.message || '未知错误'}`;
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ API检查失败: ${error.message}`;
                console.error('检查产品API时出错:', error);
            }
        }

        async function checkDatabaseDirect() {
            const statusEl = document.getElementById('status');
            const summaryEl = document.getElementById('summary-stats');
            const containerEl = document.getElementById('products-container');

            statusEl.className = 'status info';
            statusEl.textContent = '正在直接检查数据库...';
            summaryEl.style.display = 'none';
            containerEl.style.display = 'none';

            try {
                console.log('直接检查数据库...');

                const response = await fetch('/api/admin/check-database');
                const data = await response.json();

                console.log('数据库检查响应:', data);

                if (data.success && data.data) {
                    const { products, users, tables } = data.data;

                    statusEl.className = 'status success';
                    statusEl.textContent = `✅ 数据库检查成功\n产品总数: ${products.total}\n用户总数: ${users.total}\n数据表数: ${tables.length}`;

                    // 显示统计信息
                    const publishedCount = products.items.filter(p => p.is_published).length;
                    const draftCount = products.total - publishedCount;
                    const featuredCount = products.items.filter(p => p.is_featured).length;

                    summaryEl.innerHTML = `
                        <div class="stat-card">
                            <div class="stat-number">${products.total}</div>
                            <div class="stat-label">数据库产品总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${publishedCount}</div>
                            <div class="stat-label">已发布</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${draftCount}</div>
                            <div class="stat-label">草稿</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${featuredCount}</div>
                            <div class="stat-label">特色产品</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${users.total}</div>
                            <div class="stat-label">用户总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${tables.length}</div>
                            <div class="stat-label">数据表数</div>
                        </div>
                    `;
                    summaryEl.style.display = 'grid';

                    // 显示产品表格
                    const tbody = document.getElementById('products-tbody');
                    tbody.innerHTML = '';

                    if (products.items.length === 0) {
                        const row = document.createElement('tr');
                        row.innerHTML = '<td colspan="9" style="text-align: center; color: #666;">数据库中没有产品</td>';
                        tbody.appendChild(row);
                    } else {
                        products.items.forEach(product => {
                            const row = document.createElement('tr');
                            const isPublished = product.is_published;
                            const isFeatured = product.is_featured;

                            row.innerHTML = `
                                <td class="product-id">${product.id}</td>
                                <td class="product-name">${product.name || '未命名'}</td>
                                <td>${product.slug || '-'}</td>
                                <td>${product.type || '-'}</td>
                                <td>${product.size || '-'}</td>
                                <td>
                                    <span class="product-status ${isPublished ? 'status-published' : 'status-draft'}">
                                        ${isPublished ? '已发布' : '草稿'}
                                    </span>
                                    ${isFeatured ? '<br><small>⭐ 特色</small>' : ''}
                                </td>
                                <td>${isFeatured ? '是' : '否'}</td>
                                <td>${product.created_at ? new Date(product.created_at).toLocaleString() : '-'}</td>
                                <td>${product.updated_at ? new Date(product.updated_at).toLocaleString() : '-'}</td>
                            `;
                            tbody.appendChild(row);
                        });
                    }

                    containerEl.style.display = 'block';

                    // 在控制台显示详细信息
                    console.log('数据库表:', tables);
                    console.log('产品字段:', products.columns);
                    console.log('用户列表:', users.items);
                } else {
                    statusEl.className = 'status error';
                    statusEl.textContent = `❌ 数据库检查失败: ${data.message || '未知错误'}`;
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ 检查失败: ${error.message}`;
                console.error('直接检查数据库时出错:', error);
            }
        }

        // 页面加载时自动检查产品
        window.addEventListener('load', checkDatabaseDirect);
    </script>
</body>
</html>
