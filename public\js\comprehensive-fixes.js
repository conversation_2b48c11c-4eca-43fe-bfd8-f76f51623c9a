/**
 * 综合修复脚本
 * 包含多种客户端自动修复功能
 */
(function() {


  // 是否为开发环境
  const isDev = window.location.hostname === 'localhost' ||
                window.location.hostname === '127.0.0.1';



  /**
   * 1. 修复高对比度模式警告
   * 替换所有CSS中的 -ms-high-contrast 为 forced-colors
   */
  function fixHighContrastWarnings() {


    // 处理已加载的样式表
    Array.from(document.styleSheets).forEach(styleSheet => {
      try {
        // 确保可以访问样式表规则
        const rules = styleSheet.cssRules || styleSheet.rules;

        if (!rules) return;

        for (let i = 0; i < rules.length; i++) {
          const rule = rules[i];

          // 处理CSS规则
          if (rule.cssText && rule.cssText.includes('-ms-high-contrast')) {
            // 对于样式规则，使用currentStyle替换内容
            if (rule.style) {
              const cssText = rule.cssText;
              const fixedCss = cssText.replace(/-ms-high-contrast\s*:\s*[^;]+;?/g, 'forced-colors: active;');

              // 如果内容被修改，则应用修复
              if (cssText !== fixedCss) {
                // 删除原规则
                styleSheet.deleteRule(i);

                // 添加修复后的规则
                styleSheet.insertRule(fixedCss, i);

                // 由于修改了集合，需要调整索引
                i--;
              }
            }
          }
        }
      } catch (e) {
        // 跨域样式表无法访问规则，忽略错误
      }
    });

    // 通过拦截DOM API进行实时修复
    const originalSetAttribute = Element.prototype.setAttribute;
    Element.prototype.setAttribute = function(name, value) {
      // 修复style属性中的-ms-high-contrast
      if (name === 'style' && typeof value === 'string' && value.includes('-ms-high-contrast')) {
        value = value.replace(/-ms-high-contrast\s*:\s*[^;]+;?/g, 'forced-colors: active;');
      }

      return originalSetAttribute.call(this, name, value);
    };

    // 拦截样式表操作
    const originalInsertRule = CSSStyleSheet.prototype.insertRule;
    CSSStyleSheet.prototype.insertRule = function(rule, index) {
      // 修复规则中的-ms-high-contrast
      if (rule.includes('-ms-high-contrast')) {
        rule = rule.replace(/-ms-high-contrast\s*:\s*[^;]+;?/g, 'forced-colors: active;');
      }

      return originalInsertRule.call(this, rule, index);
    };


  }

  /**
   * 2. 修复API端口问题
   * 确保API请求使用正确的端口
   */
  function fixApiPortIssues() {
    // 获取当前端口
    const currentPort = window.location.port || '';

    // 保存原始fetch函数
    const originalFetch = window.fetch;

    // 重写fetch函数
    window.fetch = function(url, options) {
      // 修复URL中的端口
      if (typeof url === 'string') {
        // 只处理相对URL或同源URL
        if (url.startsWith('/') || url.startsWith(window.location.origin)) {
          // 3000端口和3001端口相互转换
          if (currentPort === '3001' && url.includes(':3000/')) {
            url = url.replace(':3000/', ':3001/');
          } else if (currentPort === '3000' && url.includes(':3001/')) {
            url = url.replace(':3001/', ':3000/');
          }
        }
      }

      // 调用原始fetch
      return originalFetch.call(this, url, options);
    };

    // 修复XMLHttpRequest
    const originalOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
      // 修复URL中的端口
      if (typeof url === 'string') {
        // 只处理相对URL或同源URL
        if (url.startsWith('/') || url.startsWith(window.location.origin)) {
          // 3000端口和3001端口相互转换
          if (currentPort === '3001' && url.includes(':3000/')) {
            url = url.replace(':3000/', ':3001/');
          } else if (currentPort === '3000' && url.includes(':3001/')) {
            url = url.replace(':3001/', ':3000/');
          }
        }
      }

      // 调用原始open方法
      return originalOpen.call(this, method, url, ...args);
    };


  }

  /**
   * 3. 修复页面链接
   * 确保页面上的链接使用正确的端口
   */
  function fixPageLinks() {


    // 获取当前端口
    const currentPort = window.location.port || '';

    // 修复函数
    function updateLinks() {
      // 处理所有链接
      document.querySelectorAll('a[href]').forEach(link => {
        const href = link.getAttribute('href');

        // 跳过空链接和锚点链接
        if (!href || href.startsWith('#')) return;

        // 只处理相对URL或同源URL
        if (href.startsWith('/') || href.startsWith(window.location.origin) ||
            href.startsWith('http://localhost') || href.startsWith('http://127.0.0.1')) {
          // 需要修复的情况
          let needsFix = false;
          let fixedHref = href;

          // 3000端口和3001端口相互转换
          if (currentPort === '3001' && href.includes(':3000/')) {
            fixedHref = href.replace(':3000/', ':3001/');
            needsFix = true;
          } else if (currentPort === '3000' && href.includes(':3001/')) {
            fixedHref = href.replace(':3001/', ':3000/');
            needsFix = true;
          }

          // 应用修复
          if (needsFix) {
            if (isDev) {
              console.log(`[综合修复] 修复链接: ${href} -> ${fixedHref}`);
            }
            link.setAttribute('href', fixedHref);
          }
        }
      });
    }

    // 初始修复
    updateLinks();

    // 监听DOM变化，持续修复
    const observer = new MutationObserver(mutations => {
      let hasNewLinks = false;

      // 检查是否有新链接被添加
      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach(node => {
            // 检查节点是否为元素且包含链接
            if (node.nodeType === 1) { // ELEMENT_NODE
              if (node.tagName === 'A' && node.hasAttribute('href')) {
                hasNewLinks = true;
              } else if (node.querySelectorAll) {
                // 检查子元素
                const links = node.querySelectorAll('a[href]');
                if (links.length > 0) {
                  hasNewLinks = true;
                }
              }
            }
          });
        }
      });

      // 如果有新链接，则更新
      if (hasNewLinks) {
        updateLinks();
      }
    });

    // 开始观察整个文档
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    console.log('[综合修复] 页面链接修复完成');
  }

  // 执行所有修复
  fixHighContrastWarnings();
  fixApiPortIssues();
  // 延迟执行链接修复，确保页面已加载
  window.addEventListener('DOMContentLoaded', fixPageLinks);

  // 导出全局修复对象，方便调试和手动调用
  window.AppFixes = {
    fixHighContrastWarnings,
    fixApiPortIssues,
    fixPageLinks,
    reapplyAllFixes: function() {
      fixHighContrastWarnings();
      fixApiPortIssues();
      fixPageLinks();
      console.log('[综合修复] 已重新应用所有修复');
    }
  };

  console.log('[综合修复] 所有修复已初始化');
})();