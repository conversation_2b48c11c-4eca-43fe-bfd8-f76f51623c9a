/**
 * 强制浅色模式脚本
 * 防止网站进入深色模式，避免UI黑屏问题
 */
(function() {
  // 立即应用浅色模式
  function forceLightMode() {
    // 修改HTML元素
    document.documentElement.classList.remove('dark');
    document.documentElement.classList.add('light');
    document.documentElement.setAttribute('data-theme', 'light');
    document.documentElement.setAttribute('data-color-mode', 'light');
    
    // 修改BODY元素
    if (document.body) {
      document.body.classList.remove('dark', 'dark-mode');
      document.body.classList.add('light', 'light-mode');
      document.body.setAttribute('data-theme', 'light');
      document.body.setAttribute('data-color-mode', 'light');
    }
    
    // 移除任何可能存在的暗色模式属性
    localStorage.removeItem('theme');
    localStorage.removeItem('color-mode');
    localStorage.removeItem('darkMode');
    localStorage.removeItem('dark-mode');
    localStorage.setItem('theme', 'light');
    localStorage.setItem('color-mode', 'light');
    
    // 修改媒体查询偏好
    const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    if (darkModeMediaQuery.matches) {
      console.log('检测到系统深色模式，强制使用浅色模式');
    }
    
    // 覆盖媒体查询行为（如果浏览器支持）
    if (typeof darkModeMediaQuery.addListener === 'function') {
      darkModeMediaQuery.addListener((e) => {
        if (e.matches) {
          applyLightModeOverrides();
        }
      });
    } else if (typeof darkModeMediaQuery.addEventListener === 'function') {
      darkModeMediaQuery.addEventListener('change', (e) => {
        if (e.matches) {
          applyLightModeOverrides();
        }
      });
    }
  }
  
  // 应用浅色模式样式覆盖
  function applyLightModeOverrides() {
    // 创建一个样式元素来覆盖深色模式
    const styleEl = document.createElement('style');
    styleEl.setAttribute('id', 'light-mode-override');
    styleEl.setAttribute('type', 'text/css');
    styleEl.textContent = `
      :root {
        color-scheme: light !important;
      }
      
      body, .dark, [data-theme="dark"], [data-mode="dark"], [data-color-mode="dark"] {
        background-color: #ffffff !important;
        color: #333333 !important;
      }
      
      .bg-black, .bg-gray-800, .bg-gray-900, 
      .dark\\:bg-black, .dark\\:bg-gray-800, .dark\\:bg-gray-900 {
        background-color: #f8f9fa !important;
      }
      
      .text-white, .dark\\:text-white {
        color: #333333 !important;
      }
      
      /* 针对特定组件的覆盖 */
      .header, .footer, .navbar, .sidebar {
        background-color: #ffffff !important;
        color: #333333 !important;
      }
      
      /* 按钮覆盖 */
      .btn-dark, .dark\\:btn {
        background-color: #f8f9fa !important;
        color: #333333 !important;
        border-color: #dee2e6 !important;
      }
      
      /* 输入框覆盖 */
      input, textarea, select {
        background-color: #ffffff !important;
        color: #333333 !important;
        border-color: #ced4da !important;
      }
    `;
    
    // 添加到文档头部
    document.head.appendChild(styleEl);
  }
  
  // 监听DOM变化，防止动态添加的深色模式类
  function observeDarkModeChanges() {
    if (!window.MutationObserver) return;
    
    const observer = new MutationObserver((mutations) => {
      let needsUpdate = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'class' || 
             mutation.attributeName === 'data-theme' || 
             mutation.attributeName === 'data-color-mode')) {
          needsUpdate = true;
        }
      });
      
      if (needsUpdate) {
        forceLightMode();
      }
    });
    
    // 观察文档和body元素
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class', 'data-theme', 'data-color-mode']
    });
    
    if (document.body) {
      observer.observe(document.body, {
        attributes: true,
        attributeFilter: ['class', 'data-theme', 'data-color-mode']
      });
    }
  }
  
  // 初始应用
  forceLightMode();
  applyLightModeOverrides();
  
  // 当DOM准备好时再次应用，并开始观察
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      forceLightMode();
      observeDarkModeChanges();
    });
  } else {
    observeDarkModeChanges();
  }
  
  // 在窗口加载完成后再次应用
  window.addEventListener('load', forceLightMode);
})(); 