
/**
 * 修复高对比度模式
 * 动态替换页面上所有样式表中的-ms-high-contrast
 */
(function() {
  // 替换规则
  const replacements = [
    {
      from: /@media\s+\(\s*-ms-high-contrast\s*:\s*active\s*\)/g,
      to: '@media (forced-colors: active)'
    },
    {
      from: /@media\s+\(\s*-ms-high-contrast\s*:\s*none\s*\)/g,
      to: '@media (forced-colors: none)'
    },
    {
      from: /@media\s+\(\s*-ms-high-contrast\s*:\s*black-on-white\s*\)/g,
      to: '@media (forced-colors: active)'
    },
    {
      from: /@media\s+\(\s*-ms-high-contrast\s*:\s*white-on-black\s*\)/g,
      to: '@media (forced-colors: active)'
    }
  ];

  // 处理所有样式表
  function fixStylesheets() {
    const styleSheets = document.styleSheets;

    for (let i = 0; i < styleSheets.length; i++) {
      try {
        const sheet = styleSheets[i];

        // 跳过非同源样式表（CORS限制）
        if (!sheet.href || sheet.href.startsWith(window.location.origin) || sheet.href.startsWith('/')) {
          const rules = sheet.cssRules || sheet.rules;

          if (rules) {
            for (let j = 0; j < rules.length; j++) {
              const rule = rules[j];

              // 检查并替换@media规则
              if (rule.type === CSSRule.MEDIA_RULE && rule.conditionText && rule.conditionText.includes('-ms-high-contrast')) {
                for (const replacement of replacements) {
                  if (replacement.from.test(rule.conditionText)) {
                    // 由于无法直接修改conditionText，我们需要删除旧规则并添加新规则
                    const cssText = rule.cssText.replace(replacement.from, replacement.to);
                    sheet.deleteRule(j);
                    sheet.insertRule(cssText, j);

                    break;
                  }
                }
              }
            }
          }
        }
      } catch (e) {
        // 跨域样式表会抛出安全错误，忽略它们
      }
    }
  }

  // 拦截CSS API
  const originalInsertRule = CSSStyleSheet.prototype.insertRule;
  CSSStyleSheet.prototype.insertRule = function(rule, index) {
    // 检查规则是否包含-ms-high-contrast
    let newRule = rule;

    for (const replacement of replacements) {
      if (replacement.from.test(rule)) {
        newRule = rule.replace(replacement.from, replacement.to);
        break;
      }
    }

    // 调用原始方法
    return originalInsertRule.call(this, newRule, index);
  };

  // 处理动态添加的样式
  const observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        // 检查是否是样式元素
        if (node.nodeName === 'STYLE' || node.nodeName === 'LINK' && node.rel === 'stylesheet') {
          setTimeout(fixStylesheets, 0);
        }
      });
    });
  });

  // 开始观察DOM变化
  observer.observe(document.documentElement, {
    childList: true,
    subtree: true
  });

  // 初始处理
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', fixStylesheets);
  } else {
    fixStylesheets();
  }
})();
