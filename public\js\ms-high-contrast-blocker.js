/**
 * MS High Contrast 阻止器 - 终极版 v2
 *
 * 此脚本通过劫持CSS APIs来彻底阻止任何-ms-high-contrast媒体查询
 * 并增加早期干预、轮询检查和尝试处理Shadow DOM的机制
 */
(function () {
  // 标记脚本是否已初始化
  let initialized = false;
  let pollingInterval = null;
  let pollCount = 0;
  const MAX_POLLS = 20; // 轮询20次（大约10秒）
  const POLLING_INTERVAL_MS = 500; // 每500毫秒轮询一次

  // 核心清理和样式覆盖函数
  function applyFixes() {
    addOverrideStyles();
    removeExistingRules(document);
    iterateShadowDOMs(document.body);
  }

  // 1. 劫持insertRule方法
  if (typeof CSSStyleSheet !== 'undefined' && CSSStyleSheet.prototype.insertRule) {
    const originalInsertRule = CSSStyleSheet.prototype.insertRule;
    CSSStyleSheet.prototype.insertRule = function (rule, index) {
      if (typeof rule === 'string' && rule.includes('-ms-high-contrast')) {
        rule = rule.replace(
          /@media\s*\(\s*-ms-high-contrast\s*:([^)]*)\)/g,
          '@media (forced-colors: active)'
        );
      }
      try {
        return originalInsertRule.call(this, rule, index);
      } catch (e) {
        // 在某些情况下，即使规则被修改，原始调用也可能失败，尤其是在严格的CSP环境中
        // 忽略错误，因为我们的目标是阻止原始规则
        return null;
      }
    };
  }

  // 2. 劫持addRule方法 (旧版本浏览器)
  if (typeof CSSStyleSheet !== 'undefined' && CSSStyleSheet.prototype.addRule) {
    const originalAddRule = CSSStyleSheet.prototype.addRule;
    CSSStyleSheet.prototype.addRule = function (selector, cssText, index) {
      if (typeof selector === 'string' && selector.includes('-ms-high-contrast')) {
        selector = selector.replace(/-ms-high-contrast/g, 'forced-colors');
      }
      try {
        return originalAddRule.call(this, selector, cssText, index);
      } catch (e) {
        return null;
      }
    };
  }

  // 3. 劫持CSS媒体查询的构造函数
  if (typeof window !== 'undefined' && typeof window.matchMedia !== 'undefined') {
    const originalMatchMedia = window.matchMedia;
    window.matchMedia = function (mediaQuery) {
      if (typeof mediaQuery === 'string' && mediaQuery.includes('-ms-high-contrast')) {
        mediaQuery = mediaQuery.replace(/-ms-high-contrast/g, 'forced-colors');
      }
      return originalMatchMedia.call(window, mediaQuery);
    };
  }

  // 4. 添加样式覆盖
  function addOverrideStyles() {
    if (!document.head) return; // 确保head存在
    const styleId = 'ms-high-contrast-override-styles';
    if (document.getElementById(styleId)) return; // 防止重复添加

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      @media (forced-colors: active),
             (forced-colors: black-on-white),
             (forced-colors: white-on-black),
             (forced-colors: none) {
        * { forced-color-adjust: none !important; forced-color-adjust: none !important; }
      }
      @media (forced-colors: active) {
        * { forced-color-adjust: none !important; }
        a, button, input, select, textarea, [role="button"], .btn {
          forced-color-adjust: auto !important; /* 确保交互元素在高对比度模式下依然可用 */
        }
      }
    `;
    document.head.appendChild(style);
  }

  // 5. 扫描并移除已存在的-ms-high-contrast规则
  function removeExistingRules(docContext) {
    if (!docContext || !docContext.styleSheets) return;
    try {
      for (let i = 0; i < docContext.styleSheets.length; i++) {
        const sheet = docContext.styleSheets[i];
        try {
          if (!sheet.cssRules) continue;
          const rulesToRemove = [];
          for (let j = 0; j < sheet.cssRules.length; j++) {
            const rule = sheet.cssRules[j];
            if (rule.type === CSSRule.MEDIA_RULE) {
              let mediaText = rule.media?.mediaText || rule.conditionText || '';
              if (mediaText.includes('-ms-high-contrast')) {
                rulesToRemove.push(j);
              }
            }
          }
          for (let k = rulesToRemove.length - 1; k >= 0; k--) {
            try {
              sheet.deleteRule(rulesToRemove[k]);
            } catch (e) {}
          }
        } catch (e) {
          continue;
        }
      }
    } catch (e) {}
  }

  // 6. 遍历 Shadow DOM 并应用修复
  function iterateShadowDOMs(rootNode) {
    if (!rootNode) return;
    const elements = rootNode.querySelectorAll('*');
    elements.forEach(el => {
      if (el.shadowRoot) {
        removeExistingRules(el.shadowRoot);
        // 递归处理嵌套的 Shadow DOM
        iterateShadowDOMs(el.shadowRoot);
      }
    });
  }

  // 7. 设置MutationObserver监视新添加的样式表
  function setupObserver() {
    if (typeof MutationObserver === 'undefined') return;

    const observerCallback = function (mutations) {
      let styleChanged = false;
      for (const mutation of mutations) {
        if (mutation.type === 'childList') {
          const nodes = Array.from(mutation.addedNodes);
          styleChanged = nodes.some(
            node =>
              (node.nodeName === 'LINK' &&
                (node.rel === 'stylesheet' || node.href?.endsWith('.css'))) ||
              node.nodeName === 'STYLE'
          );
          if (styleChanged) break;
        }
      }
      if (styleChanged) {
        applyFixes(); // 当检测到样式变化时，重新运行所有修复
      }
    };

    const observer = new MutationObserver(observerCallback);
    // 观察head和body
    if (document.head) observer.observe(document.head, { childList: true, subtree: true });
    if (document.body) observer.observe(document.body, { childList: true, subtree: true });
  }

  // 8. 轮询检查函数
  function pollForChanges() {
    applyFixes();
    pollCount++;
    if (pollCount >= MAX_POLLS && pollingInterval) {
      clearInterval(pollingInterval);
      pollingInterval = null;
    }
  }

  // 9. 初始化函数
  function init() {
    if (initialized) return;
    initialized = true;

    applyFixes(); // 立即应用一次
    setupObserver();

    // 开始轮询
    if (!pollingInterval) {
      pollingInterval = setInterval(pollForChanges, POLLING_INTERVAL_MS);
    }

    // 在关键的加载事件点再次应用修复
    if (document.readyState !== 'complete') {
      document.addEventListener('DOMContentLoaded', applyFixes, { once: true });
      window.addEventListener('load', applyFixes, { once: true });
    } else {
      // 如果文档已经加载完毕，再次运行一次以确保捕获所有内容
      setTimeout(applyFixes, 0);
    }
  }

  // 尝试尽早执行，但也要确保DOM基本可用
  if (document.documentElement) {
    // 检查documentElement是否已存在
    init();
  } else {
    // 如果documentElement还不存在，等待它出现
    const earlyObserver = new MutationObserver(() => {
      if (document.documentElement) {
        earlyObserver.disconnect();
        init();
      }
    });
    earlyObserver.observe(document, { childList: true });
  }
})();

/**
 * 高对比度CSS API劫持脚本
 *
 * 这个脚本拦截并替换与-ms-high-contrast相关的CSS API调用
 * 用于解决Microsoft Edge浏览器中的-ms-high-contrast弃用警告
 */

(function() {
  'use strict';

  // 存储原始的CSS API
  const originalSetProperty = CSSStyleDeclaration.prototype.setProperty;
  const originalGetPropertyValue = CSSStyleDeclaration.prototype.getPropertyValue;
  const originalRemoveProperty = CSSStyleDeclaration.prototype.removeProperty;

  // 属性映射表 - 将-ms-high-contrast属性映射到现代替代方案
  const propertyMap = {
    "forced-colors": 'forced-colors',
    "forced-color-adjust": 'forced-color-adjust'
  };

  // 重写setProperty方法
  CSSStyleDeclaration.prototype.setProperty = function(propertyName, value, priority) {
    // 检查是否是需要替换的属性
    if (propertyName in propertyMap) {

      // 使用映射的现代属性替代
      return originalSetProperty.call(this, propertyMap[propertyName], value, priority);
    }
    // 否则使用原始方法
    return originalSetProperty.call(this, propertyName, value, priority);
  };

  // 重写getPropertyValue方法
  CSSStyleDeclaration.prototype.getPropertyValue = function(propertyName) {
    // 检查是否是需要替换的属性
    if (propertyName in propertyMap) {

      // 获取映射的现代属性值
      return originalGetPropertyValue.call(this, propertyMap[propertyName]);
    }
    // 否则使用原始方法
    return originalGetPropertyValue.call(this, propertyName);
  };

  // 重写removeProperty方法
  CSSStyleDeclaration.prototype.removeProperty = function(propertyName) {
    // 检查是否是需要替换的属性
    if (propertyName in propertyMap) {

      // 移除映射的现代属性
      return originalRemoveProperty.call(this, propertyMap[propertyName]);
    }
    // 否则使用原始方法
    return originalRemoveProperty.call(this, propertyName);
  };

  // 替换媒体查询API
  if (window.matchMedia) {
    const originalMatchMedia = window.matchMedia;
    window.matchMedia = function(query) {
      // 替换-ms-high-contrast媒体查询
      if (query.includes('-ms-high-contrast')) {
        const newQuery = query.replace(/\(\s*-ms-high-contrast\s*:\s*active\s*\)/g, '(forced-colors: active)');

        return originalMatchMedia.call(window, newQuery);
      }
      // 否则使用原始方法
      return originalMatchMedia.call(window, query);
    };
  }


})();
