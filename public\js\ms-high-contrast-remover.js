/**
 * 高对比度清理脚本
 *
 * 此脚本在DOM加载后运行，扫描并修复文档中的-ms-high-contrast相关样式
 */
(function() {
  'use strict';

  // 替换样式表中的-ms-high-contrast相关规则
  function replaceHighContrastRules() {
    // 遍历所有样式表
    for (let i = 0; i < document.styleSheets.length; i++) {
      try {
        const styleSheet = document.styleSheets[i];

        // 跳过跨域样式表
        if (!styleSheet.cssRules) continue;

        // 遍历所有CSS规则
        for (let j = 0; j < styleSheet.cssRules.length; j++) {
          const rule = styleSheet.cssRules[j];

          // 处理媒体查询规则
          if (rule.media && rule.media.mediaText.includes('-ms-high-contrast')) {
            const newMediaText = rule.media.mediaText.replace(
              /\(\s*-ms-high-contrast\s*:\s*active\s*\)/g,
              '(forced-colors: active)'
            );

            // 删除旧规则
            styleSheet.deleteRule(j);

            // 尝试添加新规则
            try {
              styleSheet.insertRule(
                `@media ${newMediaText} { ${rule.cssText.substring(rule.cssText.indexOf('{') + 1, rule.cssText.lastIndexOf('}'))} }`,
                j
              );

            } catch (error) {
              console.error('[High Contrast Fix] 替换媒体查询规则失败:', error);
            }
          }

          // 处理样式规则
          if (rule.style) {
            // 检查并替换-ms-high-contrast属性
            if (rule.style.getPropertyValue('-ms-high-contrast')) {
              const value = rule.style.getPropertyValue('-ms-high-contrast');
              rule.style.removeProperty('-ms-high-contrast');
              rule.style.setProperty('forced-colors', value);

            }

            // 检查并替换-ms-high-contrast-adjust属性
            if (rule.style.getPropertyValue('-ms-high-contrast-adjust')) {
              const value = rule.style.getPropertyValue('-ms-high-contrast-adjust');
              rule.style.removeProperty('-ms-high-contrast-adjust');
              rule.style.setProperty('forced-color-adjust', value);

            }
          }
        }
      } catch (error) {
        // 跨域样式表会抛出安全错误，忽略这些错误
      }
    }
  }

  // 检测高对比度模式
  function isHighContrastMode() {
    // 尝试使用现代API
    if (window.matchMedia('(forced-colors: active)').matches) {
      return true;
    }

    // 回退检测方法
    const testDiv = document.createElement('div');
    testDiv.style.border = '1px solid transparent';
    document.body.appendChild(testDiv);
    const computed = getComputedStyle(testDiv);
    const borderColor = computed.borderBottomColor;
    document.body.removeChild(testDiv);

    // 如果边框颜色不是透明的，可能处于高对比度模式
    return borderColor !== 'transparent' && borderColor !== 'rgba(0, 0, 0, 0)';
  }

  // 添加高对比度模式类名
  function addHighContrastClass() {
    if (isHighContrastMode()) {
      document.documentElement.classList.add('forced-colors-mode');
      document.documentElement.classList.remove('ms-high-contrast-mode');

    }
  }

  // 在DOM加载完成后运行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      replaceHighContrastRules();
      addHighContrastClass();
    });
  } else {
    replaceHighContrastRules();
    addHighContrastClass();
  }

  // 在window加载完成后再次运行，确保处理动态加载的样式表
  window.addEventListener('load', function() {
    replaceHighContrastRules();
    addHighContrastClass();
  });


})();
