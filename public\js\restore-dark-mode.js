/**
 * 恢复深色模式脚本
 * 移除强制浅色模式设置，恢复网站原有的深色设计
 */
(function() {
  console.log('[模式恢复] 正在恢复网站原有的深色模式...');
  
  // 移除强制浅色模式
  function restoreDarkMode() {
    // 移除HTML元素上的浅色模式类和属性
    document.documentElement.classList.remove('light');
    document.documentElement.classList.add('dark');
    document.documentElement.setAttribute('data-theme', 'dark');
    document.documentElement.setAttribute('data-color-mode', 'dark');
    
    // 移除BODY元素上的浅色模式类和属性
    if (document.body) {
      document.body.classList.remove('light', 'light-mode');
      document.body.classList.add('dark', 'dark-mode');
      document.body.setAttribute('data-theme', 'dark');
      document.body.setAttribute('data-color-mode', 'dark');
    }
    
    // 设置深色模式存储
    localStorage.setItem('theme', 'dark');
    localStorage.setItem('color-mode', 'dark');
    
    // 移除我们添加的任何强制浅色模式样式
    const lightModeOverride = document.getElementById('light-mode-override');
    if (lightModeOverride) {
      lightModeOverride.remove();
    }
  }
  
  // 添加恢复深色模式的CSS
  function addDarkModeStyles() {
    const styleEl = document.createElement('style');
    styleEl.setAttribute('id', 'dark-mode-restore');
    styleEl.setAttribute('type', 'text/css');
    styleEl.textContent = `
      /* 恢复深色模式 */
      :root {
        color-scheme: dark !important;
      }
      
      body {
        background-color: #121212 !important;
        color: #f5f5f5 !important;
      }
      
      /* 恢复深色背景 */
      .bg-black, .bg-gray-800, .bg-gray-900,
      .dark\\:bg-black, .dark\\:bg-gray-800, .dark\\:bg-gray-900 {
        background-color: #121212 !important;
      }
      
      /* 恢复深色文本 */
      .text-white, .dark\\:text-white {
        color: #f5f5f5 !important;
      }
      
      /* 恢复特定组件的深色样式 */
      .header, .footer, .navbar, .sidebar {
        background-color: #1e1e1e !important;
        color: #f5f5f5 !important;
      }
      
      /* 按钮恢复 */
      .btn-dark, .dark\\:btn {
        background-color: #343a40 !important;
        color: #f8f9fa !important;
        border-color: #495057 !important;
      }
      
      /* 输入框恢复 */
      .dark input, .dark textarea, .dark select,
      [data-theme="dark"] input, [data-theme="dark"] textarea, [data-theme="dark"] select {
        background-color: #2d2d2d !important;
        color: #f5f5f5 !important;
        border-color: #495057 !important;
      }
      
      /* 保留高对比度修复但不强制颜色 */
      @media (forced-colors: active) {
        a, button, input, select, textarea, [role="button"], .btn {
          forced-color-adjust: auto;
        }
      }
    `;
    
    // 添加到文档头部
    document.head.appendChild(styleEl);
  }
  
  // 禁用之前的脚本
  function disableLightModeScript() {
    // 尝试找到并禁用强制浅色模式的脚本
    const scripts = document.querySelectorAll('script');
    scripts.forEach(script => {
      if (script.src && (
          script.src.includes('disable-dark-mode.js') || 
          script.src.includes('light-mode-enforcer.js')
        )) {
        console.log('[模式恢复] 禁用强制浅色模式脚本:', script.src);
        script.setAttribute('disabled', 'true');
        script.setAttribute('data-disabled', 'true');
      }
    });
  }
  
  // 执行恢复
  restoreDarkMode();
  addDarkModeStyles();
  disableLightModeScript();
  
  // 监听DOM变化，防止再次被修改
  if (window.MutationObserver) {
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'class' || 
             mutation.attributeName === 'data-theme' || 
             mutation.attributeName === 'data-color-mode')) {
          
          const target = mutation.target;
          // 如果变回了浅色模式，再次恢复深色模式
          if (target.classList.contains('light') || 
              target.getAttribute('data-theme') === 'light' ||
              target.getAttribute('data-color-mode') === 'light') {
            
            console.log('[模式恢复] 检测到切换回浅色模式，再次恢复深色模式');
            restoreDarkMode();
          }
        }
      });
    });
    
    // 观察文档和body元素
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class', 'data-theme', 'data-color-mode']
    });
    
    if (document.body) {
      observer.observe(document.body, {
        attributes: true,
        attributeFilter: ['class', 'data-theme', 'data-color-mode']
      });
    }
  }
  
  console.log('[模式恢复] 深色模式已恢复');
})(); 