/**
 * 终极高对比度修复器
 * 
 * 这个脚本结合多种方法彻底解决-ms-high-contrast弃用警告
 */

(function() {
  // 1. 动态CSS方法 - 创建全局样式表覆盖所有高对比度设置
  function addGlobalStyles() {
    const styleId = 'ultimate-high-contrast-fix';
    if (document.getElementById(styleId)) return;
    
    const style = document.createElement('style');
    style.id = styleId;
    style.innerHTML = `
      /* 全局禁用所有高对比度样式 */
      * {
        forced-colors: none !important; 
        forced-color-adjust: none !important;
      }
      
      /* 使用现代替代方案 */
      @media (forced-colors: active) {
        body * {
          forced-color-adjust: none !important;
          forced-colors: none !important;
        }
        
        /* 保证基本可访问性 */
        a, button, input, select, textarea, [role="button"] {
          forced-color-adjust: auto !important;
        }
        
        img, svg, canvas, video {
          forced-color-adjust: auto !important;
        }
      }
    `;
    
    document.head.appendChild(style);
  }
  
  // 2. CSS样式表扫描方法 - 在现有样式表中查找并删除高对比度样式
  function cleanupStylesheets() {
    try {
      for (let i = 0; i < document.styleSheets.length; i++) {
        const sheet = document.styleSheets[i];
        try {
          if (!sheet.cssRules) continue; // 跳过不可访问的样式表
          
          for (let j = 0; j < sheet.cssRules.length; j++) {
            const rule = sheet.cssRules[j];
            
            // 处理媒体查询
            if (rule.type === CSSRule.MEDIA_RULE) {
              const mediaText = rule.media?.mediaText || '';
              if (mediaText.includes('-ms-high-contrast')) {
                try {
                  sheet.deleteRule(j);
                  j--; // 调整索引
                } catch (e) {}
              }
            }
            
            // 处理样式规则中的-ms-high-contrast属性
            if (rule.style) {
              if (rule.style.getPropertyValue('-ms-high-contrast') || 
                  rule.style.getPropertyValue('-ms-high-contrast-adjust')) {
                try {
                  // 尝试替换或删除规则
                  const cssText = rule.cssText.replace(/-ms-high-contrast[^:]*:[^;]+;?/g, '');
                  sheet.deleteRule(j);
                  sheet.insertRule(cssText, j);
                } catch (e) {}
              }
            }
          }
        } catch (e) {
          // 忽略跨域样式表错误
        }
      }
    } catch (e) {
      console.error('[Ultimate Fixer] Error cleaning stylesheets:', e);
    }
  }
  
  // 3. CSS API劫持方法 - 拦截CSS API调用并修改
  function interceptCssApis() {
    // 保存原始方法
    const originalSetProperty = CSSStyleDeclaration.prototype.setProperty;
    const originalGetPropertyValue = CSSStyleDeclaration.prototype.getPropertyValue;
    const originalRemoveProperty = CSSStyleDeclaration.prototype.removeProperty;
    
    // 重写setProperty方法
    CSSStyleDeclaration.prototype.setProperty = function(property, value, priority) {
      if (property && typeof property === 'string' && property.includes('-ms-high-contrast')) {
        return; // 直接阻止设置
      }
      return originalSetProperty.apply(this, arguments);
    };
    
    // 重写getPropertyValue方法
    CSSStyleDeclaration.prototype.getPropertyValue = function(property) {
      if (property && typeof property === 'string' && property.includes('-ms-high-contrast')) {
        return ''; // 返回空值
      }
      return originalGetPropertyValue.apply(this, arguments);
    };
    
    // 重写removeProperty方法
    CSSStyleDeclaration.prototype.removeProperty = function(property) {
      if (property && typeof property === 'string' && property.includes('-ms-high-contrast')) {
        return ''; // 返回空值
      }
      return originalRemoveProperty.apply(this, arguments);
    };
    
    // 劫持matchMedia API
    if (window.matchMedia) {
      const originalMatchMedia = window.matchMedia;
      window.matchMedia = function(query) {
        if (query && typeof query === 'string' && query.includes('-ms-high-contrast')) {
          query = query.replace(/-ms-high-contrast[^)]+\)/g, 'forced-colors: active)');
        }
        return originalMatchMedia.call(this, query);
      };
    }
  }
  
  // 4. DOM观察方法 - 监视DOM变化并清理新添加的样式
  function setupObserver() {
    if (!window.MutationObserver) return;
    
    const observer = new MutationObserver(function(mutations) {
      let needsCleanup = false;
      
      for (const mutation of mutations) {
        if (mutation.type === 'childList') {
          // 检查是否添加了新的样式表或样式元素
          for (const node of mutation.addedNodes) {
            if (node.nodeName === 'STYLE' || 
                (node.nodeName === 'LINK' && 
                 node.rel === 'stylesheet')) {
              needsCleanup = true;
              break;
            }
          }
        }
      }
      
      if (needsCleanup) {
        setTimeout(cleanupStylesheets, 0);
      }
    });
    
    // 观察整个文档
    observer.observe(document.documentElement, {
      childList: true,
      subtree: true
    });
  }
  
  // 5. 修复内联样式方法 - 检查并修复所有元素的内联样式
  function fixInlineStyles() {
    const elements = document.querySelectorAll('*[style]');
    elements.forEach(el => {
      const style = el.style;
      if (style['-ms-high-contrast'] || style.forcedColors) {
        style.removeProperty('-ms-high-contrast');
        delete style.forcedColors;
      }
      if (style['-ms-high-contrast-adjust'] || style.forcedColorsAdjust) {
        style.removeProperty('-ms-high-contrast-adjust');
        delete style.forcedColorsAdjust;
      }
    });
  }
  
  // 6. 强制CSS注入方法 - 使用!important强制覆盖所有样式
  function injectForcedOverrides() {
    const overrideStyles = `
      /* 使用!important覆盖所有可能的高对比度样式 */
      @media all {
        html, body, * {
          forced-colors: none !important;
          forced-color-adjust: none !important;
        }
      }
      
      /* 拦截所有-ms-high-contrast媒体查询 */
      @media (forced-colors: active), 
             (forced-colors: black-on-white), 
             (forced-colors: white-on-black),
             (forced-colors: none) {
        * { 
          forced-colors: none !important;
          forced-color-adjust: none !important;
        }
      }
    `;
    
    const styleEl = document.createElement('style');
    styleEl.id = 'ms-high-contrast-forced-override';
    styleEl.innerHTML = overrideStyles;
    
    // 确保样式表添加到最后
    if (document.head.lastChild) {
      document.head.appendChild(styleEl);
    } else {
      document.head.appendChild(styleEl);
    }
  }
  
  // 初始化所有修复方法
  function init() {
    // 立即应用所有修复
    addGlobalStyles();
    cleanupStylesheets();
    interceptCssApis();
    fixInlineStyles();
    injectForcedOverrides();
    
    // 设置观察器监视未来的变化
    setupObserver();
    
    // 在不同的事件点重新应用修复
    window.addEventListener('DOMContentLoaded', function() {
      cleanupStylesheets();
      fixInlineStyles();
    });
    
    window.addEventListener('load', function() {
      cleanupStylesheets();
      fixInlineStyles();
    });
  }
  
  // 立即执行初始化
  init();
})(); 