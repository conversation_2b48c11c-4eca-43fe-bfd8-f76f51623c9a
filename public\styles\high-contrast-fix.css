/**
 * 高对比度模式修复样式
 * 用于修复Microsoft Edge和其他浏览器中的高对比度模式显示问题
 */

/* 使用现代标准替代-ms-high-contrast */
@media (forced-colors: active) {
  /* 全局覆盖 */
  :root {
    forced-color-adjust: none;
  }
  
  /* 基础UI元素强制使用适当的颜色 */
  body {
    background-color: Canvas;
    color: CanvasText;
  }
  
  a {
    color: LinkText;
    text-decoration: underline;
  }
  
  a:visited {
    color: VisitedText;
  }
  
  button, 
  input[type="button"],
  input[type="submit"],
  input[type="reset"],
  .btn {
    background-color: ButtonFace;
    color: ButtonText;
    border-color: ButtonText;
    border-width: 1px;
    border-style: solid;
  }
  
  /* 表单元素 */
  input, 
  textarea, 
  select {
    background-color: Field;
    color: FieldText;
    border: 1px solid FieldText;
  }
  
  /* 确保图标在高对比度模式下可见 */
  svg {
    fill: currentColor;
  }
  
  /* 修复图片和背景图片 */
  img {
    forced-color-adjust: auto;
  }
  
  /* 修复特定UI组件 */
  .card,
  .panel,
  .alert,
  .message,
  .notification {
    border: 1px solid CanvasText;
  }
  
  /* 修复导航条 */
  nav, 
  header, 
  footer {
    background-color: Canvas;
    color: CanvasText;
    border-color: CanvasText;
  }
}

/* 专门针对高对比度黑白模式 */
@media (forced-colors: active) and (prefers-color-scheme: dark) {
  /* 如果系统同时处于高对比度和深色模式，应用这些样式 */
  body {
    background-color: Black;
    color: White;
  }
  
  /* 边框和分隔线 */
  hr, 
  .divider {
    border-color: White;
  }
}

/* 禁用微软翻译器添加的高对比度样式 */
[style*="-ms-high-contrast"] {
  forced-colors: none !important;
  forced-colors: none !important;
}

/* 修复链接在某些高对比度模式下的显示问题 */
a[href]:not([class]) {
  color: LinkText;
  text-decoration: underline;
}

/* 恢复深色模式样式 */
body.dark, 
[data-theme="dark"] body {
  background-color: #121212 !important;
  color: #f5f5f5 !important;
}

/* 恢复深色背景 */
.dark .bg-black, 
.dark .bg-dark, 
.dark .bg-gray-800, 
.dark .bg-gray-900,
.dark [class*="bg-dark"],
.dark [class*="dark:bg"],
[data-theme="dark"] .bg-black,
[data-theme="dark"] .bg-dark,
[data-theme="dark"] .bg-gray-800,
[data-theme="dark"] .bg-gray-900 {
  background-color: #121212 !important;
  color: #f5f5f5 !important;
}

/* 恢复深色文本 */
.dark .text-white,
.dark .text-light,
.dark [class*="text-white"],
.dark [class*="dark:text-white"],
[data-theme="dark"] .text-white,
[data-theme="dark"] .text-light,
[data-theme="dark"] [class*="text-white"] {
  color: #ffffff !important;
}

/* 确保链接可见 */
.dark a, 
.dark button, 
.dark .btn,
[data-theme="dark"] a,
[data-theme="dark"] button,
[data-theme="dark"] .btn {
  color: #60a5fa !important;
}

.dark a:hover, 
.dark button:hover, 
.dark .btn:hover,
[data-theme="dark"] a:hover,
[data-theme="dark"] button:hover,
[data-theme="dark"] .btn:hover {
  color: #93c5fd !important;
}

/* 确保边框可见 */
.dark .border, 
.dark [class*="border-"],
[data-theme="dark"] .border,
[data-theme="dark"] [class*="border-"] {
  border-color: #4b5563 !important;
}

/* 确保阴影可见 */
.dark .shadow, 
.dark [class*="shadow-"],
[data-theme="dark"] .shadow,
[data-theme="dark"] [class*="shadow-"] {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4) !important;
}

/* 确保图片不会被隐藏 */
img, svg {
  opacity: 1 !important;
  visibility: visible !important;
}

/**
 * 高对比度修复样式表
 * 用于处理Microsoft Edge中的-ms-high-contrast弃用警告
 */

/* 强制覆盖高对比度样式，确保兼容性 */
@media (forced-colors: active) {
  body * {
    /* 确保所有元素使用正确的强制色彩模式属性 */
    forced-color-adjust: none !important;
  }
  
  /* 修复特定高对比度模式下的问题 */
  a, button, input, select, textarea, [role="button"], .btn {
    forced-color-adjust: auto !important;
  }
  
  /* 确保图片在高对比度模式下正确显示 */
  img, svg, video, canvas {
    forced-color-adjust: auto !important;
  }
}

/* 确保没有任何元素使用已弃用的-ms-high-contrast属性 */
* {
  forced-colors: none !important; /* 这行会触发警告，但可以覆盖已有样式 */
  forced-colors: auto; /* 现代替代方案 */
} 