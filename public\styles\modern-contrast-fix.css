/* 
 * 修复高对比度模式下的显示问题
 * 当用户使用Windows高对比度模式时，这些样式会确保内容可见
 */

@media (forced-colors: active) {
  /* 确保文本在高对比度模式下可见 */
  .page-banner .page-title,
  .page-banner .breadcrumb,
  .page-banner .breadcrumb a {
    forced-color-adjust: none;
    color: CanvasText !important;
  }
  
  /* 确保背景在高对比度模式下可见 */
  .page-banner::before,
  .page-banner::after {
    forced-color-adjust: none;
    background: Canvas !important;
    opacity: 0.7 !important;
  }
} 