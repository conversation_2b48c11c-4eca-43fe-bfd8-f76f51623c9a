<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮测试</title>
    <link rel="stylesheet" href="/styles/unified-cta.css">
    <style>
        body {
            margin: 0;
            padding: 50px;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .cta-section {
            background: #0a59f7;
            color: white;
            padding: 40px;
            text-align: center;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>CTA按钮测试</h1>
        
        <div class="test-section">
            <h2>标准按钮（应该显示正常文本）</h2>
            <a href="#" class="btn-primary" data-text="立即联系我们">
                <i>立</i><i>即</i><i>联</i><i>系</i><i>我</i><i>们</i>
            </a>
        </div>

        <div class="test-section">
            <h2>英文按钮</h2>
            <a href="#" class="btn-primary" data-text="CONTACT US NOW">
                <i>C</i><i>O</i><i>N</i><i>T</i><i>A</i><i>C</i><i>T</i><i>&nbsp;</i><i>U</i><i>S</i><i>&nbsp;</i><i>N</i><i>O</i><i>W</i>
            </a>
        </div>

        <div class="test-section cta-section">
            <h2>CTA区域中的按钮</h2>
            <p>这个按钮在CTA区域中，应该有相同的效果</p>
            <a href="#" class="btn-primary" data-text="立即联系我们">
                <i>立</i><i>即</i><i>联</i><i>系</i><i>我</i><i>们</i>
            </a>
        </div>

        <div class="test-section">
            <h2>使用说明</h2>
            <ul>
                <li>默认状态：应该显示 data-text 属性中的文本</li>
                <li>悬停状态：文本向上滑出，字母从下方逐个出现</li>
                <li>点击状态：按钮向下按压</li>
            </ul>
        </div>
    </div>
</body>
</html>
