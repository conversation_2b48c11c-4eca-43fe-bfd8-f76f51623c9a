import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';
import dotenv from 'dotenv';
import { Pool } from 'pg';

// 加载环境变量
dotenv.config();

// 获取当前脚本目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 数据库连接
const connectionString = process.env.POSTGRES_URI || 
  "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require";

// 创建连接池
const pool = new Pool({
  connectionString,
  ssl: {
    rejectUnauthorized: false
  }
});

// 执行查询函数
async function query(text, params = []) {
  const client = await pool.connect();
  try {
    return await client.query(text, params);
  } finally {
    client.release();
  }
}

async function addCategories() {
  try {
    console.log('开始添加分类到数据库...');
    console.log('使用连接字符串:', connectionString.replace(/\/\/(.*):(.*)@/, '//***:***@'));
    
    // 检查连接
    const testResult = await query('SELECT 1 as test');
    console.log('数据库连接测试结果:', testResult.rows[0]);
    
    // 检查categories表是否存在
    const tableCheck = await query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'categories'
      ) as exists
    `);
    
    // 如果表不存在，创建表
    if (!tableCheck.rows[0].exists) {
      console.log('categories表不存在，创建表...');
      await query(`
        CREATE TABLE categories (
          id SERIAL PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          slug VARCHAR(100) NOT NULL UNIQUE,
          description TEXT,
          parent_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      console.log('categories表创建成功');
    }
    
    // 添加基本分类
    const categories = [
      { name: '所有产品', slug: 'all-products', description: '所有产品分类' },
      { name: '室内游乐场', slug: 'indoor-playground', description: '各种室内游乐场解决方案' },
      { name: '蹦床公园', slug: 'trampoline-park', description: '专业蹦床公园解决方案' },
      { name: '100-500平方米', slug: '100-500-sqm', description: '100-500平方米空间的游乐场解决方案' },
      { name: '500-1000平方米', slug: '500-1000-sqm', description: '500-1000平方米空间的游乐场解决方案' },
      { name: '1000+平方米', slug: '1000-plus-sqm', description: '1000平方米以上空间的游乐场解决方案' }
    ];
    
    // 逐个添加分类，如果已存在则更新
    for (const category of categories) {
      // 检查分类是否已存在
      const exists = await query('SELECT * FROM categories WHERE slug = $1', [category.slug]);
      
      if (exists.rows.length === 0) {
        // 添加新分类
        await query(`
          INSERT INTO categories (name, slug, description, is_active)
          VALUES ($1, $2, $3, $4)
        `, [category.name, category.slug, category.description, true]);
        console.log(`添加分类: ${category.name}`);
      } else {
        // 更新现有分类
        await query(`
          UPDATE categories SET 
            name = $1, 
            description = $2, 
            is_active = $3,
            updated_at = CURRENT_TIMESTAMP
          WHERE slug = $4
        `, [category.name, category.description, true, category.slug]);
        console.log(`更新分类: ${category.name}`);
      }
    }
    
    console.log('分类添加/更新完成');
    
    // 查询添加的分类
    const result = await query('SELECT * FROM categories ORDER BY id');
    console.log('数据库中的分类:');
    console.table(result.rows);
    
    // 查询产品表外键
    await addCategoryIdToProducts();
    
    // 关闭连接
    await pool.end();
    console.log('数据库连接已关闭');
    
  } catch (error) {
    console.error('添加分类失败:', error);
    process.exit(1);
  }
}

async function addCategoryIdToProducts() {
  try {
    // 检查products表是否存在category_id列
    const columnCheck = await query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'category_id'
      ) as exists
    `);
    
    // 如果列不存在，添加外键列
    if (!columnCheck.rows[0].exists) {
      console.log('在products表中添加category_id外键...');
      
      // 添加外键列
      await query(`
        ALTER TABLE products 
        ADD COLUMN category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL
      `);
      
      // 根据现有category字段更新category_id
      await query(`
        UPDATE products p
        SET category_id = c.id
        FROM categories c
        WHERE 
          (p.category = '室内游乐场' AND c.slug = 'indoor-playground') OR
          (p.category = '蹦床公园' AND c.slug = 'trampoline-park')
      `);
      
      console.log('外键添加并更新完成');
    }
    
    // 显示product表与category的关联
    const productCategories = await query(`
      SELECT p.id, p.name, p.category, c.name as category_name
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LIMIT 10
    `);
    
    console.log('产品与分类的关联:');
    console.table(productCategories.rows);
    
  } catch (error) {
    console.error('添加分类外键失败:', error);
  }
}

// 执行函数
addCategories(); 