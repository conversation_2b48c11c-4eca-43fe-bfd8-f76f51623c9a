// 脚本用于添加项目定制指南到解决方案导航菜单
const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function addCustomGuideItems() {
  // 连接数据库
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL || process.env.POSTGRES_URI,
  });

  try {
    // 检查是否已存在项目定制指南菜单项
    const checkResult = await pool.query(`
      SELECT id, name, slug, is_featured, featured_type, translations
      FROM categories 
      WHERE slug = 'custom-playground-design' OR slug = 'custom-solutions'
    `);

    const existingSlugs = checkResult.rows.map(row => row.slug);
    console.log('检查已存在菜单项:', existingSlugs);

    // 添加项目定制指南1
    if (!existingSlugs.includes('custom-playground-design')) {
      console.log('创建项目定制指南1菜单项...');

      const result = await pool.query(`
        INSERT INTO categories
        (name, slug, description, type, is_active, order_num, is_featured, featured_order, featured_type, translations)
        VALUES
        ('项目定制指南', 'custom-playground-design', '定制您的游乐场项目', 'other', true, 4, true, 4, 'solution', '{"zh":{"name":"项目定制指南"},"en":{"name":"Custom Project Guide"}}'::jsonb)
        RETURNING id, name
      `);

      console.log(`成功创建菜单项: ${result.rows[0].name} (ID: ${result.rows[0].id})`);
    } else {
      console.log('项目定制指南1菜单项已存在，更新其设置...');
      const item = checkResult.rows.find(row => row.slug === 'custom-playground-design');

      // 更新为显示在解决方案导航菜单中
      await pool.query(
        `
        UPDATE categories
        SET 
          is_featured = true,
          featured_type = 'solution',
          featured_order = 4,
          translations = '{"zh":{"name":"项目定制指南"},"en":{"name":"Custom Project Guide"}}'::jsonb
        WHERE id = $1
      `,
        [item.id]
      );

      console.log(`已将 ${item.name} (ID: ${item.id}) 设置为显示在解决方案导航菜单中`);
    }

    // 添加项目定制指南2
    if (!existingSlugs.includes('custom-solutions')) {
      console.log('创建项目定制指南2菜单项...');

      const result = await pool.query(`
        INSERT INTO categories
        (name, slug, description, type, is_active, order_num, is_featured, featured_order, featured_type, translations)
        VALUES
        ('项目定制指南', 'custom-solutions', '定制您的解决方案', 'other', true, 5, true, 5, 'solution', '{"zh":{"name":"项目定制指南"},"en":{"name":"Custom Project Guide"}}'::jsonb)
        RETURNING id, name
      `);

      console.log(`成功创建菜单项: ${result.rows[0].name} (ID: ${result.rows[0].id})`);
    } else {
      console.log('项目定制指南2菜单项已存在，更新其设置...');
      const item = checkResult.rows.find(row => row.slug === 'custom-solutions');

      // 更新为显示在解决方案导航菜单中
      await pool.query(
        `
        UPDATE categories
        SET 
          is_featured = true,
          featured_type = 'solution',
          featured_order = 5,
          translations = '{"zh":{"name":"项目定制指南"},"en":{"name":"Custom Project Guide"}}'::jsonb
        WHERE id = $1
      `,
        [item.id]
      );

      console.log(`已将 ${item.name} (ID: ${item.id}) 设置为显示在解决方案导航菜单中`);
    }

    // 确认最终结果
    const finalResult = await pool.query(`
      SELECT id, name, slug, is_featured, featured_type, featured_order, translations
      FROM categories 
      WHERE is_featured = true AND featured_type = 'solution'
      ORDER BY featured_order
    `);

    console.log('当前解决方案导航菜单项:');
    console.log(finalResult.rows);
  } catch (error) {
    console.error('添加项目定制指南菜单项时出错:', error);
  } finally {
    await pool.end();
  }
}

// 运行函数
addCustomGuideItems().catch(console.error);
