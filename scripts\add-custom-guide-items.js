const { Pool } = require('pg');
require('dotenv').config();

async function addCustomGuideItems() {
  // Database connection
  const pool = new Pool({
    connectionString:
      process.env.DATABASE_URL ||
      'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
    ssl: {
      rejectUnauthorized: false,
    },
  });

  try {
    console.log('Connecting to database...');
    const client = await pool.connect();
    console.log('Connected successfully');

    // Check existing slugs
    const checkResult = await client.query(`
      SELECT id, name, slug
      FROM categories
      WHERE slug IN ('custom-playground-design', 'custom-solutions')
    `);

    const existingSlugs = checkResult.rows.map(row => row.slug);
    console.log('Existing slugs:', existingSlugs);

    // Add first item: 全息定制方案
    if (!existingSlugs.includes('custom-playground-design')) {
      console.log('Creating "全息定制方案" menu item...');

      const result = await client.query(`
        INSERT INTO categories
        (name, slug, description, type, is_active, order_num, is_featured, featured_order, featured_type, translations)
        VALUES
        ('全息定制方案', 'custom-playground-design', '专业定制全息投影方案', 'solution_type', true, 1, true, 1, 'solution', '{"zh":{"name":"全息定制方案"},"en":{"name":"Holographic Custom Design"}}'::jsonb)
        RETURNING id, name
      `);

      console.log(`Created menu item: ${result.rows[0].name} (ID: ${result.rows[0].id})`);
    } else {
      console.log('"全息定制方案" menu item already exists, updating its settings...');
      const item = checkResult.rows.find(row => row.slug === 'custom-playground-design');

      await client.query(
        `
        UPDATE categories
        SET 
          name = '全息定制方案',
          is_featured = true,
          featured_type = 'solution',
          featured_order = 1,
          translations = '{"zh":{"name":"全息定制方案"},"en":{"name":"Holographic Custom Design"}}'::jsonb
        WHERE id = $1
      `,
        [item.id]
      );

      console.log(
        `Updated ${item.name} (ID: ${item.id}) to display in the solution menu as "全息定制方案"`
      );
    }

    // Add second item: 全息解决方案
    if (!existingSlugs.includes('custom-solutions')) {
      console.log('Creating "全息解决方案" menu item...');

      const result = await client.query(`
        INSERT INTO categories
        (name, slug, description, type, is_active, order_num, is_featured, featured_order, featured_type, translations)
        VALUES
        ('全息解决方案', 'custom-solutions', '全息投影解决方案', 'solution_type', true, 2, true, 2, 'solution', '{"zh":{"name":"全息解决方案"},"en":{"name":"Holographic Solutions"}}'::jsonb)
        RETURNING id, name
      `);

      console.log(`Created menu item: ${result.rows[0].name} (ID: ${result.rows[0].id})`);
    } else {
      console.log('"全息解决方案" menu item already exists, updating its settings...');
      const item = checkResult.rows.find(row => row.slug === 'custom-solutions');

      await client.query(
        `
        UPDATE categories
        SET 
          name = '全息解决方案',
          is_featured = true,
          featured_type = 'solution',
          featured_order = 2,
          translations = '{"zh":{"name":"全息解决方案"},"en":{"name":"Holographic Solutions"}}'::jsonb
        WHERE id = $1
      `,
        [item.id]
      );

      console.log(
        `Updated ${item.name} (ID: ${item.id}) to display in the solution menu as "全息解决方案"`
      );
    }

    // Check final result
    const finalResult = await client.query(`
      SELECT id, name, slug, is_featured, featured_type, featured_order, translations
      FROM categories 
      WHERE is_featured = true AND featured_type = 'solution'
      ORDER BY featured_order
    `);

    console.log('\nCurrent solution menu items:');
    console.log('--------------------------');

    if (finalResult.rows.length === 0) {
      console.log('No solution menu items found');
    } else {
      finalResult.rows.forEach(item => {
        console.log(`ID: ${item.id}`);
        console.log(`Name: ${item.name}`);
        console.log(`Slug: ${item.slug}`);
        console.log(`Featured Order: ${item.featured_order}`);
        console.log(`Translations: ${JSON.stringify(item.translations, null, 2)}`);
        console.log('--------------------------');
      });
    }

    client.release();
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the function
addCustomGuideItems().catch(console.error);
