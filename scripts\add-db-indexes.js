/**
 * 数据库索引脚本
 * 用于创建必要的索引以提高查询性能
 */

require('dotenv').config();
const { Pool } = require('pg');

// 数据库连接配置
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

// 要创建的索引列表
const indexes = [
  // 产品表索引
  {
    table: 'products',
    name: 'idx_products_slug',
    columns: ['slug'],
    unique: true,
    description: '产品slug唯一索引，加速根据slug查询产品',
  },
  {
    table: 'products',
    name: 'idx_products_category',
    columns: ['category'],
    unique: false,
    description: '产品分类索引，加速按分类查询',
  },
  {
    table: 'products',
    name: 'idx_products_featured',
    columns: ['is_featured'],
    unique: false,
    description: '产品特色标记索引，加速查询特色产品',
  },
  {
    table: 'products',
    name: 'idx_products_search',
    columns: ['name', 'description'],
    unique: false,
    description: '产品搜索索引，加速全文搜索(如果PostgreSQL支持)',
  },

  // 分类表索引(如果存在)
  {
    table: 'categories',
    name: 'idx_categories_slug',
    columns: ['slug'],
    unique: true,
    description: '分类slug唯一索引',
  },
];

/**
 * 创建索引的函数
 */
async function createIndexes() {
  const client = await pool.connect();

  try {
    // 开始事务
    await client.query('BEGIN');

    for (const index of indexes) {
      const { table, name, columns, unique, description } = index;

      // 检查表是否存在
      const tableCheck = await client.query(
        `
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = $1
        )
      `,
        [table]
      );

      if (!tableCheck.rows[0].exists) {
        console.log(`表 ${table} 不存在，跳过索引 ${name}`);
        continue;
      }

      // 检查索引是否已存在
      const indexCheck = await client.query(
        `
        SELECT EXISTS (
          SELECT FROM pg_indexes 
          WHERE indexname = $1
        )
      `,
        [name]
      );

      if (indexCheck.rows[0].exists) {
        console.log(`索引 ${name} 已存在，跳过`);
        continue;
      }

      // 创建索引
      const uniqueStr = unique ? 'UNIQUE' : '';
      const columnsStr = columns.join(', ');

      console.log(`正在创建索引: ${name} 在表 ${table} 上 (${columnsStr})`);

      // 使用注释记录索引用途
      await client.query(`
        CREATE ${uniqueStr} INDEX ${name} ON ${table} (${columnsStr});
        COMMENT ON INDEX ${name} IS '${description}';
      `);

      console.log(`索引 ${name} 创建成功`);
    }

    // 提交事务
    await client.query('COMMIT');
    console.log('所有索引创建完成');
  } catch (error) {
    // 发生错误，回滚事务
    await client.query('ROLLBACK');
    console.error('创建索引时出错:', error);
  } finally {
    // 释放客户端
    client.release();
    await pool.end();
  }
}

// 运行主函数
createIndexes().catch(err => {
  console.error('程序执行错误:', err);
  process.exit(1);
});
