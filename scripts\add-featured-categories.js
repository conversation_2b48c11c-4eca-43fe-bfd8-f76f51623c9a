const { Pool } = require('pg');

// Get PostgreSQL connection parameters from environment variables
const POSTGRES_URI =
  process.env.POSTGRES_URI ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// Create connection pool
const pool = new Pool({
  connectionString: POSTGRES_URI,
  ssl: { rejectUnauthorized: false },
});

async function addFeaturedCategoryField() {
  try {
    console.log('添加精选分类字段到categories表...');

    // 检查表是否存在
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'categories'
      ) as exists
    `);

    if (!tableCheck.rows[0].exists) {
      console.log('categories表不存在，请先创建categories表');
      process.exit(1);
    }

    // 检查is_featured字段是否已存在
    const featuredCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'categories' AND column_name = 'is_featured'
      ) as exists
    `);

    if (featuredCheck.rows[0].exists) {
      console.log('is_featured字段已存在');
    } else {
      // 添加is_featured字段
      await pool.query(`
        ALTER TABLE categories 
        ADD COLUMN is_featured BOOLEAN DEFAULT false
      `);
      console.log('is_featured字段添加成功');
    }

    // 检查featured_order字段是否已存在
    const orderCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'categories' AND column_name = 'featured_order'
      ) as exists
    `);

    if (orderCheck.rows[0].exists) {
      console.log('featured_order字段已存在');
    } else {
      // 添加featured_order字段
      await pool.query(`
        ALTER TABLE categories 
        ADD COLUMN featured_order INTEGER DEFAULT 0
      `);
      console.log('featured_order字段添加成功');
    }

    // 设置默认的精选分类
    console.log('设置默认精选分类...');
    await pool.query(`
      UPDATE categories
      SET is_featured = true, featured_order = 1
      WHERE slug = 'indoor-playground'
    `);

    await pool.query(`
      UPDATE categories
      SET is_featured = true, featured_order = 2
      WHERE slug = 'trampoline-park'
    `);

    // 查看表结构
    const structureResult = await pool.query(`
      SELECT column_name, data_type, column_default, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'categories'
      ORDER BY ordinal_position
    `);

    console.log('更新后的categories表结构:');
    console.table(structureResult.rows);

    // 查询表中的数据
    const dataResult = await pool.query('SELECT * FROM categories ORDER BY id');
    console.log('Categories表数据:');
    console.table(dataResult.rows);

    console.log('脚本执行完成');
    process.exit(0);
  } catch (error) {
    console.error('脚本执行失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接池
    await pool.end();
  }
}

addFeaturedCategoryField();
