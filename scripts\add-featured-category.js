const path = require('path');
const { createRequire } = require('module');
const requireFromRoot = createRequire(path.resolve('./'));

// 正确导入数据库模块
const dbModule = requireFromRoot('./lib/db');

async function addFeaturedFieldToCategories() {
  try {
    console.log('开始添加精选分类字段...');

    // 检查is_featured字段是否存在
    const columnCheck = await dbModule.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'categories' AND column_name = 'is_featured'
      ) as exists
    `, []);
  
    // 如果字段不存在，添加字段
    if (!columnCheck.rows[0].exists) {
      console.log('is_featured字段不存在，添加字段...');
      await dbModule.query(`
        ALTER TABLE categories 
        ADD COLUMN is_featured BOOLEAN DEFAULT false,
        ADD COLUMN featured_order INTEGER DEFAULT 0
      `, []);
      console.log('is_featured和featured_order字段添加成功');
    } else {
      console.log('is_featured字段已存在');
    }

    // 设置默认的精选分类（室内游乐场和蹦床公园）
    const defaultFeatured = [
      { slug: 'indoor-playground', order: 1 },
      { slug: 'trampoline-park', order: 2 }
    ];

    for (const featured of defaultFeatured) {
      await dbModule.query(`
        UPDATE categories 
        SET is_featured = true, featured_order = $1
        WHERE slug = $2
      `, [featured.order, featured.slug]);
      console.log(`设置 ${featured.slug} 为精选分类，排序为 ${featured.order}`);
    }

    // 查询所有分类
    const result = await dbModule.query('SELECT id, name, slug, is_featured, featured_order FROM categories ORDER BY featured_order, id', []);
    console.log('当前分类列表（包含精选状态）:');
    console.table(result.rows);

    console.log('精选分类字段添加完成');
    process.exit(0);
  } catch (error) {
    console.error('添加精选分类字段失败:', error);
    process.exit(1);
  }
}

addFeaturedFieldToCategories(); 
