// 添加精选分类类型字段到数据库
const { Pool } = require('pg');
require('dotenv').config(); // 加载.env文件

// 数据库连接配置
const pool = new Pool({
  connectionString:
    process.env.DATABASE_URL ||
    'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: {
    rejectUnauthorized: false,
  },
});

// 查询函数
async function query(text, params = []) {
  const client = await pool.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } finally {
    client.release();
  }
}

async function addFeaturedTypeField() {
  try {
    console.log('添加featured_type字段到categories表...');

    // 检查字段是否已存在
    const checkField = await query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'categories' AND column_name = 'featured_type'
    `);

    if (checkField.rows.length > 0) {
      console.log('featured_type字段已存在');
    } else {
      // 添加字段
      await query(`
        ALTER TABLE categories
        ADD COLUMN featured_type varchar(20) DEFAULT 'product'
      `);
      console.log('featured_type字段添加成功');
    }

    // 更新现有精选分类
    await query(`
      UPDATE categories
      SET featured_type = 'product'
      WHERE is_featured = true AND featured_type IS NULL
    `);

    // 显示更新后的表结构
    const tableStructure = await query(`
      SELECT column_name, data_type, column_default, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'categories'
      ORDER BY ordinal_position
    `);

    console.log('更新后的categories表结构:');
    console.table(tableStructure.rows);

    // 显示当前分类数据
    const categoriesData = await query('SELECT * FROM categories');
    console.log('Categories表数据:');
    console.table(categoriesData.rows);
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // 关闭连接池
    await pool.end();
  }
}

// 执行迁移
addFeaturedTypeField()
  .then(() => console.log('脚本执行完成'))
  .catch(err => console.error('脚本执行失败:', err));
