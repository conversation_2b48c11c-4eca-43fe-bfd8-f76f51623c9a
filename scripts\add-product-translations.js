const fs = require('fs');
const path = require('path');

// 产品翻译映射
const translations = {
  // 产品名称翻译
  names: {
    "互动足球系统": "Interactive Football System",
    "AR体感蹦床": "AR Motion Trampoline", 
    "体感攀岩系统": "Motion Sensing Climbing",
    "互动砸球游戏": "Interactive Ball Smash Game",
    "AR教育系统": "AR Education System",
    "KTV互动系统": "KTV Interactive System",
    "互动一体机": "Interactive All-in-One",
    "3D电子沙盘": "3D Electronic Sandbox",
    "互动足球专业版": "Interactive Football Pro",
    "AR体感蹦床专业版": "AR Motion Trampoline Pro",
    "体感攀岩专业版": "Motion Sensing Climbing Pro",
    "互动砸球专业版": "Interactive Ball Smash Pro",
    "AR教育专业版": "AR Education Pro",
    "KTV系统专业版": "KTV System Pro",
    "一体机专业版": "All-in-One Pro",
    "3D沙盘专业版": "3D Sandbox Pro",
    "宴会厅互动系统": "Banquet Hall Interactive System",
    "儿童互动沙滩": "Children's Interactive Beach",
    "全息屏幕系统": "Holographic Screen System",
    "保龄球互动系统": "Bowling Interactive System",
    "全息舞台系统": "Holographic Stage System"
  },
  
  // 分类翻译
  categories: {
    "运动娱乐": "Sports & Entertainment",
    "游戏娱乐": "Gaming & Entertainment", 
    "教育科技": "Educational Technology",
    "娱乐社交": "Social Entertainment",
    "商业设备": "Commercial Equipment",
    "教育展示": "Educational Display",
    "商业娱乐": "Commercial Entertainment",
    "儿童娱乐": "Children's Entertainment",
    "全息科技": "Holographic Technology"
  },
  
  // 风格翻译
  styles: {
    "互动,娱乐": "Interactive,Entertainment",
    "互动": "Interactive",
    "娱乐": "Entertainment",
    "教育": "Educational",
    "商业": "Commercial",
    "科技": "Technology"
  },
  
  // 特性翻译
  features: {
    "体感识别": "Motion Sensing",
    "投影互动": "Projection Interaction", 
    "多人游戏": "Multiplayer Gaming",
    "安全防护": "Safety Protection",
    "竞技模式": "Competition Mode",
    "互动投影": "Interactive Projection",
    "体感控制": "Motion Control",
    "计分系统": "Scoring System",
    "多人对战": "Multiplayer Battle",
    "趣味挑战": "Fun Challenges",
    "AR技术": "AR Technology",
    "教育内容": "Educational Content",
    "互动学习": "Interactive Learning",
    "多媒体展示": "Multimedia Display",
    "个性化教学": "Personalized Teaching",
    "智能系统": "Smart System",
    "社交功能": "Social Features",
    "音效优化": "Audio Optimization",
    "氛围灯光": "Ambient Lighting",
    "用户友好": "User Friendly",
    "多点触控": "Multi-touch",
    "高清显示": "HD Display",
    "一体化设计": "Integrated Design",
    "多媒体支持": "Multimedia Support",
    "易于安装": "Easy Installation",
    "3D显示": "3D Display",
    "实时交互": "Real-time Interaction",
    "数据可视化": "Data Visualization",
    "教育应用": "Educational Application",
    "专业展示": "Professional Display",
    "氛围营造": "Atmosphere Creation",
    "互动体验": "Interactive Experience",
    "主题切换": "Theme Switching",
    "智能控制": "Smart Control",
    "商业应用": "Commercial Application"
  }
};

// 生成描述翻译的函数
function generateDescriptionTranslation(chineseDesc, productName) {
  // 基础模板
  const templates = {
    "interactive": "The {name} uses advanced motion sensing and projection technology to provide users with an immersive entertainment experience. Suitable for all ages, safe, reliable, and endlessly fun.",
    "ar": "The {name} combines AR augmented reality technology with interactive elements to create engaging and educational experiences. Supports multi-scenario content and enhances user engagement.",
    "education": "The {name} provides innovative learning experiences through AR augmented reality technology. Supports multi-disciplinary content and improves teaching effectiveness.",
    "ktv": "The {name} integrates multiple entertainment functions to enhance user experience. Modern interface, easy operation, suitable for social entertainment venues.",
    "machine": "The {name} integrates advanced technology, suitable for commercial display and educational scenarios. High performance, easy maintenance, and powerful functionality.",
    "sandbox": "The {name} provides immersive display and interactive experiences, suitable for education, planning and various scenarios.",
    "banquet": "The {name} provides unique entertainment experiences for commercial venues, creating distinctive atmospheres and enhancing venue quality.",
    "beach": "The {name} creates safe and fun interactive play areas with ocean themes, perfect for children's entertainment and education.",
    "screen": "The {name} delivers stunning holographic displays with naked-eye 3D technology, creating immersive visual experiences.",
    "bowling": "The {name} combines traditional bowling with interactive technology, creating modern sports entertainment experiences.",
    "stage": "The {name} provides spectacular visual performance solutions for events, concerts, and entertainment venues."
  };
  
  // 根据产品名称选择合适的模板
  const name = productName.toLowerCase();
  let template = templates.interactive; // 默认模板
  
  if (name.includes('ar') || name.includes('augmented')) {
    template = templates.ar;
  } else if (name.includes('education') || name.includes('educational')) {
    template = templates.education;
  } else if (name.includes('ktv')) {
    template = templates.ktv;
  } else if (name.includes('all-in-one') || name.includes('machine')) {
    template = templates.machine;
  } else if (name.includes('sandbox') || name.includes('沙盘')) {
    template = templates.sandbox;
  } else if (name.includes('banquet') || name.includes('宴会')) {
    template = templates.banquet;
  } else if (name.includes('beach') || name.includes('沙滩')) {
    template = templates.beach;
  } else if (name.includes('screen') || name.includes('屏幕')) {
    template = templates.screen;
  } else if (name.includes('bowling') || name.includes('保龄')) {
    template = templates.bowling;
  } else if (name.includes('stage') || name.includes('舞台')) {
    template = templates.stage;
  }
  
  return template.replace('{name}', translations.names[productName] || productName);
}

// 处理产品数据
function processProducts() {
  const filePath = path.join(process.cwd(), 'public', 'mock-products.json');
  
  try {
    // 读取现有数据
    const data = fs.readFileSync(filePath, 'utf8');
    const products = JSON.parse(data);
    
    console.log(`处理 ${products.length} 个产品...`);
    
    // 为每个产品添加英文翻译
    const updatedProducts = products.map((product, index) => {
      console.log(`处理产品 ${index + 1}: ${product.name}`);
      
      return {
        ...product,
        name_en: translations.names[product.name] || product.name,
        description_en: generateDescriptionTranslation(product.description, product.name),
        category_en: translations.categories[product.category] || product.category,
        style_en: translations.styles[product.style] || product.style,
        features_en: product.features ? product.features.map(feature => 
          translations.features[feature] || feature
        ) : []
      };
    });
    
    // 写回文件
    fs.writeFileSync(filePath, JSON.stringify(updatedProducts, null, 2), 'utf8');
    console.log('✅ 产品翻译添加完成！');
    
  } catch (error) {
    console.error('❌ 处理产品翻译时出错:', error);
  }
}

// 执行处理
processProducts();
