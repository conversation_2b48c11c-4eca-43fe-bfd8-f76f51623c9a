/**
 * 添加一个简单的测试产品
 */
const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function addSimpleProduct() {
  const client = await pool.connect();
  
  try {
    console.log('添加测试产品...');
    
    // 检查是否已存在测试产品
    const existing = await client.query('SELECT id FROM products WHERE slug = $1', ['test-product']);
    
    if (existing.rows.length > 0) {
      console.log('测试产品已存在，删除后重新创建...');
      await client.query('DELETE FROM products WHERE slug = $1', ['test-product']);
    }
    
    // 插入简单的测试产品
    const result = await client.query(
      `INSERT INTO products (name, slug, description, price, in_stock, type, size, style, features, images, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
       RETURNING id`,
      [
        '测试产品',
        'test-product',
        '这是一个测试产品，用于验证系统功能',
        99.99,
        true,
        'test',
        'medium',
        'modern',
        JSON.stringify(['测试功能1', '测试功能2']),
        JSON.stringify(['/images/test-product.jpg'])
      ]
    );
    
    console.log(`✅ 成功创建测试产品，ID: ${result.rows[0].id}`);
    
    // 验证产品是否创建成功
    const verification = await client.query('SELECT * FROM products WHERE id = $1', [result.rows[0].id]);
    console.log('产品详情:', verification.rows[0]);
    
  } catch (error) {
    console.error('❌ 创建测试产品失败:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

addSimpleProduct();
