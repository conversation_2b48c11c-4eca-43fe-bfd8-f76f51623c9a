const { Pool } = require('pg');
require('dotenv').config();

const connectionString = process.env.POSTGRES_URI ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

const pool = new Pool({
  connectionString,
  ssl: { rejectUnauthorized: false },
});

// 测试产品数据
const testProducts = [
  {
    name: '全息投影展示台',
    slug: 'hologram-display',
    description: '先进的全息投影技术，创造令人惊叹的3D视觉效果',
    in_stock: true
  },
  {
    name: '互动地面投影',
    slug: 'interactive-floor-projection',
    description: '地面互动投影系统，通过脚步触发各种视觉效果',
    in_stock: true
  },
  {
    name: '体感游戏机',
    slug: 'motion-sensing-game',
    description: '无需手柄的体感游戏设备，全身动作控制游戏',
    in_stock: true
  },
  {
    name: '虚拟现实头盔',
    slug: 'vr-headset',
    description: '高分辨率VR头盔，提供沉浸式虚拟现实体验',
    in_stock: true
  },
  {
    name: '增强现实眼镜',
    slug: 'ar-glasses',
    description: 'AR智能眼镜，将数字信息叠加到现实世界',
    in_stock: true
  },
  {
    name: '互动投影球',
    slug: 'interactive-projection-ball',
    description: '球形投影设备，360度全方位投影展示',
    in_stock: true
  },
  {
    name: '智能魔镜',
    slug: 'smart-mirror',
    description: '交互式智能镜面显示系统，支持触摸和语音控制',
    in_stock: true
  },
  {
    name: '全息风扇',
    slug: 'hologram-fan',
    description: '3D全息风扇显示器，创造悬浮的视觉效果',
    in_stock: true
  },
  {
    name: '互动桌面',
    slug: 'interactive-table',
    description: '多点触控互动桌面，支持多人同时操作',
    in_stock: true
  },
  {
    name: '投影沙盘',
    slug: 'projection-sandbox',
    description: '互动投影沙盘系统，实时地形识别和投影',
    in_stock: true
  }
];

async function addTestProducts() {
  let client;

  try {
    client = await pool.connect();
    console.log('数据库连接成功');

    for (const product of testProducts) {
      try {
        // 检查产品是否已存在
        const existingProduct = await client.query(
          'SELECT id FROM products WHERE slug = $1',
          [product.slug]
        );

        if (existingProduct.rows.length === 0) {
          // 插入新产品
          await client.query(
            `INSERT INTO products (name, slug, description, in_stock, price, created_at, updated_at)
             VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
            [product.name, product.slug, product.description, product.in_stock, 0]
          );
          console.log(`✅ 添加产品: ${product.name}`);
        } else {
          console.log(`⚠️  产品已存在: ${product.name}`);
        }
      } catch (error) {
        console.error(`❌ 添加产品失败 ${product.name}:`, error.message);
      }
    }

    // 检查总产品数量
    const countResult = await client.query('SELECT COUNT(*) as count FROM products WHERE in_stock = true');
    console.log(`\n📊 当前已发布产品总数: ${countResult.rows[0].count}`);

  } catch (error) {
    console.error('操作失败:', error);
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
}

addTestProducts().catch(console.error);
