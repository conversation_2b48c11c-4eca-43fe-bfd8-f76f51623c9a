/**
 * 为产品表添加必要的列
 */
const { Pool } = require('pg');
require('dotenv').config();

// 数据库连接信息
const connectionString =
  process.env.POSTGRES_URI ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

const pool = new Pool({
  connectionString,
  ssl: { rejectUnauthorized: false },
});

async function alterProductsTable() {
  let client;

  try {
    client = await pool.connect();
    console.log('数据库连接成功');

    // 检查images列是否存在
    const checkImagesColumn = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'products' AND column_name = 'images'
    `);

    if (checkImagesColumn.rows.length === 0) {
      console.log('添加images列...');
      await client.query(`
        ALTER TABLE products 
        ADD COLUMN images TEXT DEFAULT '[]'
      `);
      console.log('images列添加成功');
    } else {
      console.log('images列已存在');
    }

    // 检查detail_images列是否存在
    const checkDetailImagesColumn = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'products' AND column_name = 'detail_images'
    `);

    if (checkDetailImagesColumn.rows.length === 0) {
      console.log('添加detail_images列...');
      await client.query(`
        ALTER TABLE products 
        ADD COLUMN detail_images TEXT DEFAULT '[]'
      `);
      console.log('detail_images列添加成功');
    } else {
      console.log('detail_images列已存在');
    }

    // 检查video_url列是否存在
    const checkVideoUrlColumn = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'products' AND column_name = 'video_url'
    `);

    if (checkVideoUrlColumn.rows.length === 0) {
      console.log('添加video_url列...');
      await client.query(`
        ALTER TABLE products 
        ADD COLUMN video_url TEXT
      `);
      console.log('video_url列添加成功');
    } else {
      console.log('video_url列已存在');
    }

    console.log('产品表修改完成');
  } catch (error) {
    console.error('修改产品表时出错:', error);
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
}

alterProductsTable().catch(console.error);
