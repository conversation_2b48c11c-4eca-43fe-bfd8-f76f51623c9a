/**
 * 分析样式文件使用情况，识别可以安全删除的旧样式
 */
const fs = require('fs');
const path = require('path');

console.log('🔍 分析样式文件使用情况...\n');

// 当前正在使用的样式文件（从 globals.css 和 layout.tsx 中导入的）
const activeStyles = [
  'globals.css',
  'about-us.css',
  'banner-fix.css',
  'modern-contrast-fix.css',
  'ms-fix.css',
  'high-contrast-override.css',
  'ms-high-contrast-blocker.css',
  'animations.css',
  'unified-cta.css',
  'step-image-unified.css',
  'force-modern-product-style.css',
  'modern-product-grid.css',
  'ms-high-contrast-killer.css',
  'ms-translator-blocker.css',
  'product-detail-fix.css',
  'hero.css',
  'home-page.css',
  'home-page-fix.css',
  'custom-overrides.css',
  'global-quote-form.css',
  'loading-fix.css',
  'top-space-fix.css',
  'tailwind.css'
];

// 获取所有样式文件
const stylesDir = 'app/styles';
const allStyleFiles = fs.readdirSync(stylesDir).filter(file => file.endsWith('.css'));

console.log('📋 当前样式文件分析:\n');

// 分类样式文件
const usedFiles = [];
const unusedFiles = [];
const duplicateFiles = [];

allStyleFiles.forEach(file => {
  if (activeStyles.includes(file)) {
    usedFiles.push(file);
  } else {
    unusedFiles.push(file);
  }
});

// 检查重复或相似的文件
const potentialDuplicates = [
  ['products.css', 'products-page.css'],
  ['product-detail.css', 'product-detail-fix.css'],
  ['product-card-fix.css', 'force-modern-product-style.css'],
  ['ms-high-contrast-blocker.css', 'ms-high-contrast-killer.css'],
  ['custom-solutions.css', 'custom-solutions.css.temp', 'custom-solutions.module.css']
];

console.log('✅ 正在使用的样式文件:');
usedFiles.forEach(file => {
  console.log(`   - ${file}`);
});

console.log('\n❓ 可能未使用的样式文件:');
unusedFiles.forEach(file => {
  console.log(`   - ${file}`);
});

console.log('\n🔍 检查文件内容和依赖关系:\n');

// 检查每个未使用的文件是否被其他地方引用
unusedFiles.forEach(file => {
  const filePath = path.join(stylesDir, file);
  const content = fs.readFileSync(filePath, 'utf8');
  
  console.log(`📄 ${file}:`);
  
  // 检查文件大小
  const stats = fs.statSync(filePath);
  console.log(`   📏 大小: ${(stats.size / 1024).toFixed(2)} KB`);
  
  // 检查是否包含重要样式
  const hasImportantStyles = content.includes('!important') || 
                            content.includes('@media') || 
                            content.includes('animation') ||
                            content.includes('transform');
  
  if (hasImportantStyles) {
    console.log(`   ⚠️  包含重要样式规则`);
  }
  
  // 检查是否是空文件或只有注释
  const nonCommentContent = content.replace(/\/\*[\s\S]*?\*\//g, '').replace(/\/\/.*$/gm, '').trim();
  if (nonCommentContent.length < 50) {
    console.log(`   🗑️  文件内容很少，可能可以删除`);
  }
  
  // 检查是否被其他文件引用
  const isReferenced = checkFileReferences(file);
  if (isReferenced.length > 0) {
    console.log(`   🔗 被以下文件引用: ${isReferenced.join(', ')}`);
  } else {
    console.log(`   ✅ 未被其他文件引用`);
  }
  
  console.log('');
});

// 检查文件引用
function checkFileReferences(filename) {
  const references = [];
  const searchDirs = ['app', 'pages', 'components', 'src'];
  
  searchDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      scanDirectory(dir, filename, references);
    }
  });
  
  return references;
}

function scanDirectory(dir, targetFile, references) {
  const items = fs.readdirSync(dir);
  
  items.forEach(item => {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      scanDirectory(fullPath, targetFile, references);
    } else if (stat.isFile() && (item.endsWith('.js') || item.endsWith('.jsx') || item.endsWith('.ts') || item.endsWith('.tsx') || item.endsWith('.css'))) {
      try {
        const content = fs.readFileSync(fullPath, 'utf8');
        if (content.includes(targetFile)) {
          references.push(fullPath);
        }
      } catch (error) {
        // 忽略读取错误
      }
    }
  });
}

console.log('🎯 建议删除的文件:\n');

const safeToDelete = unusedFiles.filter(file => {
  const filePath = path.join(stylesDir, file);
  const content = fs.readFileSync(filePath, 'utf8');
  const nonCommentContent = content.replace(/\/\*[\s\S]*?\*\//g, '').replace(/\/\/.*$/gm, '').trim();
  const isReferenced = checkFileReferences(file);
  
  return nonCommentContent.length < 100 && isReferenced.length === 0;
});

const needsReview = unusedFiles.filter(file => !safeToDelete.includes(file));

console.log('✅ 可以安全删除的文件:');
safeToDelete.forEach(file => {
  console.log(`   - ${file}`);
});

console.log('\n⚠️  需要手动检查的文件:');
needsReview.forEach(file => {
  console.log(`   - ${file}`);
});

console.log('\n📊 统计:');
console.log(`   总文件数: ${allStyleFiles.length}`);
console.log(`   正在使用: ${usedFiles.length}`);
console.log(`   未使用: ${unusedFiles.length}`);
console.log(`   可安全删除: ${safeToDelete.length}`);
console.log(`   需要检查: ${needsReview.length}`);

console.log('\n✅ 分析完成！');
