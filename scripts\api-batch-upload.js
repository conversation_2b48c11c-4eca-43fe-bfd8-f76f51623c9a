/**
 * 通过API批量上传产品
 * 从JSON文件读取产品数据并通过API上传
 */

const fs = require('fs');

/**
 * 通过API创建产品
 */
async function createProductViaAPI(productData) {
  try {
    // 使用node-fetch或者内置fetch
    let fetch;
    try {
      fetch = (await import('node-fetch')).default;
    } catch {
      // 如果node-fetch不可用，尝试使用Node.js 18+的内置fetch
      fetch = globalThis.fetch;
    }

    const response = await fetch('http://localhost:3001/api/products', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(productData)
    });

    const result = await response.json();
    return result;
  } catch (error) {
    throw new Error(`API请求失败: ${error.message}`);
  }
}

/**
 * 批量上传产品
 */
async function batchUploadProducts(products) {
  const results = {
    success: [],
    failed: [],
    skipped: []
  };

  console.log(`开始批量上传 ${products.length} 个产品...\n`);

  for (let i = 0; i < products.length; i++) {
    const product = products[i];
    const productName = product.title || product.name || `产品 ${i + 1}`;
    
    try {
      console.log(`[${i + 1}/${products.length}] 处理产品: ${productName}`);

      // 验证必需字段
      if (!product.title || !product.slug || !product.description) {
        console.log(`❌ 缺少必需字段`);
        results.failed.push({ product: productName, error: '缺少必需字段' });
        continue;
      }

      // 创建产品
      const result = await createProductViaAPI(product);
      
      if (result.success) {
        console.log(`✅ 创建成功 (ID: ${result.productId})`);
        results.success.push({ product: productName, id: result.productId, slug: product.slug });
      } else {
        if (result.message && result.message.includes('already exists')) {
          console.log(`⚠️  产品已存在，跳过: ${product.slug}`);
          results.skipped.push({ product: productName, slug: product.slug });
        } else {
          console.log(`❌ 创建失败: ${result.message}`);
          results.failed.push({ product: productName, error: result.message });
        }
      }

    } catch (error) {
      console.log(`❌ 创建失败: ${error.message}`);
      results.failed.push({ product: productName, error: error.message });
    }

    console.log(''); // 空行分隔
    
    // 添加延迟避免API限制
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  return results;
}

/**
 * 显示上传结果统计
 */
function showResults(results) {
  console.log('📊 上传结果统计:');
  console.log('================');
  console.log(`✅ 成功: ${results.success.length} 个`);
  console.log(`⚠️  跳过: ${results.skipped.length} 个`);
  console.log(`❌ 失败: ${results.failed.length} 个`);

  if (results.success.length > 0) {
    console.log('\n✅ 成功创建的产品:');
    results.success.forEach(item => {
      console.log(`   - ${item.product} (ID: ${item.id}, Slug: ${item.slug})`);
    });
  }

  if (results.skipped.length > 0) {
    console.log('\n⚠️  跳过的产品:');
    results.skipped.forEach(item => {
      console.log(`   - ${item.product} (Slug: ${item.slug})`);
    });
  }

  if (results.failed.length > 0) {
    console.log('\n❌ 失败的产品:');
    results.failed.forEach(item => {
      console.log(`   - ${item.product}: ${item.error}`);
    });
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 API批量产品上传工具');
  console.log('=====================');
  console.log('注意: 请确保Next.js应用正在运行 (npm run dev)');

  // 获取命令行参数
  const jsonFile = process.argv[2];
  
  if (!jsonFile) {
    console.log('\n用法: node api-batch-upload.js <JSON文件路径>');
    console.log('');
    console.log('示例:');
    console.log('  node api-batch-upload.js sample-products.json');
    console.log('  node api-batch-upload.js ../data/products.json');
    process.exit(1);
  }

  try {
    // 检查文件是否存在
    if (!fs.existsSync(jsonFile)) {
      console.log(`❌ 文件不存在: ${jsonFile}`);
      process.exit(1);
    }

    // 读取JSON文件
    console.log(`\n📖 读取文件: ${jsonFile}`);
    const fileContent = fs.readFileSync(jsonFile, 'utf8');
    const products = JSON.parse(fileContent);

    if (!Array.isArray(products)) {
      console.log('❌ JSON文件格式错误，应该是产品数组');
      process.exit(1);
    }

    if (products.length === 0) {
      console.log('❌ 没有找到产品数据');
      process.exit(1);
    }

    console.log(`📦 找到 ${products.length} 个产品\n`);

    // 批量上传
    const results = await batchUploadProducts(products);

    // 显示结果
    showResults(results);

  } catch (error) {
    if (error instanceof SyntaxError) {
      console.log('❌ JSON文件格式错误:', error.message);
    } else {
      console.log('❌ 错误:', error.message);
    }
  }
}

// 运行主函数
main();
