/**
 * 通过API更新产品图片
 */

// 产品图片映射
const productUpdates = [
  {
    slug: 'holographic-restaurant-system',
    images: [
      '/images/holographic/holographic-restaurant.jpg',
      '/images/holographic/exhibition-hall.jpg',
      '/images/company/holographic-display.jpg',
      '/images/holographic/museum.jpg'
    ]
  },
  {
    slug: 'interactive-floor-projection',
    images: [
      '/images/products/3d-1.jpg',
      '/images/products/3d-2.jpg',
      '/images/products/3d-3.jpg',
      '/images/products/3d-4.jpg'
    ]
  },
  {
    slug: 'motion-sensing-game-system',
    images: [
      '/images/products/ar-1.jpg',
      '/images/products/ar-2.jpg',
      '/images/products/ar-3.jpg',
      '/images/products/ar-4.jpg'
    ]
  },
  {
    slug: 'ar-education-system',
    images: [
      '/images/products/ar-education-1.jpg',
      '/images/products/ar-education-2.jpg',
      '/images/products/ar-education-3.jpg',
      '/images/products/ar-education-4.jpg'
    ]
  }
];

async function updateProductImages() {
  console.log('🖼️  开始通过API更新产品图片...');
  
  try {
    let successCount = 0;
    let failCount = 0;
    
    for (const update of productUpdates) {
      try {
        console.log(`\n📝 更新产品: ${update.slug}`);
        console.log(`   新图片: ${update.images.join(', ')}`);
        
        // 首先获取产品信息
        const getResponse = await fetch(`http://localhost:3000/api/products/by-slug/${update.slug}`);
        
        if (!getResponse.ok) {
          console.log(`   ❌ 获取产品失败: ${getResponse.status}`);
          failCount++;
          continue;
        }
        
        const productData = await getResponse.json();
        console.log(`   ✅ 获取产品成功: ${productData.title || productData.name}`);
        
        // 更新产品图片
        const updateData = {
          ...productData,
          images: update.images
        };
        
        const updateResponse = await fetch(`http://localhost:3000/api/products/${productData.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updateData)
        });
        
        if (updateResponse.ok) {
          console.log(`   ✅ 图片更新成功 (${update.images.length} 张图片)`);
          successCount++;
        } else {
          console.log(`   ❌ 图片更新失败: ${updateResponse.status}`);
          failCount++;
        }
        
      } catch (error) {
        console.log(`   ❌ 更新失败: ${error.message}`);
        failCount++;
      }
      
      // 添加延迟避免API限制
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n📊 更新结果统计:');
    console.log(`✅ 成功: ${successCount} 个`);
    console.log(`❌ 失败: ${failCount} 个`);
    
    if (successCount > 0) {
      console.log('\n🎉 图片更新完成!');
      console.log('\n🌐 现在您可以访问以下页面查看不同的图片:');
      productUpdates.forEach(update => {
        console.log(`- http://localhost:3000/zh/products/${update.slug}`);
      });
    }
    
  } catch (error) {
    console.error('❌ 更新过程中发生错误:', error.message);
  }
}

// 检查Next.js应用是否运行
async function checkApp() {
  try {
    const response = await fetch('http://localhost:3000/api/products');
    return response.ok;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log('🚀 产品图片更新工具');
  console.log('==================');
  
  // 检查应用状态
  console.log('🔍 检查Next.js应用状态...');
  const isAppRunning = await checkApp();
  
  if (!isAppRunning) {
    console.log('❌ Next.js应用未运行或API不可访问');
    console.log('请先运行: npm run dev');
    return;
  }
  
  console.log('✅ Next.js应用运行正常');
  
  await updateProductImages();
}

main();
