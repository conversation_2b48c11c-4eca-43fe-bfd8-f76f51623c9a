/**
 * 通过API上传产品的命令行工具
 * 使用现有的API端点来上传产品
 */

const readline = require('readline');

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 产品类型选项
const PRODUCT_TYPES = {
  '1': 'interactive_equipment',
  '2': 'holographic_solutions', 
  '3': 'indoor_playground',
  '4': 'trampoline_park',
  '5': 'family_entertainment_center',
  '6': 'event_solutions'
};

// 尺寸选项
const SIZE_OPTIONS = {
  '1': '100-500 SQM',
  '2': '500-1000 SQM', 
  '3': '1000+ SQM',
  '4': 'Custom Size'
};

// 风格选项
const STYLE_OPTIONS = {
  '1': '现代,互动',
  '2': '科技,未来',
  '3': '传统,经典',
  '4': '儿童,卡通',
  '5': 'Custom Style'
};

/**
 * 提示用户输入
 */
function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

/**
 * 显示选项菜单
 */
function showOptions(title, options) {
  console.log(`\n${title}:`);
  Object.entries(options).forEach(([key, value]) => {
    console.log(`  ${key}. ${value}`);
  });
}

/**
 * 生成slug
 */
function generateSlug(name) {
  return name
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/--+/g, '-')
    .trim();
}

/**
 * 收集产品特性
 */
async function collectFeatures() {
  const features = [];
  
  console.log('\n输入产品特性 (每行一个，空行结束):');
  while (true) {
    const feature = await question('特性: ');
    if (!feature.trim()) break;
    features.push(feature.trim());
  }

  return features;
}

/**
 * 通过API创建产品
 */
async function createProductViaAPI(productData) {
  try {
    // 使用node-fetch或者内置fetch
    let fetch;
    try {
      fetch = (await import('node-fetch')).default;
    } catch {
      // 如果node-fetch不可用，尝试使用Node.js 18+的内置fetch
      fetch = globalThis.fetch;
    }

    const response = await fetch('http://localhost:3000/api/admin/products', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 这里可能需要添加认证头，但为了测试先跳过
      },
      body: JSON.stringify(productData)
    });

    const result = await response.json();
    return result;
  } catch (error) {
    throw new Error(`API请求失败: ${error.message}`);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 API产品上传工具');
  console.log('==================');
  console.log('注意: 请确保Next.js应用正在运行 (npm run dev)');

  try {
    // 收集产品信息
    const name = await question('\n产品名称: ');
    if (!name.trim()) {
      console.log('❌ 产品名称不能为空');
      process.exit(1);
    }

    let slug = await question(`产品slug (默认: ${generateSlug(name)}): `);
    if (!slug.trim()) {
      slug = generateSlug(name);
    }

    const description = await question('产品描述: ');
    if (!description.trim()) {
      console.log('❌ 产品描述不能为空');
      process.exit(1);
    }

    // 选择产品类型
    showOptions('选择产品类型', PRODUCT_TYPES);
    const typeChoice = await question('请选择 (1-6): ');
    const type = PRODUCT_TYPES[typeChoice];
    if (!type) {
      console.log('❌ 无效选择');
      process.exit(1);
    }

    // 选择尺寸
    showOptions('选择产品尺寸', SIZE_OPTIONS);
    const sizeChoice = await question('请选择 (1-4): ');
    let size = SIZE_OPTIONS[sizeChoice];
    if (sizeChoice === '4') {
      size = await question('输入自定义尺寸: ');
    }

    // 选择风格
    showOptions('选择产品风格', STYLE_OPTIONS);
    const styleChoice = await question('请选择 (1-5): ');
    let style = STYLE_OPTIONS[styleChoice];
    if (styleChoice === '5') {
      style = await question('输入自定义风格: ');
    }

    // 收集特性
    const features = await collectFeatures();

    // 图片URL
    const imagesInput = await question('\n图片URL (用逗号分隔，可选): ');
    const images = imagesInput.trim() ? imagesInput.split(',').map(img => img.trim()) : [];

    // 是否发布
    const publishChoice = await question('\n是否发布产品? (y/N): ');
    const isPublished = publishChoice.toLowerCase() === 'y';

    // 是否推荐
    const featuredChoice = await question('是否设为推荐产品? (y/N): ');
    const isFeatured = featuredChoice.toLowerCase() === 'y';

    // 价格
    const priceInput = await question('产品价格 (默认: 0): ');
    const price = parseFloat(priceInput) || 0;

    // 确认信息
    console.log('\n📋 产品信息确认:');
    console.log(`名称: ${name}`);
    console.log(`Slug: ${slug}`);
    console.log(`描述: ${description}`);
    console.log(`类型: ${type}`);
    console.log(`尺寸: ${size}`);
    console.log(`风格: ${style}`);
    console.log(`特性: ${features.join(', ')}`);
    console.log(`图片: ${images.length} 张`);
    console.log(`发布状态: ${isPublished ? '已发布' : '草稿'}`);
    console.log(`推荐状态: ${isFeatured ? '推荐' : '普通'}`);
    console.log(`价格: ${price}`);

    const confirm = await question('\n确认创建产品? (y/N): ');
    if (confirm.toLowerCase() !== 'y') {
      console.log('❌ 操作已取消');
      process.exit(0);
    }

    // 创建产品数据
    const productData = {
      name,
      slug,
      description,
      type,
      size,
      style,
      features,
      images,
      isPublished,
      isFeatured,
      price
    };

    console.log('\n🚀 正在创建产品...');
    const result = await createProductViaAPI(productData);
    
    if (result.success) {
      console.log(`✅ 产品创建成功! ID: ${result.productId}`);
    } else {
      console.log(`❌ 产品创建失败: ${result.message}`);
    }

  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    rl.close();
  }
}

// 运行主函数
main();
