/**
 * 批量产品上传工具
 * 从JSON文件批量上传产品到数据库
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// 数据库连接配置
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 
    'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

/**
 * 验证产品数据
 */
function validateProduct(product, index) {
  const errors = [];
  
  if (!product.name || !product.name.trim()) {
    errors.push(`产品 ${index + 1}: 名称不能为空`);
  }
  
  if (!product.slug || !product.slug.trim()) {
    errors.push(`产品 ${index + 1}: slug不能为空`);
  }
  
  if (!product.description || !product.description.trim()) {
    errors.push(`产品 ${index + 1}: 描述不能为空`);
  }
  
  // 验证slug格式
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  if (product.slug && !slugRegex.test(product.slug)) {
    errors.push(`产品 ${index + 1}: slug格式无效 (${product.slug})`);
  }
  
  return errors;
}

/**
 * 检查产品是否已存在
 */
async function checkProductExists(slug) {
  try {
    const result = await pool.query('SELECT id FROM products WHERE slug = $1', [slug]);
    return result.rows.length > 0;
  } catch (error) {
    console.error('检查产品时出错:', error.message);
    return false;
  }
}

/**
 * 创建单个产品
 */
async function createProduct(productData) {
  const client = await pool.connect();
  try {
    const result = await client.query(
      `INSERT INTO products
       (name, slug, description, size, style, type, features, images, in_stock, is_featured, price)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
       RETURNING id`,
      [
        productData.name,
        productData.slug,
        productData.description,
        productData.size || null,
        productData.style || null,
        productData.type || 'interactive_equipment',
        JSON.stringify(productData.features || []),
        JSON.stringify(productData.images || []),
        productData.isPublished || false,
        productData.isFeatured || false,
        productData.price || 0
      ]
    );

    return result.rows[0].id;
  } finally {
    client.release();
  }
}

/**
 * 批量上传产品
 */
async function batchUploadProducts(products) {
  const results = {
    success: [],
    failed: [],
    skipped: []
  };

  console.log(`开始批量上传 ${products.length} 个产品...\n`);

  for (let i = 0; i < products.length; i++) {
    const product = products[i];
    const productName = product.name || `产品 ${i + 1}`;
    
    try {
      console.log(`[${i + 1}/${products.length}] 处理产品: ${productName}`);

      // 验证产品数据
      const validationErrors = validateProduct(product, i);
      if (validationErrors.length > 0) {
        console.log(`❌ 验证失败:`);
        validationErrors.forEach(error => console.log(`   ${error}`));
        results.failed.push({ product: productName, error: validationErrors.join(', ') });
        continue;
      }

      // 检查是否已存在
      if (await checkProductExists(product.slug)) {
        console.log(`⚠️  产品已存在，跳过: ${product.slug}`);
        results.skipped.push({ product: productName, slug: product.slug });
        continue;
      }

      // 创建产品
      const productId = await createProduct(product);
      console.log(`✅ 创建成功 (ID: ${productId})`);
      results.success.push({ product: productName, id: productId, slug: product.slug });

    } catch (error) {
      console.log(`❌ 创建失败: ${error.message}`);
      results.failed.push({ product: productName, error: error.message });
    }

    console.log(''); // 空行分隔
  }

  return results;
}

/**
 * 显示上传结果统计
 */
function showResults(results) {
  console.log('📊 上传结果统计:');
  console.log('================');
  console.log(`✅ 成功: ${results.success.length} 个`);
  console.log(`⚠️  跳过: ${results.skipped.length} 个`);
  console.log(`❌ 失败: ${results.failed.length} 个`);

  if (results.success.length > 0) {
    console.log('\n✅ 成功创建的产品:');
    results.success.forEach(item => {
      console.log(`   - ${item.product} (ID: ${item.id}, Slug: ${item.slug})`);
    });
  }

  if (results.skipped.length > 0) {
    console.log('\n⚠️  跳过的产品:');
    results.skipped.forEach(item => {
      console.log(`   - ${item.product} (Slug: ${item.slug})`);
    });
  }

  if (results.failed.length > 0) {
    console.log('\n❌ 失败的产品:');
    results.failed.forEach(item => {
      console.log(`   - ${item.product}: ${item.error}`);
    });
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 批量产品上传工具');
  console.log('==================');

  // 获取命令行参数
  const jsonFile = process.argv[2];
  
  if (!jsonFile) {
    console.log('用法: node batch-upload-products.js <JSON文件路径>');
    console.log('');
    console.log('示例:');
    console.log('  node batch-upload-products.js products-data.json');
    console.log('  node batch-upload-products.js ../data/products.json');
    process.exit(1);
  }

  try {
    // 检查文件是否存在
    if (!fs.existsSync(jsonFile)) {
      console.log(`❌ 文件不存在: ${jsonFile}`);
      process.exit(1);
    }

    // 读取JSON文件
    console.log(`📖 读取文件: ${jsonFile}`);
    const fileContent = fs.readFileSync(jsonFile, 'utf8');
    const products = JSON.parse(fileContent);

    if (!Array.isArray(products)) {
      console.log('❌ JSON文件格式错误，应该是产品数组');
      process.exit(1);
    }

    if (products.length === 0) {
      console.log('❌ 没有找到产品数据');
      process.exit(1);
    }

    console.log(`📦 找到 ${products.length} 个产品`);

    // 测试数据库连接
    await pool.query('SELECT 1');
    console.log('✅ 数据库连接成功\n');

    // 批量上传
    const results = await batchUploadProducts(products);

    // 显示结果
    showResults(results);

  } catch (error) {
    if (error instanceof SyntaxError) {
      console.log('❌ JSON文件格式错误:', error.message);
    } else {
      console.log('❌ 错误:', error.message);
    }
  } finally {
    await pool.end();
  }
}

// 运行主函数
main();
