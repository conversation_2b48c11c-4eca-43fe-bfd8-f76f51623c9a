const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function checkAllDatabaseData() {
  console.log('🔍 检查数据库中的所有数据...');
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    // 1. 检查所有表
    console.log('\n📋 数据库中的所有表:');
    const tables = await client.query(`
      SELECT table_name, 
             (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
      FROM information_schema.tables t
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    for (const table of tables.rows) {
      const countResult = await client.query(`SELECT COUNT(*) as count FROM ${table.table_name}`);
      console.log(`   📊 ${table.table_name}: ${countResult.rows[0].count} 条记录 (${table.column_count} 个字段)`);
    }
    
    // 2. 详细检查products表
    if (tables.rows.some(t => t.table_name === 'products')) {
      console.log('\n📦 Products表详细信息:');
      
      // 表结构
      const columns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'products'
        ORDER BY ordinal_position
      `);
      
      console.log('\n🏗️  表结构:');
      columns.rows.forEach(col => {
        const nullable = col.is_nullable === 'YES' ? 'nullable' : 'not null';
        const defaultVal = col.column_default ? ` (default: ${col.column_default})` : '';
        console.log(`   - ${col.column_name}: ${col.data_type} (${nullable})${defaultVal}`);
      });
      
      // 产品数据
      const products = await client.query(`
        SELECT id, name, slug, description, price, type, style, 
               is_featured, in_stock, 
               CASE WHEN images IS NULL OR images = '' THEN '无' ELSE '有' END as has_images,
               CASE WHEN features IS NULL OR features = '' THEN '无' ELSE '有' END as has_features,
               created_at, updated_at
        FROM products 
        ORDER BY id
      `);
      
      console.log(`\n📋 所有产品数据 (${products.rows.length} 个):`);
      products.rows.forEach((product, index) => {
        const featured = product.is_featured ? '⭐' : '  ';
        const stock = product.in_stock ? '✅' : '❌';
        console.log(`\n   ${index + 1}. ${featured} ${stock} ${product.name} (ID: ${product.id})`);
        console.log(`      Slug: ${product.slug}`);
        console.log(`      类型: ${product.type || '未设置'}`);
        console.log(`      风格: ${product.style || '未设置'}`);
        console.log(`      价格: ${product.price || 0}`);
        console.log(`      图片: ${product.has_images}`);
        console.log(`      特性: ${product.has_features}`);
        console.log(`      描述: ${product.description ? product.description.substring(0, 50) + '...' : '无'}`);
        console.log(`      创建时间: ${product.created_at || '未设置'}`);
        console.log(`      更新时间: ${product.updated_at || '未设置'}`);
      });
      
      // 统计信息
      const stats = await client.query(`
        SELECT 
          COUNT(*) as total_products,
          COUNT(CASE WHEN is_featured = true THEN 1 END) as featured_products,
          COUNT(CASE WHEN in_stock = true THEN 1 END) as in_stock_products,
          COUNT(CASE WHEN images IS NOT NULL AND images != '' THEN 1 END) as products_with_images,
          COUNT(CASE WHEN features IS NOT NULL AND features != '' THEN 1 END) as products_with_features,
          COUNT(CASE WHEN description IS NOT NULL AND description != '' THEN 1 END) as products_with_description,
          COUNT(DISTINCT type) as unique_types,
          AVG(price) as avg_price
        FROM products
      `);
      
      const stat = stats.rows[0];
      console.log(`\n📈 产品统计:`);
      console.log(`   📦 总产品数: ${stat.total_products}`);
      console.log(`   ⭐ 特色产品: ${stat.featured_products}`);
      console.log(`   ✅ 有库存产品: ${stat.in_stock_products}`);
      console.log(`   📷 有图片产品: ${stat.products_with_images}`);
      console.log(`   🏷️  有特性产品: ${stat.products_with_features}`);
      console.log(`   📝 有描述产品: ${stat.products_with_description}`);
      console.log(`   🎯 产品类型数: ${stat.unique_types}`);
      console.log(`   💰 平均价格: ${parseFloat(stat.avg_price || 0).toFixed(2)}`);
      
      // 按类型分组
      const typeStats = await client.query(`
        SELECT type, COUNT(*) as count
        FROM products 
        WHERE type IS NOT NULL
        GROUP BY type
        ORDER BY count DESC
      `);
      
      if (typeStats.rows.length > 0) {
        console.log(`\n🏷️  按类型分组:`);
        typeStats.rows.forEach(row => {
          console.log(`   ${row.type}: ${row.count} 个产品`);
        });
      }
    }
    
    // 3. 检查其他表的数据
    for (const table of tables.rows) {
      if (table.table_name !== 'products') {
        const countResult = await client.query(`SELECT COUNT(*) as count FROM ${table.table_name}`);
        if (parseInt(countResult.rows[0].count) > 0) {
          console.log(`\n📊 ${table.table_name}表数据:`);
          try {
            const sampleData = await client.query(`SELECT * FROM ${table.table_name} LIMIT 3`);
            if (sampleData.rows.length > 0) {
              console.log(`   前3条记录:`);
              sampleData.rows.forEach((row, index) => {
                console.log(`   ${index + 1}. ${JSON.stringify(row, null, 2)}`);
              });
            }
          } catch (error) {
            console.log(`   ❌ 无法读取数据: ${error.message}`);
          }
        }
      }
    }
    
    client.release();
    console.log('\n🎉 数据库检查完成!');
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    await pool.end();
  }
}

checkAllDatabaseData();
