require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

// 获取数据库连接参数
const connectionString =
  process.env.DATABASE_URL ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 创建数据库连接池
const pool = new Pool({ connectionString });

async function checkCategories() {
  try {
    console.log('==== 分类信息诊断 ====');

    // 1. 查询所有分类
    console.log('\n1. 查询所有分类:');
    const categoriesResult = await pool.query('SELECT * FROM categories ORDER BY id');

    if (categoriesResult.rows.length === 0) {
      console.log('❌ 数据库中没有任何分类，请先添加分类');
      return;
    }

    console.log(`找到 ${categoriesResult.rows.length} 个分类:`);
    categoriesResult.rows.forEach(category => {
      console.log(
        `ID: ${category.id}, 名称: ${category.name}, 别名: ${category.slug}, 类型: ${category.type || 'N/A'}, 精选: ${category.is_featured ? '是' : '否'}, 激活: ${category.is_active ? '是' : '否'}`
      );
    });

    // 2. 查询所有产品
    console.log('\n2. 查询所有产品:');
    const productsResult = await pool.query('SELECT * FROM products');

    if (productsResult.rows.length === 0) {
      console.log('⚠️ 数据库中没有任何产品，请先添加产品');
    } else {
      console.log(`找到 ${productsResult.rows.length} 个产品:`);
      productsResult.rows.forEach(product => {
        console.log(
          `ID: ${product.id}, 名称: ${product.name}, 别名: ${product.slug}, 分类: ${product.category}`
        );
      });
    }

    // 3. 验证分类是否有关联产品
    console.log('\n3. 验证分类关联:');
    for (const category of categoriesResult.rows) {
      const relatedProducts = productsResult.rows.filter(
        product => product.category === category.slug
      );
      if (relatedProducts.length === 0) {
        console.log(`⚠️ 分类 "${category.name}" (${category.slug}) 没有任何关联产品`);
      } else {
        console.log(`✅ 分类 "${category.name}" 有 ${relatedProducts.length} 个关联产品`);
      }
    }

    // 4. 检查前端显示逻辑
    console.log('\n4. 前端显示诊断:');
    console.log('- 请确认新添加的分类属性设置正确:');
    console.log('  • is_active 应为 true 才会在前端显示');
    console.log('  • type 应设置正确的分类类型');
    console.log('- 请确认前端页面已经刷新缓存');
    console.log('- 添加产品时确保产品的 category 字段使用分类的 slug 值');

    const inactiveCategories = categoriesResult.rows.filter(cat => !cat.is_active);
    if (inactiveCategories.length > 0) {
      console.log('\n⚠️ 以下分类未激活，可能不会在前端显示:');
      inactiveCategories.forEach(cat => {
        console.log(`- ${cat.name} (${cat.slug})`);
      });
    }

    console.log('\n==== 诊断完成 ====');
    console.log('如果需要添加测试产品，可以运行 add-test-product.js 脚本');
  } catch (error) {
    console.error('错误:', error);
  } finally {
    await pool.end();
  }
}

checkCategories();
