const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function checkCurrentProducts() {
  console.log('🔍 检查数据库中当前的产品数量...');
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    // 获取产品总数
    const countResult = await client.query('SELECT COUNT(*) as total FROM products');
    const totalProducts = parseInt(countResult.rows[0].total);
    
    console.log(`\n📊 数据库中产品总数: ${totalProducts}`);
    
    if (totalProducts > 0) {
      // 获取所有产品的基本信息
      const productsResult = await client.query(`
        SELECT id, name, slug, is_featured, in_stock,
               CASE WHEN images IS NULL OR images = '' THEN 0 
                    ELSE json_array_length(images::json) 
               END as image_count,
               created_at
        FROM products 
        ORDER BY id
      `);
      
      console.log(`\n📋 所有产品列表:`);
      productsResult.rows.forEach((product, index) => {
        const featured = product.is_featured ? '⭐' : '  ';
        const stock = product.in_stock ? '✅' : '❌';
        const imageInfo = product.image_count > 0 ? `📷 ${product.image_count}张` : '📷 无图片';
        
        console.log(`   ${index + 1}. ${featured} ${stock} ${product.name} (ID: ${product.id})`);
        console.log(`      Slug: ${product.slug}`);
        console.log(`      图片: ${imageInfo}`);
        console.log(`      创建时间: ${new Date(product.created_at).toLocaleString('zh-CN')}`);
        console.log('');
      });
      
      // 统计信息
      const stats = await client.query(`
        SELECT 
          COUNT(*) as total_products,
          COUNT(CASE WHEN is_featured = true THEN 1 END) as featured_products,
          COUNT(CASE WHEN in_stock = true THEN 1 END) as in_stock_products,
          COUNT(CASE WHEN images IS NOT NULL AND images != '' AND images != '[]' THEN 1 END) as products_with_images,
          SUM(CASE WHEN images IS NOT NULL AND images != '' AND images != '[]' 
                   THEN json_array_length(images::json) 
                   ELSE 0 END) as total_images
        FROM products
      `);
      
      const stat = stats.rows[0];
      console.log(`📈 产品统计:`);
      console.log(`   📦 总产品数: ${stat.total_products}`);
      console.log(`   ⭐ 特色产品: ${stat.featured_products}`);
      console.log(`   ✅ 有库存产品: ${stat.in_stock_products}`);
      console.log(`   📷 有图片产品: ${stat.products_with_images}`);
      console.log(`   🖼️  总图片数: ${stat.total_images || 0}`);
      
      // 按图片数量分组
      const imageStats = await client.query(`
        SELECT 
          CASE WHEN images IS NULL OR images = '' OR images = '[]' THEN 0 
               ELSE json_array_length(images::json) 
          END as image_count,
          COUNT(*) as product_count
        FROM products 
        GROUP BY 
          CASE WHEN images IS NULL OR images = '' OR images = '[]' THEN 0 
               ELSE json_array_length(images::json) 
          END
        ORDER BY image_count
      `);
      
      console.log(`\n📊 按图片数量分组:`);
      imageStats.rows.forEach(row => {
        console.log(`   ${row.image_count} 张图片: ${row.product_count} 个产品`);
      });
      
    } else {
      console.log('\n❌ 数据库中没有产品');
    }
    
    client.release();
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    
    if (error.message.includes('quota')) {
      console.log('\n🚨 数据库配额问题:');
      console.log('   - Neon免费计划的计算时间配额已耗尽');
      console.log('   - 需要升级计划或等待配额重置');
      console.log('   - 或者使用其他数据库服务');
    }
    
  } finally {
    await pool.end();
  }
}

checkCurrentProducts();
