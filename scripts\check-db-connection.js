/**
 * 数据库连接测试脚本
 */
const { Pool } = require('pg');
const { neon } = require('@neondatabase/serverless');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// 加载环境变量
const envPath = path.resolve(__dirname, '../env.txt');
if (fs.existsSync(envPath)) {
  console.log(`使用环境变量文件: ${envPath}`);
  const envContent = fs.readFileSync(envPath, 'utf-8');
  const envVars = dotenv.parse(envContent);
  Object.entries(envVars).forEach(([key, value]) => {
    process.env[key] = value;
  });
}

// 从环境变量获取连接信息
const connectionString = process.env.DATABASE_URL || 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 显示连接信息(隐藏密码)
const sanitizedConnString = connectionString.replace(/:(.*?)@/, ':****@');
console.log(`使用连接字符串: ${sanitizedConnString}`);

// 创建Neon SQL客户端
const sql = neon(connectionString);

// 创建连接池
const pool = new Pool({
  connectionString,
  ssl: { rejectUnauthorized: false },
  max: 5,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 5000,
});

// 测试连接
async function testConnection() {
  console.log(`\n========== PostgreSQL 连接测试 ==========`);
  console.log(`时间: ${new Date().toISOString()}`);
  console.log(`环境: ${process.env.NODE_ENV || 'development'}`);
  
  // 测试连接池
  let poolClient = null;
  try {
    console.log('\n1. 测试连接池连接...');
    poolClient = await pool.connect();
    
    console.log('   ✅ 连接池连接成功!');
    
    console.log('\n2. 测试简单查询...');
    const result = await poolClient.query('SELECT NOW() as time');
    console.log(`   ✅ 查询成功! 服务器时间: ${result.rows[0].time}`);
    
    // 获取PostgreSQL版本
    const versionResult = await poolClient.query('SELECT version()');
    console.log(`   ✅ PostgreSQL版本: ${versionResult.rows[0].version.split(',')[0]}`);
    
    // 获取数据库大小
    const sizeResult = await poolClient.query(`
      SELECT pg_size_pretty(pg_database_size(current_database())) as size
    `);
    console.log(`   ✅ 当前数据库大小: ${sizeResult.rows[0].size}`);
    
    // 检查表
    console.log('\n3. 检查数据库表...');
    const tablesResult = await poolClient.query(`
      SELECT 
        table_name, 
        (SELECT count(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
      FROM information_schema.tables t
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    if (tablesResult.rows.length === 0) {
      console.log('   ⚠️ 没有找到任何表，可能需要初始化数据库');
    } else {
      console.log('   找到以下表:');
      tablesResult.rows.forEach(table => {
        console.log(`   - ${table.table_name} (${table.column_count}列)`);
      });
    }
  } catch (error) {
    console.error('   ❌ 连接测试失败:', error.message);
    console.error('   错误详情:', error);
  } finally {
    if (poolClient) {
      poolClient.release();
      console.log('\n连接已释放');
    }
  }
  
  // 测试serverless连接
  try {
    console.log('\n4. 测试serverless连接...');
    const result = await sql`SELECT NOW() as time`;
    console.log(`   ✅ Serverless连接成功! 服务器时间: ${result[0].time}`);
  } catch (error) {
    console.error('   ❌ Serverless连接测试失败:', error.message);
  }
  
  // 清理资源
  try {
    console.log('\n5. 关闭连接池...');
    await pool.end();
    console.log('   ✅ 连接池已关闭');
  } catch (error) {
    console.error('   ❌ 关闭连接池失败:', error.message);
  }
  
  console.log('\n========== 测试完成 ==========');
}

// 执行测试
testConnection().catch(err => {
  console.error('程序执行出错:', err);
  process.exit(1);
}); 