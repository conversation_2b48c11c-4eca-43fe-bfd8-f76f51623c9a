/**
 * 检查数据库表结构
 */
const { query } = require('../lib/db.js');

async function checkDatabaseSchema() {
  console.log('🔍 检查数据库表结构...\n');

  try {
    // 检查products表的列结构
    console.log('📋 Products表结构:');
    const columnsResult = await query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'products' 
      ORDER BY ordinal_position
    `);

    if (columnsResult.rows.length > 0) {
      console.log('   列名                 | 数据类型        | 可空  | 默认值');
      console.log('   -------------------- | --------------- | ----- | --------');
      columnsResult.rows.forEach(row => {
        const name = row.column_name.padEnd(20);
        const type = row.data_type.padEnd(15);
        const nullable = row.is_nullable.padEnd(5);
        const defaultVal = (row.column_default || 'NULL').substring(0, 20);
        console.log(`   ${name} | ${type} | ${nullable} | ${defaultVal}`);
      });
    } else {
      console.log('   ❌ Products表不存在或无列信息');
    }

    // 检查现有产品数据
    console.log('\n📊 现有产品数据:');
    const productsResult = await query('SELECT id, name, slug FROM products LIMIT 10');
    
    if (productsResult.rows.length > 0) {
      console.log(`   找到 ${productsResult.rows.length} 个产品:`);
      productsResult.rows.forEach(row => {
        console.log(`   - ID: ${row.id}, 名称: ${row.name}, Slug: ${row.slug}`);
      });
    } else {
      console.log('   📝 暂无产品数据');
    }

    // 检查表的索引
    console.log('\n🔑 表索引信息:');
    const indexResult = await query(`
      SELECT indexname, indexdef 
      FROM pg_indexes 
      WHERE tablename = 'products'
    `);

    if (indexResult.rows.length > 0) {
      indexResult.rows.forEach(row => {
        console.log(`   - ${row.indexname}: ${row.indexdef}`);
      });
    } else {
      console.log('   📝 暂无索引信息');
    }

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 执行检查
if (require.main === module) {
  checkDatabaseSchema()
    .then(() => {
      console.log('\n✅ 检查完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 检查失败:', error);
      process.exit(1);
    });
}

module.exports = { checkDatabaseSchema };
