// 检查表单样式的脚本
const fs = require('fs');
const path = require('path');

// 检查CSS文件是否包含正确的样式
function checkFormStyles() {
  console.log('检查表单样式设置...\n');

  // 检查全局表单CSS
  const globalFormCssPath = './app/styles/global-quote-form.css';
  if (fs.existsSync(globalFormCssPath)) {
    const globalFormCss = fs.readFileSync(globalFormCssPath, 'utf8');
    
    console.log('✅ 全局表单CSS存在');
    
    // 检查基础样式
    if (globalFormCss.includes('margin: 2rem auto !important')) {
      console.log('✅ 基础表单样式包含居中设置');
    } else {
      console.log('❌ 基础表单样式缺少居中设置');
    }
    
    // 检查产品页面特定样式
    if (globalFormCss.includes('.products-page-quote-form.quote-form-container')) {
      console.log('✅ 产品页面特定样式存在');
      if (globalFormCss.includes('margin: 3rem auto !important')) {
        console.log('✅ 产品页面表单有居中设置');
      } else {
        console.log('❌ 产品页面表单缺少居中设置');
      }
    } else {
      console.log('❌ 产品页面特定样式不存在');
    }
  } else {
    console.log('❌ 全局表单CSS文件不存在');
  }

  // 检查产品页面CSS
  const productsCssPath = './app/styles/products.css';
  if (fs.existsSync(productsCssPath)) {
    const productsCss = fs.readFileSync(productsCssPath, 'utf8');
    
    console.log('\n✅ 产品页面CSS存在');
    
    if (productsCss.includes('.products-page-quote-form')) {
      console.log('✅ 产品页面表单样式类存在');
      if (productsCss.includes('margin: 2rem auto !important')) {
        console.log('✅ 产品页面表单有额外的居中设置');
      } else {
        console.log('⚠️ 产品页面表单没有额外的居中设置');
      }
    } else {
      console.log('❌ 产品页面表单样式类不存在');
    }
  } else {
    console.log('❌ 产品页面CSS文件不存在');
  }

  // 检查产品页面组件
  const productsPagePath = './app/[lang]/products/page.tsx';
  if (fs.existsSync(productsPagePath)) {
    const productsPage = fs.readFileSync(productsPagePath, 'utf8');
    
    console.log('\n✅ 产品页面组件存在');
    
    if (productsPage.includes('products-page-quote-form')) {
      console.log('✅ 产品页面使用了正确的CSS类');
    } else {
      console.log('❌ 产品页面没有使用正确的CSS类');
    }
    
    if (productsPage.includes('w-full flex justify-center')) {
      console.log('✅ 产品页面包含外层居中布局');
    } else {
      console.log('❌ 产品页面缺少外层居中布局');
    }
  } else {
    console.log('❌ 产品页面组件不存在');
  }

  console.log('\n检查完成！');
}

checkFormStyles(); 