const fs = require('fs');
const path = require('path');

// 检查.next/server/app目录下的HTML文件
function scanDir(dir) {
  try {
    const entries = fs.readdirSync(dir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);

      if (entry.isDirectory()) {
        scanDir(fullPath);
      } else if (entry.name.endsWith('.html')) {
        console.log(`找到HTML文件: ${fullPath}`);
        const content = fs.readFileSync(fullPath, 'utf-8');

        // 检查是否包含分类导航
        if (
          content.includes('100-500-sqm') ||
          content.includes('500-1000-sqm') ||
          content.includes('1000-plus-sqm')
        ) {
          console.log(`- 该文件包含分类导航`);
        }
      }
    }
  } catch (error) {
    console.error(`扫描目录错误 ${dir}:`, error);
  }
}

// 开始扫描
console.log('开始扫描HTML文件...');
scanDir('./.next/server/app');
console.log('扫描完成');
