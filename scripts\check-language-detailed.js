// 详细检查语言切换功能
const fs = require('fs');
const path = require('path');

console.log('🔍 详细检查语言切换功能\n');

// 1. 检查Header组件的语言切换
console.log('📌 检查Header组件的语言切换功能...');
const headerPath = 'app/components/Header.tsx';
if (fs.existsSync(headerPath)) {
  const content = fs.readFileSync(headerPath, 'utf8');
  console.log('Header.tsx 包含的语言切换相关代码:');
  
  if (content.includes('LanguageSwitcher')) {
    console.log('✅ 引用了 LanguageSwitcher 组件');
  }
  
  if (content.includes('usePathname')) {
    console.log('✅ 使用了 usePathname');
  }
  
  if (content.includes('/zh') || content.includes('/en')) {
    console.log('✅ 包含语言路径');
  }
  
  // 检查是否有硬编码文本
  const chineseMatches = content.match(/[\u4e00-\u9fa5]+/g);
  if (chineseMatches) {
    console.log(`⚠️ 发现 ${chineseMatches.length} 处中文硬编码:`, chineseMatches.slice(0, 3));
  }
}

// 2. 检查LanguageSwitcher组件
console.log('\n📌 检查LanguageSwitcher组件...');
const langSwitcherPath = 'app/components/LanguageSwitcher.tsx';
if (fs.existsSync(langSwitcherPath)) {
  const content = fs.readFileSync(langSwitcherPath, 'utf8');
  console.log('✅ LanguageSwitcher.tsx 存在');
  
  if (content.includes('router.push') || content.includes('router.replace')) {
    console.log('✅ 使用了路由切换');
  }
  
  if (content.includes('pathname.replace')) {
    console.log('✅ 正确替换路径中的语言参数');
  }
} else {
  console.log('❌ LanguageSwitcher.tsx 不存在');
}

// 3. 检查中间件
console.log('\n📌 检查中间件语言处理...');
const middlewarePath = 'middleware.ts';
if (fs.existsSync(middlewarePath)) {
  const content = fs.readFileSync(middlewarePath, 'utf8');
  console.log('Middleware.ts 语言相关内容:');
  
  // 查找语言相关代码
  const lines = content.split('\n');
  lines.forEach((line, index) => {
    if (line.includes('locale') || line.includes('/zh') || line.includes('/en') || line.includes('lang')) {
      console.log(`  Line ${index + 1}: ${line.trim()}`);
    }
  });
}

// 4. 检查特定页面的国际化
console.log('\n📌 检查重要页面的国际化实现...');
const pagesToCheck = [
  'app/[lang]/page.tsx',
  'app/[lang]/products/page.tsx',
  'app/[lang]/products/[slug]/page.tsx',
  'app/[lang]/pages/about-us/page.tsx',
  'app/[lang]/pages/contact-us/page.tsx'
];

pagesToCheck.forEach(pagePath => {
  if (fs.existsSync(pagePath)) {
    const content = fs.readFileSync(pagePath, 'utf8');
    console.log(`\n检查 ${pagePath}:`);
    
    // 检查是否使用了getDictionary
    if (content.includes('getDictionary')) {
      console.log('  ✅ 使用了 getDictionary');
    } else {
      console.log('  ❌ 未使用 getDictionary');
    }
    
    // 检查是否处理了params.lang
    if (content.includes('params.lang') || content.includes('params?.lang')) {
      console.log('  ✅ 处理了语言参数');
    } else {
      console.log('  ⚠️ 可能未处理语言参数');
    }
    
    // 检查硬编码的中文
    const chineseMatches = content.match(/['"`]([^'"`]*[\u4e00-\u9fa5]+[^'"`]*)['"`]/g);
    if (chineseMatches && chineseMatches.length > 0) {
      console.log(`  ⚠️ 发现 ${chineseMatches.length} 处硬编码中文`);
      chineseMatches.slice(0, 2).forEach(match => {
        console.log(`    - ${match}`);
      });
    }
  } else {
    console.log(`\n❌ ${pagePath} 不存在`);
  }
});

// 5. 检查字典文件的完整性
console.log('\n📌 检查字典文件...');
const dictPaths = {
  zh: 'app/dictionaries/zh.json',
  en: 'app/dictionaries/en.json'
};

const dictionaries = {};
for (const [lang, dictPath] of Object.entries(dictPaths)) {
  if (fs.existsSync(dictPath)) {
    try {
      dictionaries[lang] = JSON.parse(fs.readFileSync(dictPath, 'utf8'));
      const keys = countKeys(dictionaries[lang]);
      console.log(`✅ ${lang}.json: ${keys} 个键`);
    } catch (error) {
      console.log(`❌ ${lang}.json 解析失败: ${error.message}`);
    }
  } else {
    console.log(`❌ ${dictPath} 不存在`);
  }
}

// 比较字典键
if (dictionaries.zh && dictionaries.en) {
  console.log('\n比较字典键的差异:');
  const zhKeys = getAllKeys(dictionaries.zh);
  const enKeys = getAllKeys(dictionaries.en);
  
  const onlyInZh = zhKeys.filter(key => !enKeys.includes(key));
  const onlyInEn = enKeys.filter(key => !zhKeys.includes(key));
  
  if (onlyInZh.length > 0) {
    console.log(`\n仅在中文字典中的键 (${onlyInZh.length} 个):`);
    onlyInZh.slice(0, 5).forEach(key => console.log(`  - ${key}`));
  }
  
  if (onlyInEn.length > 0) {
    console.log(`\n仅在英文字典中的键 (${onlyInEn.length} 个):`);
    onlyInEn.slice(0, 5).forEach(key => console.log(`  - ${key}`));
  }
}

// 6. 检查组件中的t()函数使用
console.log('\n📌 检查关键组件的t()函数使用...');
const componentsToCheck = [
  'app/components/Header.tsx',
  'app/components/Footer.tsx',
  'app/components/HeroSection.tsx',
  'app/components/ProductGrid.tsx',
  'app/components/QuoteForm.tsx'
];

componentsToCheck.forEach(compPath => {
  if (fs.existsSync(compPath)) {
    const content = fs.readFileSync(compPath, 'utf8');
    const tFunctionCalls = content.match(/t\(['"]([\w.]+)['"]\)/g) || [];
    console.log(`\n${compPath}:`);
    console.log(`  t()函数调用: ${tFunctionCalls.length} 次`);
    if (tFunctionCalls.length > 0) {
      console.log(`  示例: ${tFunctionCalls.slice(0, 3).join(', ')}`);
    }
  }
});

console.log('\n' + '='.repeat(60));
console.log('🎯 语言切换功能总结');
console.log('='.repeat(60));

// 辅助函数
function countKeys(obj) {
  let count = 0;
  for (const key in obj) {
    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      count += countKeys(obj[key]);
    } else {
      count++;
    }
  }
  return count;
}

function getAllKeys(obj, prefix = '') {
  let keys = [];
  for (const key in obj) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      keys = keys.concat(getAllKeys(obj[key], fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  return keys;
} 