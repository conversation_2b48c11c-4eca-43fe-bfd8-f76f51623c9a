/**
 * 检查产品详情的完整性
 */
const { Pool } = require('pg');
require('dotenv').config();

// 数据库连接信息
const connectionString =
  process.env.POSTGRES_URI ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

const pool = new Pool({
  connectionString,
  ssl: { rejectUnauthorized: false },
});

async function checkProductDetails() {
  let client;

  try {
    client = await pool.connect();
    console.log('数据库连接成功');

    // 获取表结构
    const tableStructure = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'products'
      ORDER BY ordinal_position
    `);

    console.log('产品表列名:');
    console.log(tableStructure.rows.map(row => row.column_name).join(', '));

    // 检查产品详情
    const productDetailsResult = await client.query(`
      SELECT id, name, slug, price, image_url, images, detail_images, features, is_featured
      FROM products
      WHERE name LIKE '%Interactive Experience%'
      LIMIT 1
    `);

    if (productDetailsResult.rows.length === 0) {
      console.log('没有找到产品');
      return;
    }

    const product = productDetailsResult.rows[0];
    console.log('\n产品详情示例:');
    console.log(`ID: ${product.id}`);
    console.log(`名称: ${product.name}`);
    console.log(`Slug: ${product.slug}`);
    console.log(`价格: ${product.price}`);
    console.log(`主图: ${product.image_url}`);
    console.log(`图片集: ${product.images}`);
    console.log(`详情图: ${product.detail_images}`);
    console.log(`特性: ${product.features}`);
    console.log(`是否特色: ${product.is_featured}`);

    // 尝试解析images字段
    try {
      const images = JSON.parse(product.images);
      console.log('\n解析后的图片集:');
      console.log(images);
    } catch (e) {
      console.log('\n解析图片集失败:', e.message);
    }

    // 尝试解析detail_images字段
    try {
      const detailImages = JSON.parse(product.detail_images);
      console.log('\n解析后的详情图:');
      console.log(detailImages);
    } catch (e) {
      console.log('\n解析详情图失败:', e.message);
    }
  } catch (error) {
    console.error('检查产品详情时出错:', error);
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
}

checkProductDetails().catch(console.error);
