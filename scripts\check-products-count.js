const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function checkProductsCount() {
  console.log('🔍 检查数据库产品数量...');
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    // 获取产品总数
    const countResult = await client.query('SELECT COUNT(*) as total FROM products');
    const totalProducts = parseInt(countResult.rows[0].total);
    
    console.log(`\n📊 产品总数: ${totalProducts}`);
    
    if (totalProducts > 0) {
      // 获取所有产品详情
      const productsResult = await client.query(
        'SELECT id, name, slug, type, is_featured, in_stock FROM products ORDER BY id'
      );
      
      console.log(`\n📋 产品列表:`);
      productsResult.rows.forEach((row, index) => {
        const featured = row.is_featured ? '⭐' : '  ';
        const stock = row.in_stock ? '✅' : '❌';
        console.log(`   ${index + 1}. ${featured} ${stock} ${row.name} (${row.slug})`);
        console.log(`      类型: ${row.type}`);
      });
      
      // 统计信息
      const featuredCount = productsResult.rows.filter(row => row.is_featured).length;
      const inStockCount = productsResult.rows.filter(row => row.in_stock).length;
      
      // 按类型统计
      const typeStats = {};
      productsResult.rows.forEach(row => {
        typeStats[row.type] = (typeStats[row.type] || 0) + 1;
      });
      
      console.log(`\n📈 统计信息:`);
      console.log(`   📦 总产品数: ${totalProducts}`);
      console.log(`   ⭐ 特色产品: ${featuredCount}`);
      console.log(`   ✅ 有库存: ${inStockCount}`);
      
      console.log(`\n🏷️  按类型统计:`);
      Object.entries(typeStats).forEach(([type, count]) => {
        console.log(`   ${type}: ${count} 个产品`);
      });
      
      // 检查图片信息
      const imagesResult = await client.query(
        'SELECT name, images FROM products WHERE images IS NOT NULL'
      );
      
      console.log(`\n📷 图片信息:`);
      imagesResult.rows.forEach(row => {
        try {
          const images = JSON.parse(row.images);
          console.log(`   ${row.name}: ${images.length} 张图片`);
        } catch (e) {
          console.log(`   ${row.name}: 图片数据解析错误`);
        }
      });
      
    } else {
      console.log('\n❌ 数据库中没有产品');
    }
    
    client.release();
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    await pool.end();
  }
}

checkProductsCount();
