/**
 * 检查产品表结构
 */
const { Pool } = require('pg');
require('dotenv').config();

// 数据库连接信息
const connectionString =
  process.env.POSTGRES_URI ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

const pool = new Pool({
  connectionString,
  ssl: { rejectUnauthorized: false },
});

async function checkProductsTable() {
  let client;

  try {
    client = await pool.connect();
    console.log('数据库连接成功');

    // 检查产品表结构
    const tableResult = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'products' 
      ORDER BY ordinal_position
    `);

    if (tableResult.rows.length === 0) {
      console.log('产品表不存在或没有列');
    } else {
      console.log('产品表结构:');
      console.table(tableResult.rows);
    }

    // 检查产品表中是否有数据
    const countResult = await client.query('SELECT COUNT(*) FROM products');
    console.log(`产品表中有 ${countResult.rows[0].count} 条记录`);
  } catch (error) {
    console.error('查询产品表结构时出错:', error);
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
}

checkProductsTable().catch(console.error);
