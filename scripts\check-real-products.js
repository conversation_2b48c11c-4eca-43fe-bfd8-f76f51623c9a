const { Pool } = require('pg');

// 数据库连接
const connectionString =
  process.env.POSTGRES_URI ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 创建连接池
const pool = new Pool({
  connectionString,
  ssl: {
    rejectUnauthorized: false,
  },
});

// 执行查询函数
async function query(text, params = []) {
  const client = await pool.connect();
  try {
    return await client.query(text, params);
  } finally {
    client.release();
  }
}

async function checkProducts() {
  try {
    console.log('检查数据库中的产品...');
    console.log('使用连接字符串:', connectionString.replace(/\/\/(.*):(.*)@/, '//***:***@'));

    // 获取所有产品
    const result = await query('SELECT * FROM products ORDER BY id');

    console.log(`数据库中存在 ${result.rows.length} 个产品：`);
    result.rows.forEach(product => {
      console.log(`ID: ${product.id} | 名称: ${product.name} | Slug: ${product.slug}`);
    });

    // 获取产品slug列表
    const productSlugs = result.rows.map(p => p.slug);
    console.log('\n存在的产品Slug列表:');
    console.log(productSlugs);

    // 列出页面中硬编码的产品
    const hardcodedProducts = [
      '730-sqm-customizable-pastel-style-indoor-playground-solution',
      '240-sqm-customizable-morandi-style-indoor-playground-solution',
      '2300-sqm-trampoline-park-custom-indoor-sports-arena',
      '1200-sqm-customizable-indoor-playground-adventure-family-entertainment',
      '400-sqm-customizable-interstellar-style-indoor-playground-solution',
      '800-sqm-customizable-colorful-style-indoor-playground-solution',
      '600-sqm-customizable-morandi-style-indoor-playground-solution',
      '375-sqm-customizable-pastel-style-indoor-playground-solution',
      '1500-sqm-trampoline-park-adventure-entertainment-center',
      '800-sqm-trampoline-park-family-entertainment-solution',
    ];

    // 检查哪些硬编码产品不在数据库中
    console.log('\n不在数据库中的产品:');
    const missingProducts = hardcodedProducts.filter(slug => !productSlugs.includes(slug));
    missingProducts.forEach(slug => {
      console.log(`- ${slug}`);
    });

    // 关闭连接
    await pool.end();
  } catch (error) {
    console.error('查询产品失败:', error);
  }
}

// 执行检查
checkProducts();
