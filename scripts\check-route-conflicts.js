/**
 * 检查App Router和Pages Router之间的路由冲突
 */
const fs = require('fs');
const path = require('path');

// 日志函数
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m', // 青色
    success: '\x1b[32m', // 绿色
    warning: '\x1b[33m', // 黄色
    error: '\x1b[31m', // 红色
  };

  console.log(`${colors[type]}[${type.toUpperCase()}]\x1b[0m ${message}`);
}

// 递归获取路由文件
function getRouteFiles(directory, filePattern) {
  const results = [];

  function traverse(dir, base = '') {
    if (!fs.existsSync(dir)) return;

    const files = fs.readdirSync(dir);

    for (const file of files) {
      const fullPath = path.join(dir, file);
      const relativePath = path.join(base, file);
      const stats = fs.statSync(fullPath);

      if (stats.isDirectory()) {
        // 跳过API目录，因为它们不会与页面路由冲突
        if (file === 'api') continue;

        // 如果是动态路由参数目录([param])，添加它的基本版本
        let routeName = file;
        if (file.startsWith('[') && file.endsWith(']')) {
          routeName = ':' + file.slice(1, -1);
        }

        traverse(fullPath, path.join(base, routeName));
      } else if (stats.isFile() && file.match(filePattern)) {
        const parsedPath = path.parse(relativePath);
        const routePath = parsedPath.dir;

        // 对于页面路由，page.js/tsx表示当前目录
        // 对于传统路由，文件名就是路由的一部分(除非是index)
        let finalRoute = routePath;

        if (parsedPath.name !== 'page' && parsedPath.name !== 'index') {
          finalRoute = path.join(routePath, parsedPath.name);
        }

        // 处理page.js/index.js的特殊情况
        if (finalRoute === '') {
          finalRoute = '/';
        } else {
          finalRoute = '/' + finalRoute.replace(/\\/g, '/');
        }

        results.push({
          route: finalRoute,
          file: fullPath,
        });
      }
    }
  }

  traverse(directory);
  return results;
}

// 检查路由冲突
function checkRouteConflicts() {
  // 获取App Router路由
  log('Scanning App Router routes...', 'info');
  const appRoutes = getRouteFiles(path.join(process.cwd(), 'app'), /page\.(js|jsx|ts|tsx)$/);

  // 获取Pages Router路由
  log('Scanning Pages Router routes...', 'info');
  const pagesRoutes = getRouteFiles(path.join(process.cwd(), 'pages'), /\.(js|jsx|ts|tsx)$/);

  // 排除_app.js, _document.js等特殊文件
  const filteredPagesRoutes = pagesRoutes.filter(item => !path.basename(item.file).startsWith('_'));

  // 查找冲突
  log('Checking for conflicts...', 'info');
  const conflicts = [];

  for (const appRoute of appRoutes) {
    // 从app/[lang]路由中提取实际路径
    let normalizedAppRoute = appRoute.route;
    if (normalizedAppRoute.startsWith('/:lang')) {
      normalizedAppRoute = normalizedAppRoute.replace('/:lang', '');
      if (normalizedAppRoute === '') normalizedAppRoute = '/';
    }

    for (const pageRoute of filteredPagesRoutes) {
      if (normalizedAppRoute === pageRoute.route) {
        conflicts.push({
          route: normalizedAppRoute,
          appFile: appRoute.file,
          pageFile: pageRoute.file,
        });
      }
    }
  }

  // 显示结果
  if (conflicts.length === 0) {
    log('No route conflicts found!', 'success');
  } else {
    log(`Found ${conflicts.length} route conflicts:`, 'error');

    conflicts.forEach((conflict, index) => {
      log(`${index + 1}. Route: "${conflict.route}"`, 'error');
      log(`   App Router: ${conflict.appFile}`, 'warning');
      log(`   Pages Router: ${conflict.pageFile}`, 'warning');
      log('   Migration needed!', 'info');
      console.log(); // 空行
    });

    log('Migration suggestions:', 'info');
    log('1. Prefer App Router implementations over Pages Router', 'info');
    log('2. Ensure all Links and redirects point to the new routes', 'info');
    log('3. Test thoroughly after migration', 'info');

    process.exit(1);
  }

  // 检查API路由
  log('Checking API route duplications...', 'info');

  // 获取App Router API路由
  const appApiRoutes = getRouteFiles(
    path.join(process.cwd(), 'app', 'api'),
    /route\.(js|jsx|ts|tsx)$/
  );

  // 获取Pages Router API路由
  const pagesApiRoutes = getRouteFiles(
    path.join(process.cwd(), 'pages', 'api'),
    /\.(js|jsx|ts|tsx)$/
  );

  // 显示API路由
  log(`Found ${appApiRoutes.length} App Router API routes`, 'info');
  log(`Found ${pagesApiRoutes.length} Pages Router API routes`, 'info');

  if (appApiRoutes.length > 0 && pagesApiRoutes.length > 0) {
    log(
      'You have API routes in both routing systems. Consider migrating all to App Router.',
      'warning'
    );
  }
}

// 运行检查
checkRouteConflicts();
