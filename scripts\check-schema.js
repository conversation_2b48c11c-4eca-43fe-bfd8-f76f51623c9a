// 查看PostgreSQL数据库表结构
const { Pool } = require('pg');

async function checkSchema() {
  const connectionString =
    'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

  const pool = new Pool({
    connectionString,
    ssl: { rejectUnauthorized: false },
  });

  try {
    const client = await pool.connect();
    console.log('连接数据库成功\n');

    // 获取所有表
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);

    console.log('数据库中的表:');
    if (tablesResult.rows.length === 0) {
      console.log('没有找到表');
    } else {
      for (const tableRow of tablesResult.rows) {
        const tableName = tableRow.table_name;
        console.log(`\n表名: ${tableName}`);
        console.log('------------------------');

        // 获取表结构
        const columnsResult = await client.query(
          `
          SELECT column_name, data_type, character_maximum_length, 
                 column_default, is_nullable
          FROM information_schema.columns
          WHERE table_schema = 'public' 
          AND table_name = $1
          ORDER BY ordinal_position
        `,
          [tableName]
        );

        if (columnsResult.rows.length === 0) {
          console.log('没有找到列');
        } else {
          columnsResult.rows.forEach(column => {
            console.log(`列名: ${column.column_name}`);
            console.log(
              `数据类型: ${column.data_type}${column.character_maximum_length ? `(${column.character_maximum_length})` : ''}`
            );
            console.log(`可为空: ${column.is_nullable === 'YES' ? '是' : '否'}`);
            console.log(`默认值: ${column.column_default || '无'}`);
            console.log('------------------------');
          });
        }
      }
    }

    client.release();
    await pool.end();
  } catch (error) {
    console.error('错误:', error);
  }
}

checkSchema().catch(console.error);
