const { Pool } = require('pg');
require('dotenv').config();

async function checkSolutionMenu() {
  // Database connection
  const pool = new Pool({
    connectionString:
      process.env.DATABASE_URL ||
      'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
    ssl: {
      rejectUnauthorized: false,
    },
  });

  try {
    console.log('Connecting to database...');
    const client = await pool.connect();
    console.log('Connected successfully');

    // Query all categories details
    console.log('\nFetching all categories:');
    const allCats = await client.query(`
      SELECT id, name, slug, is_featured, featured_type, featured_order, translations
      FROM categories
      ORDER BY id
    `);

    console.log(`Found ${allCats.rows.length} categories in total.`);
    console.log('--------------------------');

    if (allCats.rows.length === 0) {
      console.log('No categories found');
    } else {
      allCats.rows.forEach(item => {
        console.log(`ID: ${item.id}`);
        console.log(`Name: ${item.name}`);
        console.log(`Slug: ${item.slug}`);
        console.log(`Featured: ${item.is_featured}`);
        console.log(`Featured Type: ${item.featured_type || 'null'}`);
        console.log(`Featured Order: ${item.featured_order || 'null'}`);
        console.log(`Translations: ${JSON.stringify(item.translations, null, 2)}`);
        console.log('--------------------------');
      });
    }

    client.release();
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the function
checkSolutionMenu().catch(console.error);
