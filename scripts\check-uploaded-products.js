/**
 * 查看已上传的产品
 */
const { Pool } = require('pg');
require('dotenv').config();

// 数据库连接信息
const connectionString =
  process.env.POSTGRES_URI ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

const pool = new Pool({
  connectionString,
  ssl: { rejectUnauthorized: false },
});

async function listProducts() {
  let client;

  try {
    client = await pool.connect();
    console.log('数据库连接成功');

    // 查询所有产品
    const result = await client.query(`
      SELECT id, name, slug, type, image_url, in_stock, created_at
      FROM products 
      ORDER BY id DESC
    `);

    if (result.rows.length === 0) {
      console.log('没有找到产品');
    } else {
      console.log(`找到 ${result.rows.length} 个产品：`);
      result.rows.forEach(product => {
        console.log(
          `ID: ${product.id}, 名称: ${product.name}, 类型: ${product.type}, 图片: ${product.image_url || '无'}, 创建时间: ${product.created_at}`
        );
      });
    }
  } catch (error) {
    console.error('查询产品时出错:', error);
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
}

listProducts().catch(console.error);
