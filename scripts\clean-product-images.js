const fs = require('fs');
const path = require('path');

const targetDir = 'public/images/products';

// 要保留的图片列表 - 每个产品只保留必要的图片
const keepImages = [
  // 互动足球系统
  'interactive-football-main.jpg',
  'interactive-football-1.jpg',
  'interactive-football-2.jpg',
  'interactive-football-3.jpg',
  'interactive-football-4.jpg',
  'interactive-football-5.jpg',
  'interactive-football-6.jpg',
  
  // AR体感蹦床
  'ar-trampoline-main.jpg',
  'ar-trampoline-extra-1.jpg',
  'ar-trampoline-extra-2.jpg',
  'ar-trampoline-extra-3.jpg',
  'ar-trampoline-extra-4.jpg',
  
  // 体感攀岩
  'motion-sensing-climbing-1.jpg',
  'motion-sensing-climbing-2.jpg',
  'motion-sensing-climbing-3.jpg',
  'motion-sensing-climbing-4.jpg',
  'motion-sensing-climbing-5.jpg',
  'motion-sensing-climbing-6.jpg',
  
  // 互动砸球
  'interactive-ball-main.jpg',
  'interactive-ball-extra-1.jpg',
  'interactive-ball-extra-2.jpg',
  'interactive-ball-extra-3.jpg',
  'interactive-ball-extra-4.jpg',
  
  // AR教育
  'ar-education-main.jpg',
  'ar-education-1.jpg',
  'ar-education-2.jpg',
  'ar-education-3.jpg',
  'ar-education-4.jpg',
  
  // KTV系统
  'ktv-main.jpg',
  'ktv-1.jpg',
  'ktv-2.jpg',
  'ktv-3.jpg',
  'ktv-4.jpg',
  'ktv-5.jpg',
  'ktv-6.jpg',
  
  // 一体机
  'all-in-one-1.jpg',
  'all-in-one-2.jpg',
  'all-in-one-3.jpg',
  'all-in-one-4.jpg',
  'all-in-one-5.jpg',
  'all-in-one-6.jpg',
  
  // 3D电子沙盘
  '3d-sandbox-main.jpg',
  '3d-sandbox-1.jpg',
  '3d-sandbox-2.jpg',
  '3d-sandbox-3.jpg',
  '3d-sandbox-4.jpg',
  '3d-sandbox-5.jpg',
  '3d-sandbox-6.jpg',
  
  // 保龄球
  'bowling-main.jpg',
  'bowling-1.jpg',
  'bowling-2.jpg',
  'bowling-3.jpg',
  'bowling-4.jpg',
  'bowling-5.jpg',
  'bowling-6.jpg',
  
  // 儿童互动沙滩
  'children-beach-main.jpg',
  'children-beach-extra-1.jpg',
  'children-beach-extra-2.jpg',
  'children-beach-extra-3.jpg',
  'children-beach-extra-4.jpg',
  'children-beach-extra-5.jpg',
  'children-beach-extra-6.jpg',
  
  // 儿童互动砸球
  'children-ball-main.jpg',
  'children-ball-1.jpg',
  'children-ball-2.jpg',
  'children-ball-3.jpg',
  'children-ball-4.jpg',
  'children-ball-5.jpg',
  'children-ball-6.jpg',
  
  // 全息沙幕
  'holographic-screen-main.jpg',
  'holographic-screen-1.jpg',
  'holographic-screen-2.jpg',
  'holographic-screen-3.jpg',
  'holographic-screen-4.jpg',
  'holographic-screen-5.jpg',
  'holographic-screen-6.jpg',
  
  // 全息沙桌
  'holographic-table-main.jpg',
  'holographic-table-1.jpg',
  'holographic-table-2.jpg',
  'holographic-table-3.jpg',
  'holographic-table-4.jpg',
  'holographic-table-5.jpg',
  'holographic-table-6.jpg',
  
  // 全息舞台
  'holographic-stage-main.jpg',
  'holographic-stage-1.jpg',
  'holographic-stage-2.jpg',
  'holographic-stage-3.jpg',
  'holographic-stage-4.jpg',
  'holographic-stage-5.jpg',
  
  // 全息餐厅
  'holographic-dining-extra-1.jpg',
  'holographic-dining-extra-2.jpg',
  'holographic-dining-extra-3.jpg',
  'holographic-dining-extra-4.jpg',
  'holographic-dining-extra-5.jpg',
  'holographic-dining-extra-6.jpg',
  
  // 保留必要的系统文件
  'placeholder.jpg',
  'product-banner.png'
];

function cleanProductImages() {
  console.log('🧹 开始清理产品图片...\n');
  
  if (!fs.existsSync(targetDir)) {
    console.log('❌ 产品图片目录不存在');
    return;
  }
  
  const allFiles = fs.readdirSync(targetDir);
  const imageFiles = allFiles.filter(file => 
    /\.(jpg|jpeg|png|gif)$/i.test(file) && 
    fs.statSync(path.join(targetDir, file)).isFile()
  );
  
  console.log(`📊 找到 ${imageFiles.length} 个图片文件`);
  
  let deletedCount = 0;
  let keptCount = 0;
  
  imageFiles.forEach(file => {
    if (keepImages.includes(file)) {
      console.log(`✅ 保留: ${file}`);
      keptCount++;
    } else {
      try {
        fs.unlinkSync(path.join(targetDir, file));
        console.log(`🗑️  删除: ${file}`);
        deletedCount++;
      } catch (error) {
        console.log(`❌ 删除失败: ${file} - ${error.message}`);
      }
    }
  });
  
  // 删除空的子目录
  const subDirs = allFiles.filter(item => {
    const itemPath = path.join(targetDir, item);
    return fs.existsSync(itemPath) && fs.statSync(itemPath).isDirectory();
  });
  
  subDirs.forEach(dir => {
    const dirPath = path.join(targetDir, dir);
    try {
      const dirFiles = fs.readdirSync(dirPath);
      if (dirFiles.length === 0) {
        fs.rmdirSync(dirPath);
        console.log(`🗑️  删除空目录: ${dir}`);
      } else {
        console.log(`📁 保留非空目录: ${dir} (${dirFiles.length} 个文件)`);
      }
    } catch (error) {
      console.log(`❌ 处理目录失败: ${dir} - ${error.message}`);
    }
  });
  
  console.log(`\n🎉 清理完成！`);
  console.log(`📊 统计信息:`);
  console.log(`   - 保留图片: ${keptCount}`);
  console.log(`   - 删除图片: ${deletedCount}`);
  
  // 显示最终的图片列表
  console.log(`\n📋 最终保留的图片:`);
  const finalFiles = fs.readdirSync(targetDir)
    .filter(file => /\.(jpg|jpeg|png|gif)$/i.test(file))
    .sort();
  
  finalFiles.forEach(img => {
    console.log(`   📷 ${img}`);
  });
  
  console.log(`\n✅ 总共保留 ${finalFiles.length} 张产品图片`);
}

cleanProductImages();
