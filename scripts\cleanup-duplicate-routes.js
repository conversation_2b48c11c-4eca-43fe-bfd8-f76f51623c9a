/**
 * 彻底清理重复路由问题：将pages/api目录下的所有API文件移动到备份目录
 */
const fs = require('fs');
const path = require('path');

// 日志函数
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m', // 青色
    success: '\x1b[32m', // 绿色
    warning: '\x1b[33m', // 黄色
    error: '\x1b[31m', // 红色
  };

  console.log(`${colors[type]}[${type.toUpperCase()}]\x1b[0m ${message}`);
}

// 确保目录存在
function ensureDirectoryExists(directory) {
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
    log(`Created directory: ${directory}`, 'success');
  }
}

// 获取所有API文件
function getAllApiFiles(directory) {
  const files = [];

  function traverseDirectory(currentPath, relativePath = '') {
    const entries = fs.readdirSync(currentPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(currentPath, entry.name);
      const relPath = path.join(relativePath, entry.name);

      if (entry.isDirectory()) {
        traverseDirectory(fullPath, relPath);
      } else if (entry.isFile() && (entry.name.endsWith('.js') || entry.name.endsWith('.ts'))) {
        files.push({
          fullPath,
          relativePath: relPath,
        });
      }
    }
  }

  traverseDirectory(directory);
  return files;
}

// 移动文件到备份目录
function moveFileToBackup(sourceFile, targetFile) {
  try {
    // 确保目标目录存在
    const targetDir = path.dirname(targetFile);
    ensureDirectoryExists(targetDir);

    // 读取源文件内容
    const content = fs.readFileSync(sourceFile, 'utf8');

    // 写入备份文件
    fs.writeFileSync(targetFile, content);

    // 删除源文件
    fs.unlinkSync(sourceFile);

    log(`Moved to backup: ${sourceFile} -> ${targetFile}`, 'success');
    return true;
  } catch (error) {
    log(`Failed to move file ${sourceFile}: ${error.message}`, 'error');
    return false;
  }
}

// 主函数
async function main() {
  log('Starting complete API routes cleanup...', 'info');

  // 项目根目录
  const rootDir = process.cwd();

  // 源目录和备份目录
  const sourceApiDir = path.join(rootDir, 'pages', 'api');
  const backupDir = path.join(rootDir, 'backup', 'pages-api');

  // 确保备份目录存在
  ensureDirectoryExists(backupDir);

  // 检查源目录是否存在
  if (!fs.existsSync(sourceApiDir)) {
    log(`Source API directory not found: ${sourceApiDir}`, 'error');
    return;
  }

  // 获取所有API文件
  const apiFiles = getAllApiFiles(sourceApiDir);
  log(`Found ${apiFiles.length} API files in pages/api directory`, 'info');

  let successCount = 0;
  let errorCount = 0;

  // 处理每个文件
  for (const file of apiFiles) {
    const targetFile = path.join(backupDir, file.relativePath);
    const success = moveFileToBackup(file.fullPath, targetFile);

    if (success) {
      successCount++;
    } else {
      errorCount++;
    }
  }

  // 显示结果
  log(`Operation completed.`, 'success');
  log(`Successfully moved: ${successCount}`, 'success');
  log(`Errors: ${errorCount}`, 'error');

  if (successCount > 0) {
    log(`All API routes have been moved to backup directory.`, 'success');
    log(`Please restart the development server to see the changes.`, 'info');
  }

  // 清理空目录
  const cleanEmptyDirectories = dirPath => {
    const isDirectory = fs.statSync(dirPath).isDirectory();
    if (!isDirectory) return false;

    let files = fs.readdirSync(dirPath);

    if (files.length > 0) {
      let removed = 0;
      for (const file of files) {
        const fullPath = path.join(dirPath, file);
        const isEmpty = cleanEmptyDirectories(fullPath);
        if (isEmpty) {
          removed++;
        }
      }

      files = fs.readdirSync(dirPath);
    }

    if (files.length === 0) {
      fs.rmdirSync(dirPath);
      log(`Removed empty directory: ${dirPath}`, 'info');
      return true;
    }

    return false;
  };

  try {
    cleanEmptyDirectories(sourceApiDir);
    log('Cleaned up empty directories', 'success');
  } catch (error) {
    log(`Error cleaning empty directories: ${error.message}`, 'error');
  }
}

// 执行主函数
main().catch(error => {
  log(`An error occurred: ${error.message}`, 'error');
  process.exit(1);
});
