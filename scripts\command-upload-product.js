/**
 * 命令行产品上传工具
 * 交互式命令行界面，用于上传单个产品到数据库
 */

const readline = require('readline');
const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// 数据库连接配置
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 
    'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 产品类型选项
const PRODUCT_TYPES = {
  '1': 'interactive_equipment',
  '2': 'holographic_solutions', 
  '3': 'indoor_playground',
  '4': 'trampoline_park',
  '5': 'family_entertainment_center',
  '6': 'event_solutions'
};

// 尺寸选项
const SIZE_OPTIONS = {
  '1': '100-500 SQM',
  '2': '500-1000 SQM', 
  '3': '1000+ SQM',
  '4': 'Custom Size'
};

// 风格选项
const STYLE_OPTIONS = {
  '1': '现代,互动',
  '2': '科技,未来',
  '3': '传统,经典',
  '4': '儿童,卡通',
  '5': 'Custom Style'
};

/**
 * 提示用户输入
 */
function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

/**
 * 显示选项菜单
 */
function showOptions(title, options) {
  console.log(`\n${title}:`);
  Object.entries(options).forEach(([key, value]) => {
    console.log(`  ${key}. ${value}`);
  });
}

/**
 * 验证slug格式
 */
function validateSlug(slug) {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
}

/**
 * 生成slug
 */
function generateSlug(name) {
  return name
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/--+/g, '-')
    .trim();
}

/**
 * 复制图片到public目录
 */
function copyImageToPublic(sourcePath, destFilename) {
  try {
    const targetDir = 'public/images/products';
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }

    const destPath = path.join(targetDir, destFilename);
    fs.copyFileSync(sourcePath, destPath);
    return `/images/products/${destFilename}`;
  } catch (error) {
    console.error(`复制图片失败: ${error.message}`);
    return null;
  }
}

/**
 * 处理图片上传
 */
async function handleImageUpload(productSlug) {
  const images = [];
  
  while (true) {
    const imagePath = await question('\n输入图片路径 (回车跳过): ');
    if (!imagePath.trim()) break;

    if (!fs.existsSync(imagePath)) {
      console.log('❌ 图片文件不存在，请重新输入');
      continue;
    }

    const ext = path.extname(imagePath);
    const destFilename = `${productSlug}-${images.length + 1}${ext}`;
    const imageUrl = copyImageToPublic(imagePath, destFilename);
    
    if (imageUrl) {
      images.push(imageUrl);
      console.log(`✅ 图片已添加: ${imageUrl}`);
    }
  }

  return images;
}

/**
 * 收集产品特性
 */
async function collectFeatures() {
  const features = [];
  
  console.log('\n输入产品特性 (每行一个，空行结束):');
  while (true) {
    const feature = await question('特性: ');
    if (!feature.trim()) break;
    features.push(feature.trim());
  }

  return features;
}

/**
 * 检查产品是否已存在
 */
async function checkProductExists(slug) {
  try {
    const result = await pool.query('SELECT id FROM products WHERE slug = $1', [slug]);
    return result.rows.length > 0;
  } catch (error) {
    console.error('检查产品时出错:', error.message);
    return false;
  }
}

/**
 * 创建产品
 */
async function createProduct(productData) {
  const client = await pool.connect();
  try {
    const result = await client.query(
      `INSERT INTO products
       (name, slug, description, size, style, type, features, images, in_stock, is_featured, price)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
       RETURNING id`,
      [
        productData.name,
        productData.slug,
        productData.description,
        productData.size,
        productData.style,
        productData.type,
        JSON.stringify(productData.features || []),
        JSON.stringify(productData.images || []),
        productData.isPublished || false,
        productData.isFeatured || false,
        productData.price || 0
      ]
    );

    return result.rows[0].id;
  } finally {
    client.release();
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 产品上传工具');
  console.log('================');

  try {
    // 测试数据库连接
    await pool.query('SELECT 1');
    console.log('✅ 数据库连接成功');

    // 收集产品信息
    const name = await question('\n产品名称: ');
    if (!name.trim()) {
      console.log('❌ 产品名称不能为空');
      process.exit(1);
    }

    let slug = await question(`产品slug (默认: ${generateSlug(name)}): `);
    if (!slug.trim()) {
      slug = generateSlug(name);
    }

    if (!validateSlug(slug)) {
      console.log('❌ Slug格式无效，只能包含小写字母、数字和连字符');
      process.exit(1);
    }

    // 检查slug是否已存在
    if (await checkProductExists(slug)) {
      console.log('❌ 该slug已存在，请使用其他slug');
      process.exit(1);
    }

    const description = await question('产品描述: ');
    if (!description.trim()) {
      console.log('❌ 产品描述不能为空');
      process.exit(1);
    }

    // 选择产品类型
    showOptions('选择产品类型', PRODUCT_TYPES);
    const typeChoice = await question('请选择 (1-6): ');
    const type = PRODUCT_TYPES[typeChoice];
    if (!type) {
      console.log('❌ 无效选择');
      process.exit(1);
    }

    // 选择尺寸
    showOptions('选择产品尺寸', SIZE_OPTIONS);
    const sizeChoice = await question('请选择 (1-4): ');
    let size = SIZE_OPTIONS[sizeChoice];
    if (sizeChoice === '4') {
      size = await question('输入自定义尺寸: ');
    }

    // 选择风格
    showOptions('选择产品风格', STYLE_OPTIONS);
    const styleChoice = await question('请选择 (1-5): ');
    let style = STYLE_OPTIONS[styleChoice];
    if (styleChoice === '5') {
      style = await question('输入自定义风格: ');
    }

    // 收集特性
    const features = await collectFeatures();

    // 处理图片
    const images = await handleImageUpload(slug);

    // 是否发布
    const publishChoice = await question('\n是否发布产品? (y/N): ');
    const isPublished = publishChoice.toLowerCase() === 'y';

    // 价格
    const priceInput = await question('产品价格 (默认: 0): ');
    const price = parseFloat(priceInput) || 0;

    // 确认信息
    console.log('\n📋 产品信息确认:');
    console.log(`名称: ${name}`);
    console.log(`Slug: ${slug}`);
    console.log(`描述: ${description}`);
    console.log(`类型: ${type}`);
    console.log(`尺寸: ${size}`);
    console.log(`风格: ${style}`);
    console.log(`特性: ${features.join(', ')}`);
    console.log(`图片: ${images.length} 张`);
    console.log(`发布状态: ${isPublished ? '已发布' : '草稿'}`);
    console.log(`价格: ${price}`);

    const confirm = await question('\n确认创建产品? (y/N): ');
    if (confirm.toLowerCase() !== 'y') {
      console.log('❌ 操作已取消');
      process.exit(0);
    }

    // 创建产品
    const productData = {
      name,
      slug,
      description,
      type,
      size,
      style,
      features,
      images,
      isPublished,
      price
    };

    const productId = await createProduct(productData);
    console.log(`✅ 产品创建成功! ID: ${productId}`);

  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    rl.close();
    await pool.end();
  }
}

// 运行主函数
main();
