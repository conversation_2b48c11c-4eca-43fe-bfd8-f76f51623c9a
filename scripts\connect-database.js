const { Pool } = require('pg');

// 数据库连接配置
const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function connectDatabase() {
  console.log('🔗 正在连接数据库...');
  console.log('📍 数据库地址: ep-lucky-boat-a1yjp7be-pooler.ap-southeast-1.aws.neon.tech');
  console.log('🗄️  数据库名称: neondb');
  console.log('👤 用户名: neondb_owner');
  
  try {
    // 测试连接
    const client = await pool.connect();
    console.log('✅ 数据库连接成功!');
    
    // 获取数据库基本信息
    console.log('\n📊 数据库信息:');
    
    // 数据库版本
    const versionResult = await client.query('SELECT version()');
    console.log(`   🔢 PostgreSQL版本: ${versionResult.rows[0].version.split(' ')[1]}`);
    
    // 当前时间
    const timeResult = await client.query('SELECT NOW() as current_time');
    console.log(`   ⏰ 服务器时间: ${timeResult.rows[0].current_time}`);
    
    // 数据库大小
    const sizeResult = await client.query(`
      SELECT pg_size_pretty(pg_database_size('neondb')) as database_size
    `);
    console.log(`   💾 数据库大小: ${sizeResult.rows[0].database_size}`);
    
    // 连接信息
    const connResult = await client.query(`
      SELECT 
        current_database() as database_name,
        current_user as user_name,
        inet_server_addr() as server_ip,
        inet_server_port() as server_port
    `);
    const conn = connResult.rows[0];
    console.log(`   🗄️  当前数据库: ${conn.database_name}`);
    console.log(`   👤 当前用户: ${conn.user_name}`);
    console.log(`   🌐 服务器IP: ${conn.server_ip || 'N/A'}`);
    console.log(`   🔌 服务器端口: ${conn.server_port || 'N/A'}`);
    
    // 表统计
    const tablesResult = await client.query(`
      SELECT 
        COUNT(*) as table_count,
        SUM(CASE WHEN table_type = 'BASE TABLE' THEN 1 ELSE 0 END) as base_tables,
        SUM(CASE WHEN table_type = 'VIEW' THEN 1 ELSE 0 END) as views
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    const tables = tablesResult.rows[0];
    console.log(`\n📋 表统计:`);
    console.log(`   📊 总表数: ${tables.table_count}`);
    console.log(`   📦 基础表: ${tables.base_tables}`);
    console.log(`   👁️  视图: ${tables.views}`);
    
    // 列出所有表
    const tableListResult = await client.query(`
      SELECT table_name, table_type
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    console.log(`\n📋 所有表列表:`);
    tableListResult.rows.forEach((table, index) => {
      console.log(`   ${index + 1}. ${table.table_name} (${table.table_type})`);
    });
    
    // 测试查询
    console.log(`\n🧪 测试查询:`);
    
    // 产品数量
    const productCount = await client.query('SELECT COUNT(*) as count FROM products');
    console.log(`   📦 产品数量: ${productCount.rows[0].count}`);
    
    // 表单提交数量
    const formCount = await client.query('SELECT COUNT(*) as count FROM form_submissions');
    console.log(`   📝 表单提交: ${formCount.rows[0].count}`);
    
    // 管理员数量
    const adminCount = await client.query('SELECT COUNT(*) as count FROM admin_users');
    console.log(`   👤 管理员数量: ${adminCount.rows[0].count}`);
    
    // 连接池信息
    console.log(`\n🏊 连接池状态:`);
    console.log(`   🔗 总连接数: ${pool.totalCount}`);
    console.log(`   💤 空闲连接: ${pool.idleCount}`);
    console.log(`   🏃 等待连接: ${pool.waitingCount}`);
    
    client.release();
    console.log('\n🎉 数据库连接测试完成!');
    console.log('\n💡 连接字符串格式:');
    console.log('   postgresql://用户名:密码@主机:端口/数据库名?sslmode=require');
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.error('🔍 错误详情:', error.code || 'Unknown error');
    
    // 常见错误解决方案
    console.log('\n🛠️  常见问题解决方案:');
    console.log('   1. 检查网络连接');
    console.log('   2. 验证数据库凭据');
    console.log('   3. 确认SSL设置');
    console.log('   4. 检查防火墙设置');
    
  } finally {
    await pool.end();
    console.log('\n🔌 数据库连接已关闭');
  }
}

// 执行连接测试
connectDatabase();
