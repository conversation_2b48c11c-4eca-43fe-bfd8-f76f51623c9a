const fs = require('fs');
const path = require('path');

// 确保目标目录存在
const targetDir = 'public/images/products';
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// 产品文件夹映射
const productFolders = [
  // 第一批产品 (产品/1/)
  { folder: '产品/1/互动足球', slug: 'interactive-football' },
  { folder: '产品/1/AR体感蹦床', slug: 'ar-trampoline' },
  { folder: '产品/1/体感攀岩', slug: 'motion-sensing-climbing' },
  { folder: '产品/1/互动砸球', slug: 'interactive-ball' },
  { folder: '产品/1/AR教育', slug: 'ar-education' },
  { folder: '产品/1/ktv', slug: 'ktv' },
  { folder: '产品/1/一体机', slug: 'all-in-one' },
  { folder: '产品/1/3d电子沙盘', slug: '3d-sandbox' },
  
  // 第二批产品 (产品/3/)
  { folder: '产品/3/保龄球', slug: 'bowling' },
  { folder: '产品/3/儿童互动沙滩', slug: 'children-beach' },
  { folder: '产品/3/儿童互动砸球', slug: 'children-ball' },
  { folder: '产品/3/全息沙幕', slug: 'holographic-screen' },
  { folder: '产品/3/全息沙桌', slug: 'holographic-table' },
  { folder: '产品/3/全息舞台', slug: 'holographic-stage' },
  { folder: '产品/3/全息餐厅', slug: 'holographic-dining' }
];

// 复制单个图片文件
function copyImage(sourcePath, destPath) {
  try {
    if (fs.existsSync(sourcePath)) {
      // 确保目标目录存在
      const destDir = path.dirname(destPath);
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
      }
      
      fs.copyFileSync(sourcePath, destPath);
      console.log(`✅ 复制成功: ${sourcePath} -> ${destPath}`);
      return true;
    } else {
      console.log(`❌ 源文件不存在: ${sourcePath}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ 复制失败: ${sourcePath} -> ${error.message}`);
    return false;
  }
}

// 处理单个产品文件夹
function processProductFolder(productInfo) {
  const { folder, slug } = productInfo;
  console.log(`\n📁 处理产品: ${folder} (${slug})`);
  
  let imageCount = 0;
  
  // 1. 复制主图片（未标题-1.jpg）
  const mainImagePath = path.join(folder, '未标题-1.jpg');
  const mainDestPath = path.join(targetDir, `${slug}-main.jpg`);
  if (copyImage(mainImagePath, mainDestPath)) {
    imageCount++;
  }
  
  // 2. 复制images文件夹中的所有图片
  const imagesDir = path.join(folder, 'images');
  if (fs.existsSync(imagesDir)) {
    const imageFiles = fs.readdirSync(imagesDir)
      .filter(file => /\.(jpg|jpeg|png|gif)$/i.test(file))
      .sort();
    
    console.log(`  📷 找到 ${imageFiles.length} 张图片`);
    
    imageFiles.forEach((file, index) => {
      const sourcePath = path.join(imagesDir, file);
      const fileExt = path.extname(file);
      const destPath = path.join(targetDir, `${slug}-${index + 1}${fileExt}`);
      
      if (copyImage(sourcePath, destPath)) {
        imageCount++;
      }
    });
  }
  
  // 3. 复制其他根目录下的图片文件
  if (fs.existsSync(folder)) {
    const rootFiles = fs.readdirSync(folder)
      .filter(file => {
        const filePath = path.join(folder, file);
        return fs.statSync(filePath).isFile() && 
               /\.(jpg|jpeg|png|gif)$/i.test(file) &&
               file !== '未标题-1.jpg'; // 避免重复复制主图
      })
      .sort();
    
    if (rootFiles.length > 0) {
      console.log(`  📷 根目录找到 ${rootFiles.length} 张额外图片`);
      
      rootFiles.forEach((file, index) => {
        const sourcePath = path.join(folder, file);
        const fileExt = path.extname(file);
        const destPath = path.join(targetDir, `${slug}-extra-${index + 1}${fileExt}`);
        
        if (copyImage(sourcePath, destPath)) {
          imageCount++;
        }
      });
    }
  }
  
  console.log(`  ✨ ${slug} 总共复制了 ${imageCount} 张图片`);
  return imageCount;
}

// 主函数
function copyAllProductImages() {
  console.log('🚀 开始复制所有产品图片...\n');
  
  let totalImages = 0;
  let processedProducts = 0;
  
  productFolders.forEach(productInfo => {
    const imageCount = processProductFolder(productInfo);
    totalImages += imageCount;
    processedProducts++;
  });
  
  console.log(`\n🎉 复制完成！`);
  console.log(`📊 统计信息:`);
  console.log(`   - 处理产品数: ${processedProducts}`);
  console.log(`   - 总图片数: ${totalImages}`);
  
  // 列出所有复制的图片
  console.log(`\n📋 复制的图片列表:`);
  const copiedImages = fs.readdirSync(targetDir)
    .filter(file => /\.(jpg|jpeg|png|gif)$/i.test(file))
    .sort();
  
  copiedImages.forEach(img => {
    console.log(`   📷 ${img}`);
  });
  
  console.log(`\n✅ 总共有 ${copiedImages.length} 张产品图片`);
}

// 执行复制
copyAllProductImages();
