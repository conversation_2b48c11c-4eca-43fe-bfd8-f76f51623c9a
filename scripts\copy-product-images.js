// 复制产品图片到public目录的脚本
const fs = require('fs');
const path = require('path');

// 源文件路径和目标路径
const filesToCopy = [
  // 产品体验图片
  {
    src: path.join(__dirname, '../产品/3/全息餐厅/1_01.jpg'),
    dest: path.join(__dirname, '../public/images/products/hologram-dining-1.jpg'),
    description: '数字化餐饮体验-全息餐厅',
  },
  {
    src: path.join(__dirname, '../产品/3/全息餐厅/1_02.jpg'),
    dest: path.join(__dirname, '../public/images/products/hologram-dining-2.jpg'),
    description: '数字化餐饮体验-全息餐厅',
  },
  {
    src: path.join(__dirname, '../产品/3/全息舞台/images/产品介绍模板_01.jpg'),
    dest: path.join(__dirname, '../public/images/products/hologram-stage-1.jpg'),
    description: '高端宴会空间视听解决方案-全息舞台',
  },
  {
    src: path.join(__dirname, '../产品/3/全息舞台/images/产品介绍模板_02.jpg'),
    dest: path.join(__dirname, '../public/images/products/hologram-stage-2.jpg'),
    description: '高端宴会空间视听解决方案-全息舞台',
  },
  {
    src: path.join(__dirname, '../产品/1/3d电子沙盘/images/产品介绍模板_01.jpg'),
    dest: path.join(__dirname, '../public/images/products/3d-sandbox-1.jpg'),
    description: '震撼立体视觉体验-3D电子沙盘',
  },
  {
    src: path.join(__dirname, '../产品/1/互动足球/images/产品介绍模板_01.jpg'),
    dest: path.join(__dirname, '../public/images/products/interactive-football-1.jpg'),
    description: '创新公共空间互动体验-互动足球',
  },

  // 案例展示图片 - 使用不同的图片
  {
    src: path.join(__dirname, '../产品/1/3d电子沙盘/images/产品介绍模板_02.jpg'),
    dest: path.join(__dirname, '../public/images/solutions/case1.jpg'),
    description: '案例-上海国际会展中心全息投影',
  },
  {
    src: path.join(__dirname, '../产品/1/互动足球/images/产品介绍模板_03.jpg'),
    dest: path.join(__dirname, '../public/images/solutions/case2.jpg'),
    description: '案例-迪拜购物中心互动地面投影',
  },
  {
    src: path.join(__dirname, '../产品/3/全息餐厅/1_04.jpg'),
    dest: path.join(__dirname, '../public/images/solutions/case3.jpg'),
    description: '案例-悉尼沉浸式主题餐厅',
  },
  {
    src: path.join(__dirname, '../产品/1/ktv/images/产品介绍模板_02.jpg'),
    dest: path.join(__dirname, '../public/images/solutions/case4.jpg'),
    description: '案例-纽约企业科技展厅',
  },
  {
    src: path.join(__dirname, '../产品/3/全息舞台/images/产品介绍模板_03.jpg'),
    dest: path.join(__dirname, '../public/images/solutions/case5.jpg'),
    description: '案例-莫斯科豪华酒店宴会厅',
  },
  {
    src: path.join(__dirname, '../产品/3/保龄球/images/产品介绍模板_01.jpg'),
    dest: path.join(__dirname, '../public/images/solutions/case6.jpg'),
    description: '案例-东京零售体验中心',
  },

  // 公司简介图片
  {
    src: path.join(__dirname, '../产品/16283334_1703063679.jpg'),
    dest: path.join(__dirname, '../public/images/company/company.jpg'),
    description: '公司大楼图片',
  },

  // 首页轮播图 - 高级图片
  {
    src: path.join(__dirname, '../产品/3/全息餐厅/1_05.jpg'),
    dest: path.join(__dirname, '../public/images/products/slide1.jpg'),
    description: '首页轮播图1 - 全息餐厅高级图片',
  },
  {
    src: path.join(__dirname, '../产品/3/全息舞台/images/产品介绍模板_04.jpg'),
    dest: path.join(__dirname, '../public/images/products/slide2.jpg'),
    description: '首页轮播图2 - 全息舞台高级图片',
  },
  {
    src: path.join(__dirname, '../产品/1/ktv/images/产品介绍模板_05.jpg'),
    dest: path.join(__dirname, '../public/images/products/slide3.jpg'),
    description: '首页轮播图3 - KTV展厅高级图片',
  },
];

// 执行复制
filesToCopy.forEach(file => {
  try {
    // 确保目标目录存在
    const destDir = path.dirname(file.dest);
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true });
    }

    // 复制文件
    fs.copyFileSync(file.src, file.dest);
    console.log(`✅ 成功复制: ${file.description}`);
    console.log(`   从: ${file.src}`);
    console.log(`   到: ${file.dest}`);
  } catch (error) {
    console.error(`❌ 复制失败: ${file.description}`);
    console.error(`   错误: ${error.message}`);
  }
});

console.log('图片复制完成！');
