const fs = require('fs');
const path = require('path');

// 确保目标目录存在
const targetDir = 'public/images/products';
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// 第二批产品图片复制映射
const imageCopyMap = [
  // 保龄球
  {
    src: '产品/3/保龄球/未标题-1.jpg',
    dest: 'public/images/products/bowling.jpg'
  },
  // 儿童互动沙滩
  {
    src: '产品/3/儿童互动沙滩/未标题-1.jpg',
    dest: 'public/images/products/children-beach.jpg'
  },
  // 儿童互动砸球
  {
    src: '产品/3/儿童互动砸球/未标题-1.jpg',
    dest: 'public/images/products/children-ball.jpg'
  },
  // 全息沙幕
  {
    src: '产品/3/全息沙幕/未标题-1.jpg',
    dest: 'public/images/products/holographic-screen.jpg'
  },
  // 全息沙桌
  {
    src: '产品/3/全息沙桌/未标题-1.jpg',
    dest: 'public/images/products/holographic-table.jpg'
  },
  // 全息舞台
  {
    src: '产品/3/全息舞台/未标题-1.jpg',
    dest: 'public/images/products/holographic-stage.jpg'
  },
  // 全息餐厅
  {
    src: '产品/3/全息餐厅/1_01.jpg',
    dest: 'public/images/products/holographic-dining-1.jpg'
  }
];

// 复制图片
let successCount = 0;
let failCount = 0;

console.log('开始复制第二批产品图片...\n');

imageCopyMap.forEach(({ src, dest }) => {
  try {
    if (fs.existsSync(src)) {
      fs.copyFileSync(src, dest);
      console.log(`✅ 复制成功: ${src} -> ${dest}`);
      successCount++;
    } else {
      console.log(`❌ 源文件不存在: ${src}`);
      failCount++;
    }
  } catch (error) {
    console.log(`❌ 复制失败: ${src} -> ${error.message}`);
    failCount++;
  }
});

console.log(`\n第二批复制完成！成功: ${successCount}, 失败: ${failCount}`);

// 检查现有图片
console.log('\n检查现有产品图片:');
const existingImages = fs.readdirSync(targetDir);
existingImages.forEach(img => {
  console.log(`📷 ${img}`);
});

console.log(`\n总共有 ${existingImages.length} 张产品图片`);
