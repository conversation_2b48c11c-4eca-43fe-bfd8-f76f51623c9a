/**
 * 创建默认管理员用户
 */
const { Pool } = require('pg');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function createAdminUser() {
  const client = await pool.connect();
  
  try {
    console.log('开始创建管理员用户...');
    
    // 首先创建用户表（如果不存在）
    console.log('创建用户表...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(255) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        role VARCHAR(50) DEFAULT 'user',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // 检查是否已经有管理员用户
    const existingAdmin = await client.query(
      "SELECT id FROM users WHERE role = 'admin' OR username = 'admin'"
    );
    
    if (existingAdmin.rows.length > 0) {
      console.log('✅ 管理员用户已存在，跳过创建');
      
      // 显示现有管理员信息
      const adminInfo = await client.query(
        "SELECT username, email, role FROM users WHERE role = 'admin' OR username = 'admin'"
      );
      
      console.log('现有管理员用户:');
      adminInfo.rows.forEach(user => {
        console.log(`  - 用户名: ${user.username}`);
        console.log(`  - 邮箱: ${user.email}`);
        console.log(`  - 角色: ${user.role}`);
      });
      
      return;
    }
    
    // 创建默认管理员用户
    const adminData = {
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123', // 默认密码，建议首次登录后修改
      role: 'admin'
    };
    
    // 加密密码
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(adminData.password, saltRounds);
    
    // 插入管理员用户
    const result = await client.query(
      `INSERT INTO users (username, email, password_hash, role, created_at, updated_at)
       VALUES ($1, $2, $3, $4, NOW(), NOW())
       RETURNING id, username, email, role`,
      [adminData.username, adminData.email, passwordHash, adminData.role]
    );
    
    const newAdmin = result.rows[0];
    
    console.log('✅ 管理员用户创建成功!');
    console.log('管理员信息:');
    console.log(`  - ID: ${newAdmin.id}`);
    console.log(`  - 用户名: ${newAdmin.username}`);
    console.log(`  - 邮箱: ${newAdmin.email}`);
    console.log(`  - 角色: ${newAdmin.role}`);
    console.log(`  - 默认密码: ${adminData.password}`);
    console.log('');
    console.log('⚠️  重要提示:');
    console.log('   请在首次登录后立即修改默认密码！');
    console.log('   登录地址: http://localhost:3000/zh/admin/login');
    
    // 验证用户表
    const userCount = await client.query('SELECT COUNT(*) as count FROM users');
    console.log(`\n数据库中现有用户总数: ${userCount.rows[0].count}`);
    
  } catch (error) {
    console.error('❌ 创建管理员用户时出错:', error.message);
    
    if (error.code === '23505') {
      console.log('用户名或邮箱已存在，请检查现有用户');
    }
  } finally {
    client.release();
    await pool.end();
  }
}

// 执行脚本
if (require.main === module) {
  createAdminUser()
    .then(() => {
      console.log('\n🎉 管理员用户设置完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { createAdminUser };
