require('dotenv').config();
const { neon } = require('@neondatabase/serverless');
const bcrypt = require('bcryptjs');

async function createAdminTable(sql) {
  try {
    const result = await sql`
      CREATE TABLE IF NOT EXISTS admin_users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(255) NOT NULL UNIQUE,
        email VARCHAR(255) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        is_super_admin BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    console.log('Admin users table created successfully');
    return true;
  } catch (error) {
    console.error('Error creating admin users table:', error);
    return false;
  }
}

async function createAdminUser(sql, admin) {
  try {
    // 检查用户名是否已存在
    const existingUser = await sql`
      SELECT * FROM admin_users WHERE username = ${admin.username} OR email = ${admin.email}
    `;

    if (existingUser.length > 0) {
      console.log('Admin user already exists');
      return false;
    }

    // 加密密码
    const salt = await bcrypt.genSalt(10);
    const passwordHash = await bcrypt.hash(admin.password, salt);

    // 插入管理员用户
    const result = await sql`
      INSERT INTO admin_users (
        username, 
        email, 
        password_hash, 
        is_super_admin
      )
      VALUES (
        ${admin.username},
        ${admin.email},
        ${passwordHash},
        ${admin.is_super_admin || false}
      )
      RETURNING id, username, email, is_super_admin, created_at
    `;

    console.log('Admin user created successfully:', result[0]);
    return true;
  } catch (error) {
    console.error('Error creating admin user:', error);
    return false;
  }
}

async function main() {
  // 使用连接字符串
  const connectionString =
    process.env.DATABASE_URL ||
    'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

  if (!connectionString) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  try {
    console.log('Connecting to database...');
    const sql = neon(connectionString);

    // 创建管理员表
    await createAdminTable(sql);

    // 创建超级管理员用户
    const admin = {
      username: 'admin',
      email: '<EMAIL>',
      password: 'Admin123!', // 在实际使用中请使用更强的密码
      is_super_admin: true,
    };

    await createAdminUser(sql, admin);

    console.log('Setup completed successfully');
  } catch (error) {
    console.error('Setup failed:', error);
    process.exit(1);
  }
}

main();
