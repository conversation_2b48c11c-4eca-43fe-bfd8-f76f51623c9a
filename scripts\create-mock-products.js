const fs = require('fs');
const path = require('path');

// 确保目标目录存在
const targetDir = 'public/images/products';
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// 所有产品目录映射 - 包含产品/1、产品/2、产品/3的所有产品
const allProductDirectories = [
  // 产品/1/ 目录
  { folder: '产品/1/互动足球', slug: 'interactive-football-system', name: '互动足球系统', category: '运动娱乐' },
  { folder: '产品/1/AR体感蹦床', slug: 'ar-motion-trampoline', name: 'AR体感蹦床', category: '运动娱乐' },
  { folder: '产品/1/体感攀岩', slug: 'motion-sensing-climbing', name: '体感攀岩系统', category: '运动娱乐' },
  { folder: '产品/1/互动砸球', slug: 'interactive-ball-smash', name: '互动砸球游戏', category: '游戏娱乐' },
  { folder: '产品/1/AR教育', slug: 'ar-education-system', name: 'AR教育系统', category: '教育科技' },
  { folder: '产品/1/ktv', slug: 'ktv-interactive-system', name: 'KTV互动系统', category: '娱乐社交' },
  { folder: '产品/1/一体机', slug: 'interactive-all-in-one', name: '互动一体机', category: '商业设备' },
  { folder: '产品/1/3d电子沙盘', slug: '3d-electronic-sandbox', name: '3D电子沙盘', category: '教育展示' },
  
  // 产品/2/ 目录 (重复产品，使用不同slug)
  { folder: '产品/2/互动足球', slug: 'interactive-football-pro', name: '互动足球专业版', category: '运动娱乐' },
  { folder: '产品/2/AR体感蹦床', slug: 'ar-trampoline-pro', name: 'AR体感蹦床专业版', category: '运动娱乐' },
  { folder: '产品/2/体感攀岩', slug: 'motion-climbing-pro', name: '体感攀岩专业版', category: '运动娱乐' },
  { folder: '产品/2/互动砸球', slug: 'interactive-ball-pro', name: '互动砸球专业版', category: '游戏娱乐' },
  { folder: '产品/2/AR教育', slug: 'ar-education-pro', name: 'AR教育专业版', category: '教育科技' },
  { folder: '产品/2/ktv', slug: 'ktv-system-pro', name: 'KTV系统专业版', category: '娱乐社交' },
  { folder: '产品/2/一体机', slug: 'all-in-one-pro', name: '一体机专业版', category: '商业设备' },
  { folder: '产品/2/3d电子沙盘', slug: '3d-sandbox-pro', name: '3D沙盘专业版', category: '教育展示' },
  { folder: '产品/2/宴会厅', slug: 'banquet-hall-system', name: '宴会厅互动系统', category: '商业娱乐' },
  { folder: '产品/2/保龄球', slug: 'bowling-system-v2', name: '保龄球系统V2', category: '运动娱乐' },
  { folder: '产品/2/儿童互动沙滩', slug: 'children-beach-v2', name: '儿童沙滩V2', category: '儿童娱乐' },
  { folder: '产品/2/儿童互动砸球', slug: 'children-ball-v2', name: '儿童砸球V2', category: '儿童娱乐' },
  { folder: '产品/2/全息沙幕', slug: 'holographic-screen-v2', name: '全息沙幕V2', category: '全息科技' },
  { folder: '产品/2/全息沙桌', slug: 'holographic-table-v2', name: '全息沙桌V2', category: '全息科技' },
  { folder: '产品/2/全息舞台', slug: 'holographic-stage-v2', name: '全息舞台V2', category: '全息科技' },
  { folder: '产品/2/全息餐厅', slug: 'holographic-dining-v2', name: '全息餐厅V2', category: '全息科技' },
  
  // 产品/3/ 目录
  { folder: '产品/3/保龄球', slug: 'bowling-interactive-system', name: '保龄球互动系统', category: '运动娱乐' },
  { folder: '产品/3/儿童互动沙滩', slug: 'children-interactive-beach', name: '儿童互动沙滩', category: '儿童娱乐' },
  { folder: '产品/3/儿童互动砸球', slug: 'children-interactive-ball', name: '儿童互动砸球', category: '儿童娱乐' },
  { folder: '产品/3/全息沙幕', slug: 'holographic-screen-system', name: '全息沙幕系统', category: '全息科技' },
  { folder: '产品/3/全息沙桌', slug: 'holographic-table-system', name: '全息沙桌系统', category: '全息科技' },
  { folder: '产品/3/全息舞台', slug: 'holographic-stage-system', name: '全息舞台系统', category: '全息科技' },
  { folder: '产品/3/全息餐厅', slug: 'holographic-dining-system', name: '全息餐厅系统', category: '全息科技' }
];

// 复制单个图片文件
function copyImage(sourcePath, destPath) {
  try {
    if (fs.existsSync(sourcePath)) {
      const destDir = path.dirname(destPath);
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
      }
      
      fs.copyFileSync(sourcePath, destPath);
      console.log(`✅ 复制: ${path.basename(sourcePath)} -> ${path.basename(destPath)}`);
      return true;
    } else {
      console.log(`❌ 不存在: ${path.basename(sourcePath)}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ 失败: ${path.basename(sourcePath)} - ${error.message}`);
    return false;
  }
}

// 处理单个产品文件夹
function processProductFolder(productInfo) {
  const { folder, slug, name } = productInfo;
  console.log(`\n📁 ${name} (${slug})`);
  
  let imageCount = 0;
  let imagePaths = [];
  
  if (!fs.existsSync(folder)) {
    console.log(`❌ 文件夹不存在: ${folder}`);
    return { imageCount: 0, imagePaths: [] };
  }
  
  // 1. 复制主图片（未标题-1.jpg）
  const mainImagePath = path.join(folder, '未标题-1.jpg');
  const mainDestPath = path.join(targetDir, `${slug}-main.jpg`);
  if (copyImage(mainImagePath, mainDestPath)) {
    imagePaths.push(`/images/products/${slug}-main.jpg`);
    imageCount++;
  }
  
  // 2. 复制产品介绍模板图片
  const templateImages = [];
  for (let i = 1; i <= 10; i++) {
    const templatePath = path.join(folder, `产品介绍模板_${i.toString().padStart(2, '0')}.jpg`);
    if (fs.existsSync(templatePath)) {
      templateImages.push(templatePath);
    }
  }
  
  templateImages.forEach((templatePath, index) => {
    const destPath = path.join(targetDir, `${slug}-${index + 1}.jpg`);
    if (copyImage(templatePath, destPath)) {
      imagePaths.push(`/images/products/${slug}-${index + 1}.jpg`);
      imageCount++;
    }
  });
  
  // 3. 复制其他图片文件
  const allFiles = fs.readdirSync(folder);
  const otherImages = allFiles.filter(file => {
    const filePath = path.join(folder, file);
    return fs.statSync(filePath).isFile() && 
           /\.(jpg|jpeg|png|gif)$/i.test(file) &&
           file !== '未标题-1.jpg' &&
           !file.startsWith('产品介绍模板_');
  }).sort();
  
  otherImages.forEach((file, index) => {
    const sourcePath = path.join(folder, file);
    const fileExt = path.extname(file);
    const destPath = path.join(targetDir, `${slug}-extra-${index + 1}${fileExt}`);
    
    if (copyImage(sourcePath, destPath)) {
      imagePaths.push(`/images/products/${slug}-extra-${index + 1}${fileExt}`);
      imageCount++;
    }
  });
  
  console.log(`  ✨ 复制了 ${imageCount} 张图片`);
  return { imageCount, imagePaths };
}

// 生成产品描述
function generateDescription(name, category) {
  const descriptions = {
    '运动娱乐': `${name}采用先进的体感识别和投影技术，为用户提供沉浸式的运动娱乐体验。适合各年龄段用户，安全可靠，趣味无穷。`,
    '游戏娱乐': `${name}结合互动投影和体感技术，创造刺激有趣的游戏体验。支持多人同时游戏，增强互动乐趣。`,
    '教育科技': `${name}通过AR增强现实技术，为教育提供生动有趣的学习体验。支持多学科内容，提升教学效果。`,
    '娱乐社交': `${name}集成多种娱乐功能，提升用户体验。现代化界面，操作简便，适合社交娱乐场所。`,
    '商业设备': `${name}集成先进技术，适用于商业展示和教育场景。高性能，易维护，功能强大。`,
    '教育展示': `${name}提供沉浸式的展示和交互体验，适用于教育、规划等多种场景。`,
    '商业娱乐': `${name}为商业场所提供独特的娱乐体验，营造特色氛围，提升场所档次。`,
    '儿童娱乐': `${name}专为儿童设计，通过安全有趣的互动体验，促进儿童运动和智力发展。`,
    '全息科技': `${name}采用先进的全息投影技术，创造震撼的视觉效果，适用于各种展示和娱乐场景。`
  };
  
  return descriptions[category] || `${name}采用先进的互动投影技术，为用户提供优质的体验。`;
}

// 生成特性列表
function generateFeatures(category) {
  const featureMap = {
    '运动娱乐': ["体感识别", "投影互动", "多人游戏", "安全防护", "竞技模式"],
    '游戏娱乐': ["互动投影", "体感控制", "计分系统", "多人对战", "趣味挑战"],
    '教育科技': ["AR技术", "教育内容", "互动学习", "多媒体展示", "个性化教学"],
    '娱乐社交': ["智能系统", "社交功能", "音效优化", "氛围灯光", "用户友好"],
    '商业设备': ["多点触控", "高清显示", "一体化设计", "多媒体支持", "易于安装"],
    '教育展示': ["3D显示", "实时交互", "数据可视化", "教育应用", "专业展示"],
    '商业娱乐': ["氛围营造", "互动体验", "主题切换", "智能控制", "商业应用"],
    '儿童娱乐': ["儿童专用", "安全设计", "教育游戏", "成长记录", "亲子互动"],
    '全息科技': ["全息投影", "震撼视效", "高清显示", "远程控制", "多场景应用"]
  };
  
  return featureMap[category] || ["互动投影", "用户友好", "高质量", "多功能", "创新技术"];
}

async function createMockProducts() {
  console.log('🚀 开始创建模拟产品数据...\n');
  
  let totalImages = 0;
  let processedProducts = 0;
  let mockProducts = [];
  
  // 1. 复制所有产品图片并生成数据
  console.log('📷 复制产品图片并生成数据:');
  
  for (const productInfo of allProductDirectories) {
    const result = processProductFolder(productInfo);
    totalImages += result.imageCount;
    processedProducts++;
    
    // 生成模拟产品数据
    const mockProduct = {
      id: processedProducts,
      name: productInfo.name,
      slug: productInfo.slug,
      description: generateDescription(productInfo.name, productInfo.category),
      type: 'interactive_equipment',
      category: productInfo.category,
      style: '互动,娱乐',
      features: generateFeatures(productInfo.category),
      images: result.imagePaths,
      in_stock: true,
      is_featured: Math.random() > 0.7, // 30%概率为特色产品
      price: Math.floor(Math.random() * 50000) + 10000, // 10000-60000随机价格
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    mockProducts.push(mockProduct);
  }
  
  // 2. 保存模拟数据到JSON文件
  const mockDataPath = 'data/mock-products.json';
  const dataDir = path.dirname(mockDataPath);
  
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  fs.writeFileSync(mockDataPath, JSON.stringify(mockProducts, null, 2), 'utf8');
  
  console.log(`\n📊 处理完成！`);
  console.log(`   - 处理产品数: ${processedProducts}`);
  console.log(`   - 总图片数: ${totalImages}`);
  console.log(`   - 模拟数据文件: ${mockDataPath}`);
  
  // 3. 统计信息
  const categories = {};
  const featuredCount = mockProducts.filter(p => p.is_featured).length;
  
  mockProducts.forEach(product => {
    categories[product.category] = (categories[product.category] || 0) + 1;
  });
  
  console.log(`\n📈 产品统计:`);
  console.log(`   📦 总产品数: ${mockProducts.length}`);
  console.log(`   ⭐ 特色产品: ${featuredCount}`);
  console.log(`   📷 总图片数: ${totalImages}`);
  
  console.log(`\n🏷️  按类别分组:`);
  Object.entries(categories).forEach(([category, count]) => {
    console.log(`   ${category}: ${count} 个产品`);
  });
  
  console.log(`\n📋 前5个产品预览:`);
  mockProducts.slice(0, 5).forEach(product => {
    const featured = product.is_featured ? '⭐' : '  ';
    console.log(`   ${featured} ${product.name} (${product.category}) - ${product.images.length}张图片`);
  });
  
  console.log('\n🎉 模拟产品数据创建完成!');
  console.log(`💡 使用方法: 在代码中导入 ${mockDataPath} 文件`);
}

createMockProducts();
