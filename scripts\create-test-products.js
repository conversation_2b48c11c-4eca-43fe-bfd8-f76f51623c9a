/**
 * 创建测试产品数据
 */
const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

// 数据库连接配置
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

const testProducts = [
  {
    title: '智能攀岩墙',
    slug: 'smart-climbing-wall',
    description: '配备LED灯光和传感器的智能攀岩墙，提供多种挑战模式',
    type: 'climbing',
    size: 'large',
    style: 'sport',
    features: ['LED指示', '难度调节', '计时功能', '安全保护'],
    images: ['/images/products/climbing-1.jpg'],
    is_published: true
  },
  {
    title: '虚拟现实体验舱',
    slug: 'vr-experience-pod',
    description: '沉浸式VR体验设备，支持多种虚拟现实游戏和教育内容',
    type: 'vr',
    size: 'medium',
    style: 'high-tech',
    features: ['VR头显', '体感控制', '多种内容', '舒适座椅'],
    images: ['/images/products/vr-1.jpg'],
    is_published: false
  }
];

async function createTestProducts() {
  const client = await pool.connect();

  try {
    console.log('开始创建测试产品...');

    // 清空现有产品（可选）
    // await client.query('DELETE FROM products');
    // console.log('已清空现有产品');

    for (const product of testProducts) {
      try {
        // 检查产品是否已存在
        const existing = await client.query('SELECT id FROM products WHERE slug = $1', [product.slug]);

        if (existing.rows.length > 0) {
          console.log(`产品 "${product.title}" 已存在，跳过创建`);
          continue;
        }

        // 插入产品
        const result = await client.query(
          `INSERT INTO products (name, slug, description, type, size, style, features, images, in_stock, price, created_at, updated_at)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
           RETURNING id`,
          [
            product.title,
            product.slug,
            product.description,
            product.type,
            product.size,
            product.style,
            JSON.stringify(product.features),
            JSON.stringify(product.images),
            product.is_published,
            0 // 默认价格
          ]
        );

        console.log(`✅ 创建产品: ${product.title} (ID: ${result.rows[0].id})`);
      } catch (error) {
        console.error(`❌ 创建产品 "${product.title}" 失败:`, error.message);
      }
    }

    // 检查创建结果
    const count = await client.query('SELECT COUNT(*) as count FROM products');
    console.log(`\n数据库中现有产品总数: ${count.rows[0].count}`);

  } catch (error) {
    console.error('❌ 创建测试产品时出错:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// 执行脚本
if (require.main === module) {
  createTestProducts()
    .then(() => {
      console.log('\n🎉 测试产品创建完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { createTestProducts };
