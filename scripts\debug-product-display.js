/**
 * 调试产品显示问题
 */
const fs = require('fs');

console.log('🔍 调试产品显示问题...\n');

// 1. 检查 ProductGrid 组件
console.log('📄 检查 ProductGrid 组件:');
const productGridFile = 'app/components/ProductGrid.tsx';

if (fs.existsSync(productGridFile)) {
  const content = fs.readFileSync(productGridFile, 'utf8');
  
  if (content.includes('published=true')) {
    console.log('   ✅ ProductGrid 正确请求已发布产品');
  } else {
    console.log('   ❌ ProductGrid 未请求已发布产品');
  }
  
  if (content.includes('products.length === 0')) {
    console.log('   ✅ ProductGrid 包含空状态处理');
  } else {
    console.log('   ❌ ProductGrid 缺少空状态处理');
  }
  
  if (content.includes('ModernProductCard')) {
    console.log('   ✅ ProductGrid 使用 ModernProductCard');
  } else {
    console.log('   ❌ ProductGrid 未使用 ModernProductCard');
  }
} else {
  console.log('   ❌ ProductGrid 文件不存在');
}

// 2. 检查产品页面
console.log('\n📄 检查产品页面:');
const productPageFile = 'app/[lang]/products/page.tsx';

if (fs.existsSync(productPageFile)) {
  const content = fs.readFileSync(productPageFile, 'utf8');
  
  if (content.includes('ProductGrid')) {
    console.log('   ✅ 产品页面使用 ProductGrid');
  } else {
    console.log('   ❌ 产品页面未使用 ProductGrid');
  }
  
  if (content.includes('全部产品') || content.includes('All Products')) {
    console.log('   ✅ 产品页面包含标题');
  } else {
    console.log('   ❌ 产品页面缺少标题');
  }
} else {
  console.log('   ❌ 产品页面文件不存在');
}

// 3. 检查 API 路由
console.log('\n📄 检查 API 路由:');
const apiFile = 'app/api/products/route.ts';

if (fs.existsSync(apiFile)) {
  const content = fs.readFileSync(apiFile, 'utf8');
  
  if (content.includes('published !== \'\'')) {
    console.log('   ✅ API 支持发布状态过滤');
  } else {
    console.log('   ❌ API 不支持发布状态过滤');
  }
  
  if (content.includes('is_published = $')) {
    console.log('   ✅ API 包含发布状态查询');
  } else {
    console.log('   ❌ API 缺少发布状态查询');
  }
} else {
  console.log('   ❌ API 文件不存在');
}

// 4. 检查样式文件
console.log('\n📄 检查样式文件:');
const forceStyleFile = 'app/styles/force-modern-product-style.css';

if (fs.existsSync(forceStyleFile)) {
  const content = fs.readFileSync(forceStyleFile, 'utf8');
  
  if (content.includes('display: none !important')) {
    console.log('   ✅ 强制样式隐藏旧组件');
  } else {
    console.log('   ❌ 强制样式未隐藏旧组件');
  }
} else {
  console.log('   ❌ 强制样式文件不存在');
}

// 5. 检查全局样式
console.log('\n📄 检查全局样式:');
const globalStyleFile = 'app/styles/globals.css';

if (fs.existsSync(globalStyleFile)) {
  const content = fs.readFileSync(globalStyleFile, 'utf8');
  
  if (content.includes("@import './force-modern-product-style.css'")) {
    console.log('   ✅ 全局样式导入强制样式');
  } else {
    console.log('   ❌ 全局样式未导入强制样式');
  }
} else {
  console.log('   ❌ 全局样式文件不存在');
}

console.log('\n🎯 调试总结:');
console.log('1. 确保 ProductGrid 请求 published=true');
console.log('2. 确保产品页面使用 ProductGrid 组件');
console.log('3. 确保 API 正确过滤已发布产品');
console.log('4. 确保强制样式隐藏旧组件');
console.log('5. 清除浏览器缓存并刷新页面');

console.log('\n✅ 调试完成！');

// 6. 生成测试 URL
console.log('\n🔗 测试 URL:');
console.log('产品页面: http://localhost:3000/zh/products');
console.log('API 测试: http://localhost:3000/api/products?published=true');
console.log('重定向测试: http://localhost:3000/products');
