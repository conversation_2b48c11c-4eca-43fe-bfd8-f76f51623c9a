const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function deleteAllProductImages() {
  console.log('🗑️  开始删除数据库中所有产品图片数据...');
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    // 1. 首先显示当前产品的图片情况
    console.log('\n📊 删除前的图片统计:');
    const beforeResult = await client.query(`
      SELECT id, name, slug, images 
      FROM products 
      WHERE images IS NOT NULL AND images != ''
      ORDER BY id
    `);
    
    let totalImagesBefore = 0;
    beforeResult.rows.forEach(product => {
      try {
        const images = JSON.parse(product.images);
        console.log(`   ${product.id}. ${product.name}: ${images.length} 张图片`);
        totalImagesBefore += images.length;
      } catch (e) {
        console.log(`   ${product.id}. ${product.name}: 图片数据解析错误`);
      }
    });
    
    console.log(`\n📷 删除前总图片数: ${totalImagesBefore}`);
    console.log(`📦 有图片的产品数: ${beforeResult.rows.length}`);
    
    // 2. 清空所有产品的图片数据
    console.log('\n🗑️  清空所有产品的图片数据...');
    const updateResult = await client.query(`
      UPDATE products 
      SET images = NULL, 
          detail_images = NULL,
          image_url = NULL
      WHERE images IS NOT NULL OR detail_images IS NOT NULL OR image_url IS NOT NULL
    `);
    
    console.log(`✅ 已清空 ${updateResult.rowCount} 个产品的图片数据`);
    
    // 3. 验证删除结果
    console.log('\n📊 删除后验证:');
    const afterResult = await client.query(`
      SELECT COUNT(*) as total_products,
             COUNT(CASE WHEN images IS NOT NULL AND images != '' THEN 1 END) as products_with_images,
             COUNT(CASE WHEN detail_images IS NOT NULL AND detail_images != '' THEN 1 END) as products_with_detail_images,
             COUNT(CASE WHEN image_url IS NOT NULL AND image_url != '' THEN 1 END) as products_with_image_url
      FROM products
    `);
    
    const stats = afterResult.rows[0];
    console.log(`   📦 总产品数: ${stats.total_products}`);
    console.log(`   📷 有images的产品: ${stats.products_with_images}`);
    console.log(`   📷 有detail_images的产品: ${stats.products_with_detail_images}`);
    console.log(`   📷 有image_url的产品: ${stats.products_with_image_url}`);
    
    // 4. 显示所有产品的当前状态
    console.log('\n📋 所有产品当前状态:');
    const allProducts = await client.query(`
      SELECT id, name, slug, 
             CASE WHEN images IS NULL OR images = '' THEN '无' ELSE '有' END as has_images,
             CASE WHEN detail_images IS NULL OR detail_images = '' THEN '无' ELSE '有' END as has_detail_images,
             CASE WHEN image_url IS NULL OR image_url = '' THEN '无' ELSE '有' END as has_image_url
      FROM products 
      ORDER BY id
    `);
    
    allProducts.rows.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.name}`);
      console.log(`      Slug: ${product.slug}`);
      console.log(`      Images: ${product.has_images} | Detail Images: ${product.has_detail_images} | Image URL: ${product.has_image_url}`);
    });
    
    console.log(`\n📈 删除统计:`);
    console.log(`   🗑️  删除前图片总数: ${totalImagesBefore}`);
    console.log(`   🗑️  清空的产品数: ${updateResult.rowCount}`);
    console.log(`   ✅ 删除后图片总数: 0`);
    
    client.release();
    console.log('\n🎉 数据库图片数据删除完成!');
    console.log('\n💡 注意: 这只是删除了数据库中的图片引用，物理图片文件仍在服务器上');
    
  } catch (error) {
    console.error('❌ 删除失败:', error.message);
  } finally {
    await pool.end();
  }
}

deleteAllProductImages();
