require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

// 获取数据库连接参数
const connectionString =
  process.env.DATABASE_URL ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 创建数据库连接池
const pool = new Pool({ connectionString });

async function deleteAllCategories() {
  try {
    // 1. 获取所有分类信息
    console.log('正在查询所有分类信息...');
    const categoriesResult = await pool.query(`SELECT id, name, slug FROM categories`);

    if (categoriesResult.rows.length === 0) {
      console.log('数据库中没有任何分类');
      return;
    }

    console.log(`找到 ${categoriesResult.rows.length} 个分类:`);
    console.log(categoriesResult.rows);

    // 2. 查询所有产品
    console.log('\n正在查询所有产品...');
    const productsResult = await pool.query(`SELECT id, name, slug, category FROM products`);

    if (productsResult.rows.length > 0) {
      console.log(`找到 ${productsResult.rows.length} 个产品:`);
      console.log(productsResult.rows);

      // 3. 删除所有产品
      console.log('\n正在删除所有产品...');
      await pool.query(`DELETE FROM products`);
      console.log('已成功删除所有产品');
    } else {
      console.log('数据库中没有任何产品');
    }

    // 4. 删除所有分类
    console.log('\n正在删除所有分类...');
    await pool.query(`DELETE FROM categories`);
    console.log('已成功删除所有分类');

    console.log('\n数据库清理完成！所有分类和产品已被删除。');
  } catch (error) {
    console.error('错误:', error);
  } finally {
    await pool.end();
    process.exit(0);
  }
}

deleteAllCategories();
