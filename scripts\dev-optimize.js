#!/usr/bin/env node

/**
 * 开发环境优化脚本
 * 用于减少 Fast Refresh 警告和提升开发体验
 */

const fs = require('fs');
const path = require('path');

// 优化配置
const optimizations = {
  // 减少文件监听
  reduceFileWatching: true,
  // 优化 CSS 处理
  optimizeCSS: true,
  // 缓存优化
  enableCaching: true,
};

// 创建开发环境配置文件
function createDevConfig() {
  const devConfigPath = path.join(process.cwd(), '.env.development.local');
  
  const devConfig = `
# 开发环境优化配置
NEXT_TELEMETRY_DISABLED=1
FAST_REFRESH_TIMEOUT=1000
WEBPACK_WATCH_POLL=1000
WEBPACK_WATCH_AGGREGATE_TIMEOUT=300

# 减少日志输出
NEXT_PRIVATE_DEBUG_CACHE=false
NEXT_PRIVATE_DEBUG_WEBPACK=false

# 优化构建性能
NEXT_PRIVATE_STANDALONE=false
NEXT_PRIVATE_OPTIMIZE_CSS=true
`;

  if (!fs.existsSync(devConfigPath)) {
    fs.writeFileSync(devConfigPath, devConfig.trim());
    console.log('✅ 创建开发环境配置文件');
  }
}

// 创建 .gitignore 优化
function optimizeGitignore() {
  const gitignorePath = path.join(process.cwd(), '.gitignore');
  
  const additionalIgnores = `
# 开发环境优化
.env.development.local
*.log
.DS_Store
Thumbs.db

# IDE 文件
.vscode/
.idea/
*.swp
*.swo

# 临时文件
*.tmp
*.temp
.cache/
`;

  if (fs.existsSync(gitignorePath)) {
    const currentContent = fs.readFileSync(gitignorePath, 'utf8');
    if (!currentContent.includes('# 开发环境优化')) {
      fs.appendFileSync(gitignorePath, additionalIgnores);
      console.log('✅ 优化 .gitignore 文件');
    }
  }
}

// 创建 VSCode 配置
function createVSCodeConfig() {
  const vscodeDir = path.join(process.cwd(), '.vscode');
  const settingsPath = path.join(vscodeDir, 'settings.json');
  
  if (!fs.existsSync(vscodeDir)) {
    fs.mkdirSync(vscodeDir);
  }

  const vscodeSettings = {
    "typescript.preferences.includePackageJsonAutoImports": "off",
    "typescript.suggest.autoImports": false,
    "files.watcherExclude": {
      "**/.git/objects/**": true,
      "**/.git/subtree-cache/**": true,
      "**/node_modules/**": true,
      "**/.next/**": true,
      "**/dist/**": true,
      "**/.cache/**": true
    },
    "search.exclude": {
      "**/node_modules": true,
      "**/.next": true,
      "**/dist": true,
      "**/.cache": true
    },
    "files.exclude": {
      "**/.git": true,
      "**/.DS_Store": true,
      "**/Thumbs.db": true
    },
    "emmet.includeLanguages": {
      "javascript": "javascriptreact",
      "typescript": "typescriptreact"
    }
  };

  if (!fs.existsSync(settingsPath)) {
    fs.writeFileSync(settingsPath, JSON.stringify(vscodeSettings, null, 2));
    console.log('✅ 创建 VSCode 配置文件');
  }
}

// 主函数
function main() {
  console.log('🚀 开始优化开发环境...');
  
  try {
    createDevConfig();
    optimizeGitignore();
    createVSCodeConfig();
    
    console.log('\n✅ 开发环境优化完成！');
    console.log('\n📝 优化说明：');
    console.log('- 减少了文件监听的敏感度');
    console.log('- 优化了 Fast Refresh 配置');
    console.log('- 添加了开发环境变量');
    console.log('- 配置了 VSCode 设置');
    console.log('\n🔄 请重启开发服务器以应用更改');
    
  } catch (error) {
    console.error('❌ 优化过程中出现错误:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  createDevConfig,
  optimizeGitignore,
  createVSCodeConfig,
  main,
};
