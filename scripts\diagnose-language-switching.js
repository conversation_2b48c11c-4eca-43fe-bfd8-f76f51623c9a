// 语言切换功能深度诊断
const fs = require('fs');
const path = require('path');

console.log('🔍 语言切换功能深度诊断\n');

// 收集问题
const issues = [];
const warnings = [];
const successes = [];

// 1. 检查LanguageProvider
console.log('📌 1. 检查LanguageProvider组件...');
const langProviderPath = 'app/components/LanguageProvider.tsx';
if (fs.existsSync(langProviderPath)) {
  const content = fs.readFileSync(langProviderPath, 'utf8');
  
  // 检查关键功能
  if (content.includes('changeLanguage')) {
    successes.push('✅ LanguageProvider 包含 changeLanguage 函数');
  }
  
  if (content.includes('localStorage.setItem(\'NEXT_LOCALE\'')) {
    successes.push('✅ 使用 localStorage 保存语言偏好');
  }
  
  if (content.includes('router.push(`/${newLocale}')) {
    successes.push('✅ 使用路由导航切换语言');
  }
  
  if (content.includes('getCurrentLocale')) {
    successes.push('✅ 从URL路径获取当前语言');
  }
} else {
  issues.push('❌ LanguageProvider.tsx 不存在');
}

// 2. 检查中间件
console.log('\n📌 2. 检查middleware.ts...');
const middlewarePath = 'middleware.ts';
if (fs.existsSync(middlewarePath)) {
  const content = fs.readFileSync(middlewarePath, 'utf8');
  
  // 检查语言检测逻辑
  if (content.includes('NEXT_LOCALE')) {
    if (content.includes('cookies.get(\'NEXT_LOCALE\')')) {
      warnings.push('⚠️ 中间件使用cookie而非localStorage');
    }
  }
  
  if (content.includes('locales = [\'en\', \'zh\']')) {
    successes.push('✅ 中间件支持 en 和 zh 语言');
  }
  
  if (!content.includes('localStorage')) {
    warnings.push('⚠️ 中间件不检查localStorage中的语言偏好');
  }
}

// 3. 检查字典文件
console.log('\n📌 3. 检查字典文件...');
const dictionaries = {
  zh: 'app/dictionaries/zh.json',
  en: 'app/dictionaries/en.json'
};

const dictContent = {};
for (const [lang, dictPath] of Object.entries(dictionaries)) {
  if (fs.existsSync(dictPath)) {
    try {
      dictContent[lang] = JSON.parse(fs.readFileSync(dictPath, 'utf8'));
      successes.push(`✅ ${lang}.json 字典文件存在且有效`);
    } catch (error) {
      issues.push(`❌ ${lang}.json 解析失败: ${error.message}`);
    }
  } else {
    issues.push(`❌ ${dictPath} 不存在`);
  }
}

// 4. 比较字典键
if (dictContent.zh && dictContent.en) {
  console.log('\n📌 4. 比较字典键...');
  
  const getKeys = (obj, prefix = '') => {
    let keys = [];
    for (const key in obj) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        keys = keys.concat(getKeys(obj[key], fullKey));
      } else {
        keys.push(fullKey);
      }
    }
    return keys;
  };
  
  const zhKeys = getKeys(dictContent.zh);
  const enKeys = getKeys(dictContent.en);
  
  const missingInEn = zhKeys.filter(key => !enKeys.includes(key));
  const missingInZh = enKeys.filter(key => !zhKeys.includes(key));
  
  if (missingInEn.length > 0) {
    issues.push(`❌ 英文字典缺少 ${missingInEn.length} 个键`);
    console.log('  缺失的键:', missingInEn.slice(0, 5));
  }
  
  if (missingInZh.length > 0) {
    issues.push(`❌ 中文字典缺少 ${missingInZh.length} 个键`);
    console.log('  缺失的键:', missingInZh.slice(0, 5));
  }
  
  if (missingInEn.length === 0 && missingInZh.length === 0) {
    successes.push('✅ 字典键完全匹配');
  }
}

// 5. 检查特定页面
console.log('\n📌 5. 检查重要页面的语言支持...');
const pagesToCheck = [
  { path: 'app/[lang]/page.tsx', name: '首页' },
  { path: 'app/[lang]/products/page.tsx', name: '产品页' },
  { path: 'app/[lang]/pages/about-us/page.tsx', name: '关于我们' },
  { path: 'app/[lang]/pages/contact-us/page.tsx', name: '联系我们' }
];

pagesToCheck.forEach(({ path: pagePath, name }) => {
  if (fs.existsSync(pagePath)) {
    const content = fs.readFileSync(pagePath, 'utf8');
    
    // 检查页面是否使用了国际化
    if (content.includes('getDictionary') || content.includes('useLanguage')) {
      successes.push(`✅ ${name} 使用了国际化`);
    } else {
      warnings.push(`⚠️ ${name} 可能未使用国际化`);
    }
    
    // 检查硬编码中文
    const chinesePattern = /['"`]([^'"`]*[\u4e00-\u9fa5]+[^'"`]*)['"`]/g;
    const matches = content.match(chinesePattern);
    if (matches && matches.length > 0) {
      warnings.push(`⚠️ ${name} 包含 ${matches.length} 处硬编码中文`);
    }
  } else {
    warnings.push(`⚠️ ${name} (${pagePath}) 不存在`);
  }
});

// 6. 检查组件中的语言切换器
console.log('\n📌 6. 检查Header中的语言切换器...');
const headerPath = 'app/components/Header.tsx';
if (fs.existsSync(headerPath)) {
  const content = fs.readFileSync(headerPath, 'utf8');
  
  if (content.includes('changeLanguage')) {
    successes.push('✅ Header 包含语言切换功能');
  }
  
  if (content.includes('language-selector')) {
    successes.push('✅ Header 包含语言选择器UI');
  }
  
  // 检查硬编码的语言选项
  if (content.includes("lang === 'en' ? 'English' : lang === 'zh' ? '中文'")) {
    successes.push('✅ 语言选项正确显示');
  }
}

// 7. 潜在问题诊断
console.log('\n📌 7. 潜在问题诊断...');

// Cookie vs localStorage 不一致
if (warnings.some(w => w.includes('cookie而非localStorage'))) {
  issues.push('❌ 中间件和LanguageProvider使用不同的存储机制');
  console.log('\n⚠️ 问题详情:');
  console.log('  - LanguageProvider 使用 localStorage');
  console.log('  - Middleware 使用 cookie');
  console.log('  - 这可能导致语言切换不一致');
}

// 生成报告
console.log('\n' + '='.repeat(60));
console.log('📊 语言切换功能诊断报告\n');

console.log(`✅ 成功项: ${successes.length}`);
console.log(`⚠️ 警告项: ${warnings.length}`);
console.log(`❌ 问题项: ${issues.length}`);

if (issues.length > 0) {
  console.log('\n❌ 发现的问题:');
  issues.forEach(issue => console.log(`  ${issue}`));
}

if (warnings.length > 0) {
  console.log('\n⚠️ 警告:');
  warnings.forEach(warning => console.log(`  ${warning}`));
}

console.log('\n💡 修复建议:');
console.log('1. 统一中间件和LanguageProvider的语言存储机制');
console.log('2. 中间件应该同时检查cookie和localStorage');
console.log('3. 确保所有页面都正确使用getDictionary或useLanguage');
console.log('4. 将硬编码文本移至字典文件');
console.log('5. 补充缺失的字典键');

console.log('\n🔧 建议的修复代码:');
console.log(`
// middleware.ts 修改建议
function getLocale(request: NextRequest) {
  // 1. 先检查URL中的语言
  const pathname = request.nextUrl.pathname;
  const pathnameLocale = locales.find(
    locale => pathname.startsWith(\`/\${locale}/\`) || pathname === \`/\${locale}\`
  );
  if (pathnameLocale) return pathnameLocale;
  
  // 2. 检查cookie
  const cookieLocale = request.cookies.get('NEXT_LOCALE')?.value;
  if (cookieLocale && locales.includes(cookieLocale)) {
    return cookieLocale;
  }
  
  // 3. 检查Accept-Language header
  // ...existing code...
  
  return defaultLocale;
}
`);

console.log('\n' + '='.repeat(60)); 