// 补充最后79个缺失的英文翻译
const fs = require('fs');

console.log('🌐 开始补充最后的英文翻译...\n');

// 函数工具
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => {
    if (!current[key]) current[key] = {};
    return current[key];
  }, obj);
  target[lastKey] = value;
}

try {
  // 读取文件
  const zhDict = JSON.parse(fs.readFileSync('app/dictionaries/zh.json', 'utf8'));
  const enDict = JSON.parse(fs.readFileSync('app/dictionaries/en.json', 'utf8'));
  const remainingKeys = JSON.parse(fs.readFileSync('scripts/remaining-translations-detailed.json', 'utf8'));
  
  console.log(`📊 发现 ${remainingKeys.length} 个剩余的英文翻译键`);
  
  // 最后的翻译映射
  const finalTranslations = {
    // Custom Solutions页面
    'custom_solutions.image_alt': 'Custom playground solutions',
    'custom_solutions.intro.title': 'Tailored Playground Solutions for Your Unique Needs',
    'custom_solutions.intro.paragraph1': 'At Guangzhou Junsheng Technology Co., Ltd., we understand that every playground project is unique. Therefore, we provide solutions tailored to your specific requirements, space constraints, and budget.',
    'custom_solutions.intro.paragraph2': 'Whether you\'re looking for a completely custom-designed playground or need guidance on purchasing your first indoor playground, our team of experts is here to help you every step of the way.',
    'custom_solutions.list.title': 'Our Custom Solutions',
    'custom_solutions.design.image_alt': 'Custom playground design',
    'custom_solutions.design.description': 'Turn your vision into reality with our custom playground design services. Our talented designers will work closely with you to create a unique play space that perfectly meets your needs.',
    'custom_solutions.design.feature1': 'Tailored design according to your requirements',
    'custom_solutions.design.feature2': '3D visualization of your playground',
    'custom_solutions.design.feature3': 'Themed playground options',
    'custom_solutions.design.feature4': 'Space optimization solutions',
    'custom_solutions.purchase.image_alt': 'How to purchase your first indoor playground',
    'custom_solutions.purchase.description': 'New to the indoor playground business? Our comprehensive guide will walk you through every aspect of purchasing your first indoor playground, ensuring your business success.',
    'custom_solutions.purchase.feature1': 'Step-by-step purchasing process',
    'custom_solutions.purchase.feature2': 'Budget and financial planning',
    'custom_solutions.purchase.feature3': 'Location and space planning tips',
    'custom_solutions.purchase.feature4': 'Equipment selection guidance',
    'custom_solutions.process.title': 'Our Custom Design Process',
    'custom_solutions.process.step1.title': 'Initial Consultation',
    'custom_solutions.process.step1.description': 'We start by understanding your vision, requirements, space constraints, and budget.',
    'custom_solutions.process.step2.title': 'Concept Development',
    'custom_solutions.process.step2.description': 'Our design team creates initial concepts based on your input and requirements.',
    'custom_solutions.process.step3.title': 'Design Refinement',
    'custom_solutions.process.step3.description': 'We refine the selected concept based on your feedback and preferences.',
    'custom_solutions.process.step4.title': 'Final Design & Implementation',
    'custom_solutions.process.step4.description': 'Once approved, we finalize the design and begin the manufacturing process.',
    'custom_solutions.cta.title': 'Ready to Discuss Your Custom Solution?',
    'custom_solutions.cta.description': 'Contact our team today to explore how we can create the perfect playground solution for your needs.',
    
    // Purchase Guide页面
    'purchase_guide.description': 'A comprehensive guide to purchasing your first indoor playground.',
    'purchase_guide.intro.image_alt': 'Indoor playground purchase guide',
    'purchase_guide.intro.title': 'Your Roadmap to a Successful Indoor Playground',
    'purchase_guide.intro.paragraph1': 'Purchasing your first indoor playground is an exciting venture, but it also requires careful planning and consideration. This comprehensive guide will walk you through the entire process from initial concept to grand opening, ensuring your indoor playground business gets off to a successful start.',
    'purchase_guide.intro.paragraph2': 'At Guangzhou Junsheng Technology Co., Ltd., we have helped hundreds of clients establish successful indoor playground businesses, and we\'re sharing our expertise to make your journey smoother.',
    'purchase_guide.steps.title': 'Step-by-Step Purchase Guide',
    'purchase_guide.steps.pro_tip': 'Pro Tip:',
    'purchase_guide.steps.step1.title': 'Market Research & Business Planning',
    'purchase_guide.steps.step1.item1': 'Analyze your local market to identify demand and competition',
    'purchase_guide.steps.step1.item2': 'Define your target audience (age groups, demographics)',
    'purchase_guide.steps.step1.item3': 'Develop a business plan including financial projections',
    'purchase_guide.steps.step1.item4': 'Determine your unique selling proposition',
    'purchase_guide.steps.step1.item5': 'Research local regulations and permit requirements',
    'purchase_guide.steps.step1.tip': 'Focus on creating a unique experience that differentiates your playground from existing local options.',
    'purchase_guide.steps.step1.image_alt': 'Business planning',
    'purchase_guide.steps.step2.title': 'Location & Space Planning',
    'purchase_guide.steps.step2.item1': 'Choose a strategic location with good visibility and accessibility',
    'purchase_guide.steps.step2.item2': 'Ensure adequate space for play equipment, seating, party rooms, etc.',
    'purchase_guide.steps.step2.item3': 'Consider ceiling height requirements (minimum 4-5 meters for most structures)',
    'purchase_guide.steps.step2.item4': 'Plan for support areas: restrooms, kitchen/cafe, storage, office',
    'purchase_guide.steps.step2.item5': 'Verify zoning restrictions and building codes',
    'purchase_guide.steps.step2.tip': 'The ideal location balances affordable rent with high foot traffic and family accessibility.',
    'purchase_guide.steps.step2.image_alt': 'Location planning',
    'purchase_guide.steps.step3.title': 'Budget & Financing',
    'purchase_guide.steps.step3.item1': 'Establish a comprehensive budget covering all aspects of your business',
    'purchase_guide.steps.step3.item2': 'Plan for playground equipment (typically 30-40% of total investment)',
    'purchase_guide.steps.step3.item3': 'Include costs for renovation, flooring, HVAC, electrical, etc.',
    'purchase_guide.steps.step3.item4': 'Budget for pre-opening expenses, marketing, staff training',
    'purchase_guide.steps.step3.item5': 'Explore financing options: loans, investors, equipment financing',
    'purchase_guide.steps.step3.tip': 'Always include at least 15-20% contingency funds for unexpected expenses.',
    'purchase_guide.steps.step3.image_alt': 'Budget and financing',
    'purchase_guide.steps.step4.title': 'Equipment Selection & Design',
    'purchase_guide.steps.step4.item1': 'Work with reputable suppliers like Guangzhou Junsheng Technology Co., Ltd.',
    'purchase_guide.steps.step4.item2': 'Choose equipment appropriate for your target age groups',
    'purchase_guide.steps.step4.item3': 'Consider space efficiency and traffic flow',
    'purchase_guide.steps.step4.item4': 'Select themes that appeal to your market',
    'purchase_guide.steps.step4.item5': 'Ensure all equipment meets safety standards',
    'purchase_guide.steps.step4.tip': 'Invest in quality equipment from the start – it reduces maintenance costs and provides better customer experience.',
    'purchase_guide.steps.step4.image_alt': 'Equipment selection',
    'purchase_guide.considerations.title': 'Key Considerations for Success',
    'purchase_guide.considerations.safety.title': 'Safety First',
    'purchase_guide.considerations.safety.description': 'Prioritize safety in every aspect of your playground business, from equipment selection to daily operations. This is non-negotiable.',
    'purchase_guide.considerations.revenue.title': 'Revenue Streams',
    'purchase_guide.considerations.revenue.description': 'Develop multiple revenue sources beyond admission fees: birthday parties, food service, merchandise, membership programs, etc.',
    'purchase_guide.considerations.staff.title': 'Staff Quality',
    'purchase_guide.considerations.staff.description': 'Hire staff who love working with children and provide comprehensive training in operations and customer service.',
    'purchase_guide.considerations.marketing.title': 'Marketing Consistency',
    'purchase_guide.considerations.marketing.description': 'Implement consistent marketing efforts with special focus on digital channels where parents search for activities.',
    'purchase_guide.cta.title': 'Ready to Start Your Indoor Playground Journey?',
    'purchase_guide.cta.description': 'Contact our team for a free consultation and learn how we can help you create a successful indoor playground business.',
    'purchase_guide.cta.button': 'Schedule Consultation',
    
    // Service页面 (其他缺失翻译)
    'service.intro.paragraph1': 'At Guangzhou Junsheng Technology, we believe that exceptional service is just as important as quality equipment.',
    'service.intro.paragraph2': 'Our comprehensive service offerings ensure your indoor playground operates smoothly and profitably for years to come.',
    'service.consultation.title': 'Consultation Services',
    'service.consultation.description': 'Expert guidance from concept to completion',
    'service.consultation.point1': 'Site evaluation and space planning',
    'service.consultation.point2': 'Business model development',
    'service.consultation.point3': 'ROI projections and financial planning',
    'service.consultation.point4': 'Regulatory compliance assistance',
    'service.design.title': 'Design Services',
    'service.design.description': 'Bringing your vision to life with professional design',
    'service.design.point1': '3D visualization and rendering',
    'service.design.point2': 'Custom theming and branding',
    'service.design.point3': 'Safety-optimized layouts',
    'service.design.point4': 'Traffic flow planning',
    'service.installation.title': 'Installation Services',
    'service.installation.description': 'Professional installation by certified technicians',
    'service.installation.point1': 'Complete turnkey installation',
    'service.installation.point2': 'Safety inspections and certification',
    'service.installation.point3': 'Staff training on equipment',
    'service.installation.point4': 'Grand opening support',
    'service.maintenance.title': 'Maintenance Services',
    'service.maintenance.description': 'Keep your playground in perfect condition',
    'service.maintenance.point1': 'Regular inspection programs',
    'service.maintenance.point2': 'Preventive maintenance plans',
    'service.maintenance.point3': 'Emergency repair services',
    'service.maintenance.point4': 'Parts replacement programs',
    'service.cta.title': 'Experience Our Full-Service Approach',
    'service.cta.description': 'Let us handle every aspect of your indoor playground project.',
    'service.cta.button': 'Explore Our Services',
    
    // Quality Control页面
    'quality_control.intro.paragraph1': 'Quality is at the heart of everything we do at Guangzhou Junsheng Technology.',
    'quality_control.intro.paragraph2': 'Our rigorous quality control processes ensure that every piece of equipment meets the highest standards of safety, durability, and play value.',
    'quality_control.process.title': 'Our Quality Control Process',
    'quality_control.materials.title': 'Material Selection',
    'quality_control.materials.description': 'Premium materials that exceed industry standards',
    'quality_control.materials.point1': 'Non-toxic, child-safe materials only',
    'quality_control.materials.point2': 'UV-resistant plastics for color retention',
    'quality_control.materials.point3': 'Heavy-duty steel framework',
    'quality_control.materials.point4': 'Premium foam and padding materials',
    'quality_control.manufacturing.title': 'Manufacturing Excellence',
    'quality_control.manufacturing.description': 'Precision manufacturing with attention to detail',
    'quality_control.manufacturing.point1': 'Computer-aided design and cutting',
    'quality_control.manufacturing.point2': 'Skilled craftsman assembly',
    'quality_control.manufacturing.point3': 'Multi-point quality inspections',
    'quality_control.manufacturing.point4': 'Load and stress testing',
    'quality_control.testing.title': 'Testing & Certification',
    'quality_control.testing.description': 'Comprehensive testing ensures safety and durability',
    'quality_control.testing.point1': 'Impact and load testing',
    'quality_control.testing.point2': 'Sharp edge and entrapment checks',
    'quality_control.testing.point3': 'Chemical safety testing',
    'quality_control.testing.point4': 'Third-party certification',
    'quality_control.ongoing.title': 'Ongoing Quality Assurance',
    'quality_control.ongoing.description': 'Our commitment continues after installation',
    'quality_control.ongoing.point1': 'Regular quality audits',
    'quality_control.ongoing.point2': 'Customer feedback integration',
    'quality_control.ongoing.point3': 'Continuous improvement programs',
    'quality_control.ongoing.point4': 'Long-term warranty support',
    'quality_control.cta.title': 'Quality You Can Trust',
    'quality_control.cta.description': 'Learn more about our commitment to excellence.',
    'quality_control.cta.button': 'View Quality Standards'
  };
  
  // 应用翻译
  let addedCount = 0;

  remainingKeys.forEach(item => {
    if (finalTranslations[item.key]) {
      setNestedValue(enDict, item.key, finalTranslations[item.key]);
      addedCount++;
    } else {
      console.log(`⚠️ 未找到翻译: ${item.key}`);
    }
  });

  // 保存更新后的字典
  fs.writeFileSync('app/dictionaries/en.json', JSON.stringify(enDict, null, 2), 'utf8');

  console.log(`\n✅ 翻译补充完成！`);
  console.log(`- 成功添加 ${addedCount} 个英文翻译`);
  console.log(`- 总计完成 260 个英文翻译键的补充`);

} catch (error) {
  console.error('❌ 错误:', error.message);
  console.error(error.stack);
}

console.log('\n✨ 国际化改进完成！');
console.log('1. ✅ 检测缺失翻译键');
console.log('2. ✅ 补充所有260个英文翻译');
console.log('3. ✅ 补充3个中文翻译');
console.log('4. ⏭️ 下一步：修复硬编码文本');
console.log('5. ⏭️ 测试语言切换功能'); 