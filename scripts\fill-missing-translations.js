// 自动补充缺失的英文翻译const fs = require('fs');const path = require('path');console.log('🌐 开始补充缺失的英文翻译...\n');try {  // 读取字典文件  const zhDict = JSON.parse(fs.readFileSync('app/dictionaries/zh.json', 'utf8'));  const enDict = JSON.parse(fs.readFileSync('app/dictionaries/en.json', 'utf8'));  const missingKeys = JSON.parse(fs.readFileSync('scripts/missing-dictionary-keys.json', 'utf8'));    console.log(`📊 检测到 ${missingKeys.missingInEnglish.length} 个缺失的英文翻译键`);

// 获取嵌套对象的值
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

// 设置嵌套对象的值
function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => {
    if (!current[key]) current[key] = {};
    return current[key];
  }, obj);
  target[lastKey] = value;
}

// 翻译映射表
const translations = {
  // About page
  'about.image_alt': 'About Us - Indoor Playground Equipment',
  'about.company_name': 'Junsheng Amusement Equipment',
  'about.mission.title': 'Our Mission',
  'about.mission.paragraph1': 'We are dedicated to creating innovative and safe indoor playground equipment that brings joy to children worldwide.',
  'about.mission.paragraph2': 'With over 15 years of experience, we combine creativity, safety, and fun to deliver exceptional play experiences.',
  'about.stats.projects': 'Projects Completed',
  'about.stats.experience': 'Years of Experience',
  'about.stats.safety': 'Safety Record',
  'about.cta.description': 'Ready to create an amazing indoor playground?',
  
  // Products - Indoor
  'products.indoor.description': 'Explore our comprehensive range of indoor playground equipment designed for maximum fun and safety.',
  'products.indoor.features.title': 'Key Features',
  'products.indoor.features.themes.title': 'Themed Designs',
  'products.indoor.features.themes.description': 'Ocean, jungle, space, and custom themes available',
  'products.indoor.features.multi.title': 'Multi-Level Play',
  'products.indoor.features.multi.description': 'Maximize space with vertical play structures',
  'products.indoor.features.toddler.title': 'Toddler Areas',
  'products.indoor.features.toddler.description': 'Safe zones designed for younger children',
  'products.indoor.features.interactive.title': 'Interactive Elements',
  'products.indoor.features.interactive.description': 'Digital games and sensory play features',
  'products.indoor.cta.title': 'Design Your Indoor Playground',
  'products.indoor.cta.description': 'Let our experts help you create the perfect indoor play space',
  'products.indoor.cta.button': 'Get Started',
  
  // Products - Trampoline
  'products.trampoline.description': 'Professional trampoline parks with maximum safety and entertainment value.',
  'products.trampoline.features.title': 'Park Features',
  'products.trampoline.features.jump.title': 'Free Jump Zone',
  'products.trampoline.features.jump.description': 'Open jumping areas for freestyle fun',
  'products.trampoline.features.sports.title': 'Dodgeball Courts',
  'products.trampoline.features.sports.description': 'Competitive games on trampolines',
  'products.trampoline.features.foam.title': 'Foam Pits',
  'products.trampoline.features.foam.description': 'Safe landing zones for tricks',
  'products.trampoline.features.safety.title': 'Safety Systems',
  'products.trampoline.features.safety.description': 'Padded walls and professional mats',
  'products.trampoline.cta.title': 'Build Your Trampoline Park',
  'products.trampoline.cta.description': 'Create an exciting trampoline destination',
  'products.trampoline.cta.button': 'Start Planning',
  
  // Products - Small (100-500 sqm)
  'products.small.description': 'Perfect solutions for limited spaces with maximum play value.',
  'products.small.benefits.title': 'Benefits',
  'products.small.benefits.cost.title': 'Cost Effective',
  'products.small.benefits.cost.description': 'Lower initial investment with high returns',
  'products.small.benefits.location.title': 'Flexible Location',
  'products.small.benefits.location.description': 'Fits in shopping malls, restaurants, and small venues',
  'products.small.benefits.roi.title': 'Quick ROI',
  'products.small.benefits.roi.description': 'Faster return on investment due to lower overhead',
  'products.small.benefits.efficient.title': 'Space Efficient',
  'products.small.benefits.efficient.description': 'Optimized designs for maximum play in minimal space',
  'products.small.cta.title': 'Small Space, Big Fun',
  'products.small.cta.description': 'Discover our compact playground solutions',
  'products.small.cta.button': 'View Options',
  
  // Products - Medium (500-1000 sqm)
  'products.medium.description': 'Balanced solutions offering variety and excitement for growing businesses.',
  'products.medium.benefits.title': 'Advantages',
  'products.medium.benefits.capacity.title': 'Higher Capacity',
  'products.medium.benefits.capacity.description': 'Accommodate more children simultaneously',
  'products.medium.benefits.variety.title': 'More Variety',
  'products.medium.benefits.variety.description': 'Multiple play zones and activities',
  'products.medium.benefits.party.title': 'Party Facilities',
  'products.medium.benefits.party.description': 'Dedicated areas for birthday celebrations',
  'products.medium.benefits.balance.title': 'Perfect Balance',
  'products.medium.benefits.balance.description': 'Ideal size for manageable operations',
  'products.medium.cta.title': 'Medium-Sized Excellence',
  'products.medium.cta.description': 'Explore our mid-range playground options',
  'products.medium.cta.button': 'Learn More',
  
  // Products - Large (1000+ sqm)
  'products.large.description': 'Premium large-scale entertainment centers for maximum impact.',
  'products.large.benefits.title': 'Features',
  'products.large.benefits.destination.title': 'Destination Venue',
  'products.large.benefits.destination.description': 'Become a regional entertainment hub',
  'products.large.benefits.attractions.title': 'Multiple Attractions',
  'products.large.benefits.attractions.description': 'Diverse play zones for all ages',
  'products.large.benefits.amenities.title': 'Full Amenities',
  'products.large.benefits.amenities.description': 'Cafes, party rooms, and parent lounges',
  'products.large.benefits.revenue.title': 'Revenue Streams',
  'products.large.benefits.revenue.description': 'Multiple income sources from various services',
  'products.large.cta.title': 'Go Big with Confidence',
  'products.large.cta.description': 'Create a landmark entertainment destination',
  'products.large.cta.button': 'Start Your Project',
  
  // Custom Playground
  'customPlayground.title': 'Custom Playground Design',
  'customPlayground.subtitle': 'Bring your unique vision to life with our custom design services',
  'customPlayground.cta': 'Start Custom Design',
  
  // Factory
  'factory.title_prefix': 'Factory Tour',
  'factory.slider_label': 'Factory Images',
  'factory.info_btn': 'Factory Information',
  'factory.info_btn_aria': 'View factory information',
  'factory.prev_slide_aria': 'Previous image',
  'factory.next_slide_aria': 'Next image',
  'factory.slide_dot_aria': 'Go to slide',
  'factory.info_card.header': 'Our Manufacturing Facility',
  'factory.info_card.description': 'State-of-the-art production facility ensuring quality and safety',
  'factory.info_card.cert_title': 'Certifications',
  'factory.stats.years': 'Years of Excellence',
  'factory.stats.products': 'Products Delivered',
  'factory.certifications.ce': 'CE Certified',
  'factory.certifications.iso': 'ISO 9001:2015',
  'factory.slides.slide1': 'Main production floor',
  'factory.slides.slide2': 'Quality testing area',
  'factory.slides.slide3': 'Assembly line',
  'factory.slides.slide4': 'Finished products warehouse',
  
  // Contact
  'contact.description': 'Get in touch with us for your indoor playground needs',
  'contact.office': 'Office',
  'contact.email_us': 'Email Us',
  'contact.call_us': 'Call Us',
  'contact.working_hours': 'Working Hours',
  'contact.hours': 'Monday - Friday: 9:00 AM - 6:00 PM',
  'contact.form.title': 'Send us a message',
  'contact.form.subtitle': "We'll get back to you within 24 hours",
  'contact.form.name_placeholder': 'Your Name',
  'contact.form.email_placeholder': 'Your Email',
  'contact.form.phone': 'Phone',
  'contact.form.phone_placeholder': 'Your Phone Number',
  'contact.form.country': 'Country',
  'contact.form.country_placeholder': 'Your Country',
  'contact.form.playground_size': 'Playground Size',
  'contact.form.size_option1': '100-500 sqm',
  'contact.form.size_option2': '500-1000 sqm',
  'contact.form.size_option3': '1000+ sqm',
  'contact.form.size_option4': 'Not sure yet',
  'contact.form.message_placeholder': 'Tell us about your project...',
  'contact.form.sending': 'Sending...',
  
  // Safe Standards
  'safe_standard.intro.paragraph2': 'We adhere to the highest international safety standards to ensure worry-free play.',
  'safe_standard.standards.title': 'Certifications & Standards',
  'safe_standard.standards.astm': 'ASTM International Standards',
  'safe_standard.standards.en': 'EN 1176 European Standards',
  'safe_standard.standards.iso': 'ISO 9001 Quality Management',
  'safe_standard.standards.ce': 'CE Marking Compliance',
  'safe_standard.standards.tuv': 'TÜV Certified Products',
  'safe_standard.standards.csa': 'CSA Standards Approved',
  'safe_standard.features.title': 'Safety Features',
  'safe_standard.features.image_alt': 'Safety features demonstration',
  'safe_standard.features.rounded_edges.title': 'Rounded Edges',
  'safe_standard.features.rounded_edges.description': 'All corners and edges are carefully rounded',
  'safe_standard.features.non_toxic.title': 'Non-Toxic Materials',
  'safe_standard.features.non_toxic.description': 'Food-grade plastics and safe paints only',
  'safe_standard.features.anti_slip.title': 'Anti-Slip Surfaces',
  'safe_standard.features.anti_slip.description': 'Textured surfaces prevent slips and falls',
  'safe_standard.features.impact_absorbing.title': 'Impact Absorption',
  'safe_standard.features.impact_absorbing.description': 'Soft padding in all critical areas',
  'safe_standard.features.secure.title': 'Secure Fastening',
  'safe_standard.features.secure.description': 'Heavy-duty bolts and regular inspections',
  'safe_standard.features.spacing.title': 'Proper Spacing',
  'safe_standard.features.spacing.description': 'Designed to prevent entrapment',
  'safe_standard.features.enclosed.title': 'Enclosed Heights',
  'safe_standard.features.enclosed.description': 'Safety nets and barriers at all levels',
  'safe_standard.features.age_appropriate.title': 'Age Zoning',
  'safe_standard.features.age_appropriate.description': 'Separate areas for different age groups',
  'safe_standard.cta.title': 'Safety is Our Priority',
  'safe_standard.cta.description': 'Learn more about our commitment to safety',
  'safe_standard.cta.button': 'Download Safety Guide'
};

// 继续添加更多翻译...
const moreTranslations = {
  // Marketing Support
  'marketing_support.intro.paragraph2': 'We provide comprehensive marketing support to help your business thrive.',
  'marketing_support.services.title': 'Marketing Services',
  'marketing_support.services.brand.title': 'Brand Development',
  'marketing_support.services.brand.item1': 'Logo and visual identity design',
  'marketing_support.services.brand.item2': 'Brand positioning strategy',
  'marketing_support.services.brand.item3': 'Marketing material templates',
  'marketing_support.services.brand.item4': 'Signage and display design',
  'marketing_support.services.digital.title': 'Digital Marketing',
  'marketing_support.services.digital.item1': 'Social media setup and strategy',
  'marketing_support.services.digital.item2': 'Website development assistance',
  'marketing_support.services.digital.item3': 'Online advertising campaigns',
  'marketing_support.services.digital.item4': 'SEO optimization guidance',
  'marketing_support.services.promotional.title': 'Promotional Support',
  'marketing_support.services.promotional.item1': 'Grand opening event planning',
  'marketing_support.services.promotional.item2': 'Seasonal promotion ideas',
  'marketing_support.services.promotional.item3': 'Loyalty program development',
  'marketing_support.services.promotional.item4': 'Partnership opportunities',
  'marketing_support.services.opening.title': 'Launch Support',
  'marketing_support.services.opening.item1': 'Pre-opening marketing timeline',
  'marketing_support.services.opening.item2': 'Staff training materials',
  'marketing_support.services.opening.item3': 'Operations manual',
  'marketing_support.services.opening.item4': 'Ongoing consultation',
  'marketing_support.process.title': 'Our Process',
  'marketing_support.process.step1.title': 'Market Analysis',
  'marketing_support.process.step1.description': 'Understanding your local market and competition',
  'marketing_support.process.step2.title': 'Strategy Development',
  'marketing_support.process.step2.description': 'Creating a customized marketing plan',
  'marketing_support.process.step3.title': 'Implementation',
  'marketing_support.process.step3.description': 'Rolling out marketing initiatives',
  'marketing_support.process.step4.title': 'Training',
  'marketing_support.process.step4.description': 'Empowering your team with knowledge',
  'marketing_support.process.step5.title': 'Ongoing Support',
  'marketing_support.process.step5.description': 'Continuous guidance and optimization',
  'marketing_support.cta.title': 'Grow Your Business',
  'marketing_support.cta.description': 'Let us help you build a successful indoor playground business',
  'marketing_support.cta.button': 'Get Marketing Support'
};

// 继续添加剩余的翻译...
const remainingTranslations = {
  // Custom Solutions
  'custom_solutions.image_alt': 'Custom playground solutions',
  'custom_solutions.intro.title': 'Tailored Solutions for Your Vision',
  'custom_solutions.intro.paragraph1': 'Every space is unique, and so should be your playground.',
  'custom_solutions.intro.paragraph2': 'Our custom design service brings your specific vision to life.',
  'custom_solutions.list.title': 'What We Offer',
  'custom_solutions.design.image_alt': 'Custom design process',
  'custom_solutions.design.description': 'Professional design services tailored to your needs',
  'custom_solutions.design.feature1': '3D visualization and planning',
  'custom_solutions.design.feature2': 'Space optimization expertise',
  'custom_solutions.design.feature3': 'Theme development',
  'custom_solutions.design.feature4': 'Safety compliance assurance',
  'custom_solutions.purchase.image_alt': 'Purchasing process',
  'custom_solutions.purchase.description': 'Flexible purchasing options to suit your budget',
  'custom_solutions.purchase.feature1': 'Phased installation options',
  'custom_solutions.purchase.feature2': 'Financing assistance',
  'custom_solutions.purchase.feature3': 'Warranty programs',
  'custom_solutions.purchase.feature4': 'Maintenance packages',
  'custom_solutions.process.title': 'Design Process',
  'custom_solutions.process.step1.title': 'Consultation',
  'custom_solutions.process.step1.description': 'Understanding your vision and requirements',
  'custom_solutions.process.step2.title': 'Concept Design',
  'custom_solutions.process.step2.description': '3D rendering and layout planning',
  'custom_solutions.process.step3.title': 'Refinement',
  'custom_solutions.process.step3.description': 'Adjusting design based on feedback',
  'custom_solutions.process.step4.title': 'Production',
  'custom_solutions.process.step4.description': 'Manufacturing with quality control',
  'custom_solutions.cta.title': 'Ready for Custom Design?',
  'custom_solutions.cta.description': 'Start creating your unique playground today',
  
  // Purchase Guide
  'purchase_guide.description': 'A comprehensive guide to purchasing your first indoor playground',
  'purchase_guide.intro.image_alt': 'Indoor playground purchase guide',
  'purchase_guide.intro.title': 'Your Journey to Playground Ownership',
  'purchase_guide.intro.paragraph1': 'Starting an indoor playground business is an exciting venture.',
  'purchase_guide.intro.paragraph2': 'This guide will walk you through every step of the process.',
  'purchase_guide.steps.title': 'Step-by-Step Guide',
  'purchase_guide.steps.pro_tip': 'Pro Tip',
  'purchase_guide.steps.step1.title': 'Step 1: Research & Planning',
  'purchase_guide.steps.step1.item1': 'Analyze local market demand',
  'purchase_guide.steps.step1.item2': 'Study competitor offerings',
  'purchase_guide.steps.step1.item3': 'Define target age groups',
  'purchase_guide.steps.step1.item4': 'Set preliminary budget',
  'purchase_guide.steps.step1.item5': 'Choose ideal location',
  'purchase_guide.steps.step1.tip': 'Visit successful playgrounds in other cities for inspiration',
  'purchase_guide.steps.step1.image_alt': 'Research and planning phase',
  'purchase_guide.steps.step2.title': 'Step 2: Space & Design',
  'purchase_guide.steps.step2.item1': 'Measure available space accurately',
  'purchase_guide.steps.step2.item2': 'Consider ceiling height requirements',
  'purchase_guide.steps.step2.item3': 'Plan for parent seating areas',
  'purchase_guide.steps.step2.item4': 'Include party room if needed',
  'purchase_guide.steps.step2.item5': 'Design traffic flow patterns',
  'purchase_guide.steps.step2.tip': 'Leave 30% open space for circulation and future additions',
  'purchase_guide.steps.step2.image_alt': 'Space planning and design',
  'purchase_guide.steps.step3.title': 'Step 3: Budget & Financing',
  'purchase_guide.steps.step3.item1': 'Equipment costs (40-50% of total)',
  'purchase_guide.steps.step3.item2': 'Installation and shipping',
  'purchase_guide.steps.step3.item3': 'Safety surfacing materials',
  'purchase_guide.steps.step3.item4': 'Permits and insurance',
  'purchase_guide.steps.step3.item5': 'Marketing and grand opening',
  'purchase_guide.steps.step3.tip': 'Budget extra 15-20% for unexpected costs',
  'purchase_guide.steps.step3.image_alt': 'Budget planning',
  'purchase_guide.steps.step4.title': 'Step 4: Installation & Launch',
  'purchase_guide.steps.step4.item1': 'Coordinate delivery schedule',
  'purchase_guide.steps.step4.item2': 'Supervise installation process',
  'purchase_guide.steps.step4.item3': 'Complete safety inspections',
  'purchase_guide.steps.step4.item4': 'Train staff thoroughly',
  'purchase_guide.steps.step4.item5': 'Plan grand opening event',
  'purchase_guide.steps.step4.tip': 'Do a soft opening with friends and family first',
  'purchase_guide.steps.step4.image_alt': 'Installation and launch',
  'purchase_guide.considerations.title': 'Important Considerations',
  'purchase_guide.considerations.safety.title': 'Safety First',
  'purchase_guide.considerations.safety.description': 'Never compromise on safety standards and certifications',
  'purchase_guide.considerations.revenue.title': 'Revenue Planning',
  'purchase_guide.considerations.revenue.description': 'Consider multiple income streams: admission, parties, cafe',
  'purchase_guide.considerations.staff.title': 'Staffing Needs',
  'purchase_guide.considerations.staff.description': 'Plan for adequate supervision and customer service',
  'purchase_guide.considerations.marketing.title': 'Marketing Strategy',
  'purchase_guide.considerations.marketing.description': 'Build awareness before opening with social media and partnerships',
  'purchase_guide.cta.title': 'Ready to Start?',
  'purchase_guide.cta.description': 'Let our experts guide you through the process',
  'purchase_guide.cta.button': 'Get Expert Consultation'
};

// 合并所有翻译
const allTranslations = {
  ...translations,
  ...moreTranslations,
  ...remainingTranslations
};

// 补充缺失的英文翻译
let addedCount = 0;
let skippedCount = 0;

missingKeys.missingInEnglish.forEach(key => {
  const zhValue = getNestedValue(zhDict, key);
  
  if (allTranslations[key]) {
    setNestedValue(enDict, key, allTranslations[key]);
    addedCount++;
  } else if (zhValue) {
    // 对于没有手动翻译的，暂时使用中文值作为占位符
    console.log(`⚠️ 需要翻译: ${key} = "${zhValue}"`);
    setNestedValue(enDict, key, zhValue); // 临时使用中文
    skippedCount++;
  }
});

// 补充中文字典缺失的键
missingKeys.missingInChinese.forEach(key => {
  const enValue = getNestedValue(enDict, key);
  if (enValue) {
    // 为缺失的中文键提供翻译
    if (key === 'custom_playground.subtitle') {
      setNestedValue(zhDict, key, '将您的独特愿景变为现实，我们提供定制设计服务');
    } else if (key === 'custom_playground.cta') {
      setNestedValue(zhDict, key, '开始定制设计');
    } else if (key === 'footer.products') {
      setNestedValue(zhDict, key, '产品');
    }
  }
});

// 保存更新后的字典
fs.writeFileSync('app/dictionaries/en.json', JSON.stringify(enDict, null, 2), 'utf8');
fs.writeFileSync('app/dictionaries/zh.json', JSON.stringify(zhDict, null, 2), 'utf8');

console.log(`\n✅ 翻译补充完成！`);
console.log(`- 添加了 ${addedCount} 个英文翻译`);
console.log(`- 有 ${skippedCount} 个键需要人工翻译`);
console.log(`- 补充了 ${missingKeys.missingInChinese.length} 个中文翻译`);

// 创建需要人工翻译的列表
const needsTranslation = [];
missingKeys.missingInEnglish.forEach(key => {
  if (!allTranslations[key]) {
    const zhValue = getNestedValue(zhDict, key);
    if (zhValue) {
      needsTranslation.push({ key, chinese: zhValue });
    }
  }
});

if (needsTranslation.length > 0) {
  fs.writeFileSync(
    'scripts/needs-manual-translation.json',
    JSON.stringify(needsTranslation, null, 2),
    'utf8'
  );
  console.log(`\n📝 需要人工翻译的键已保存到: scripts/needs-manual-translation.json`);
}

console.log('\n🎯 下一步建议:');
console.log('1. 检查 scripts/needs-manual-translation.json 完成剩余翻译');
console.log('2. 运行硬编码文本修复脚本');
console.log('3. 测试语言切换功能'); 