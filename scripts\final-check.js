// 最终验证表单居中修复
const fs = require('fs');

console.log('🔍 最终验证表单居中修复\n');

// 检查产品页面组件
const productPage = fs.readFileSync('./app/[lang]/products/page.tsx', 'utf8');
console.log('📁 产品页面组件结构:');

if (productPage.includes('w-full flex justify-center')) {
  console.log('   ✅ 外层 flex 居中');
} else {
  console.log('   ❌ 外层 flex 居中');
}

if (productPage.includes('max-w-4xl')) {
  console.log('   ✅ 最大宽度限制');
} else {
  console.log('   ❌ 最大宽度限制');
}

if (productPage.includes('products-page-quote-form')) {
  console.log('   ✅ CSS类名正确');
} else {
  console.log('   ❌ CSS类名正确');
}

// 检查全局样式
const globalCss = fs.readFileSync('./app/styles/global-quote-form.css', 'utf8');
console.log('\n📁 全局表单样式:');

if (globalCss.includes('margin: 2rem auto !important')) {
  console.log('   ✅ 基础居中样式');
} else {
  console.log('   ❌ 基础居中样式');
}

if (globalCss.includes('.products-page-quote-form.quote-form-container')) {
  console.log('   ✅ 产品页面特定样式');
} else {
  console.log('   ❌ 产品页面特定样式');
}

if (globalCss.includes('margin: 3rem auto !important')) {
  console.log('   ✅ 产品页面居中样式');
} else {
  console.log('   ❌ 产品页面居中样式');
}

// 检查产品样式
const productsCss = fs.readFileSync('./app/styles/products.css', 'utf8');
console.log('\n📁 产品页面样式:');

if (productsCss.includes('.products-page-quote-form')) {
  console.log('   ✅ 产品页面表单类');
} else {
  console.log('   ❌ 产品页面表单类');
}

console.log('\n🎉 修复完成！请在浏览器中访问:');
console.log('- http://localhost:3000/zh/products');
console.log('- http://localhost:3000/en/products');
console.log('\n表单应该在页面底部正确居中显示！'); 