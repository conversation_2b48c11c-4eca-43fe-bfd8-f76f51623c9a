/**
 * 最终调试脚本 - 检查产品显示问题
 */
const fs = require('fs');
const path = require('path');

console.log('🔍 最终调试 - 产品显示问题\n');

// 1. 检查关键文件是否存在
console.log('📁 检查关键文件:');
const keyFiles = [
  'app/[lang]/products/page.tsx',
  'app/components/ProductGrid.tsx',
  'app/components/ModernProductCard.tsx',
  'app/api/products/route.ts',
  'app/styles/globals.css',
  'app/styles/force-modern-product-style.css'
];

keyFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file}`);
  } else {
    console.log(`   ❌ ${file} - 文件不存在`);
  }
});

// 2. 检查 ProductGrid 的具体实现
console.log('\n📄 检查 ProductGrid 实现:');
const productGridPath = 'app/components/ProductGrid.tsx';
if (fs.existsSync(productGridPath)) {
  const content = fs.readFileSync(productGridPath, 'utf8');
  
  // 检查 API 调用
  const apiCallMatch = content.match(/fetch\(`([^`]+)`/);
  if (apiCallMatch) {
    console.log(`   ✅ API 调用: ${apiCallMatch[1]}`);
  } else {
    console.log('   ❌ 未找到 API 调用');
  }
  
  // 检查错误处理
  if (content.includes('setError')) {
    console.log('   ✅ 包含错误处理');
  } else {
    console.log('   ❌ 缺少错误处理');
  }
  
  // 检查加载状态
  if (content.includes('setLoading')) {
    console.log('   ✅ 包含加载状态');
  } else {
    console.log('   ❌ 缺少加载状态');
  }
  
  // 检查产品映射
  if (content.includes('products.map')) {
    console.log('   ✅ 包含产品映射');
  } else {
    console.log('   ❌ 缺少产品映射');
  }
}

// 3. 检查产品页面实现
console.log('\n📄 检查产品页面实现:');
const productPagePath = 'app/[lang]/products/page.tsx';
if (fs.existsSync(productPagePath)) {
  const content = fs.readFileSync(productPagePath, 'utf8');
  
  if (content.includes('<ProductGrid')) {
    console.log('   ✅ 使用 ProductGrid 组件');
  } else {
    console.log('   ❌ 未使用 ProductGrid 组件');
  }
  
  if (content.includes('import ProductGrid')) {
    console.log('   ✅ 导入 ProductGrid');
  } else {
    console.log('   ❌ 未导入 ProductGrid');
  }
}

// 4. 检查 ModernProductCard 实现
console.log('\n📄 检查 ModernProductCard 实现:');
const cardPath = 'app/components/ModernProductCard.tsx';
if (fs.existsSync(cardPath)) {
  const content = fs.readFileSync(cardPath, 'utf8');
  
  if (content.includes('export default function ModernProductCard')) {
    console.log('   ✅ 正确导出组件');
  } else {
    console.log('   ❌ 组件导出有问题');
  }
  
  if (content.includes('ArrowUpRight')) {
    console.log('   ✅ 包含箭头图标');
  } else {
    console.log('   ❌ 缺少箭头图标');
  }
}

// 5. 检查样式文件
console.log('\n📄 检查样式文件:');
const globalCssPath = 'app/styles/globals.css';
if (fs.existsSync(globalCssPath)) {
  const content = fs.readFileSync(globalCssPath, 'utf8');
  
  if (content.includes("@import './force-modern-product-style.css'")) {
    console.log('   ✅ 全局样式导入强制样式');
  } else {
    console.log('   ❌ 全局样式未导入强制样式');
  }
}

// 6. 生成问题排查建议
console.log('\n🔧 问题排查建议:');
console.log('1. 清除浏览器缓存 (Ctrl+Shift+R)');
console.log('2. 检查浏览器开发者工具的 Console 标签页是否有错误');
console.log('3. 检查 Network 标签页，确认 API 请求是否成功');
console.log('4. 确认服务器正在运行 (npm run dev)');
console.log('5. 检查数据库连接是否正常');

// 7. 生成测试命令
console.log('\n🧪 测试命令:');
console.log('# 测试 API 响应');
console.log('curl "http://localhost:3000/api/products?published=true"');
console.log('');
console.log('# 检查服务器日志');
console.log('# 查看终端中的 Next.js 开发服务器输出');
console.log('');
console.log('# 重启开发服务器');
console.log('# Ctrl+C 停止服务器，然后运行 npm run dev');

console.log('\n✅ 调试完成！');

// 8. 检查是否有语法错误
console.log('\n🔍 检查语法错误:');
try {
  // 简单的语法检查
  const files = [productGridPath, cardPath, productPagePath];
  files.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      
      // 检查基本的 React 语法
      const hasExport = content.includes('export');
      const hasImport = content.includes('import');
      const hasFunction = content.includes('function') || content.includes('=>');
      
      if (hasExport && hasImport && hasFunction) {
        console.log(`   ✅ ${path.basename(file)} - 语法看起来正常`);
      } else {
        console.log(`   ⚠️  ${path.basename(file)} - 可能有语法问题`);
      }
    }
  });
} catch (error) {
  console.log(`   ❌ 语法检查失败: ${error.message}`);
}
