require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

// 获取数据库连接参数
const connectionString =
  process.env.DATABASE_URL ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 创建数据库连接池
const pool = new Pool({ connectionString });

async function findRemainingCategory() {
  try {
    console.log('查询剩余分类...');
    const result = await pool.query('SELECT * FROM categories');

    if (result.rows.length === 0) {
      console.log('没有找到任何分类，数据库中的分类已全部删除');
    } else {
      console.log(`找到 ${result.rows.length} 个分类:`);
      result.rows.forEach(category => {
        console.log(JSON.stringify(category, null, 2));
      });

      // 尝试删除找到的分类
      console.log('\n尝试删除这些分类...');
      for (const category of result.rows) {
        console.log(`删除分类 ID: ${category.id}, 名称: ${category.name}`);
        await pool.query('DELETE FROM categories WHERE id = $1', [category.id]);
      }

      // 验证删除
      const verifyResult = await pool.query('SELECT COUNT(*) as count FROM categories');
      const count = parseInt(verifyResult.rows[0].count);
      if (count === 0) {
        console.log('\n✅ 成功: 所有分类现在已被删除！');
      } else {
        console.log(`\n❌ 警告: 仍有 ${count} 个分类未被删除`);
      }
    }
  } catch (error) {
    console.error('错误:', error);
  } finally {
    await pool.end();
  }
}

findRemainingCategory();
