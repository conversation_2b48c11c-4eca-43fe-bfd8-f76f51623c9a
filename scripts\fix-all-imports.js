/**
 * 批量修复导入路径问题
 */
const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

async function fixImports() {
  console.log('开始修复导入路径问题...');

  const files = await glob('app/api/**/*.ts');
  console.log(`找到 ${files.length} 个API文件需要检查`);

  let fixedCount = 0;

  for (const file of files) {
    let content = fs.readFileSync(file, 'utf8');
    let original = content;

    // 修复auth导入
    content = content.replace(
      /import\s+\{\s*authOptions\s*\}\s+from\s+['"]\.\.\/\.\.\/\.\.\/\.\.\/auth\/\[\.\.\.\s*nextauth\]\/route['"];?/g,
      `import { authOptions } from '@/app/api/auth/[...nextauth]/route.js';`
    );

    // 修复lib/db导入
    content = content.replace(
      /import\s+\{\s*query\s*(?:,\s*[\w\s,]+)?\s*\}\s+from\s+['"]\.\.\/\.\.\/\.\.\/(?:\.\.\/)*lib\/db['"];?/g,
      `import { query } from '@/lib/db.js';`
    );



    // 修复lib/postgresql导入
    content = content.replace(
      /import\s+(?:[\w\s]+\s+from\s+)['"]\.\.\/\.\.\/\.\.\/(?:\.\.\/)*lib\/postgresql['"];?/g,
      `import dbConnect from '@/lib/postgresql.js';`
    );

    // 修复lib/cache导入
    content = content.replace(
      /import\s+\{\s*memoryCache\s*\}\s+from\s+['"]\.\.\/\.\.\/\.\.\/(?:\.\.\/)*lib\/cache['"];?/g,
      `import { memoryCache } from '@/lib/cache';`
    );

    // 修复models导入
    content = content.replace(
      /import\s+(\w+)\s+from\s+['"]\.\.\/\.\.\/\.\.\/(?:\.\.\/)*models\/\1['"];?/g,
      `import $1 from '@/models/$1';`
    );

    // 修复其他lib导入
    content = content.replace(
      /import\s+\{\s*([\w\s,]+)\s*\}\s+from\s+['"]\.\.\/\.\.\/\.\.\/(?:\.\.\/)*lib\/([\w-]+)['"];?/g,
      `import { $1 } from '@/lib/$2';`
    );

    if (content !== original) {
      fs.writeFileSync(file, content, 'utf8');
      console.log(`✓ 修复文件: ${file}`);
      fixedCount++;
    }
  }

  console.log(`完成修复! 共修复了 ${fixedCount} 个文件`);
}

fixImports().catch(console.error);