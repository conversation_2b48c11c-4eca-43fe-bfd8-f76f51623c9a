/**
 * 修复产品删除后仍显示的问题
 * 问题分析：管理员API会在数据库为空时从本地JSON文件读取数据
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function fixDeleteIssue() {
  console.log('🔧 开始修复产品删除问题...');
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    // 1. 检查数据库中的产品
    console.log('\n📋 检查数据库中的产品...');
    const dbProducts = await client.query('SELECT id, name, slug FROM products ORDER BY id');
    console.log(`数据库中有 ${dbProducts.rows.length} 个产品:`);
    dbProducts.rows.forEach(product => {
      console.log(`  - ID: ${product.id}, 名称: ${product.name}, Slug: ${product.slug}`);
    });
    
    // 2. 检查本地JSON文件
    console.log('\n📁 检查本地JSON文件...');
    const localJsonPath = path.join(process.cwd(), 'app', 'data', 'products.json');
    
    if (fs.existsSync(localJsonPath)) {
      console.log('✅ 找到本地JSON文件:', localJsonPath);
      
      try {
        const localData = fs.readFileSync(localJsonPath, 'utf8');
        const localProducts = JSON.parse(localData);
        console.log(`本地JSON文件中有 ${localProducts.length} 个产品:`);
        localProducts.forEach((product, index) => {
          console.log(`  - ${index + 1}. 名称: ${product.name || '未命名'}, Slug: ${product.slug || '无slug'}`);
        });
        
        // 3. 提供解决方案选项
        console.log('\n🎯 问题分析:');
        console.log('管理员API在数据库为空时会从本地JSON文件读取数据，');
        console.log('这导致删除数据库产品后，界面仍显示本地JSON文件中的产品。');
        
        console.log('\n💡 解决方案选项:');
        console.log('1. 清空本地JSON文件 (推荐)');
        console.log('2. 备份并重命名本地JSON文件');
        console.log('3. 修改管理员API逻辑');
        
        // 4. 执行解决方案1：备份并清空本地JSON文件
        console.log('\n🔧 执行解决方案：备份并清空本地JSON文件...');
        
        // 创建备份
        const backupPath = localJsonPath + '.backup.' + Date.now();
        fs.copyFileSync(localJsonPath, backupPath);
        console.log(`✅ 已备份到: ${backupPath}`);
        
        // 清空JSON文件
        fs.writeFileSync(localJsonPath, '[]', 'utf8');
        console.log('✅ 已清空本地JSON文件');
        
      } catch (error) {
        console.log('❌ 读取本地JSON文件失败:', error.message);
      }
    } else {
      console.log('ℹ️  本地JSON文件不存在，这很正常');
    }
    
    // 5. 验证修复结果
    console.log('\n🔍 验证修复结果...');
    console.log('现在刷新管理后台应该只显示数据库中的产品');
    
    client.release();
    
    console.log('\n🎉 修复完成!');
    console.log('\n📝 修复说明:');
    console.log('- 已备份原始本地JSON文件');
    console.log('- 已清空本地JSON文件，现在只显示数据库产品');
    console.log('- 删除产品后应该立即从界面消失');
    
    console.log('\n🌐 请刷新管理后台验证修复效果:');
    console.log('http://localhost:3000/admin/products');
    
  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error.message);
  } finally {
    await pool.end();
  }
}

fixDeleteIssue();
