/**
 * 修复产品图片重复问题 - 为每个产品分配不同的真实图片
 */

const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

// 为每个产品分配不同的真实图片
const productImageMappings = {
  'holographic-restaurant-system': [
    '/images/holographic/holographic-restaurant.jpg',
    '/images/holographic/exhibition-hall.jpg',
    '/images/company/holographic-display.jpg',
    '/images/holographic/museum.jpg'
  ],
  'interactive-floor-projection': [
    '/images/products/3d-1.jpg',
    '/images/products/3d-2.jpg',
    '/images/products/3d-3.jpg',
    '/images/products/3d-4.jpg'
  ],
  'motion-sensing-game-system': [
    '/images/products/ar-1.jpg',
    '/images/products/ar-2.jpg',
    '/images/products/ar-3.jpg',
    '/images/products/ar-4.jpg'
  ],
  'ar-education-system': [
    '/images/products/ar-education-1.jpg',
    '/images/products/ar-education-2.jpg',
    '/images/products/ar-education-3.jpg',
    '/images/products/ar-education-4.jpg'
  ]
};

async function fixDuplicateImages() {
  console.log('🔧 开始修复产品图片重复问题...');
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    // 获取所有需要更新的产品
    const products = await client.query(`
      SELECT id, name, slug, images 
      FROM products 
      WHERE slug IN ($1, $2, $3, $4)
    `, [
      'holographic-restaurant-system',
      'interactive-floor-projection', 
      'motion-sensing-game-system',
      'ar-education-system'
    ]);
    
    console.log(`\n📋 找到 ${products.rows.length} 个产品需要更新图片`);
    
    let updateCount = 0;
    
    for (const product of products.rows) {
      try {
        console.log(`\n🖼️  更新产品: ${product.name} (${product.slug})`);
        
        // 获取当前产品的图片
        const currentImages = product.images ? JSON.parse(product.images) : [];
        console.log(`   当前图片: ${currentImages.join(', ')}`);
        
        // 获取新的图片列表
        const newImages = productImageMappings[product.slug] || ['/images/products/placeholder.jpg'];
        console.log(`   新图片: ${newImages.join(', ')}`);
        
        // 更新产品图片
        const result = await client.query(
          'UPDATE products SET images = $1 WHERE id = $2 RETURNING id',
          [JSON.stringify(newImages), product.id]
        );
        
        if (result.rows.length > 0) {
          console.log(`   ✅ 图片更新成功 (${newImages.length} 张图片)`);
          updateCount++;
        }
        
      } catch (error) {
        console.log(`   ❌ 更新失败: ${error.message}`);
      }
    }
    
    console.log(`\n📊 更新结果统计:`);
    console.log(`✅ 成功更新: ${updateCount} 个产品`);
    
    // 验证更新结果
    console.log('\n🔍 验证更新结果:');
    const verifyResult = await client.query(`
      SELECT name, slug, images
      FROM products 
      WHERE slug IN ($1, $2, $3, $4)
      ORDER BY name
    `, [
      'holographic-restaurant-system',
      'interactive-floor-projection', 
      'motion-sensing-game-system',
      'ar-education-system'
    ]);
    
    verifyResult.rows.forEach(row => {
      console.log(`\n📸 ${row.name} (${row.slug})`);
      if (row.images) {
        const imageList = JSON.parse(row.images);
        imageList.forEach((img, index) => {
          console.log(`   ${index + 1}. ${img}`);
        });
      } else {
        console.log('   ⚠️  没有图片');
      }
    });
    
    client.release();
    
    console.log('\n🎉 图片重复问题修复完成!');
    console.log('\n🌐 现在每个产品都有不同的图片，您可以访问以下页面查看:');
    console.log('- http://localhost:3001/zh/products/holographic-restaurant-system (全息餐厅 - 4张不同图片)');
    console.log('- http://localhost:3001/zh/products/interactive-floor-projection (地面投影 - 4张不同图片)');
    console.log('- http://localhost:3001/zh/products/motion-sensing-game-system (体感游戏 - 4张不同图片)');
    console.log('- http://localhost:3001/zh/products/ar-education-system (AR教育 - 4张不同图片)');
    
  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error.message);
  } finally {
    await pool.end();
  }
}

fixDuplicateImages();
