const fs = require('fs');

const envContent = `# Database Configuration
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
POSTGRES_URI=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-development-secret-key-do-not-use-in-production

# Admin Credentials (for development)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
ADMIN_EMAIL=<EMAIL>
`;

try {
  // 备份原始文件
  if (fs.existsSync('.env.local')) {
    fs.copyFileSync('.env.local', '.env.local.backup-' + Date.now());
    console.log('原始文件已备份');
  }

  // 写入新文件
  fs.writeFileSync('.env.local', envContent.replace(/\n/g, '\r\n'));
  console.log('环境变量文件已成功更新！');
} catch (error) {
  console.error('更新环境变量文件时出错:', error);
}
