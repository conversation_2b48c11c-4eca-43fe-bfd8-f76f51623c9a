// 检测和修复硬编码国际化文本的脚本
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 要搜索的组件目录
const componentsDir = './app/components';
const pagesDir = './app/[lang]/pages';

// 硬编码文本的正则表达式
const hardcodedPatterns = [
  {
    pattern: /['"`](了解更多|Learn More|联系我们|Contact Us|提交|Submit)['"`]/g,
    description: '按钮文本',
  },
  {
    pattern: /locale\s*===\s*['"]zh['"]\s*\?\s*['"](.+?)['"]\s*:\s*['"](.+?)['"]/g,
    description: '三元条件文本',
  },
  {
    pattern: /(\{\s*locale\s*===\s*['"]zh['"]\s*\?\s*['"](.+?)['"]\s*:\s*['"](.+?)['"]\s*\})/g,
    description: 'JSX中的三元条件文本',
  },
];

// 收集结果
const results = {
  components: {},
  pages: {},
};

// 检查单个文件
function checkFile(filePath, category) {
  console.log(`检查文件: ${filePath}`);

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(filePath);
    const matches = [];

    // 应用所有模式匹配
    hardcodedPatterns.forEach(({ pattern, description }) => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        matches.push({
          type: description,
          value: match[0],
          position: {
            start: match.index,
            end: match.index + match[0].length,
          },
          line: getLineNumber(content, match.index),
        });
      }
    });

    if (matches.length > 0) {
      results[category][filePath] = matches;
      console.log(`⚠️ 发现 ${matches.length} 处硬编码文本`);
    } else {
      console.log('✓ 没有发现硬编码文本');
    }
  } catch (error) {
    console.error(`无法读取文件 ${filePath}:`, error.message);
  }
}

// 获取匹配位置的行号
function getLineNumber(content, position) {
  const lines = content.substring(0, position).split('\n');
  return lines.length;
}

// 递归检查目录
function checkDirectory(dirPath, category) {
  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const item of items) {
      const itemPath = path.join(dirPath, item.name);

      if (item.isDirectory()) {
        checkDirectory(itemPath, category);
      } else if (
        item.isFile() &&
        (item.name.endsWith('.tsx') || item.name.endsWith('.jsx') || item.name.endsWith('.js'))
      ) {
        checkFile(itemPath, category);
      }
    }
  } catch (error) {
    console.error(`无法读取目录 ${dirPath}:`, error.message);
  }
}

// 生成翻译键
function generateTranslationKey(text, context) {
  // 将文本转换为小写，只保留字母数字，用下划线连接
  const baseKey = text
    .toLowerCase()
    .replace(/[^\w\s]/g, '')
    .trim()
    .replace(/\s+/g, '_')
    .substring(0, 30);

  return `${context}.${baseKey}`;
}

// 生成修复建议
function generateFixSuggestions() {
  const i18nEntries = {
    zh: {},
    en: {},
  };

  // 遍历所有结果，生成国际化键
  for (const category of Object.keys(results)) {
    for (const filePath of Object.keys(results[category])) {
      const fileName = path.basename(filePath, path.extname(filePath));

      for (const match of results[category][filePath]) {
        if (
          match.type === '三元条件文本' &&
          match.value.match(/locale\s*===\s*['"]zh['"]\s*\?\s*['"](.+?)['"]\s*:\s*['"](.+?)['"]/)
        ) {
          // 提取中文和英文文本
          const parts = match.value.match(
            /locale\s*===\s*['"]zh['"]\s*\?\s*['"](.+?)['"]\s*:\s*['"](.+?)['"]/
          );
          if (parts && parts.length >= 3) {
            const zhText = parts[1];
            const enText = parts[2];
            const key = generateTranslationKey(enText, fileName);

            i18nEntries.zh[key] = zhText;
            i18nEntries.en[key] = enText;
          }
        } else if (
          match.type === 'JSX中的三元条件文本' &&
          match.value.match(
            /\{\s*locale\s*===\s*['"]zh['"]\s*\?\s*['"](.+?)['"]\s*:\s*['"](.+?)['"]\s*\}/
          )
        ) {
          // 提取JSX中的中文和英文文本
          const parts = match.value.match(
            /\{\s*locale\s*===\s*['"]zh['"]\s*\?\s*['"](.+?)['"]\s*:\s*['"](.+?)['"]\s*\}/
          );
          if (parts && parts.length >= 3) {
            const zhText = parts[1];
            const enText = parts[2];
            const key = generateTranslationKey(enText, fileName);

            i18nEntries.zh[key] = zhText;
            i18nEntries.en[key] = enText;
          }
        } else if (match.type === '按钮文本') {
          // 简单按钮文本
          let text = match.value.replace(/['"]/g, '');
          let key = generateTranslationKey(text, fileName);

          // 根据常见的中文/英文文本对设置双语支持
          if (text === '了解更多') {
            i18nEntries.zh[key] = '了解更多';
            i18nEntries.en[key] = 'Learn More';
          } else if (text === 'Learn More') {
            i18nEntries.zh[key] = '了解更多';
            i18nEntries.en[key] = 'Learn More';
          } else if (text === '联系我们') {
            i18nEntries.zh[key] = '联系我们';
            i18nEntries.en[key] = 'Contact Us';
          } else if (text === 'Contact Us') {
            i18nEntries.zh[key] = '联系我们';
            i18nEntries.en[key] = 'Contact Us';
          } else if (text === '提交') {
            i18nEntries.zh[key] = '提交';
            i18nEntries.en[key] = 'Submit';
          } else if (text === 'Submit') {
            i18nEntries.zh[key] = '提交';
            i18nEntries.en[key] = 'Submit';
          } else {
            // 其他未知文本，只添加找到的语言
            if (/[\u4e00-\u9fa5]/.test(text)) {
              i18nEntries.zh[key] = text;
              i18nEntries.en[key] = text; // 暂时设为相同值，需人工翻译
            } else {
              i18nEntries.en[key] = text;
              i18nEntries.zh[key] = text; // 暂时设为相同值，需人工翻译
            }
          }
        }
      }
    }
  }

  return i18nEntries;
}

// 生成字典文件内容
function generateDictionaryContent(entries) {
  let content = '{\n';

  // 按键名排序并添加到内容
  Object.keys(entries)
    .sort()
    .forEach(key => {
      content += `  "${key}": "${entries[key]}",\n`;
    });

  // 移除最后一个逗号并添加闭合括号
  if (content.endsWith(',\n')) {
    content = content.slice(0, -2) + '\n';
  }
  content += '}\n';

  return content;
}

// 主函数
async function main() {
  console.log('开始检测硬编码国际化文本...\n');

  // 检查组件目录
  if (fs.existsSync(componentsDir)) {
    console.log(`\n扫描组件目录: ${componentsDir}`);
    checkDirectory(componentsDir, 'components');
  } else {
    console.log(`⚠️ 组件目录不存在: ${componentsDir}`);
  }

  // 检查页面目录
  if (fs.existsSync(pagesDir)) {
    console.log(`\n扫描页面目录: ${pagesDir}`);
    checkDirectory(pagesDir, 'pages');
  } else {
    console.log(`⚠️ 页面目录不存在: ${pagesDir}`);
  }

  // 统计结果
  const componentFileCount = Object.keys(results.components).length;
  const pageFileCount = Object.keys(results.pages).length;
  const totalFiles = componentFileCount + pageFileCount;

  let totalMatches = 0;
  for (const category of Object.keys(results)) {
    for (const filePath of Object.keys(results[category])) {
      totalMatches += results[category][filePath].length;
    }
  }

  console.log('\n扫描结果:');
  console.log(`发现 ${totalFiles} 个文件包含 ${totalMatches} 处硬编码文本`);
  console.log(`- 组件文件: ${componentFileCount} 个`);
  console.log(`- 页面文件: ${pageFileCount} 个`);

  // 生成修复建议
  if (totalMatches > 0) {
    console.log('\n生成修复建议...');
    const i18nEntries = generateFixSuggestions();

    // 创建输出目录
    const outputDir = './i18n-fixes';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // 写入字典文件
    fs.writeFileSync(
      path.join(outputDir, 'zh.json'),
      generateDictionaryContent(i18nEntries.zh),
      'utf8'
    );

    fs.writeFileSync(
      path.join(outputDir, 'en.json'),
      generateDictionaryContent(i18nEntries.en),
      'utf8'
    );

    console.log(`\n✅ 已生成翻译字典文件:`);
    console.log(`- ${outputDir}/zh.json`);
    console.log(`- ${outputDir}/en.json`);

    console.log('\n后续步骤:');
    console.log('1. 复查生成的字典文件，确保翻译准确');
    console.log('2. 将这些条目合并到项目的国际化字典中');
    console.log('3. 使用t()函数替换硬编码文本，例如: t("hero_slider.learn_more")');
  } else {
    console.log('\n✅ 没有发现硬编码文本，无需修复');
  }
}

// 执行主函数
main();
