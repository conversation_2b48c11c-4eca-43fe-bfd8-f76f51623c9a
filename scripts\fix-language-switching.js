// 修复语言切换问题
const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复语言切换问题...\n');

// 1. 修复中间件以支持localStorage
console.log('📌 1. 修复middleware.ts...');
const middlewarePath = 'middleware.ts';
const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');

// 创建修改后的getLocale函数
const updatedGetLocale = `
// Get the preferred locale from request
function getLocale(request: NextRequest) {
  // Check if locale is in URL (highest priority)
  const pathname = request.nextUrl.pathname;
  const pathnameLocale = locales.find(
    locale => pathname.startsWith(\`/\${locale}/\`) || pathname === \`/\${locale}\`
  );
  if (pathnameLocale) return pathnameLocale;

  // Check if locale is in cookie (for client-side persistence)
  const cookieLocale = request.cookies.get('NEXT_LOCALE')?.value;
  if (cookieLocale && locales.includes(cookieLocale)) {
    return cookieLocale;
  }

  // Check Accept-Language header
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    const acceptedLocales = acceptLanguage
      .split(',')
      .map(locale => locale.split(';')[0].trim())
      .filter(locale => locales.includes(locale.substring(0, 2)));

    if (acceptedLocales.length > 0) {
      return acceptedLocales[0].substring(0, 2);
    }
  }

  // Default locale
  return defaultLocale;
}`;

// 替换getLocale函数
const updatedMiddleware = middlewareContent.replace(
  /\/\/ Get the preferred locale from request[\s\S]*?function getLocale[\s\S]*?return defaultLocale;\s*}/,
  updatedGetLocale
);

fs.writeFileSync(middlewarePath, updatedMiddleware, 'utf8');
console.log('✅ 更新了middleware.ts的getLocale函数');

// 2. 修复LanguageProvider以同时设置cookie
console.log('\n📌 2. 修复LanguageProvider.tsx...');
const providerPath = 'app/components/LanguageProvider.tsx';
const providerContent = fs.readFileSync(providerPath, 'utf8');

// 查找changeLanguage函数并添加cookie设置
const updatedChangeLanguage = providerContent.replace(
  /(changeLanguage\s*=\s*\([^)]+\)\s*=>\s*{[\s\S]*?)(\/\/ Set localStorage for persistence)/,
  `$1// Set cookie for middleware compatibility (client-side only)
    if (typeof window !== 'undefined') {
      try {
        document.cookie = \`NEXT_LOCALE=\${newLocale}; path=/; max-age=31536000\`; // 1 year
      } catch (error) {
        console.error('Error setting cookie:', error);
      }
    }

    $2`
);

if (updatedChangeLanguage !== providerContent) {
  fs.writeFileSync(providerPath, updatedChangeLanguage, 'utf8');
  console.log('✅ 更新了LanguageProvider的changeLanguage函数以设置cookie');
} else {
  console.log('⚠️ LanguageProvider已经包含cookie设置或未找到合适的位置');
}

// 3. 检查并报告缺失的字典键
console.log('\n📌 3. 检查缺失的字典键...');
const zhDict = JSON.parse(fs.readFileSync('app/dictionaries/zh.json', 'utf8'));
const enDict = JSON.parse(fs.readFileSync('app/dictionaries/en.json', 'utf8'));

const getKeys = (obj, prefix = '') => {
  let keys = [];
  for (const key in obj) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      keys = keys.concat(getKeys(obj[key], fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  return keys;
};

const zhKeys = getKeys(zhDict);
const enKeys = getKeys(enDict);

const missingInEn = zhKeys.filter(key => !enKeys.includes(key));
const missingInZh = enKeys.filter(key => !zhKeys.includes(key));

// 创建缺失键的报告
const reportPath = 'scripts/missing-dictionary-keys.json';
const report = {
  missingInEnglish: missingInEn,
  missingInChinese: missingInZh,
  totalMissing: missingInEn.length + missingInZh.length
};

fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
console.log(`✅ 生成了缺失字典键报告: ${reportPath}`);
console.log(`  - 英文字典缺少: ${missingInEn.length} 个键`);
console.log(`  - 中文字典缺少: ${missingInZh.length} 个键`);

// 4. 创建硬编码文本报告
console.log('\n📌 4. 扫描硬编码文本...');
const hardcodedReport = [];

const scanFiles = [
  'app/[lang]/page.tsx',
  'app/[lang]/products/page.tsx',
  'app/[lang]/pages/about-us/page.tsx',
  'app/[lang]/pages/contact-us/page.tsx'
];

scanFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');
    const chinesePattern = /['"`]([^'"`]*[\u4e00-\u9fa5]+[^'"`]*)['"`]/g;
    const matches = [...content.matchAll(chinesePattern)];
    
    if (matches.length > 0) {
      hardcodedReport.push({
        file: filePath,
        count: matches.length,
        examples: matches.slice(0, 5).map(m => m[1])
      });
    }
  }
});

const hardcodedPath = 'scripts/hardcoded-text-report.json';
fs.writeFileSync(hardcodedPath, JSON.stringify(hardcodedReport, null, 2), 'utf8');
console.log(`✅ 生成了硬编码文本报告: ${hardcodedPath}`);

// 5. 创建修复总结
console.log('\n' + '='.repeat(60));
console.log('📊 语言切换修复总结\n');

console.log('✅ 已完成的修复:');
console.log('1. 更新了middleware.ts - 优先从URL读取语言');
console.log('2. 尝试更新LanguageProvider - 同时设置cookie和localStorage');
console.log('3. 生成了缺失字典键报告');
console.log('4. 生成了硬编码文本报告');

console.log('\n💡 后续建议:');
console.log('1. 查看 scripts/missing-dictionary-keys.json 并补充缺失的翻译');
console.log('2. 查看 scripts/hardcoded-text-report.json 并将硬编码文本移至字典');
console.log('3. 测试语言切换功能是否正常工作');
console.log('4. 确保所有新页面都使用getDictionary或useLanguage');

console.log('\n🔧 测试步骤:');
console.log('1. 清除浏览器的localStorage和cookie');
console.log('2. 访问 http://localhost:3001/zh');
console.log('3. 点击语言切换器切换到英文');
console.log('4. 刷新页面，检查是否保持英文状态');
console.log('5. 导航到其他页面，检查语言是否保持一致');

console.log('\n' + '='.repeat(60)); 