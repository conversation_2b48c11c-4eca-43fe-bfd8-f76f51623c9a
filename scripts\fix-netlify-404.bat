@echo off
echo 🔧 修复Netlify 404问题
echo.

echo 📋 问题诊断:
echo - Next.js动态路由在Netlify上需要特殊配置
echo - 需要添加重定向规则
echo.

echo 🛠️ 应用修复...

echo ✅ 已创建 public/_redirects 文件
echo ✅ 已创建 netlify.toml 配置文件  
echo ✅ 已修改 next.config.js 为export模式

echo.
echo 🚀 下一步操作:
echo 1. 重新构建项目: npm run build
echo 2. 重新部署到Netlify
echo 3. 等待部署完成 (约2-3分钟)
echo.

echo 📋 如果问题仍然存在:
echo 1. 检查Netlify部署日志
echo 2. 确认构建命令: npm run build
echo 3. 确认发布目录: out
echo.

set /p rebuild=是否现在重新构建? (y/n): 

if /i "%rebuild%"=="y" (
    echo 🔨 开始构建...
    call npm run build
    
    if errorlevel 1 (
        echo ❌ 构建失败，请检查错误信息
        pause
        exit /b 1
    )
    
    echo ✅ 构建完成！
    echo 📤 请重新部署到Netlify
    echo.
    echo 🌐 Netlify部署步骤:
    echo 1. 登录 https://app.netlify.com
    echo 2. 找到您的站点
    echo 3. 拖拽 'out' 文件夹到部署区域
    echo 4. 等待部署完成
) else (
    echo 💡 请手动运行: npm run build
    echo 然后重新部署到Netlify
)

echo.
echo 🎯 修复完成！
pause
