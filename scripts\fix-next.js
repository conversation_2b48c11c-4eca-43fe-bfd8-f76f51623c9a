// 对于Windows下无法删除文件的问题，可以尝试以下办法：
// 1. 关闭所有Node.js进程
// 2. 重启计算机
// 3. 然后手动删除.next目录
// 4. 运行以下命令：

/*
npm cache clean --force
npm install
npm run dev
*/

// 如果仍有"Cannot find module '...'_document.js"错误，检查以下几点：
// 1. 确保pages/_document.js或pages/_document.tsx存在且正确
// 2. 检查package.json中Next.js版本是否兼容
// 3. 检查tsconfig.json中的配置

// 如果以上方法不解决问题，可尝试：
// 1. 降级Next.js版本
// npm install next@13.4.19 react@18.2.0 react-dom@18.2.0

// 2. 或者更新Next.js到最新版
// npm install next@latest react@latest react-dom@latest
