// 修复useParams类型相关问题
const fs = require('fs');
const path = require('path');

// 要搜索的目录
const adminDir = './app/[lang]/admin';

// 递归处理目录
function processDirectory(dirPath) {
  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const item of items) {
      const itemPath = path.join(dirPath, item.name);

      if (item.isDirectory()) {
        processDirectory(itemPath);
      } else if (item.isFile() && (item.name.endsWith('.tsx') || item.name.endsWith('.jsx'))) {
        fixFile(itemPath);
      }
    }
  } catch (error) {
    console.error(`处理目录时出错: ${dirPath}`, error);
  }
}

// 修复单个文件
function fixFile(filePath) {
  try {
    console.log(`检查文件: ${filePath}`);
    let content = fs.readFileSync(filePath, 'utf8');
    let original = content;
    
    // 检查是否使用了useParams但类型有问题
    if (content.includes('useParams') && content.includes('params.id')) {
      // 1. 检查是否有'params is possibly null'警告
      if (!content.includes('params: { id: string }') && !content.includes('params: {id: string}')) {
        // 替换为正确的类型声明
        content = content.replace(
          /const\s+params\s*=\s*useParams[<(][^>)]*[>)]/g,
          'const params = useParams<{ id: string }>()'
        );
        
        // 替换为非空断言
        content = content.replace(/params\.id/g, 'params.id');
        
        console.log(`  修复params类型声明: ${filePath}`);
      }
    }
    
    // 检查是否存在id相关的类型问题
    if (content.includes('Cannot find name \'id\'')) {
      // 添加id声明
      content = content.replace(
        /(const\s+\{\s*data:\s*session\s*\}.*?;)/,
        '$1\n  const id = params?.id;'
      );
      console.log(`  添加id声明: ${filePath}`);
    }
    
    // 判断是否有修改
    if (content !== original) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
    } else {
      console.log(`  无需修改: ${filePath}`);
    }
  } catch (error) {
    console.error(`处理文件时出错: ${filePath}`, error);
  }
}

// 执行主函数
console.log('开始修复params类型问题...');
processDirectory(adminDir);
console.log('完成！'); 