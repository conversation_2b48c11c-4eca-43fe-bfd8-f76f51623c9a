/**
 * 修复产品图片显示问题
 * 添加一些测试产品数据并确保图片路径正确
 */

const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

// 测试产品数据，使用实际存在的图片
const testProducts = [
  {
    name: '全息餐厅投影系统',
    slug: 'holographic-restaurant-system',
    description: '为餐厅打造独特的全息投影用餐环境，提升顾客用餐体验，创造难忘的视觉盛宴。专业级全息投影技术，采用先进的光学投影设备，能够在餐厅空间中创造出令人惊叹的3D视觉效果。',
    type: 'holographic_solutions',
    size: '500-1000 SQM',
    style: '奢华,科技',
    features: ['360度全息投影', '主题场景切换', '音效同步', '氛围营造', '定制化内容'],
    images: ['/images/products/placeholder.jpg'], // 使用实际存在的占位符图片
    in_stock: true,
    is_featured: true,
    price: 0
  },
  {
    name: '互动地面投影系统',
    slug: 'interactive-floor-projection',
    description: '地面互动投影系统，通过脚步触发各种视觉效果，为用户带来沉浸式的互动体验。采用先进的红外感应技术，实时捕捉用户动作并生成相应的视觉反馈。',
    type: 'interactive_equipment',
    size: '500-1000 SQM',
    style: '现代,互动',
    features: ['脚步感应技术', '实时互动反馈', '多种视觉效果', '自定义内容', '多人同时互动'],
    images: ['/images/products/placeholder.jpg'],
    in_stock: true,
    is_featured: false,
    price: 0
  },
  {
    name: '体感游戏互动系统',
    slug: 'motion-sensing-game-system',
    description: '无需手柄的体感游戏设备，通过全身动作控制游戏，提供健康有趣的娱乐体验。集成了先进的3D体感识别技术，支持多人协作游戏。',
    type: 'interactive_equipment',
    size: '100-500 SQM',
    style: '现代,互动',
    features: ['体感识别技术', '无线控制', '多人游戏支持', '丰富游戏内容', '健身娱乐结合'],
    images: ['/images/products/placeholder.jpg'],
    in_stock: true,
    is_featured: false,
    price: 0
  },
  {
    name: 'AR增强现实教育系统',
    slug: 'ar-education-system',
    description: '结合增强现实技术的教育系统，让学习变得更加生动有趣，提高学习效果。支持多学科内容，提供个性化学习体验。',
    type: 'indoor_playground',
    size: '100-500 SQM',
    style: '教育,科技',
    features: ['AR增强现实', '互动学习内容', '多学科支持', '实时反馈', '个性化学习'],
    images: ['/images/products/placeholder.jpg'],
    in_stock: true,
    is_featured: false,
    price: 0
  }
];

async function fixProductImages() {
  console.log('🔧 开始修复产品图片显示问题...');
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    // 首先清理现有的测试数据
    console.log('🧹 清理现有测试数据...');
    await client.query('DELETE FROM products WHERE slug LIKE \'%test%\' OR slug LIKE \'%holographic%\' OR slug LIKE \'%interactive%\' OR slug LIKE \'%motion%\' OR slug LIKE \'%ar-%\'');
    
    let successCount = 0;
    
    for (const product of testProducts) {
      try {
        console.log(`📝 创建产品: ${product.name}`);
        
        // 插入产品
        const result = await client.query(
          `INSERT INTO products 
           (name, slug, description, size, style, type, features, images, in_stock, is_featured, price) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
           RETURNING id`,
          [
            product.name,
            product.slug,
            product.description,
            product.size,
            product.style,
            product.type,
            JSON.stringify(product.features),
            JSON.stringify(product.images),
            product.in_stock,
            product.is_featured,
            product.price
          ]
        );
        
        console.log(`✅ 产品创建成功 (ID: ${result.rows[0].id})`);
        successCount++;
        
      } catch (error) {
        console.log(`❌ 产品创建失败: ${error.message}`);
      }
    }
    
    console.log('\n📊 修复结果统计:');
    console.log(`✅ 成功创建: ${successCount} 个产品`);
    
    // 验证产品是否创建成功
    const verifyResult = await client.query('SELECT name, slug, images FROM products ORDER BY id DESC LIMIT 5');
    console.log('\n📋 最新产品列表:');
    verifyResult.rows.forEach(row => {
      console.log(`  - ${row.name} (${row.slug})`);
      console.log(`    图片: ${JSON.parse(row.images || '[]').join(', ')}`);
    });
    
    client.release();
    
    console.log('\n🎉 产品图片修复完成!');
    console.log('现在您可以访问以下产品页面测试:');
    console.log('- http://localhost:3001/zh/products/holographic-restaurant-system');
    console.log('- http://localhost:3001/zh/products/interactive-floor-projection');
    console.log('- http://localhost:3001/zh/products/motion-sensing-game-system');
    console.log('- http://localhost:3001/zh/products/ar-education-system');
    
  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error.message);
  } finally {
    await pool.end();
  }
}

fixProductImages();
