/**
 * 修复剩余的导入路径问题，特别是数据库和认证模块
 */
const fs = require('fs');
const path = require('path');

// 日志函数
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m', // 青色
    success: '\x1b[32m', // 绿色
    warning: '\x1b[33m', // 黄色
    error: '\x1b[31m', // 红色
  };

  console.log(`${colors[type]}[${type.toUpperCase()}]\x1b[0m ${message}`);
}

// 修复特定文件中的导入问题
function fixSpecificFiles() {
  const rootDir = process.cwd();
  
  // 要修复的文件列表
  const files = [
    {
      path: path.join(rootDir, 'app/api/categories/featured/route.ts'),
      replacements: [
        {
          search: /from ['"]\.\.\/\.\.\/\.\.\/\.\.\/\.\.\/lib\/db['"]/g,
          replace: "from '../../../../../lib/db'",
          fallback: "// import { query } from '../../../../../lib/db'; // 暂时注释，需要修复数据库连接模块"
        }
      ]
    },
    {
      path: path.join(rootDir, 'app/api/categories/[id]/route.ts'),
      replacements: [
        {
          search: /from ['"]\.\.\/\.\.\/\.\.\/\.\.\/auth\/\[\.\.\.[a-zA-Z]+\]\/route['"]/g,
          replace: "from '../../../auth/[...nextauth]/route'",
          fallback: "// import { authOptions } from '../../../auth/[...nextauth]/route'; // 暂时注释，需要创建NextAuth配置"
        },
        {
          search: /from ['"]next-auth\/next['"]/g,
          replace: "from 'next-auth'",
          fallback: "// import { getServerSession } from 'next-auth'; // 暂时注释，需要安装next-auth"
        }
      ]
    },
    {
      path: path.join(rootDir, 'app/api/upload/route.ts'),
      replacements: [
        {
          search: /from ['"]\.\.\/auth\/\[\.\.\.[a-zA-Z]+\]\/route['"]/g,
          replace: "from '../../auth/[...nextauth]/route'",
          fallback: "// import { authOptions } from '../../auth/[...nextauth]/route'; // 暂时注释，需要创建NextAuth配置"
        }
      ]
    }
  ];
  
  // 检查lib/db.js文件是否存在
  const dbFile = path.join(rootDir, 'lib/db.js');
  const dbExists = fs.existsSync(dbFile);
  
  // 检查NextAuth配置文件是否存在
  const authDir = path.join(rootDir, 'app/api/auth/[...nextauth]');
  const authFile = path.join(authDir, 'route.js');
  const authExists = fs.existsSync(authFile) || fs.existsSync(authFile.replace('.js', '.ts'));
  
  if (!dbExists) {
    log(`警告: 数据库连接文件 ${dbFile} 不存在，将注释相关导入`, 'warning');
  }
  
  if (!authExists) {
    log(`警告: NextAuth配置文件 ${authFile} 不存在，将注释相关导入`, 'warning');
  }
  
  // 创建临时的数据库连接模块(如果不存在)
  if (!dbExists) {
    const dbDir = path.dirname(dbFile);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }
    
    const tempDbContent = `
/**
 * 临时数据库连接模块
 * 这是一个占位文件，需要根据实际数据库配置进行调整
 */
export async function query(sql, params = []) {
  console.warn('使用了临时数据库模块，请实现真正的数据库连接!');
  return { rows: [] };
}
`;
    fs.writeFileSync(dbFile, tempDbContent, 'utf8');
    log(`创建了临时数据库连接模块: ${dbFile}`, 'success');
  }
  
  // 创建临时的NextAuth配置(如果不存在)
  if (!authExists) {
    if (!fs.existsSync(authDir)) {
      fs.mkdirSync(authDir, { recursive: true });
    }
    
    const tempAuthContent = `
/**
 * 临时NextAuth配置
 * 这是一个占位文件，需要根据实际认证需求进行配置
 */
import NextAuth from 'next-auth';

export const authOptions = {
  providers: [],
  secret: process.env.NEXTAUTH_SECRET || 'temporary-secret',
  session: {
    strategy: 'jwt',
  }
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
`;
    fs.writeFileSync(path.join(authDir, 'route.js'), tempAuthContent, 'utf8');
    log(`创建了临时NextAuth配置: ${path.join(authDir, 'route.js')}`, 'success');
  }
  
  // 处理每个文件
  let fixedCount = 0;
  
  for (const file of files) {
    try {
      if (!fs.existsSync(file.path)) {
        log(`文件不存在: ${file.path}`, 'warning');
        continue;
      }
      
      let content = fs.readFileSync(file.path, 'utf8');
      let changed = false;
      
      // 应用每个替换
      for (const replacement of file.replacements) {
        if (replacement.search.test(content)) {
          // 如果目标模块不存在且有回退选项，使用回退
          if (
            (replacement.fallback && !dbExists && content.includes('lib/db')) || 
            (replacement.fallback && !authExists && content.includes('auth/[...nextauth]'))
          ) {
            content = content.replace(replacement.search, replacement.fallback);
          } else {
            content = content.replace(replacement.search, replacement.replace);
          }
          changed = true;
        }
      }
      
      // 保存修改后的文件
      if (changed) {
        fs.writeFileSync(file.path, content, 'utf8');
        log(`修复了文件: ${file.path}`, 'success');
        fixedCount++;
      } else {
        log(`无需修改: ${file.path}`, 'info');
      }
    } catch (error) {
      log(`处理文件时出错 ${file.path}: ${error.message}`, 'error');
    }
  }
  
  return fixedCount;
}

// 主函数
async function main() {
  log('开始修复剩余的导入路径问题...', 'info');
  
  const fixedCount = fixSpecificFiles();
  
  log(`修复完成。修复了 ${fixedCount} 个文件。`, 'success');
  log(`请重新启动开发服务器以应用更改。`, 'info');
}

// 执行主函数
main().catch(error => {
  log(`发生错误: ${error.message}`, 'error');
  process.exit(1);
}); 