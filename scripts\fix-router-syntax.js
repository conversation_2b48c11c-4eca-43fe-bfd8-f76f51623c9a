// 修复路由字符串中的语法错误
const fs = require('fs');
const path = require('path');

// 要搜索的目录
const adminDir = './app/[lang]/admin';

// 要修复的模式
const patterns = [
  // 修复多余的撇号
  {
    pattern: /router\.push\(['"]\/\[lang\]\/admin\/[^'"]*['"]'\)/g,
    replacement: (match) => {
      return match.replace(/['"]'\)/, ')');
    }
  }
];

// 递归处理目录
function processDirectory(dirPath) {
  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const item of items) {
      const itemPath = path.join(dirPath, item.name);

      if (item.isDirectory()) {
        processDirectory(itemPath);
      } else if (item.isFile() && (item.name.endsWith('.tsx') || item.name.endsWith('.jsx'))) {
        fixFile(itemPath);
      }
    }
  } catch (error) {
    console.error(`处理目录时出错: ${dirPath}`, error);
  }
}

// 修复单个文件
function fixFile(filePath) {
  try {
    console.log(`检查文件: ${filePath}`);
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    for (const { pattern, replacement } of patterns) {
      if (pattern.test(content)) {
        console.log(`  发现语法错误: ${filePath}`);
        content = content.replace(pattern, replacement);
        modified = true;
      }
    }

    // 直接检查常见错误格式
    if (content.includes("'/[lang]/admin/")) {
      console.log(`  检查到路径错误: ${filePath}`);
      content = content.replace(/'\/'(\[lang\]\/admin\/[^']*)''/g, "'/$1'");
      modified = true;
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
    } else {
      console.log(`  无需修改: ${filePath}`);
    }
  } catch (error) {
    console.error(`处理文件时出错: ${filePath}`, error);
  }
}

// 执行主函数
console.log('开始修复router.push语法错误...');
processDirectory(adminDir);
console.log('完成！'); 