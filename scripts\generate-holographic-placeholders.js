const fs = require('fs');
const path = require('path');

// 需要生成的全息图片列表
const holographicImages = [
  { filename: 'consultation.jpg', title: '初步咨询', color: '#1a1a2e' },
  { filename: 'concept-development.jpg', title: '概念开发', color: '#16213e' },
  { filename: '3d-visualization.jpg', title: '3D可视化', color: '#0f3460' },
  { filename: 'engineering-design.jpg', title: '详细设计与工程', color: '#533483' },
  { filename: 'installation.jpg', title: '生产与安装', color: '#e94560' },
  { filename: 'holographic-design-studio.jpg', title: '全息设计工作室', color: '#1d4ed8' },
];

// 确保目录存在
const outputDir = path.join(__dirname, '../public/images/holographic');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 简单方法创建占位符SVG文件
holographicImages.forEach(({ filename, title, color }) => {
  const svgContent = `<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="${color}" />
    <text 
      x="50%" 
      y="50%" 
      font-family="Arial, sans-serif" 
      font-size="32" 
      fill="white" 
      text-anchor="middle" 
      dominant-baseline="middle"
    >
      ${title}
    </text>
  </svg>`;

  // 我们将保存为jpg文件，以方便Next.js Image组件处理
  // 实际上这是一个伪造的JPG（实际为SVG内容），但对于开发环境已足够
  const outputPath = path.join(outputDir, filename);
  fs.writeFileSync(outputPath, svgContent);
  console.log(`Created ${outputPath}`);
});

console.log('All holographic placeholder images have been generated.');
console.log('注意: 这些是SVG内容保存为JPG文件名。在生产环境中，建议使用真正的JPG图像。');
