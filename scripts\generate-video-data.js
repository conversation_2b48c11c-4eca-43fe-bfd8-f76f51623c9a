const fs = require('fs');
const path = require('path');

// 产品名称映射（基于产品更新目录）
const productMapping = {
  '13395311668128730': { name: '咯咯魔法椅', nameEn: 'Magic Chair', category: '儿童娱乐', categoryEn: 'Children Entertainment' },
  '13395311687228488': { name: '重力玩家', nameEn: 'Gravity Player', category: '体感运动', categoryEn: 'Motion Sensing' },
  '13395311722426731': { name: '水跃星空', nameEn: 'Water Jump Starry Sky', category: '互动投影', categoryEn: 'Interactive Projection' },
  '13395311740172146': { name: '宇宙墙面', nameEn: 'Universe Wall', category: '墙面互动', categoryEn: 'Wall Interactive' },
  '13395311756835607': { name: '互动砸球', nameEn: 'Interactive Ball Smashing', category: '互动娱乐', categoryEn: 'Interactive Entertainment' },
  '13395311785464099': { name: '互动滑梯', nameEn: 'Interactive Slide', category: '儿童娱乐', categoryEn: 'Children Entertainment' },
  '13395311798953886': { name: '神笔绘画', nameEn: 'Magic Brush Painting', category: 'AR教育', categoryEn: 'AR Education' },
  '13395311832319856': { name: '互动沙池', nameEn: 'Interactive Sand Pool', category: '儿童互动', categoryEn: 'Children Interactive' },
  '13395311848006869': { name: 'AR沙桌', nameEn: 'AR Sand Table', category: 'AR技术', categoryEn: 'AR Technology' },
  '13395311866301039': { name: '互动滑板', nameEn: 'Interactive Skateboard', category: '体感运动', categoryEn: 'Motion Sensing' },
  '13395311897107446': { name: '益智流水墙', nameEn: 'Educational Water Wall', category: '智能教育', categoryEn: 'Smart Education' },
  '13395311945076997': { name: '体感大师', nameEn: 'Motion Sensing Master', category: '体感运动', categoryEn: 'Motion Sensing' },
  '13395311986456123': { name: '体感攀岩', nameEn: 'Motion Sensing Climbing', category: '体感运动', categoryEn: 'Motion Sensing' },
  '13395312019160663': { name: '跳跃格子', nameEn: 'Jumping Grid', category: '儿童互动', categoryEn: 'Children Interactive' },
  '13395312071571352': { name: '运动一体机', nameEn: 'Sports All-in-One', category: '体感运动', categoryEn: 'Motion Sensing' },
  '13395312104138908': { name: '移动射箭', nameEn: 'Mobile Archery', category: '体感运动', categoryEn: 'Motion Sensing' },
  '13395312143443916': { name: '实弹射击', nameEn: 'Live Ammunition Shooting', category: '体感运动', categoryEn: 'Motion Sensing' },
  '13395312156081604': { name: '互动足球', nameEn: 'Interactive Football', category: '体感运动', categoryEn: 'Motion Sensing' },
  '13395312211986425': { name: '魔力滚球', nameEn: 'Magic Rolling Ball', category: '互动娱乐', categoryEn: 'Interactive Entertainment' },
  '13395312244333114': { name: '人体闯关', nameEn: 'Human Adventure', category: '体感运动', categoryEn: 'Motion Sensing' },
  '13395312273652728': { name: '体感蹦床', nameEn: 'Motion Sensing Trampoline', category: '体感运动', categoryEn: 'Motion Sensing' },
  '13395312320307968': { name: '动感单车', nameEn: 'Dynamic Bicycle', category: '体感运动', categoryEn: 'Motion Sensing' },
  '13395312403438645': { name: '模拟拳击', nameEn: 'Simulated Boxing', category: '体感运动', categoryEn: 'Motion Sensing' },
  '13395312421142831': { name: '模拟网球', nameEn: 'Simulated Tennis', category: '体感运动', categoryEn: 'Motion Sensing' },
  '13395312446431334': { name: '全息宴会厅', nameEn: 'Holographic Banquet Hall', category: '全息技术', categoryEn: 'Holographic Technology' },
  '13395311818043592': { name: 'AR智慧教室', nameEn: 'AR Smart Classroom', category: 'AR教育', categoryEn: 'AR Education' }
};

// 生成视频数据
function generateVideoData() {
  const videosDir = path.join(__dirname, '../public/videos');
  const videoFiles = fs.readdirSync(videosDir).filter(file => file.endsWith('.mp4'));
  
  const videoData = videoFiles.map((file, index) => {
    const filename = path.parse(file).name;
    const mapping = productMapping[filename] || {
      name: `产品演示 ${index + 1}`,
      nameEn: `Product Demo ${index + 1}`,
      category: '技术展示',
      categoryEn: 'Technology Demo'
    };
    
    // 生成假的时长（实际开发中应该通过ffprobe等工具获取真实时长）
    const durations = ['2:15', '3:24', '2:45', '4:12', '3:56', '2:18', '5:03', '3:37', '4:28', '6:15'];
    const duration = durations[index % durations.length];
    
    // 分配分辨率（大部分视频是横屏，少数竖屏）
    const aspectRatio = index % 4 === 0 ? 'portrait' : 'landscape';
    const resolution = aspectRatio === 'landscape' ? '1920x1080' : '1080x1920';
    
    return {
      id: (index + 10).toString(), // 从10开始，避免与现有ID冲突
      title: mapping.name,
      title_en: mapping.nameEn,
      thumbnail: `/images/products/thumb-${filename}.jpg`, // 需要生成缩略图
      duration: duration,
      category: mapping.category,
      category_en: mapping.categoryEn,
      aspectRatio: aspectRatio,
      resolution: resolution,
      videoUrl: `/videos/${file}`
    };
  });
  
  return videoData;
}

// 更新VideoGallery组件
function updateVideoGallery() {
  const newVideoData = generateVideoData();
  const videoGalleryPath = path.join(__dirname, '../app/components/VideoGallery.tsx');
  
  let content = fs.readFileSync(videoGalleryPath, 'utf8');
  
  // 找到现有的videos数组并替换
  const videosArrayStart = content.indexOf('const videos: Video[] = [');
  const videosArrayEnd = content.indexOf(']', videosArrayStart) + 1;
  
  if (videosArrayStart !== -1 && videosArrayEnd !== -1) {
    const beforeArray = content.substring(0, videosArrayStart);
    const afterArray = content.substring(videosArrayEnd);
    
    // 生成新的视频数组代码
    const newVideosArray = `const videos: Video[] = [\n${newVideoData.map(video => `  {
    id: "${video.id}",
    title: "${video.title}",
    title_en: "${video.title_en}",
    thumbnail: "${video.thumbnail}",
    duration: "${video.duration}",
    category: "${video.category}",
    category_en: "${video.category_en}",
    aspectRatio: "${video.aspectRatio}",
    resolution: "${video.resolution}",
    videoUrl: "${video.videoUrl}"
  }`).join(',\n')}\n]`;
    
    const newContent = beforeArray + newVideosArray + afterArray;
    fs.writeFileSync(videoGalleryPath, newContent, 'utf8');
    
    console.log(`✅ 成功更新VideoGallery组件，添加了${newVideoData.length}个视频`);
    console.log('📹 添加的视频:');
    newVideoData.forEach((video, index) => {
      console.log(`   ${index + 1}. ${video.title} (${video.duration})`);
    });
  } else {
    console.error('❌ 无法找到videos数组位置');
  }
}

// 生成缩略图占位符提示
function generateThumbnailPlaceholders() {
  const newVideoData = generateVideoData();
  console.log('\n📸 需要生成以下缩略图文件:');
  newVideoData.forEach(video => {
    const thumbnailPath = video.thumbnail.replace('/images/', 'public/images/');
    console.log(`   ${thumbnailPath}`);
  });
  
  console.log('\n💡 提示: 您可以使用ffmpeg从视频中提取缩略图:');
  console.log('   ffmpeg -i input.mp4 -ss 00:00:01 -frames:v 1 thumbnail.jpg');
}

// 执行更新
console.log('🚀 开始更新视频数据...');
updateVideoGallery();
generateThumbnailPlaceholders();
console.log('\n✨ 视频数据更新完成！请访问 http://localhost:3000/zh/videos 查看效果'); 