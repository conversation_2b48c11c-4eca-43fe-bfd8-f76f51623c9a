/**
 * 直接导入产品数据到数据库
 */
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// 数据库连接
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
  console.error('请在.env.local文件中设置MONGODB_URI环境变量');
  process.exit(1);
}

// 产品模型
const productSchema = new mongoose.Schema(
  {
    slug: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
    },
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    size: {
      type: String,
      required: true,
    },
    style: {
      type: String,
    },
    features: [
      {
        type: String,
      },
    ],
    images: [
      {
        type: String,
      },
    ],
    type: {
      type: String,
      required: true,
    },
    categories: [
      {
        type: String,
      },
    ],
    isPublished: {
      type: Boolean,
      default: false,
    },
    translations: {
      type: Map,
      of: {
        title: String,
        description: String,
        features: [String],
      },
    },
  },
  {
    timestamps: true,
  }
);

const Product = mongoose.models.Product || mongoose.model('Product', productSchema);

// 读取产品数据
const productDataPath = path.join(__dirname, 'products-data.json');

async function importProducts() {
  try {
    // 连接数据库
    console.log('正在连接数据库...');
    await mongoose.connect(MONGODB_URI);
    console.log('数据库连接成功');

    // 读取产品数据文件
    if (!fs.existsSync(productDataPath)) {
      console.error(`产品数据文件不存在: ${productDataPath}`);
      return;
    }

    const productsData = JSON.parse(fs.readFileSync(productDataPath, 'utf8'));
    console.log(`读取了 ${productsData.length} 个产品数据`);

    // 导入结果统计
    const results = {
      total: productsData.length,
      created: 0,
      updated: 0,
      failed: 0,
      errors: [],
    };

    // 批量导入产品
    for (const productData of productsData) {
      try {
        // 确保slug值唯一且有效
        if (!productData.slug) {
          productData.slug = `product-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
        }

        // 检查产品是否已存在
        const existingProduct = await Product.findOne({ slug: productData.slug });

        if (existingProduct) {
          // 如果已存在，更新产品
          await Product.findByIdAndUpdate(existingProduct._id, productData);
          console.log(`已更新产品: ${productData.title} (${productData.slug})`);
          results.updated++;
        } else {
          // 创建新产品
          await Product.create(productData);
          console.log(`已创建产品: ${productData.title} (${productData.slug})`);
          results.created++;
        }
      } catch (error) {
        console.error(`处理产品时出错 (${productData.title || 'unknown'}):`, error.message);
        results.failed++;
        results.errors.push(`${productData.title || 'unknown'}: ${error.message}`);
      }
    }

    // 输出导入结果
    console.log('\n产品导入完成!');
    console.log(`总计: ${results.total} 个产品`);
    console.log(`创建: ${results.created} 个产品`);
    console.log(`更新: ${results.updated} 个产品`);
    console.log(`失败: ${results.failed} 个产品`);

    if (results.failed > 0) {
      console.log('\n失败详情:');
      results.errors.forEach((err, index) => {
        console.log(`${index + 1}. ${err}`);
      });
    }
  } catch (error) {
    console.error('脚本执行错误:', error);
  } finally {
    // 关闭数据库连接
    await mongoose.connection.close();
    console.log('数据库连接已关闭');
  }
}

// 运行导入
importProducts();
