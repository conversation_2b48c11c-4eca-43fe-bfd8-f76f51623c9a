// 添加产品和管理员数据到PostgreSQL数据库
const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

async function insertData() {
  const connectionString =
    'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

  const pool = new Pool({
    connectionString,
    ssl: { rejectUnauthorized: false },
  });

  try {
    const client = await pool.connect();
    console.log('连接数据库成功');

    // 检查admin_users表结构
    console.log('检查admin_users表结构...');
    const checkAdminTable = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'admin_users'
      );
    `);

    if (!checkAdminTable.rows[0].exists) {
      console.log('创建admin_users表...');
      await client.query(`
        CREATE TABLE admin_users (
          id SERIAL PRIMARY KEY,
          username VARCHAR(50) NOT NULL UNIQUE,
          email VARCHAR(100) NOT NULL UNIQUE,
          password VARCHAR(100) NOT NULL,
          role VARCHAR(20) NOT NULL DEFAULT 'admin',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
      `);
      console.log('admin_users表创建成功');
    } else {
      console.log('admin_users表已存在');
    }

    // 检查products表结构
    console.log('检查products表结构...');
    const checkProductsTable = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'products'
      );
    `);

    if (!checkProductsTable.rows[0].exists) {
      console.log('创建products表...');
      await client.query(`
        CREATE TABLE products (
          id SERIAL PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          slug VARCHAR(150) NOT NULL UNIQUE,
          description TEXT,
          price DECIMAL(10, 2),
          category VARCHAR(50),
          image_url VARCHAR(255),
          is_featured BOOLEAN DEFAULT false,
          in_stock BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
      `);
      console.log('products表创建成功');
    } else {
      console.log('products表已存在');
    }

    // 添加slug唯一约束
    console.log('检查slug唯一约束...');

    // 检查slug列是否已有唯一约束
    const slugUniqueConstraintExists = await client.query(`
      SELECT COUNT(*) FROM pg_constraint 
      WHERE conrelid = 'products'::regclass 
      AND conname LIKE '%slug%' 
      AND contype = 'u'
    `);

    if (parseInt(slugUniqueConstraintExists.rows[0].count) === 0) {
      // 先确保所有slug值不为NULL
      await client.query(`
        UPDATE products 
        SET slug = 'product-' || id 
        WHERE slug IS NULL
      `);

      console.log('添加slug唯一约束...');
      await client.query(`
        ALTER TABLE products 
        ADD CONSTRAINT products_slug_unique UNIQUE (slug)
      `);
    }

    // 添加管理员用户
    console.log('添加管理员用户...');
    // 生成密码哈希
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);

    // 检查是否已存在管理员用户
    const adminCheck = await client.query('SELECT * FROM admin_users WHERE username = $1', [
      'admin',
    ]);

    if (adminCheck.rows.length === 0) {
      await client.query(
        `
        INSERT INTO admin_users (username, email, password, role)
        VALUES ($1, $2, $3, $4)
      `,
        ['admin', '<EMAIL>', hashedPassword, 'admin']
      );
      console.log('管理员用户添加成功');
    } else {
      console.log('管理员用户已存在，跳过添加');
    }

    // 添加产品数据
    console.log('添加产品数据...');

    // 示例产品数据
    const products = [
      {
        name: '儿童互动沙滩',
        slug: 'interactive-sand-beach',
        description:
          '适合儿童的互动沙滩游乐设施，融合高科技互动技术与传统沙滩游戏，让孩子体验沙滩乐趣的同时学习知识。',
        price: 58000.0,
        category: '互动游乐',
        image_url: '/images/products/sand-beach.jpg',
        is_featured: true,
      },
      {
        name: 'AR体感蹦床',
        slug: 'ar-trampoline',
        description:
          '结合AR技术的体感蹦床，在蹦跳过程中可与虚拟内容互动，提供沉浸式游戏体验。适合各年龄段人群。',
        price: 45000.0,
        category: '体感游乐',
        image_url: '/images/products/ar-trampoline.jpg',
        is_featured: true,
      },
      {
        name: '全息沙桌',
        slug: 'holographic-sand-table',
        description:
          '利用全息投影技术，将沙桌变为交互式学习和游戏平台，可用于地理教学、沙盘模拟和创意游戏。',
        price: 65000.0,
        category: '教育娱乐',
        image_url: '/images/products/holographic-table.jpg',
        is_featured: false,
      },
      {
        name: '互动足球墙',
        slug: 'interactive-soccer-wall',
        description:
          '智能足球训练和游戏系统，墙面配备感应器和LED显示，可用于足球技能训练和互动游戏，适合运动场所。',
        price: 38000.0,
        category: '体育互动',
        image_url: '/images/products/soccer-wall.jpg',
        is_featured: false,
      },
      {
        name: '室内小型蹦床公园套餐',
        slug: 'indoor-small-trampoline-park',
        description:
          '100-500平方米的室内蹦床公园整体解决方案，包含多种蹦床区域、泡沫池和攀岩墙等设施。',
        price: 280000.0,
        category: '100-500-sqm',
        image_url: '/images/products/small-trampoline-park.jpg',
        is_featured: true,
      },
    ];

    // 检查每个产品是否已存在，不存在则添加
    for (const product of products) {
      const productCheck = await client.query(
        'SELECT * FROM products WHERE name = $1 OR slug = $2',
        [product.name, product.slug]
      );

      if (productCheck.rows.length === 0) {
        await client.query(
          `
          INSERT INTO products (name, slug, description, price, category, image_url, is_featured, in_stock)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        `,
          [
            product.name,
            product.slug,
            product.description,
            product.price,
            product.category,
            product.image_url,
            product.is_featured,
            true, // 默认有库存
          ]
        );
        console.log(`产品 "${product.name}" 添加成功`);
      } else {
        console.log(`产品 "${product.name}" 已存在，跳过添加`);
      }
    }

    // 获取并显示所有产品
    const allProducts = await client.query('SELECT * FROM products');
    console.log('\n数据库中的产品：');
    console.log(`共有 ${allProducts.rows.length} 个产品`);

    // 获取并显示所有管理员用户
    const allAdmins = await client.query('SELECT id, username, email, role FROM admin_users');
    console.log('\n数据库中的管理员用户：');
    console.log(`共有 ${allAdmins.rows.length} 个管理员用户`);
    allAdmins.rows.forEach(admin => {
      console.log(`- ${admin.username} (${admin.email}), 角色: ${admin.role || 'admin'}`);
    });

    client.release();
    await pool.end();

    console.log('\n数据添加完成！');
  } catch (error) {
    console.error('错误:', error);
  }
}

insertData().catch(console.error);
