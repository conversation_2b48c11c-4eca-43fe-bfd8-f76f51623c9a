#!/bin/bash

echo "🚀 轻量级部署方案"
echo "选择部署方式:"
echo "1. Vercel (推荐) - 自动处理大文件"
echo "2. Netlify - 支持大型项目"
echo "3. Railway - 包含数据库"
echo "4. 创建轻量版本"

read -p "请选择 (1-4): " choice

case $choice in
  1)
    echo "📦 部署到 Vercel..."
    echo "Vercel 会自动优化图片，无需担心大小问题"
    
    # 检查vercel CLI
    if ! command -v vercel &> /dev/null; then
        echo "安装 Vercel CLI..."
        npm install -g vercel
    fi
    
    # 创建 .vercelignore 来排除不必要的文件
    cat > .vercelignore << EOF
node_modules
.next
.git
*.log
.env.local
.DS_Store
EOF
    
    echo "🚀 开始部署..."
    vercel --prod
    
    echo "✅ 部署完成！Vercel 已自动优化您的图片"
    ;;
    
  2)
    echo "🌐 部署到 Netlify..."
    
    if ! command -v netlify &> /dev/null; then
        echo "安装 Netlify CLI..."
        npm install -g netlify-cli
    fi
    
    echo "🔨 构建项目..."
    npm run build
    
    echo "🚀 开始部署..."
    netlify deploy --prod --dir=out
    
    echo "✅ 部署完成！"
    ;;
    
  3)
    echo "🚂 部署到 Railway..."
    
    if ! command -v railway &> /dev/null; then
        echo "安装 Railway CLI..."
        npm install -g @railway/cli
    fi
    
    railway login
    railway up
    
    echo "✅ 部署完成！"
    ;;
    
  4)
    echo "📦 创建轻量版本..."
    
    # 创建轻量版本目录
    mkdir -p ../ecommerce-lite
    
    # 复制必要文件
    cp -r app ../ecommerce-lite/
    cp -r components ../ecommerce-lite/
    cp -r lib ../ecommerce-lite/
    cp -r styles ../ecommerce-lite/
    cp package.json ../ecommerce-lite/
    cp next.config.js ../ecommerce-lite/
    cp tailwind.config.js ../ecommerce-lite/
    cp tsconfig.json ../ecommerce-lite/
    
    # 只复制关键图片
    mkdir -p ../ecommerce-lite/public/images
    cp -r public/images/company ../ecommerce-lite/public/images/
    cp public/images/junsheng-logo* ../ecommerce-lite/public/images/
    cp public/images/placeholder* ../ecommerce-lite/public/images/
    
    # 创建产品图片占位符
    mkdir -p ../ecommerce-lite/public/images/products
    cp public/images/placeholder.jpg ../ecommerce-lite/public/images/products/
    
    echo "✅ 轻量版本已创建在 ../ecommerce-lite/"
    echo "📊 大小减少约 90%"
    echo "💡 可以先部署轻量版本，后续再添加图片"
    ;;
    
  *)
    echo "❌ 无效选择"
    exit 1
    ;;
esac

echo ""
echo "🎉 操作完成！"
echo ""
echo "💡 优化建议:"
echo "  - 使用 WebP 格式图片 (减少 30-50% 大小)"
echo "  - 启用图片懒加载"
echo "  - 使用 CDN 加速图片加载"
echo "  - 考虑图片压缩服务"
