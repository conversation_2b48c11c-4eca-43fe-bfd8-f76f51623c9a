/**
 * 将Pages Router API迁移到App Router的自动化脚本
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 定义需要迁移的API路由
const routes = [
  {
    from: 'pages/api/categories',
    to: 'app/api/categories',
    type: 'directory',
  },
  {
    from: 'pages/api/users',
    to: 'app/api/users',
    type: 'directory',
  },
  {
    from: 'pages/api/products',
    to: 'app/api/products',
    type: 'directory',
  },
  {
    from: 'pages/api/content',
    to: 'app/api/content',
    type: 'directory',
  },
  {
    from: 'pages/api/create-default-admin.ts',
    to: 'app/api/admin/create-default/route.ts',
    type: 'file',
    done: true, // 已经手动迁移完成
  },
  {
    from: 'pages/api/upload.ts',
    to: 'app/api/upload/route.ts',
    type: 'file',
  },
  {
    from: 'pages/api/test-mongodb.ts',
    to: 'app/api/db-test/mongodb/route.ts',
    type: 'file',
  },
  {
    from: 'pages/api/products-import.ts',
    to: 'app/api/products/import/route.ts',
    type: 'file',
  },
  {
    from: 'pages/api/raw-file.ts',
    to: 'app/api/content/raw-file/route.ts',
    type: 'file',
  },
  {
    from: 'pages/api/check-db.ts',
    to: 'app/api/db-test/check/route.ts',
    type: 'file',
  },
  {
    from: 'pages/api/test-db.ts',
    to: 'app/api/db-test/route.ts',
    type: 'file',
  },
];

// 日志函数
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m', // 青色
    success: '\x1b[32m', // 绿色
    warning: '\x1b[33m', // 黄色
    error: '\x1b[31m', // 红色
  };

  console.log(`${colors[type]}[${type.toUpperCase()}]\x1b[0m ${message}`);
}

// 创建目录函数
function ensureDirectoryExists(directory) {
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
    log(`Created directory: ${directory}`, 'success');
  }
}

// 将Pages API转换为App Router API格式
function convertApiCode(fileContent) {
  // 替换导入
  let newContent = fileContent
    .replace(
      /import\s+{\s*NextApiRequest,\s*NextApiResponse\s*}\s+from\s+['"]next['"];?/g,
      `import { NextRequest, NextResponse } from 'next/server';`
    )
    .replace(
      /import\s+{\s*NextApiResponse\s*}\s+from\s+['"]next['"];?/g,
      `import { NextResponse } from 'next/server';`
    )
    .replace(
      /import\s+{\s*NextApiRequest\s*}\s+from\s+['"]next['"];?/g,
      `import { NextRequest } from 'next/server';`
    );

  // 检测并转换默认导出函数
  if (newContent.includes('export default async function handler')) {
    // 检测HTTP方法
    const methodCheck = /if\s*\(\s*req\.method\s*!==\s*['"](\w+)['"]\s*\)/;
    const methodMatch = newContent.match(methodCheck);

    if (methodMatch && methodMatch[1]) {
      const httpMethod = methodMatch[1];

      // 替换默认处理函数为HTTP方法特定函数
      newContent = newContent
        .replace(
          /export\s+default\s+async\s+function\s+handler\s*\(\s*req\s*:\s*NextApiRequest\s*,\s*res\s*:\s*NextApiResponse[^)]*\)\s*{/g,
          `export async function ${httpMethod}(request: NextRequest) {`
        )
        .replace(/if\s*\(\s*req\.method\s*!==\s*['"](\w+)['"]\s*\)\s*{[^}]*}/g, '') // 移除方法检查
        .replace(/req\./g, 'request.') // 替换req为request
        .replace(/return\s+res\.status\((\d+)\)\.json\(/g, 'return NextResponse.json(') // 替换响应格式
        .replace(/res\.status\((\d+)\)\.json\(/g, 'NextResponse.json(');
    } else {
      // 如果没有检测到HTTP方法，创建通用处理函数
      newContent = newContent
        .replace(
          /export\s+default\s+async\s+function\s+handler\s*\(\s*req\s*:\s*NextApiRequest\s*,\s*res\s*:\s*NextApiResponse[^)]*\)\s*{/g,
          `export async function GET(request: NextRequest) {
  // 此函数从pages/api路由自动转换，可能需要手动调整处理其他HTTP方法
  const { method } = request;
  
  if (method !== 'GET') {
    return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
  }
`
        )
        .replace(/req\./g, 'request.') // 替换req为request
        .replace(/return\s+res\.status\((\d+)\)\.json\(/g, 'return NextResponse.json(') // 替换响应格式
        .replace(/res\.status\((\d+)\)\.json\(/g, 'NextResponse.json(');
    }
  }

  return newContent;
}

// 迁移单个API文件
function migrateApiFile(fromPath, toPath) {
  if (!fs.existsSync(fromPath)) {
    log(`Source file not found: ${fromPath}`, 'error');
    return false;
  }

  // 确保目标目录存在
  const toDir = path.dirname(toPath);
  ensureDirectoryExists(toDir);

  // 读取源文件
  const fileContent = fs.readFileSync(fromPath, 'utf8');

  // 转换代码为App Router格式
  const newContent = convertApiCode(fileContent);

  // 写入新文件
  fs.writeFileSync(toPath, newContent);
  log(`Migrated: ${fromPath} -> ${toPath}`, 'success');

  return true;
}

// 迁移目录中的所有API文件
function migrateDirectory(fromDir, toDir) {
  if (!fs.existsSync(fromDir)) {
    log(`Source directory not found: ${fromDir}`, 'error');
    return false;
  }

  // 确保目标目录存在
  ensureDirectoryExists(toDir);

  // 递归读取源目录
  function processDirectory(currentFromDir, currentToDir) {
    const files = fs.readdirSync(currentFromDir);

    files.forEach(file => {
      const fromPath = path.join(currentFromDir, file);
      const stats = fs.statSync(fromPath);

      if (stats.isDirectory()) {
        // 如果是目录，递归处理
        const newToDir = path.join(currentToDir, file);
        ensureDirectoryExists(newToDir);
        processDirectory(fromPath, newToDir);
      } else if (stats.isFile() && (file.endsWith('.ts') || file.endsWith('.js'))) {
        // 如果是TypeScript或JavaScript文件
        let toFileName = file;

        // 替换index文件为route文件
        if (file === 'index.ts' || file === 'index.js') {
          toFileName = 'route' + path.extname(file);
        } else if (!file.includes('route')) {
          // 对于非index文件且非route文件，创建子目录
          const baseName = path.basename(file, path.extname(file));
          const newToDir = path.join(currentToDir, baseName);
          ensureDirectoryExists(newToDir);
          toFileName = 'route' + path.extname(file);

          const toPath = path.join(newToDir, toFileName);
          migrateApiFile(fromPath, toPath);
          return;
        }

        const toPath = path.join(currentToDir, toFileName);
        migrateApiFile(fromPath, toPath);
      }
    });
  }

  processDirectory(fromDir, toDir);
  log(`Migrated directory: ${fromDir} -> ${toDir}`, 'success');
  return true;
}

// 主函数
async function main() {
  log('Starting API routes migration...', 'info');

  let migratedCount = 0;
  let skippedCount = 0;

  for (const route of routes) {
    if (route.done) {
      log(`Skipping (already done): ${route.from}`, 'warning');
      skippedCount++;
      continue;
    }

    if (route.type === 'file') {
      const success = migrateApiFile(route.from, route.to);
      if (success) migratedCount++;
    } else if (route.type === 'directory') {
      const success = migrateDirectory(route.from, route.to);
      if (success) migratedCount++;
    }
  }

  log(`Migration completed. Migrated: ${migratedCount}, Skipped: ${skippedCount}`, 'success');

  // 添加迁移脚本到package.json
  log('Adding migration script to package.json...', 'info');
  try {
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    const packageJson = require(packageJsonPath);

    if (!packageJson.scripts) {
      packageJson.scripts = {};
    }

    packageJson.scripts['migrate-api'] = 'node scripts/migrate-api-routes.js';

    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    log('Added "migrate-api" script to package.json', 'success');
  } catch (error) {
    log(`Failed to update package.json: ${error.message}`, 'error');
  }
}

// 执行主函数
main().catch(error => {
  log(`Migration failed: ${error.message}`, 'error');
  process.exit(1);
});
