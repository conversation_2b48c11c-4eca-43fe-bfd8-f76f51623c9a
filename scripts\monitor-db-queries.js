 const http = require('http');

let queryCount = 0;
let lastResetTime = Date.now();

function resetCounters() {
  queryCount = 0;
  lastResetTime = Date.now();
}

async function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        queryCount++;
        resolve({
          statusCode: res.statusCode,
          data: data
        });
      });
    });
    
    req.on('error', reject);
    req.setTimeout(3000, () => {
      req.destroy();
      reject(new Error('Timeout'));
    });
    
    req.end();
  });
}

async function simulateUserActivity() {
  console.log('🚀 Starting database query monitoring...\n');
  
  // 模拟用户访问不同页面
  const routes = [
    '/api/categories/featured',
    '/api/products',
    '/api/categories/featured?type=product',
  ];
  
  setInterval(async () => {
    const route = routes[Math.floor(Math.random() * routes.length)];
    try {
      await makeRequest(route);
      console.log(`📡 Request to ${route} - Total queries in last minute: ${queryCount}`);
    } catch (error) {
      console.log(`❌ Failed request to ${route}: ${error.message}`);
    }
  }, 5000); // 每5秒发送一个请求
  
  // 每分钟重置计数器并报告
  setInterval(() => {
    const elapsed = (Date.now() - lastResetTime) / 1000;
    console.log(`\n📊 Performance Report:`);
    console.log(`   ⏱️  Time period: ${elapsed.toFixed(1)}s`);
    console.log(`   🔍 Total queries: ${queryCount}`);
    console.log(`   📈 Queries per minute: ${(queryCount / elapsed * 60).toFixed(1)}`);
    console.log(`   🎯 Target: <30 queries/min for optimal performance\n`);
    
    resetCounters();
  }, 60000); // 每分钟报告一次
}

console.log('⏳ Waiting for server to be ready...');
setTimeout(() => {
  simulateUserActivity();
}, 2000); 