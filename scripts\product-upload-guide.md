# 产品上传工具使用指南

本指南介绍如何使用命令行工具上传产品到数据库。

## 工具概览

我们提供了五种不同的产品上传工具：

### 直接数据库连接工具
1. **交互式上传工具** (`command-upload-product.js`) - 适合单个产品的详细上传
2. **快速上传工具** (`single-upload-product.js`) - 适合快速上传单个产品
3. **批量上传工具** (`batch-upload-products.js`) - 适合批量上传多个产品

### API接口工具（推荐）
4. **API交互式上传工具** (`api-upload-product.js`) - 通过API上传单个产品
5. **API批量上传工具** (`api-batch-upload.js`) - 通过API批量上传产品

## 前置要求

### 直接数据库连接工具
1. 确保已安装 Node.js
2. 确保数据库连接正常
3. 确保环境变量 `DATABASE_URL` 已设置（可选，工具有默认连接）

### API接口工具（推荐）
1. 确保已安装 Node.js
2. 确保Next.js应用正在运行 (`npm run dev`)
3. 确保API端点可访问 (http://localhost:3000)

## 1. 交互式上传工具

### 使用方法
```bash
node scripts/command-upload-product.js
```

### 功能特点
- 交互式命令行界面
- 逐步引导输入产品信息
- 支持图片文件上传
- 实时验证输入数据
- 确认后再创建产品

### 使用流程
1. 运行命令后，工具会逐步提示输入：
   - 产品名称
   - 产品slug（可自动生成）
   - 产品描述
   - 产品类型（从预设选项中选择）
   - 产品尺寸（从预设选项中选择）
   - 产品风格（从预设选项中选择）
   - 产品特性（逐个输入）
   - 图片文件路径（逐个添加）
   - 发布状态
   - 产品价格

2. 最后确认所有信息后创建产品

## 2. 快速上传工具

### 使用方法
```bash
node scripts/single-upload-product.js --name "产品名称" --description "产品描述" [其他选项]
```

### 参数说明

#### 必需参数
- `--name`: 产品名称
- `--description`: 产品描述

#### 可选参数
- `--slug`: 产品slug（默认从名称生成）
- `--type`: 产品类型（默认: interactive_equipment）
- `--size`: 产品尺寸（默认: 100-500 SQM）
- `--style`: 产品风格（默认: 现代,互动）
- `--features`: 产品特性（用逗号分隔）
- `--images`: 图片URL（用逗号分隔）
- `--price`: 产品价格（默认: 0）
- `--published`: 是否发布（true/false，默认: false）

### 使用示例
```bash
# 基本用法
node scripts/single-upload-product.js \
  --name "全息投影展示台" \
  --description "先进的全息投影技术，创造令人惊叹的3D视觉效果"

# 完整参数
node scripts/single-upload-product.js \
  --name "全息投影展示台" \
  --description "先进的全息投影技术，创造令人惊叹的3D视觉效果" \
  --type "holographic_solutions" \
  --size "100-500 SQM" \
  --style "科技,未来" \
  --features "3D全息显示,高清投影,交互控制" \
  --images "/images/products/hologram-1.jpg,/images/products/hologram-2.jpg" \
  --price 0 \
  --published true
```

## 3. 批量上传工具

### 使用方法
```bash
node scripts/batch-upload-products.js <JSON文件路径>
```

### JSON文件格式
JSON文件应包含产品数组，每个产品对象包含以下字段：

```json
[
  {
    "name": "产品名称",
    "slug": "product-slug",
    "description": "产品描述",
    "type": "产品类型",
    "size": "产品尺寸",
    "style": "产品风格",
    "features": ["特性1", "特性2"],
    "images": ["/images/products/image1.jpg"],
    "keyFeatures": ["核心特性1", "核心特性2"],
    "techSpecs": {
      "resolution": "4K",
      "brightness": "5000 lumens"
    },
    "applications": ["应用场景1", "应用场景2"],
    "price": 0,
    "isPublished": true
  }
]
```

### 使用示例
```bash
# 使用示例JSON文件
node scripts/batch-upload-products.js scripts/sample-products.json

# 使用自定义JSON文件
node scripts/batch-upload-products.js /path/to/your/products.json
```

### 批量上传特点
- 自动验证每个产品数据
- 跳过已存在的产品（基于slug）
- 显示详细的上传进度
- 提供完整的结果统计
- 错误处理和回滚

## 4. API交互式上传工具（推荐）

### 使用方法
```bash
node scripts/api-upload-product.js
```

### 功能特点
- 通过API接口上传，更安全可靠
- 交互式命令行界面
- 自动验证和错误处理
- 无需直接数据库连接
- 支持所有产品字段

### 前置条件
确保Next.js应用正在运行：
```bash
npm run dev
```

## 5. API批量上传工具（推荐）

### 使用方法
```bash
node scripts/api-batch-upload.js <JSON文件路径>
```

### 使用示例
```bash
# 使用示例JSON文件
node scripts/api-batch-upload.js scripts/sample-products.json

# 使用自定义JSON文件
node scripts/api-batch-upload.js /path/to/your/products.json
```

### 功能特点
- 通过API接口批量上传
- 自动处理重复产品
- 详细的进度显示和结果统计
- 自动延迟避免API限制
- 完整的错误处理

## 产品类型选项

支持的产品类型：
- `interactive_equipment` - 互动设备系列
- `holographic_solutions` - 全息投影解决方案
- `indoor_playground` - 室内游乐场
- `trampoline_park` - 蹦床公园
- `family_entertainment_center` - 家庭娱乐中心
- `event_solutions` - 活动解决方案

## 常见问题

### Q: 推荐使用哪种工具？
A: **推荐使用API工具**（`api-upload-product.js` 和 `api-batch-upload.js`），因为：
- 更安全可靠，通过API接口操作
- 无需直接数据库连接
- 自动验证和错误处理
- 与现有系统完全兼容

### Q: 如何处理图片上传？
A:
- 直接数据库工具：
  - 交互式工具：输入本地图片文件路径，工具会自动复制到 `public/images/products/` 目录
  - 快速工具：直接提供图片URL路径
  - 批量工具：在JSON中提供图片URL路径
- API工具：直接提供图片URL路径（需要图片已存在于服务器）

### Q: API工具需要认证吗？
A: 当前版本的API工具暂时跳过了认证，主要用于开发和测试。生产环境使用时建议添加适当的认证机制。

### Q: 如果产品slug已存在怎么办？
A: 所有工具都会检查slug是否已存在，如果存在会提示错误或跳过该产品。

### Q: Next.js应用没有运行怎么办？
A: API工具需要Next.js应用运行才能工作。请先运行：
```bash
npm run dev
```

### Q: 如何自定义产品字段？
A: 可以修改工具源码中的数据库插入语句或API请求体，添加更多字段。

### Q: 批量上传失败了怎么办？
A: 工具会显示详细的错误信息和成功/失败统计，可以根据错误信息修复数据后重新上传。

## 注意事项

1. 确保数据库连接正常
2. 产品slug必须唯一
3. 图片文件路径必须存在（交互式工具）
4. JSON格式必须正确（批量工具）
5. 建议先用少量数据测试

## 示例文件

- `scripts/sample-products.json` - 示例产品数据文件
- 可以基于此文件创建自己的产品数据

## 技术支持

如果遇到问题，请检查：
1. 数据库连接是否正常
2. 输入数据格式是否正确
3. 文件路径是否存在
4. 网络连接是否稳定
