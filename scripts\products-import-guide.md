# 产品导入指南

本文档提供了如何导入我们自动生成的产品数据的详细指南。

## 前提条件

1. Next.js应用已经启动并正常运行
2. 你有管理员账户的登录凭据

## 导入步骤

### 1. 登录管理后台

访问 `/admin` 路径并使用管理员账户登录。

### 2. 使用开发者控制台导入

1. 在管理后台页面，按下 `F12` 打开浏览器开发者工具
2. 切换到"控制台"(Console)选项卡
3. 复制粘贴以下代码，并将 `products-import-2024` 替换为您的实际导入令牌：

```javascript
// 从JSON文件读取产品数据
async function fetchProductsData() {
  try {
    const response = await fetch('/api/raw-file?path=scripts/products-data.json');
    if (!response.ok) {
      throw new Error('无法读取产品数据');
    }
    return await response.json();
  } catch (error) {
    console.error('获取产品数据失败:', error);
    return [];
  }
}

// 导入产品
async function importProducts() {
  // 获取产品数据
  const products = await fetchProductsData();

  if (!products || products.length === 0) {
    console.error('没有找到产品数据');
    return;
  }

  console.log(`准备导入 ${products.length} 个产品...`);

  try {
    const response = await fetch('/api/products-import', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        importToken: 'products-import-2024', // 替换为您的实际导入令牌
        products: products,
      }),
    });

    const result = await response.json();

    if (result.success) {
      console.log('✅ 导入成功!', result);
    } else {
      console.error('❌ 导入失败:', result);
    }

    return result;
  } catch (error) {
    console.error('导入过程中发生错误:', error);
  }
}

// 执行导入
importProducts()
  .then(() => {
    console.log('导入过程已完成');
  })
  .catch(console.error);
```

4. 按下 `Enter` 键执行代码

### 3. 手动导入单个产品

如果你想手动导入产品：

1. 进入管理后台的产品管理页面 (`/admin/products`)
2. 点击"添加产品"按钮
3. 填写产品信息，包括：
   - 标题
   - 描述
   - Slug（URL友好名称）
   - 尺寸
   - 类型
   - 特点
   - 分类
4. 上传产品图片
5. 点击"保存"按钮

## 其他脚本

除了自动生成的JSON文件外，还有以下脚本可用：

- `upload-products-simple.js`: 扫描产品文件夹，复制图片并生成产品数据
- `upload-products.js`: 尝试自动API上传（可能需要额外配置）

## 故障排除

如果导入过程中出现问题：

1. 检查浏览器控制台是否有错误信息
2. 确认你有管理员权限
3. 验证导入令牌是否正确
4. 检查网络请求是否成功完成

如果问题仍然存在，请联系技术支持团队。
