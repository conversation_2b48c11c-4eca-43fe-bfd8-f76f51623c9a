// 通过API快速添加产品来测试分页功能
const testProducts = [
  {
    name: '全息投影展示台',
    slug: 'hologram-display-1',
    description: '先进的全息投影技术，创造令人惊叹的3D视觉效果',
    key_features: ['3D全息显示', '高清投影', '交互控制'],
    tech_specs: { resolution: '4K', brightness: '5000 lumens' },
    applications: ['展览展示', '商业宣传', '教育培训'],
    images: ['/images/products/hologram-1.jpg'],
    detail_images: ['/images/products/hologram-detail-1.jpg'],
    in_stock: true
  },
  {
    name: '互动地面投影',
    slug: 'interactive-floor-1',
    description: '地面互动投影系统，通过脚步触发各种视觉效果',
    key_features: ['脚步感应', '实时互动', '多种效果'],
    tech_specs: { coverage: '10平方米', sensors: '红外感应' },
    applications: ['商场娱乐', '儿童乐园', '展厅互动'],
    images: ['/images/products/floor-1.jpg'],
    detail_images: ['/images/products/floor-detail-1.jpg'],
    in_stock: true
  },
  {
    name: '体感游戏机',
    slug: 'motion-game-1',
    description: '无需手柄的体感游戏设备，全身动作控制游戏',
    key_features: ['体感识别', '无线控制', '多人游戏'],
    tech_specs: { range: '5米', accuracy: '99%' },
    applications: ['家庭娱乐', '健身房', '游戏厅'],
    images: ['/images/products/motion-1.jpg'],
    detail_images: ['/images/products/motion-detail-1.jpg'],
    in_stock: true
  }
];

async function addProducts() {
  for (const product of testProducts) {
    try {
      const response = await fetch('http://localhost:3000/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(product)
      });

      const result = await response.json();
      
      if (result.success) {
        console.log(`✅ 添加产品成功: ${product.name}`);
      } else {
        console.log(`❌ 添加产品失败: ${product.name} - ${result.message}`);
      }
    } catch (error) {
      console.log(`❌ 网络错误: ${product.name} - ${error.message}`);
    }
  }
}

// 在浏览器控制台中运行
console.log('开始添加测试产品...');
addProducts().then(() => {
  console.log('产品添加完成！');
  // 刷新页面查看结果
  setTimeout(() => {
    window.location.reload();
  }, 1000);
});
