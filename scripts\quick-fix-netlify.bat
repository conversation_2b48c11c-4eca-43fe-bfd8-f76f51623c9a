@echo off
echo 🚀 Netlify 404 快速修复方案
echo.

echo 📋 创建简化的静态版本...

REM 创建out目录
if not exist "out" mkdir out
if not exist "out\zh" mkdir out\zh
if not exist "out\zh\products" mkdir out\zh\products
if not exist "out\en" mkdir out\en
if not exist "out\admin" mkdir out\admin
if not exist "out\images" mkdir out\images

echo 📄 创建基础HTML文件...

REM 创建主页index.html
echo ^<!DOCTYPE html^> > out\index.html
echo ^<html^> >> out\index.html
echo ^<head^> >> out\index.html
echo ^<meta charset="utf-8"^> >> out\index.html
echo ^<title^>钧盛科技 - 互动设备专家^</title^> >> out\index.html
echo ^<meta http-equiv="refresh" content="0; url=/zh/"^> >> out\index.html
echo ^</head^> >> out\index.html
echo ^<body^> >> out\index.html
echo ^<p^>正在跳转到中文页面...^</p^> >> out\index.html
echo ^<script^>window.location.href='/zh/'^</script^> >> out\index.html
echo ^</body^> >> out\index.html
echo ^</html^> >> out\index.html

REM 复制主页到各个路由
copy out\index.html out\zh\index.html
copy out\index.html out\zh\products\index.html
copy out\index.html out\en\index.html
copy out\index.html out\admin\index.html

REM 复制图片文件
if exist "public\images" (
    echo 📸 复制图片文件...
    xcopy /E /I /Y public\images out\images
)

REM 复制其他静态文件
if exist "public\mock-products.json" copy public\mock-products.json out\
if exist "public\favicon.ico" copy public\favicon.ico out\

REM 确保_redirects文件在out目录中
if exist "public\_redirects" (
    copy public\_redirects out\_redirects
) else (
    echo 📝 创建_redirects文件...
    echo /*    /index.html   200 > out\_redirects
)

echo.
echo ✅ 静态文件创建完成！
echo 📁 out目录结构:
dir /s out

echo.
echo 🚀 下一步:
echo 1. 将整个 'out' 文件夹拖拽到Netlify部署区域
echo 2. 等待部署完成
echo 3. 测试网站访问
echo.

echo 💡 如果仍有问题，请尝试:
echo 1. 清除Netlify缓存
echo 2. 重新部署
echo 3. 检查浏览器控制台错误
echo.

pause
