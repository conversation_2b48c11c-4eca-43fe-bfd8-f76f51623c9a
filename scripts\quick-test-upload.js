/**
 * 快速测试产品上传
 */

const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function uploadTestProduct() {
  console.log('🚀 开始上传测试产品...');
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    // 测试产品数据
    const productData = {
      name: '测试产品_' + Date.now(),
      slug: 'test-product-' + Date.now(),
      description: '这是一个通过命令行上传的测试产品',
      size: '100-500 SQM',
      style: '现代,互动',
      type: 'interactive_equipment',
      features: ['命令行上传', '自动化测试', '数据库集成'],
      images: ['/images/products/test-1.jpg'],
      in_stock: true,
      is_featured: false,
      price: 0
    };
    
    console.log('📝 产品信息:');
    console.log(`  名称: ${productData.name}`);
    console.log(`  Slug: ${productData.slug}`);
    console.log(`  描述: ${productData.description}`);
    console.log(`  类型: ${productData.type}`);
    
    // 插入产品
    const result = await client.query(
      `INSERT INTO products 
       (name, slug, description, size, style, type, features, images, in_stock, is_featured, price) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
       RETURNING id`,
      [
        productData.name,
        productData.slug,
        productData.description,
        productData.size,
        productData.style,
        productData.type,
        JSON.stringify(productData.features),
        JSON.stringify(productData.images),
        productData.in_stock,
        productData.is_featured,
        productData.price
      ]
    );
    
    console.log(`✅ 产品创建成功! ID: ${result.rows[0].id}`);
    
    // 验证产品是否创建成功
    const verifyResult = await client.query(
      'SELECT name, slug, type FROM products WHERE id = $1',
      [result.rows[0].id]
    );
    
    if (verifyResult.rows.length > 0) {
      console.log('✅ 产品验证成功:');
      console.log(`  ID: ${result.rows[0].id}`);
      console.log(`  名称: ${verifyResult.rows[0].name}`);
      console.log(`  Slug: ${verifyResult.rows[0].slug}`);
      console.log(`  类型: ${verifyResult.rows[0].type}`);
    }
    
    client.release();
    
  } catch (error) {
    console.error('❌ 上传失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await pool.end();
  }
}

uploadTestProduct();
