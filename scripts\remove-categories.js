/**
 * 删除分类相关的数据库表和字段
 */
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// 数据库连接配置
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

async function removeCategoriesFromDatabase() {
  const client = await pool.connect();
  
  try {
    console.log('开始删除分类相关的数据库表和字段...');
    
    // 读取 SQL 文件
    const sqlPath = path.join(__dirname, 'remove-categories.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // 执行 SQL
    const result = await client.query(sql);
    
    console.log('✅ 分类相关的表和字段已成功删除');
    console.log('删除的内容包括:');
    console.log('- product_categories 表');
    console.log('- categories 表');
    console.log('- products 表中的 categories 字段');
    console.log('- products 表中的 category 字段');
    
  } catch (error) {
    console.error('❌ 删除分类时出错:', error);
    throw error;
  } finally {
    client.release();
  }
}

// 执行脚本
if (require.main === module) {
  removeCategoriesFromDatabase()
    .then(() => {
      console.log('🎉 分类删除操作完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { removeCategoriesFromDatabase };
