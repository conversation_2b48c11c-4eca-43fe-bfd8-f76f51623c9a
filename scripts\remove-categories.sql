-- 删除分类相关的数据库表和字段
-- 这个脚本将删除所有分类相关的功能

-- 1. 删除产品分类关联表
DROP TABLE IF EXISTS product_categories CASCADE;

-- 2. 删除分类表
DROP TABLE IF EXISTS categories CASCADE;

-- 3. 从产品表中删除 categories 字段（如果存在）
ALTER TABLE products DROP COLUMN IF EXISTS categories;

-- 4. 从产品表中删除 category 字段（如果存在）
ALTER TABLE products DROP COLUMN IF EXISTS category;

-- 显示操作完成信息
SELECT 'Categories and related tables/columns have been removed successfully' as status;
