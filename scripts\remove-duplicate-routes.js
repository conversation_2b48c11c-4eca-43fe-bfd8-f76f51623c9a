/**
 * 解决重复路由问题：将已迁移的Pages Router API文件移动到备份目录
 */
const fs = require('fs');
const path = require('path');

// 日志函数
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m', // 青色
    success: '\x1b[32m', // 绿色
    warning: '\x1b[33m', // 黄色
    error: '\x1b[31m', // 红色
  };

  console.log(`${colors[type]}[${type.toUpperCase()}]\x1b[0m ${message}`);
}

// 确保目录存在
function ensureDirectoryExists(directory) {
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
    log(`Created directory: ${directory}`, 'success');
  }
}

// 移动文件到备份目录
function moveFileToBackup(sourceFile, targetFile) {
  try {
    // 读取源文件内容
    const content = fs.readFileSync(sourceFile, 'utf8');

    // 写入备份文件
    fs.writeFileSync(targetFile, content);

    // 删除源文件
    fs.unlinkSync(sourceFile);

    log(`Moved to backup: ${sourceFile} -> ${targetFile}`, 'success');
    return true;
  } catch (error) {
    log(`Failed to move file ${sourceFile}: ${error.message}`, 'error');
    return false;
  }
}

// 已经迁移的API路由路径映射
const migratedRoutes = [
  { pages: 'pages/api/categories/index.ts', app: 'app/api/categories/route.ts' },
  { pages: 'pages/api/categories/[id].ts', app: 'app/api/categories/[id]/route.ts' },
  {
    pages: 'pages/api/categories/batch-update.ts',
    app: 'app/api/categories/batch-update/route.ts',
  },
  { pages: 'pages/api/categories/featured.ts', app: 'app/api/categories/featured/route.ts' },
  { pages: 'pages/api/content/index.ts', app: 'app/api/content/route.ts' },
  { pages: 'pages/api/content/[id].ts', app: 'app/api/content/[id]/route.ts' },
  { pages: 'pages/api/products/index.ts', app: 'app/api/products/route.ts' },
  { pages: 'pages/api/products/[id].ts', app: 'app/api/products/[id]/route.ts' },
  { pages: 'pages/api/users/index.ts', app: 'app/api/users/route.ts' },
  { pages: 'pages/api/users/[id].ts', app: 'app/api/users/[id]/route.ts' },
  { pages: 'pages/api/upload.ts', app: 'app/api/upload/route.ts' },
  { pages: 'pages/api/check-db.ts', app: 'app/api/db-test/check/route.ts' },
  { pages: 'pages/api/create-default-admin.ts', app: 'app/api/admin/create-default/route.ts' },
  { pages: 'pages/api/products-import.ts', app: 'app/api/products/import/route.ts' },
  { pages: 'pages/api/raw-file.ts', app: 'app/api/content/raw-file/route.ts' },
  { pages: 'pages/api/test-db.ts', app: 'app/api/db-test/route.ts' },
  { pages: 'pages/api/test-mongodb.ts', app: 'app/api/db-test/mongodb/route.ts' },
];

// 主函数
async function main() {
  log('Starting to remove duplicate routes...', 'info');

  // 创建备份目录
  const rootDir = process.cwd();
  const backupDir = path.join(rootDir, 'backup', 'pages-api');
  ensureDirectoryExists(backupDir);

  let successCount = 0;
  let skipCount = 0;
  let errorCount = 0;

  // 处理每个迁移路由
  for (const route of migratedRoutes) {
    const sourceFile = path.join(rootDir, route.pages);

    // 验证App路由是否存在
    const appFile = path.join(rootDir, route.app);
    if (!fs.existsSync(appFile)) {
      log(`App route not found: ${route.app}. Skipping migration of ${route.pages}`, 'warning');
      skipCount++;
      continue;
    }

    // 检查源文件是否存在
    if (!fs.existsSync(sourceFile)) {
      log(`Source file not found: ${sourceFile}. Already migrated?`, 'warning');
      skipCount++;
      continue;
    }

    // 构建目标路径 (保持相对路径结构)
    const relativePath = path.relative(path.join(rootDir, 'pages', 'api'), sourceFile);
    const targetDir = path.dirname(path.join(backupDir, relativePath));
    ensureDirectoryExists(targetDir);

    const targetFile = path.join(backupDir, relativePath);

    // 移动文件到备份目录
    const success = moveFileToBackup(sourceFile, targetFile);

    if (success) {
      successCount++;
    } else {
      errorCount++;
    }
  }

  // 显示结果
  log(`Operation completed.`, 'success');
  log(`Successfully moved: ${successCount}`, 'success');
  log(`Skipped: ${skipCount}`, 'info');
  log(`Errors: ${errorCount}`, 'error');

  if (successCount > 0) {
    log(`Duplicate routes have been moved to backup directory.`, 'success');
    log(`Please restart the development server to see the changes.`, 'info');
  }
}

// 执行主函数
main().catch(error => {
  log(`An error occurred: ${error.message}`, 'error');
  process.exit(1);
});
