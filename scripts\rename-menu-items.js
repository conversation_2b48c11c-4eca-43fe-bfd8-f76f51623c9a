// 脚本用于重命名两个同名菜单项
const { Pool } = require('pg');
require('dotenv').config();

async function renameSolutionMenuItems() {
  // Database connection
  const pool = new Pool({
    connectionString:
      process.env.DATABASE_URL ||
      'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
    ssl: {
      rejectUnauthorized: false,
    },
  });

  try {
    console.log('Connecting to database...');
    const client = await pool.connect();
    console.log('Connected successfully');

    // Get current solution menu items
    const beforeResult = await client.query(`
      SELECT id, name, slug, is_featured, featured_order, translations
      FROM categories 
      WHERE featured_type = 'solution' AND is_featured = true
      ORDER BY featured_order
    `);

    console.log('\nCurrent solution menu items:');
    console.table(
      beforeResult.rows.map(row => ({
        id: row.id,
        name: row.name,
        slug: row.slug,
        featured_order: row.featured_order,
      }))
    );

    // Update the menu items with new distinct names

    // Update the first item (ID 26) - custom-playground-design
    await client.query(`
      UPDATE categories
      SET 
        name = '全息定制方案', 
        translations = '{"en":{"name":"Holographic Custom Design"},"zh":{"name":"全息定制方案"}}'::jsonb
      WHERE id = 26
    `);
    console.log('Updated item with ID 26');

    // Update the second item (ID 27) - custom-solutions
    await client.query(`
      UPDATE categories
      SET 
        name = '全息解决方案',
        translations = '{"en":{"name":"Holographic Solutions"},"zh":{"name":"全息解决方案"}}'::jsonb
      WHERE id = 27
    `);
    console.log('Updated item with ID 27');

    // Get updated solution menu items
    const afterResult = await client.query(`
      SELECT id, name, slug, is_featured, featured_order, translations
      FROM categories 
      WHERE featured_type = 'solution' AND is_featured = true
      ORDER BY featured_order
    `);

    console.log('\nUpdated solution menu items:');
    console.table(
      afterResult.rows.map(row => ({
        id: row.id,
        name: row.name,
        slug: row.slug,
        featured_order: row.featured_order,
        translations: JSON.stringify(row.translations),
      }))
    );

    client.release();
    console.log('\nMenu items renamed successfully');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the function
renameSolutionMenuItems().catch(console.error);
