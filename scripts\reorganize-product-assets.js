/**
 * 将中文目录"产品"重命名为英文"products-assets"并规范化内部结构
 */
const fs = require('fs');
const path = require('path');

// 日志函数
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m', // 青色
    success: '\x1b[32m', // 绿色
    warning: '\x1b[33m', // 黄色
    error: '\x1b[31m', // 红色
  };

  console.log(`${colors[type]}[${type.toUpperCase()}]\x1b[0m ${message}`);
}

// 确保目录存在
function ensureDirectoryExists(directory) {
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
    log(`Created directory: ${directory}`, 'success');
  }
}

// 复制文件
function copyFile(source, target) {
  try {
    fs.copyFileSync(source, target);
    return true;
  } catch (error) {
    log(`Failed to copy ${source} to ${target}: ${error.message}`, 'error');
    return false;
  }
}

// 映射中文目录名到英文目录名
const categoryNameMap = {
  1: 'interactive',
  2: 'virtual-reality',
  3: 'entertainment',
  体感攀岩: 'interactive-climbing',
  互动足球: 'interactive-football',
  互动砸球: 'interactive-ball-game',
  一体机: 'all-in-one',
  保龄球: 'bowling',
  儿童互动沙滩: 'kids-interactive-beach',
  儿童互动砸球: 'kids-ball-game',
  全息沙幕: 'holographic-sand-screen',
  全息沙桌: 'holographic-sand-table',
  全息舞台: 'holographic-stage',
  全息餐厅: 'holographic-restaurant',
  宴会厅: 'banquet-hall',
  广州钧盛科技简介2025pptx: 'company-profile-2025',
};

// 主函数：重组产品资源
async function reorganizeProductAssets() {
  const rootDir = process.cwd();
  const chineseDir = path.join(rootDir, '产品');
  const englishDir = path.join(rootDir, 'products-assets');

  if (!fs.existsSync(chineseDir)) {
    log('Chinese directory "产品" not found!', 'error');
    return false;
  }

  // 创建英文目录
  ensureDirectoryExists(englishDir);
  log('Created products-assets directory', 'success');

  // 复制和重命名根级文件
  const rootFiles = fs.readdirSync(chineseDir).filter(item => {
    return !fs.statSync(path.join(chineseDir, item)).isDirectory();
  });

  for (const file of rootFiles) {
    const sourcePath = path.join(chineseDir, file);
    // 简单的文件名翻译处理
    let targetFileName = file;

    // 特殊文件名处理
    if (file.includes('原型图PSD')) {
      targetFileName = 'prototype-design.psd';
    } else if (file.includes('产品详情页')) {
      targetFileName = 'product-detail-prototype.png';
    } else if (file.includes('生成特定背景图')) {
      targetFileName = 'generated-background.png';
    } else if (file.endsWith('.rar')) {
      // 保留数字部分
      targetFileName = `assets-${file}`;
    }

    const targetPath = path.join(englishDir, targetFileName);
    copyFile(sourcePath, targetPath);
    log(`Copied ${file} -> ${targetFileName}`, 'success');
  }

  // 处理子目录
  const dirs = fs.readdirSync(chineseDir).filter(item => {
    return fs.statSync(path.join(chineseDir, item)).isDirectory();
  });

  // 首先处理主分类目录
  for (const category of dirs) {
    // 跳过RAR文件解压目录等特殊目录
    if (category === '广州钧盛科技简介2025pptx') {
      // 处理公司介绍目录
      const sourcePath = path.join(chineseDir, category);
      const targetPath = path.join(englishDir, 'company-profile');

      ensureDirectoryExists(targetPath);

      // 复制目录内容
      copyDirectoryContents(sourcePath, targetPath);
      continue;
    }

    if (!['1', '2', '3'].includes(category)) continue;

    const categoryNameEn = categoryNameMap[category] || category;
    const sourceCategoryPath = path.join(chineseDir, category);
    const targetCategoryPath = path.join(englishDir, categoryNameEn);

    ensureDirectoryExists(targetCategoryPath);
    log(`Processing category: ${category} -> ${categoryNameEn}`, 'info');

    // 处理产品子目录
    const productDirs = fs.readdirSync(sourceCategoryPath).filter(item => {
      return fs.statSync(path.join(sourceCategoryPath, item)).isDirectory();
    });

    for (const product of productDirs) {
      const productNameEn = categoryNameMap[product] || convertToEnglishName(product);
      const sourceProductPath = path.join(sourceCategoryPath, product);
      const targetProductPath = path.join(targetCategoryPath, productNameEn);

      ensureDirectoryExists(targetProductPath);
      log(`Processing product: ${product} -> ${productNameEn}`, 'info');

      // 复制产品目录内容
      copyDirectoryContents(sourceProductPath, targetProductPath);
    }
  }

  log('Product assets reorganization completed!', 'success');
  log('You can now safely rename or delete the original "产品" directory', 'info');
  return true;
}

// 辅助函数：将中文名转换为英文文件名
function convertToEnglishName(chineseName) {
  // 如果有映射，使用映射
  if (categoryNameMap[chineseName]) {
    return categoryNameMap[chineseName];
  }

  // 否则生成一个基于拼音或者其他规则的名称
  // 这里简单使用哈希来处理
  const hash = Buffer.from(chineseName).toString('hex').substring(0, 8);
  return `product-${hash}`;
}

// 辅助函数：复制目录内容
function copyDirectoryContents(sourceDir, targetDir) {
  ensureDirectoryExists(targetDir);

  const items = fs.readdirSync(sourceDir);

  for (const item of items) {
    const sourcePath = path.join(sourceDir, item);
    const targetPath = path.join(targetDir, item);

    if (fs.statSync(sourcePath).isDirectory()) {
      // 特殊处理images目录，保持原名
      if (item === 'images') {
        ensureDirectoryExists(targetPath);
        copyDirectoryContents(sourcePath, targetPath);
      } else {
        // 其他目录进行重命名
        const targetDirName = categoryNameMap[item] || convertToEnglishName(item);
        const newTargetPath = path.join(targetDir, targetDirName);
        ensureDirectoryExists(newTargetPath);
        copyDirectoryContents(sourcePath, newTargetPath);
      }
    } else {
      // 复制文件，可以选择性地重命名
      let targetFileName = item;

      // 简单的重命名规则，去除文件名中的特殊字符
      if (item.includes('未标题')) {
        const ext = path.extname(item);
        const index = item.match(/\d+/) ? item.match(/\d+/)[0] : '';
        targetFileName = `product-image${index}${ext}`;
      }

      copyFile(sourcePath, path.join(targetDir, targetFileName));
    }
  }
}

// 执行主函数
reorganizeProductAssets().catch(error => {
  log(`Product assets reorganization failed: ${error.message}`, 'error');
  process.exit(1);
});
