#!/bin/bash

# 全息投影项目图片替换脚本 (Linux/Mac)
# 下载新图片并替换现有图片

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# 目录配置
TARGET_DIR="public/images/holographic"
BACKUP_DIR="public/images/holographic/backup"
TEMP_DIR="temp_download"

# 图片配置数组
declare -a IMAGES=(
    "holographic-design-studio.jpg|https://images.unsplash.com/photo-1705656957061-d7f8416fb5f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400&q=80|600x400|全息设计工作室主图"
    "consultation.jpg|https://images.unsplash.com/photo-1622024773508-c7567eb2835b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300&q=80|600x300|初步咨询图片"
    "concept-development.jpg|https://images.unsplash.com/photo-1597600159211-d6c104f408d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300&q=80|600x300|概念开发图片"
    "3d-visualization.jpg|https://images.unsplash.com/photo-1700701982590-51ac2daee9f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300&q=80|600x300|3D可视化图片"
    "engineering-design.jpg|https://images.unsplash.com/photo-1639094313488-fdce76185229?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300&q=80|600x300|工程设计图片"
    "installation.jpg|https://images.unsplash.com/photo-1609579857457-182bbcd11230?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300&q=80|600x300|生产与安装图片"
)

# 输出函数
print_header() {
    echo -e "${MAGENTA}========================================${NC}"
    echo -e "${MAGENTA}全息投影项目图片替换工具${NC}"
    echo -e "${MAGENTA}========================================${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${CYAN}$1${NC}"
}

# 检查依赖
check_dependencies() {
    if ! command -v curl &> /dev/null && ! command -v wget &> /dev/null; then
        print_error "需要 curl 或 wget 来下载文件"
        exit 1
    fi
}

# 创建目录
ensure_directory() {
    local dir="$1"
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        print_success "创建目录: $dir"
    fi
}

# 获取文件大小（KB）
get_file_size_kb() {
    local file="$1"
    if [ -f "$file" ]; then
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            stat -f%z "$file" | awk '{print int($1/1024)}'
        else
            # Linux
            stat -c%s "$file" | awk '{print int($1/1024)}'
        fi
    else
        echo "0"
    fi
}

# 下载文件
download_file() {
    local url="$1"
    local output="$2"
    
    if command -v curl &> /dev/null; then
        curl -L -o "$output" "$url" --silent --show-error
    elif command -v wget &> /dev/null; then
        wget -O "$output" "$url" --quiet
    else
        return 1
    fi
}

# 备份现有图片
backup_existing_images() {
    print_info "\n=== 备份现有图片 ==="
    
    ensure_directory "$BACKUP_DIR"
    
    local backup_count=0
    
    for image_info in "${IMAGES[@]}"; do
        IFS='|' read -r filename url size description <<< "$image_info"
        
        local source_path="$TARGET_DIR/$filename"
        local backup_path="$BACKUP_DIR/$filename"
        
        if [ -f "$source_path" ]; then
            if cp "$source_path" "$backup_path" 2>/dev/null; then
                print_success "备份: $filename"
                ((backup_count++))
            else
                print_error "备份失败: $filename"
            fi
        else
            echo -e "${NC}  - 文件不存在: $filename"
        fi
    done
    
    print_info "备份完成: $backup_count 个文件"
}

# 下载新图片
download_new_images() {
    print_info "\n=== 下载新图片 ==="
    
    ensure_directory "$TEMP_DIR"
    ensure_directory "$TARGET_DIR"
    
    local download_count=0
    local failed_count=0
    
    for image_info in "${IMAGES[@]}"; do
        IFS='|' read -r filename url size description <<< "$image_info"
        
        echo
        print_info "处理: $description ($size)"
        
        local temp_path="$TEMP_DIR/$filename"
        local final_path="$TARGET_DIR/$filename"
        
        echo "  下载中..."
        if download_file "$url" "$temp_path"; then
            # 检查文件大小
            local file_size=$(get_file_size_kb "$temp_path")
            
            if [ "$file_size" -lt 1 ]; then
                print_error "下载的文件太小，可能下载失败"
                rm -f "$temp_path"
                ((failed_count++))
                continue
            fi
            
            # 移动到目标位置
            if mv "$temp_path" "$final_path" 2>/dev/null; then
                print_success "完成: $filename (${file_size} KB)"
                ((download_count++))
            else
                print_error "移动文件失败: $filename"
                rm -f "$temp_path"
                ((failed_count++))
            fi
        else
            print_error "下载失败: $filename"
            ((failed_count++))
        fi
    done
    
    print_info "\n下载结果: $download_count 成功, $failed_count 失败"
    return $download_count
}

# 验证替换结果
verify_replacement() {
    print_info "\n=== 验证替换结果 ==="
    
    local verified=0
    local total=${#IMAGES[@]}
    
    for image_info in "${IMAGES[@]}"; do
        IFS='|' read -r filename url size description <<< "$image_info"
        
        local image_path="$TARGET_DIR/$filename"
        
        if [ -f "$image_path" ]; then
            local file_size=$(get_file_size_kb "$image_path")
            local last_modified
            
            if [[ "$OSTYPE" == "darwin"* ]]; then
                # macOS
                last_modified=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$image_path")
            else
                # Linux
                last_modified=$(stat -c "%y" "$image_path" | cut -d'.' -f1)
            fi
            
            print_success "$filename: ${file_size} KB (修改时间: $last_modified)"
            ((verified++))
        else
            print_error "缺失: $filename"
        fi
    done
    
    print_info "\n验证完成: $verified/$total 个文件存在"
}

# 清理临时文件
cleanup_temp_files() {
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
        echo "清理临时文件完成"
    fi
}

# 主函数
main() {
    print_header
    
    echo "目标目录: $TARGET_DIR"
    echo "备份目录: $BACKUP_DIR"
    echo "临时目录: $TEMP_DIR"
    
    # 检查依赖
    check_dependencies
    
    # 确认操作
    echo
    print_warning "此操作将替换现有图片!"
    echo "原图片将备份到: $BACKUP_DIR"
    echo
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "操作已取消"
        exit 0
    fi
    
    echo
    echo "开始处理..."
    
    # 执行操作
    backup_existing_images
    download_count=$(download_new_images)
    verify_replacement
    
    # 显示完成信息
    print_info "\n=== 替换完成 ==="
    print_success "成功替换 $download_count 张图片"
    print_warning "原图片已备份到: $BACKUP_DIR"
    
    print_info "\n建议下一步:"
    echo "1. 启动开发服务器: npm run dev"
    echo "2. 访问页面检查图片显示: /zh/pages/custom-playground-design"
    echo "3. 测试响应式效果"
    
    # 清理
    cleanup_temp_files
}

# 执行主函数
main "$@"
