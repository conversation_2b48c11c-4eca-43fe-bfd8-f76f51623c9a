/**
 * 设置和配置ESLint和Prettier
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 日志函数
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m', // 青色
    success: '\x1b[32m', // 绿色
    warning: '\x1b[33m', // 黄色
    error: '\x1b[31m', // 红色
  };

  console.log(`${colors[type]}[${type.toUpperCase()}]\x1b[0m ${message}`);
}

// 更新package.json
function updatePackageJson() {
  const packageJsonPath = path.join(process.cwd(), 'package.json');

  try {
    const packageJsonContent = fs.readFileSync(packageJsonPath, 'utf8');
    const packageJson = JSON.parse(packageJsonContent);

    // 检查是否已经安装了ESLint
    const devDependencies = packageJson.devDependencies || {};

    const newDevDependencies = {
      ...devDependencies,
      'eslint-config-prettier': '^8.8.0',
      'eslint-plugin-prettier': '^4.2.1',
      prettier: '^2.8.8',
    };

    // 确保已有的依赖不被覆盖
    packageJson.devDependencies = newDevDependencies;

    // 添加lint脚本
    if (!packageJson.scripts) {
      packageJson.scripts = {};
    }

    // 检查是否已经有lint相关脚本
    if (!packageJson.scripts.format) {
      packageJson.scripts.format = "prettier --write '**/*.{js,jsx,ts,tsx,json,md}'";
    }

    // 写回package.json
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    log('Updated package.json with linting configuration', 'success');

    return true;
  } catch (error) {
    log(`Failed to update package.json: ${error.message}`, 'error');
    return false;
  }
}

// 创建或更新ESLint配置
function updateEslintConfig() {
  const eslintConfigPath = path.join(process.cwd(), '.eslintrc.json');

  try {
    let eslintConfig = {};

    // 如果配置文件已存在，读取它
    if (fs.existsSync(eslintConfigPath)) {
      try {
        const existingConfig = fs.readFileSync(eslintConfigPath, 'utf8');
        eslintConfig = JSON.parse(existingConfig);
      } catch (error) {
        log(`Failed to parse existing .eslintrc.json: ${error.message}`, 'warning');
        // 创建新的配置
        eslintConfig = {
          extends: ['next/core-web-vitals'],
        };
      }
    } else {
      // 创建新的配置
      eslintConfig = {
        extends: ['next/core-web-vitals'],
      };
    }

    // 添加Prettier配置
    if (!eslintConfig.extends.includes('prettier')) {
      eslintConfig.extends.push('prettier');
    }

    // 添加插件
    if (!eslintConfig.plugins) {
      eslintConfig.plugins = [];
    }

    if (!eslintConfig.plugins.includes('prettier')) {
      eslintConfig.plugins.push('prettier');
    }

    // 添加规则
    if (!eslintConfig.rules) {
      eslintConfig.rules = {};
    }

    eslintConfig.rules['prettier/prettier'] = 'error';
    eslintConfig.rules['no-unused-vars'] = 'warn';
    eslintConfig.rules['no-console'] = ['warn', { allow: ['warn', 'error'] }];

    // 写入配置文件
    fs.writeFileSync(eslintConfigPath, JSON.stringify(eslintConfig, null, 2));
    log('Updated .eslintrc.json with Prettier configuration', 'success');

    return true;
  } catch (error) {
    log(`Failed to update .eslintrc.json: ${error.message}`, 'error');
    return false;
  }
}

// 创建Prettier配置
function createPrettierConfig() {
  const prettierConfigPath = path.join(process.cwd(), '.prettierrc');

  const prettierConfig = {
    semi: true,
    singleQuote: true,
    tabWidth: 2,
    trailingComma: 'es5',
    printWidth: 100,
    bracketSpacing: true,
    arrowParens: 'avoid',
  };

  try {
    fs.writeFileSync(prettierConfigPath, JSON.stringify(prettierConfig, null, 2));
    log('Created .prettierrc configuration file', 'success');
    return true;
  } catch (error) {
    log(`Failed to create .prettierrc: ${error.message}`, 'error');
    return false;
  }
}

// 创建VS Code配置
function createVsCodeConfig() {
  const vscodeDir = path.join(process.cwd(), '.vscode');
  const settingsPath = path.join(vscodeDir, 'settings.json');

  // 确保.vscode目录存在
  if (!fs.existsSync(vscodeDir)) {
    fs.mkdirSync(vscodeDir, { recursive: true });
  }

  // 读取现有设置或创建新的
  let settings = {};
  if (fs.existsSync(settingsPath)) {
    try {
      const existingSettings = fs.readFileSync(settingsPath, 'utf8');
      settings = JSON.parse(existingSettings);
    } catch (error) {
      log(`Failed to parse existing VS Code settings: ${error.message}`, 'warning');
    }
  }

  // 添加或更新设置
  settings = {
    ...settings,
    'editor.formatOnSave': true,
    'editor.defaultFormatter': 'esbenp.prettier-vscode',
    'editor.codeActionsOnSave': {
      'source.fixAll.eslint': true,
    },
    '[javascript]': {
      'editor.defaultFormatter': 'esbenp.prettier-vscode',
    },
    '[typescript]': {
      'editor.defaultFormatter': 'esbenp.prettier-vscode',
    },
    '[typescriptreact]': {
      'editor.defaultFormatter': 'esbenp.prettier-vscode',
    },
  };

  try {
    fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));
    log('Created/updated VS Code settings', 'success');
    return true;
  } catch (error) {
    log(`Failed to create/update VS Code settings: ${error.message}`, 'error');
    return false;
  }
}

// 安装依赖
function installDependencies() {
  log('Installing ESLint and Prettier dependencies...', 'info');

  try {
    execSync('npm install --save-dev eslint-config-prettier eslint-plugin-prettier prettier', {
      stdio: 'inherit',
    });
    log('Dependencies installed successfully', 'success');
    return true;
  } catch (error) {
    log(`Failed to install dependencies: ${error.message}`, 'error');
    log('Please run the following command manually:', 'info');
    log('npm install --save-dev eslint-config-prettier eslint-plugin-prettier prettier', 'info');
    return false;
  }
}

// 主函数
async function main() {
  log('Setting up ESLint and Prettier...', 'info');

  // 更新package.json
  updatePackageJson();

  // 更新ESLint配置
  updateEslintConfig();

  // 创建Prettier配置
  createPrettierConfig();

  // 创建VS Code配置
  createVsCodeConfig();

  // 安装依赖 (可选，取决于用户)
  console.log('');
  log('Setup completed!', 'success');
  log('Run the following command to install dependencies:', 'info');
  log('npm install --save-dev eslint-config-prettier eslint-plugin-prettier prettier', 'info');
  log('Or run this script with the --install flag to install dependencies automatically', 'info');

  log('You can now run ESLint and Prettier with:', 'info');
  log('npm run lint', 'info');
  log('npm run format', 'info');
}

// 检查是否自动安装依赖
if (process.argv.includes('--install')) {
  installDependencies();
}

// 执行主函数
main().catch(error => {
  log(`Setup failed: ${error.message}`, 'error');
  process.exit(1);
});
