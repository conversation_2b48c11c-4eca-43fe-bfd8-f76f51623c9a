/**
 * 数据库表创建脚本
 * 用于在PostgreSQL中创建必要的表结构
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// 从环境变量或使用默认连接信息
const connectionString =
  process.env.POSTGRES_URI ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 创建连接池
const pool = new Pool({
  connectionString,
  ssl: { rejectUnauthorized: false },
});

// 读取SQL文件
const sqlFilePath = path.join(__dirname, 'create-tables.sql');
const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

async function setupTables() {
  console.log('开始创建数据库表...');

  let client;
  try {
    client = await pool.connect();

    // 执行SQL创建表
    await client.query(sqlContent);
    console.log('表创建成功！');

    // 检查用户表中是否已存在管理员
    const adminResult = await client.query('SELECT * FROM users WHERE role = $1 LIMIT 1', [
      'admin',
    ]);

    // 如果没有管理员，创建默认管理员
    if (adminResult.rows.length === 0) {
      // 需要引入bcrypt进行密码哈希
      const bcrypt = require('bcryptjs');
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123', salt);

      await client.query(
        `INSERT INTO users (username, email, password_hash, role) 
         VALUES ($1, $2, $3, $4)`,
        ['admin', '<EMAIL>', hashedPassword, 'admin']
      );
      console.log('创建了默认管理员用户: <EMAIL>');
    } else {
      console.log('管理员用户已存在，无需创建');
    }
  } catch (error) {
    console.error('创建表时出错:', error);
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
}

setupTables().catch(console.error);
