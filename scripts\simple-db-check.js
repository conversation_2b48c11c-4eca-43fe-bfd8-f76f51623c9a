const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function simpleCheck() {
  console.log('🔍 简单数据库检查...');
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    // 检查表是否存在
    const tableCheck = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'products'
    `);
    
    if (tableCheck.rows.length === 0) {
      console.log('❌ products 表不存在');
      
      // 显示所有表
      const allTables = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `);
      
      console.log('📋 数据库中的所有表:');
      allTables.rows.forEach(row => {
        console.log(`   - ${row.table_name}`);
      });
      
    } else {
      console.log('✅ products 表存在');
      
      // 检查表结构
      const columns = await client.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'products'
        ORDER BY ordinal_position
      `);
      
      console.log('📋 products 表结构:');
      columns.rows.forEach(col => {
        console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      });
      
      // 检查产品数量
      const count = await client.query('SELECT COUNT(*) as total FROM products');
      console.log(`\n📊 产品总数: ${count.rows[0].total}`);
      
      if (parseInt(count.rows[0].total) > 0) {
        // 显示前几个产品
        const products = await client.query('SELECT id, name, slug FROM products LIMIT 5');
        console.log('\n📋 前5个产品:');
        products.rows.forEach(product => {
          console.log(`   ${product.id}. ${product.name} (${product.slug})`);
        });
      }
    }
    
    client.release();
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    await pool.end();
  }
}

simpleCheck();
