const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function testUpload() {
  console.log('🚀 测试产品上传...');
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    // 添加一个测试产品
    const result = await client.query(
      `INSERT INTO products 
       (name, slug, description, type, features, images, in_stock, is_featured, price) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
       RETURNING id`,
      [
        'AR体感蹦床',
        'ar-motion-trampoline',
        'AR增强现实体感蹦床，结合虚拟现实技术与体感运动，为用户提供全新的娱乐健身体验。',
        'interactive_equipment',
        JSON.stringify(['AR增强现实', '体感识别', '运动健身', '多人互动', '安全防护']),
        JSON.stringify(['/images/products/ar-trampoline-1.jpg', '/images/products/ar-trampoline-2.jpg']),
        true,
        true,
        0
      ]
    );
    
    console.log('✅ 产品创建成功，ID:', result.rows[0].id);
    client.release();
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
  } finally {
    await pool.end();
  }
}

testUpload();
