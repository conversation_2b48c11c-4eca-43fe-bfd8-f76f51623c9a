const { exec } = require('child_process');
const os = require('os');

// 获取本机IP地址
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

// 设置环境变量并启动开发服务器
function startWithNetworkAccess() {
  const localIP = getLocalIP();
  console.log(`🌐 检测到本机IP地址: ${localIP}`);
  console.log(`🚀 启动网络访问模式...`);
  console.log(`📱 朋友可以通过以下地址访问: http://${localIP}:3000`);
  
  // 设置环境变量
  process.env.NEXTAUTH_URL = `http://${localIP}:3000`;
  process.env.NEXT_PUBLIC_API_URL = `http://${localIP}:3000/api`;
  process.env.NEXT_PUBLIC_BASE_URL = `http://${localIP}:3000`;
  process.env.ALLOWED_ORIGINS = `http://localhost:3000,http://${localIP}:3000`;
  
  // 启动Next.js开发服务器，绑定到所有接口
  const command = 'next dev -H 0.0.0.0 -p 3000';
  
  console.log(`\n🔧 执行命令: ${command}`);
  console.log(`\n环境变量设置:`);
  console.log(`NEXTAUTH_URL=${process.env.NEXTAUTH_URL}`);
  console.log(`NEXT_PUBLIC_API_URL=${process.env.NEXT_PUBLIC_API_URL}`);
  console.log(`ALLOWED_ORIGINS=${process.env.ALLOWED_ORIGINS}`);
  console.log('\n');
  
  const child = exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`执行错误: ${error}`);
      return;
    }
    if (stderr) {
      console.error(`stderr: ${stderr}`);
    }
    console.log(`stdout: ${stdout}`);
  });
  
  // 将子进程的输出转发到当前进程
  child.stdout.on('data', (data) => {
    process.stdout.write(data);
  });
  
  child.stderr.on('data', (data) => {
    process.stderr.write(data);
  });
  
  // 处理进程终止
  process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    child.kill('SIGINT');
    process.exit(0);
  });
}

startWithNetworkAccess(); 