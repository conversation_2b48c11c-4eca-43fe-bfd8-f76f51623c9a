const http = require('http');

async function testAPIRoute(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: data,
          headers: res.headers
        });
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

async function runTests() {
  console.log('🧪 Testing API Routes...\n');
  
  const tests = [
    '/api/categories/featured',
    '/api/categories/featured?t=' + Date.now(),
    '/api/categories/1', // 假设有ID为1的分类
    '/api/categories/999', // 不存在的ID
  ];
  
  for (const path of tests) {
    try {
      console.log(`Testing: ${path}`);
      const result = await testAPIRoute(path);
      console.log(`✅ Status: ${result.statusCode}`);
      
      if (result.statusCode === 200) {
        try {
          const jsonData = JSON.parse(result.data);
          console.log(`📊 Response: ${jsonData.success ? 'Success' : 'Failed'}`);
          if (jsonData.data) {
            console.log(`📈 Data count: ${Array.isArray(jsonData.data) ? jsonData.data.length : 'Single object'}`);
          }
        } catch (e) {
          console.log(`❌ Invalid JSON response`);
        }
      } else {
        console.log(`❌ Response: ${result.data}`);
      }
      
      console.log('---');
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      console.log('---');
    }
  }
}

// 等待服务器启动
setTimeout(() => {
  runTests().then(() => {
    console.log('🎉 API route testing completed');
  }).catch(err => {
    console.error('💥 Test runner failed:', err);
  });
}, 3000); // 等待3秒让服务器启动 