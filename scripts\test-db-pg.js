const { Pool } = require('pg');

async function testConnection() {
  // 连接配置
  const config = {
    user: 'neondb_owner',
    password: 'npg_a0MKVbjDpO7B',
    host: 'ep-lucky-boat-a1yjp7be-pooler.ap-southeast-1.aws.neon.tech',
    database: 'neondb',
    ssl: {
      rejectUnauthorized: false,
      sslmode: 'require',
    },
  };

  console.log('Connecting with config:', JSON.stringify({ ...config, password: '***' }));

  const pool = new Pool(config);

  try {
    console.log('Attempting to connect to database...');
    const result = await pool.query('SELECT 1 as test');
    console.log('Database connection successful:', result.rows);
    await pool.end();
    return { success: true, message: 'Database connection successful', data: result.rows };
  } catch (error) {
    console.error('Database connection error:', error);
    await pool.end();
    process.exit(1);
  }
}

testConnection()
  .then(result => {
    console.log('Connection test result:', result);
  })
  .catch(error => {
    console.error('Script error:', error);
  });
