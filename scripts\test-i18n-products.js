const fetch = require('node-fetch');

async function testProductsI18n() {
  const baseUrl = 'http://localhost:3003';
  
  console.log('🧪 测试产品国际化功能...\n');
  
  try {
    // 测试英文版本
    console.log('📝 测试英文版本 API...');
    const enResponse = await fetch(`${baseUrl}/api/products?lang=en&limit=3`);
    const enData = await enResponse.json();
    
    if (enData.products && enData.products.length > 0) {
      console.log('✅ 英文版本 API 正常工作');
      console.log(`   产品数量: ${enData.products.length}`);
      console.log(`   第一个产品名称: ${enData.products[0].name}`);
      console.log(`   第一个产品描述: ${enData.products[0].description.substring(0, 50)}...`);
    } else {
      console.log('❌ 英文版本 API 返回空数据');
    }
    
    console.log('');
    
    // 测试中文版本
    console.log('📝 测试中文版本 API...');
    const zhResponse = await fetch(`${baseUrl}/api/products?lang=zh&limit=3`);
    const zhData = await zhResponse.json();
    
    if (zhData.products && zhData.products.length > 0) {
      console.log('✅ 中文版本 API 正常工作');
      console.log(`   产品数量: ${zhData.products.length}`);
      console.log(`   第一个产品名称: ${zhData.products[0].name}`);
      console.log(`   第一个产品描述: ${zhData.products[0].description.substring(0, 30)}...`);
    } else {
      console.log('❌ 中文版本 API 返回空数据');
    }
    
    console.log('');
    
    // 比较两个版本的差异
    if (enData.products && zhData.products && 
        enData.products.length > 0 && zhData.products.length > 0) {
      
      console.log('🔍 比较英文和中文版本差异:');
      console.log(`   英文产品名: ${enData.products[0].name}`);
      console.log(`   中文产品名: ${zhData.products[0].name}`);
      
      if (enData.products[0].name !== zhData.products[0].name) {
        console.log('✅ 产品名称国际化正常工作！');
      } else {
        console.log('⚠️  产品名称没有国际化差异');
      }
      
      if (enData.products[0].description !== zhData.products[0].description) {
        console.log('✅ 产品描述国际化正常工作！');
      } else {
        console.log('⚠️  产品描述没有国际化差异');
      }
    }
    
    console.log('\n🎉 产品国际化测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
  }
}

// 运行测试
testProductsI18n();
