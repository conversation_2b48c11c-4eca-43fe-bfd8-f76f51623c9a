const fs = require('fs');
const path = require('path');

console.log('🧪 测试模拟数据...\n');

try {
  // 1. 检查模拟数据文件
  const mockDataPath = 'data/mock-products.json';
  if (!fs.existsSync(mockDataPath)) {
    console.error('❌ 模拟数据文件不存在:', mockDataPath);
    process.exit(1);
  }
  
  console.log('✅ 模拟数据文件存在');
  
  // 2. 读取并解析模拟数据
  const mockData = JSON.parse(fs.readFileSync(mockDataPath, 'utf8'));
  console.log(`✅ 模拟数据解析成功，包含 ${mockData.length} 个产品`);
  
  // 3. 验证数据结构
  const requiredFields = ['id', 'name', 'slug', 'description', 'images'];
  let validProducts = 0;
  let totalImages = 0;
  
  mockData.forEach((product, index) => {
    const missingFields = requiredFields.filter(field => !product[field]);
    
    if (missingFields.length === 0) {
      validProducts++;
      if (product.images && Array.isArray(product.images)) {
        totalImages += product.images.length;
      }
    } else {
      console.log(`⚠️  产品 ${index + 1} (${product.name}) 缺少字段: ${missingFields.join(', ')}`);
    }
  });
  
  console.log(`✅ 有效产品数: ${validProducts}/${mockData.length}`);
  console.log(`✅ 总图片数: ${totalImages}`);
  
  // 4. 检查图片文件是否存在
  console.log('\n📷 检查图片文件...');
  const imagesDir = 'public/images/products';
  let existingImages = 0;
  let missingImages = 0;
  
  if (fs.existsSync(imagesDir)) {
    const allImagePaths = new Set();
    
    // 收集所有图片路径
    mockData.forEach(product => {
      if (product.images && Array.isArray(product.images)) {
        product.images.forEach(imagePath => {
          allImagePaths.add(imagePath);
        });
      }
    });
    
    // 检查每个图片文件
    allImagePaths.forEach(imagePath => {
      const fullPath = path.join('public', imagePath);
      if (fs.existsSync(fullPath)) {
        existingImages++;
      } else {
        missingImages++;
        console.log(`❌ 图片不存在: ${imagePath}`);
      }
    });
    
    console.log(`✅ 存在的图片: ${existingImages}`);
    console.log(`❌ 缺失的图片: ${missingImages}`);
  } else {
    console.log('❌ 图片目录不存在:', imagesDir);
  }
  
  // 5. 显示产品分类统计
  console.log('\n📊 产品分类统计:');
  const categories = {};
  const featuredCount = mockData.filter(p => p.is_featured).length;
  
  mockData.forEach(product => {
    const category = product.category || '未分类';
    categories[category] = (categories[category] || 0) + 1;
  });
  
  Object.entries(categories).forEach(([category, count]) => {
    console.log(`   ${category}: ${count} 个产品`);
  });
  
  console.log(`\n⭐ 特色产品: ${featuredCount} 个`);
  
  // 6. 显示前5个产品示例
  console.log('\n📋 前5个产品示例:');
  mockData.slice(0, 5).forEach((product, index) => {
    const featured = product.is_featured ? '⭐' : '  ';
    const imageCount = product.images ? product.images.length : 0;
    console.log(`   ${index + 1}. ${featured} ${product.name}`);
    console.log(`      Slug: ${product.slug}`);
    console.log(`      分类: ${product.category}`);
    console.log(`      图片: ${imageCount} 张`);
    console.log(`      价格: ¥${product.price}`);
    console.log('');
  });
  
  // 7. 检查slug唯一性
  console.log('🔍 检查slug唯一性...');
  const slugs = mockData.map(p => p.slug);
  const uniqueSlugs = new Set(slugs);
  
  if (slugs.length === uniqueSlugs.size) {
    console.log('✅ 所有slug都是唯一的');
  } else {
    console.log('❌ 发现重复的slug');
    const duplicates = slugs.filter((slug, index) => slugs.indexOf(slug) !== index);
    console.log('重复的slug:', [...new Set(duplicates)]);
  }
  
  console.log('\n🎉 模拟数据测试完成!');
  
  if (missingImages === 0 && validProducts === mockData.length) {
    console.log('✅ 所有数据和图片都准备就绪，可以正常使用！');
  } else {
    console.log('⚠️  存在一些问题，但基本功能应该可以正常工作');
  }
  
} catch (error) {
  console.error('❌ 测试失败:', error.message);
  process.exit(1);
}
