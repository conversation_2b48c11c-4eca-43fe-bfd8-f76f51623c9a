const { Pool } = require('pg');
require('dotenv').config();

const connectionString = process.env.POSTGRES_URI || 
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

const pool = new Pool({
  connectionString,
  ssl: { rejectUnauthorized: false },
});

async function testProductAPI() {
  let client;

  try {
    client = await pool.connect();
    console.log('数据库连接成功');

    // 检查产品表结构
    const tableStructure = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'products' 
      ORDER BY ordinal_position
    `);

    console.log('\n产品表结构:');
    tableStructure.rows.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type}`);
    });

    // 检查产品数量
    const countResult = await client.query('SELECT COUNT(*) as count FROM products');
    console.log(`\n产品总数: ${countResult.rows[0].count}`);

    // 获取第一个产品的详细信息
    const productResult = await client.query('SELECT * FROM products LIMIT 1');
    
    if (productResult.rows.length > 0) {
      console.log('\n第一个产品的数据:');
      console.log(JSON.stringify(productResult.rows[0], null, 2));
    } else {
      console.log('\n没有找到产品数据');
    }

  } catch (error) {
    console.error('测试时出错:', error);
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
}

testProductAPI().catch(console.error);
