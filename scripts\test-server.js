const http = require('http');

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/',
  method: 'GET',
};

console.log('Testing connection to development server...');

const req = http.request(options, res => {
  console.log(`Status: ${res.statusCode}`);
  if (res.statusCode === 200) {
    console.log('Server is running correctly!');
  } else {
    console.log('Server responded with non-200 status code');
  }

  let data = '';
  res.on('data', chunk => {
    data += chunk;
  });

  res.on('end', () => {
    console.log(`Response size: ${data.length} bytes`);
  });
});

req.on('error', error => {
  console.error('Error connecting to server:', error.message);
});

req.end();
