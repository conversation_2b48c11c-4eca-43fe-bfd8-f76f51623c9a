/**
 * 测试数据库连接和产品上传
 */

const { Pool } = require('pg');
require('dotenv').config();

// 数据库连接配置
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 
    'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function testConnection() {
  console.log('🔍 测试数据库连接...');
  
  try {
    // 测试基本连接
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    // 检查products表是否存在
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'products'
      ) as exists
    `);
    
    if (tableCheck.rows[0].exists) {
      console.log('✅ products表存在');
      
      // 检查表结构
      const columnsResult = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'products' 
        ORDER BY ordinal_position
      `);
      
      console.log('\n📋 products表结构:');
      columnsResult.rows.forEach(row => {
        console.log(`  - ${row.column_name}: ${row.data_type} ${row.is_nullable === 'NO' ? '(NOT NULL)' : ''}`);
      });
      
      // 检查现有产品数量
      const countResult = await client.query('SELECT COUNT(*) as count FROM products');
      console.log(`\n📊 现有产品数量: ${countResult.rows[0].count}`);
      
    } else {
      console.log('❌ products表不存在');
    }
    
    client.release();
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await pool.end();
  }
}

async function testProductCreation() {
  console.log('\n🧪 测试产品创建...');
  
  try {
    const client = await pool.connect();
    
    // 测试产品数据
    const testProduct = {
      name: '测试产品_' + Date.now(),
      slug: 'test-product-' + Date.now(),
      description: '这是一个测试产品',
      size: '100-500 SQM',
      style: '现代,互动',
      type: 'interactive_equipment',
      features: ['测试特性1', '测试特性2'],
      images: ['/images/test.jpg'],
      in_stock: true,
      is_featured: false,
      price: 0
    };
    
    console.log('📝 创建测试产品:', testProduct.name);
    
    const result = await client.query(
      `INSERT INTO products 
       (name, slug, description, size, style, type, features, images, in_stock, is_featured, price) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
       RETURNING id`,
      [
        testProduct.name,
        testProduct.slug,
        testProduct.description,
        testProduct.size,
        testProduct.style,
        testProduct.type,
        JSON.stringify(testProduct.features),
        JSON.stringify(testProduct.images),
        testProduct.in_stock,
        testProduct.is_featured,
        testProduct.price
      ]
    );
    
    console.log(`✅ 测试产品创建成功! ID: ${result.rows[0].id}`);
    
    client.release();
    
  } catch (error) {
    console.error('❌ 产品创建失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await pool.end();
  }
}

async function main() {
  console.log('🚀 数据库连接和产品上传测试');
  console.log('================================');
  
  await testConnection();
  
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  rl.question('\n是否继续测试产品创建? (y/N): ', async (answer) => {
    if (answer.toLowerCase() === 'y') {
      await testProductCreation();
    }
    rl.close();
  });
}

main().catch(console.error);
