// 脚本用于更新菜单项名称和描述，使其匹配全息投影相关业务
const { Pool } = require('pg');
require('dotenv').config();

async function updateMenuNames() {
  // Database connection with SSL configuration
  const pool = new Pool({
    connectionString:
      process.env.DATABASE_URL ||
      'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
    ssl: {
      rejectUnauthorized: false,
    },
  });

  try {
    console.log('Connecting to database...');
    const client = await pool.connect();
    console.log('Connected successfully');

    // Check existing categories with target slugs
    const checkResult = await client.query(`
      SELECT id, name, slug, is_featured, featured_type
      FROM categories
      WHERE slug IN ('custom-playground-design', 'custom-solutions')
      ORDER BY id
    `);

    console.log('\nExisting menu items:');
    checkResult.rows.forEach(item => {
      console.log(
        `ID: ${item.id}, Name: ${item.name}, Slug: ${item.slug}, Featured: ${item.is_featured}, Type: ${item.featured_type || 'null'}`
      );
    });

    // Update names with explicit IDs to ensure we're updating the correct records
    console.log('\nUpdating menu items with very distinct names...');

    // Get categories by slug to find their IDs
    const customPlaygroundItem = checkResult.rows.find(
      row => row.slug === 'custom-playground-design'
    );
    const customSolutionsItem = checkResult.rows.find(row => row.slug === 'custom-solutions');

    if (customPlaygroundItem) {
      await client.query(
        `
        UPDATE categories
        SET 
          name = '全息定制专区',
          translations = '{"zh":{"name":"全息定制专区"},"en":{"name":"Holographic Custom Zone"}}'::jsonb
        WHERE id = $1
      `,
        [customPlaygroundItem.id]
      );
      console.log(`Updated ID ${customPlaygroundItem.id}: custom-playground-design → 全息定制专区`);
    } else {
      console.log('No custom-playground-design item found to update');
    }

    if (customSolutionsItem) {
      await client.query(
        `
        UPDATE categories
        SET 
          name = '全息展示方案',
          translations = '{"zh":{"name":"全息展示方案"},"en":{"name":"Holographic Display Solutions"}}'::jsonb
        WHERE id = $1
      `,
        [customSolutionsItem.id]
      );
      console.log(`Updated ID ${customSolutionsItem.id}: custom-solutions → 全息展示方案`);
    } else {
      console.log('No custom-solutions item found to update');
    }

    // Fix ID 31 English translation
    console.log('\nFixing English translation for ID 31...');

    await client.query(`
      UPDATE categories
      SET 
        translations = '{"zh":{"name":"全息展示方案"},"en":{"name":"Holographic Display Solutions"}}'::jsonb
      WHERE id = 31
    `);

    // Verify changes
    const verifyResult = await client.query(`
      SELECT id, name, slug, translations
      FROM categories
      WHERE id = 31
    `);

    console.log('\nUpdated menu item:');
    if (verifyResult.rows.length > 0) {
      const item = verifyResult.rows[0];
      console.log(`ID: ${item.id}, Name: ${item.name}, Slug: ${item.slug}`);
      console.log(`Translations: ${JSON.stringify(item.translations, null, 2)}`);
    } else {
      console.log('Item not found');
    }

    client.release();
    console.log('\nMenu items updated successfully');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the function
updateMenuNames().catch(console.error);
