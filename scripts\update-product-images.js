/**
 * 更新所有产品的图片字段
 * 确保每个产品都有合适的图片集和详情图片
 */
const { Pool } = require('pg');
require('dotenv').config();
const fs = require('fs');
const path = require('path');

// 数据库连接信息
const connectionString =
  process.env.POSTGRES_URI ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

const pool = new Pool({
  connectionString,
  ssl: { rejectUnauthorized: false },
});

// 获取产品图片目录中的所有图片
function getProductImages() {
  const productImagesDir = path.join(process.cwd(), 'public', 'images', 'products');
  try {
    const files = fs.readdirSync(productImagesDir);
    return files
      .filter(file =>
        ['.jpg', '.jpeg', '.png', '.webp', '.gif'].includes(path.extname(file).toLowerCase())
      )
      .map(file => `/images/products/${file}`);
  } catch (error) {
    console.error('读取产品图片目录失败:', error);
    return [];
  }
}

async function updateProductImages() {
  let client;

  try {
    client = await pool.connect();
    console.log('数据库连接成功');

    // 获取所有产品
    const result = await client.query(`
      SELECT id, name, slug, image_url, images, detail_images
      FROM products
    `);

    if (result.rows.length === 0) {
      console.log('没有找到产品');
      return;
    }

    console.log(`找到 ${result.rows.length} 个产品需要更新`);

    // 获取所有产品图片
    const allProductImages = getProductImages();
    console.log(`找到 ${allProductImages.length} 张产品图片`);

    // 遍历产品并更新
    for (const product of result.rows) {
      console.log(`正在更新产品: ${product.name} (ID: ${product.id})`);

      // 检查图片集
      let images = [];
      try {
        if (product.images && typeof product.images === 'string') {
          images = JSON.parse(product.images);
        }
      } catch (e) {
        console.error(`解析images失败: ${e.message}`);
      }

      // 检查详情图片
      let detailImages = [];
      try {
        if (product.detail_images && typeof product.detail_images === 'string') {
          detailImages = JSON.parse(product.detail_images);
        }
      } catch (e) {
        console.error(`解析detail_images失败: ${e.message}`);
      }

      // 如果图片集为空，则使用主图片
      if (!images.length && product.image_url) {
        images = [product.image_url];
      }

      // 如果详情图片为空，则使用主图片或其他产品图片
      if (!detailImages.length) {
        // 首先添加产品自己的图片
        if (product.image_url) {
          detailImages.push(product.image_url);
        }

        // 再添加一些其他产品图片（确保至少有3张详情图）
        const productSlug = product.slug || '';
        const additionalImages = allProductImages
          .filter(img => img !== product.image_url && !images.includes(img))
          .slice(0, 4);

        detailImages = [...detailImages, ...additionalImages];
      }

      // 确保有主图片
      let imageUrl = product.image_url;
      if (!imageUrl && allProductImages.length > 0) {
        // 使用第一张图片作为主图
        imageUrl = allProductImages[0];
      }

      // 更新产品字段
      await client.query(
        `
        UPDATE products 
        SET 
          image_url = $1,
          images = $2,
          detail_images = $3
        WHERE id = $4
      `,
        [imageUrl, JSON.stringify(images), JSON.stringify(detailImages), product.id]
      );

      console.log(`产品 ${product.name} 更新成功`);
    }

    console.log('所有产品图片更新完成!');
  } catch (error) {
    console.error('更新产品图片时出错:', error);
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
}

updateProductImages().catch(console.error);
