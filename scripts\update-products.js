/**
 * 更新现有产品，添加全息投影产品属性
 */
const { Pool } = require('pg');
require('dotenv').config();

// 数据库连接信息
const connectionString =
  process.env.POSTGRES_URI ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

const pool = new Pool({
  connectionString,
  ssl: { rejectUnauthorized: false },
});

// 默认技术规格JSON对象
const defaultTechSpecs = {
  resolution: '1920 x 1080 (Full HD)',
  brightness: '3000 ANSI Lumens',
  projection_distance: '1.5m - 5m',
  dimensions: '400 x 300 x 100 mm',
  weight: '5 kg',
  power: 'AC 100-240V, 50/60Hz',
  interface: 'HDMI, USB, LAN, Wi-Fi',
  compatible_systems: ['Windows', 'macOS', 'Android'],
};

// 默认核心特点
const defaultKeyFeatures = [
  '高分辨率立体投影技术',
  '多角度无缝显示',
  '交互式触控体验',
  '云端内容管理系统',
  '远程更新内容',
];

// 默认应用场景
const defaultApplications = [
  '博物馆展览',
  '商业展示',
  '教育培训',
  '舞台表演',
  '活动策划',
  '科技展厅',
];

// 默认安装选项
const defaultInstallationOptions = ['吊装式安装', '壁挂式安装', '地面支架安装', '嵌入式安装'];

async function updateProducts() {
  let client;

  try {
    client = await pool.connect();
    console.log('数据库连接成功');

    // 获取所有产品
    const productsResult = await client.query(`
      SELECT id, name, category FROM products
    `);

    const products = productsResult.rows;
    console.log(`找到 ${products.length} 个产品需要更新`);

    // 更新每个产品
    for (const product of products) {
      console.log(`\n更新产品: ${product.name}`);

      // 为不同类别产品设置不同的技术规格
      const techSpecs = { ...defaultTechSpecs };

      // 根据产品类别调整技术参数
      if (product.category === '体感游乐') {
        techSpecs.resolution = '2560 x 1440 (2K)';
        techSpecs.brightness = '4000 ANSI Lumens';
      } else if (product.category === '教育娱乐') {
        techSpecs.interface = 'HDMI, USB, LAN, Wi-Fi, Bluetooth';
      }

      // 更新技术规格
      await client.query(
        `
        UPDATE products 
        SET tech_specs = $1
        WHERE id = $2
      `,
        [JSON.stringify(techSpecs), product.id]
      );
      console.log('- 技术规格已更新');

      // 更新核心特点
      await client.query(
        `
        UPDATE products 
        SET key_features = $1
        WHERE id = $2
      `,
        [JSON.stringify(defaultKeyFeatures), product.id]
      );
      console.log('- 核心特点已更新');

      // 更新应用场景
      await client.query(
        `
        UPDATE products 
        SET applications = $1
        WHERE id = $2
      `,
        [JSON.stringify(defaultApplications), product.id]
      );
      console.log('- 应用场景已更新');

      // 更新安装选项
      await client.query(
        `
        UPDATE products 
        SET installation_options = $1
        WHERE id = $2
      `,
        [JSON.stringify(defaultInstallationOptions), product.id]
      );
      console.log('- 安装选项已更新');

      // 添加视频URL (如果没有)
      await client.query(
        `
        UPDATE products 
        SET video_url = $1
        WHERE id = $2 AND (video_url IS NULL OR video_url = '')
      `,
        ['/videos/hologram-demo.mp4', product.id]
      );
      console.log('- 视频URL已更新');

      // 为图片数组添加默认图片 (如果为空)
      await client.query(
        `
        UPDATE products 
        SET images = $1
        WHERE id = $2 AND (images IS NULL OR images = '' OR images = '[]')
      `,
        [
          JSON.stringify([
            product.image_url,
            '/images/products/hologram-1.jpg',
            '/images/products/hologram-2.jpg',
            '/images/products/hologram-3.jpg',
          ]),
          product.id,
        ]
      );
      console.log('- 图片数组已更新');
    }

    console.log('\n全部产品更新完成!');
  } catch (error) {
    console.error('更新产品时出错:', error);
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
}

updateProducts().catch(console.error);
