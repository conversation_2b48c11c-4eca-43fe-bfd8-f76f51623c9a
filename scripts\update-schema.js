const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

// 获取数据库连接字符串
const connectionString =
  process.env.DATABASE_URL ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 创建连接池
const pool = new Pool({
  connectionString,
});

async function updateProductsTable() {
  const client = await pool.connect();
  try {
    console.log('正在添加缺失的列到products表...');

    // 添加categories列
    await client.query(`
      ALTER TABLE products 
      ADD COLUMN IF NOT EXISTS categories TEXT,
      ADD COLUMN IF NOT EXISTS features TEXT,
      ADD COLUMN IF NOT EXISTS type TEXT,
      ADD COLUMN IF NOT EXISTS size TEXT,
      ADD COLUMN IF NOT EXISTS style TEXT
    `);

    console.log('数据库表结构更新成功！');

    // 验证列是否已添加
    const result = await client.query(`
      SELECT column_name FROM information_schema.columns 
      WHERE table_name = 'products' 
      AND column_name IN ('categories', 'features', 'type', 'size', 'style')
    `);

    console.log('已添加的列:', result.rows.map(row => row.column_name).join(', '));
  } catch (error) {
    console.error('更新数据库表结构时出错:', error);
  } finally {
    client.release();
    // 关闭连接池
    pool.end();
  }
}

async function updateSchema() {
  // 连接数据库
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL || process.env.POSTGRES_URI,
  });

  try {
    // 检查是否已存在translations字段
    const checkResult = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'categories' AND column_name = 'translations'
    `);

    // 如果已存在，则不需要添加
    if (checkResult.rows.length > 0) {
      console.log('translations字段已存在，无需添加');
      return;
    }

    // 添加translations字段，类型为jsonb
    console.log('添加translations字段到categories表...');
    await pool.query(`
      ALTER TABLE categories
      ADD COLUMN translations JSONB DEFAULT '{}'::jsonb;
    `);

    // 填充现有记录的translations字段
    console.log('初始化现有记录的translations字段...');
    await pool.query(`
      UPDATE categories
      SET translations = jsonb_build_object(
        'zh', jsonb_build_object('name', name),
        'en', jsonb_build_object('name', COALESCE(name_en, name))
      );
    `);

    console.log('数据库schema更新完成');
  } catch (error) {
    console.error('更新数据库schema时出错:', error);
  } finally {
    await pool.end();
  }
}

updateProductsTable();
updateSchema().catch(console.error);
