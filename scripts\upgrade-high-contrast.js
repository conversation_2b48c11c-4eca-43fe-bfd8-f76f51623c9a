/**
 * 高对比度CSS属性升级脚本
 * 
 * 该脚本将项目中所有文件的已弃用-ms-high-contrast CSS属性和媒体查询
 * 升级为现代的forced-colors标准。
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readdir = promisify(fs.readdir);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const stat = promisify(fs.stat);

// 替换规则
const replacementRules = [
  // 媒体查询替换
  {
    from: /@media\s+\(\s*-ms-high-contrast\s*:\s*active\s*\)/g,
    to: '@media (forced-colors: active)'
  },
  {
    from: /@media\s+\(\s*-ms-high-contrast\s*:\s*none\s*\)/g,
    to: '@media (forced-colors: none)'
  },
  {
    from: /@media\s+\(\s*-ms-high-contrast\s*:\s*black-on-white\s*\)/g,
    to: '@media (forced-colors: active)'
  },
  {
    from: /@media\s+\(\s*-ms-high-contrast\s*:\s*white-on-black\s*\)/g,
    to: '@media (forced-colors: active)'
  },
  
  // 属性替换
  {
    from: /-ms-high-contrast\s*:/g,
    to: 'forced-colors:'
  },
  {
    from: /-ms-high-contrast-adjust\s*:/g,
    to: 'forced-color-adjust:'
  },
  
  // JavaScript属性替换
  {
    from: /['"]?-ms-high-contrast['"]?\s*:/g,
    to: '"forced-colors":'
  },
  {
    from: /['"]?-ms-high-contrast-adjust['"]?\s*:/g,
    to: '"forced-color-adjust":'
  },
  
  // 驼峰式属性名替换
  {
    from: /forcedColors/g,
    to: 'forcedColors'
  },
  {
    from: /forcedColorsAdjust/g,
    to: 'forcedColorAdjust'
  }
];

// 文件扩展名过滤
const cssExtensions = ['.css', '.scss', '.sass', '.less', '.styl', '.stylus'];
const jsExtensions = ['.js', '.jsx', '.ts', '.tsx'];
const htmlExtensions = ['.html', '.htm', '.xhtml'];
const supportedExtensions = [...cssExtensions, ...jsExtensions, ...htmlExtensions];

// 要忽略的目录
const ignoreDirs = [
  'node_modules',
  '.git',
  '.next',
  'out',
  'build',
  'dist',
  'public/js/ms-high-contrast-blocker.js',
  'public/js/ms-high-contrast-remover.js'
];

// 统计
let totalFiles = 0;
let modifiedFiles = 0;
let totalReplacements = 0;

/**
 * 处理单个文件
 * @param {string} filePath - 文件路径
 */
async function processFile(filePath) {
  try {
    const extension = path.extname(filePath).toLowerCase();
    
    // 只处理支持的文件类型
    if (!supportedExtensions.includes(extension)) {
      return;
    }
    
    totalFiles++;
    
    // 读取文件内容
    const content = await readFile(filePath, 'utf8');
    let newContent = content;
    let replacementCount = 0;
    
    // 应用所有替换规则
    for (const rule of replacementRules) {
      const matches = newContent.match(rule.from);
      if (matches) {
        replacementCount += matches.length;
        newContent = newContent.replace(rule.from, rule.to);
      }
    }
    
    // 如果内容有变化，写入文件
    if (newContent !== content) {
      await writeFile(filePath, newContent, 'utf8');
      modifiedFiles++;
      totalReplacements += replacementCount;
      console.log(`✅ 已更新: ${filePath} (${replacementCount}处替换)`);
    }
  } catch (error) {
    console.error(`❌ 处理文件时出错: ${filePath}`, error);
  }
}

/**
 * 递归处理目录
 * @param {string} dirPath - 目录路径
 */
async function processDirectory(dirPath) {
  try {
    const entries = await readdir(dirPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      // 忽略特定目录
      if (ignoreDirs.some(dir => fullPath.includes(dir))) {
        continue;
      }
      
      if (entry.isDirectory()) {
        await processDirectory(fullPath);
      } else if (entry.isFile()) {
        await processFile(fullPath);
      }
    }
  } catch (error) {
    console.error(`❌ 处理目录时出错: ${dirPath}`, error);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🔍 开始扫描项目文件以升级高对比度CSS属性...\n');
  
  const startTime = Date.now();
  const rootDir = process.cwd();
  
  try {
    await processDirectory(rootDir);
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    
    console.log('\n📊 升级完成:');
    console.log(`总扫描文件数: ${totalFiles}`);
    console.log(`修改的文件数: ${modifiedFiles}`);
    console.log(`替换总次数: ${totalReplacements}`);
    console.log(`耗时: ${duration}秒`);
    
    console.log('\n🔶 后续步骤:');
    console.log('1. 检查修改过的文件，确保替换正确');
    console.log('2. 在全局组件中添加HighContrastFixer组件');
    console.log('3. 删除旧的高对比度修复脚本');
    console.log('4. 重新构建和部署应用程序');
    
  } catch (error) {
    console.error('❌ 升级过程中出错:', error);
    process.exit(1);
  }
}

// 执行主函数
main().catch(console.error); 