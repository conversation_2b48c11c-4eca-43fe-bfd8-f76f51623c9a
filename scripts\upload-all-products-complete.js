const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');

// 数据库连接
const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

// 确保目标目录存在
const targetDir = 'public/images/products';
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// 产品目录映射
const productDirectories = [
  // 产品/1/ 目录
  { folder: '产品/1/互动足球', slug: 'interactive-football-system', name: '互动足球系统' },
  { folder: '产品/1/AR体感蹦床', slug: 'ar-motion-trampoline', name: 'AR体感蹦床' },
  { folder: '产品/1/体感攀岩', slug: 'motion-sensing-climbing', name: '体感攀岩系统' },
  { folder: '产品/1/互动砸球', slug: 'interactive-ball-smash', name: '互动砸球游戏' },
  { folder: '产品/1/AR教育', slug: 'ar-education-system', name: 'AR教育系统' },
  { folder: '产品/1/ktv', slug: 'ktv-interactive-system', name: 'KTV互动系统' },
  { folder: '产品/1/一体机', slug: 'interactive-all-in-one', name: '互动一体机' },
  { folder: '产品/1/3d电子沙盘', slug: '3d-electronic-sandbox', name: '3D电子沙盘' },
  
  // 产品/2/ 目录 (新增产品)
  { folder: '产品/2/宴会厅', slug: 'banquet-hall-system', name: '宴会厅互动系统' },
  
  // 产品/3/ 目录
  { folder: '产品/3/保龄球', slug: 'bowling-interactive-system', name: '保龄球互动系统' },
  { folder: '产品/3/儿童互动沙滩', slug: 'children-interactive-beach', name: '儿童互动沙滩' },
  { folder: '产品/3/儿童互动砸球', slug: 'children-interactive-ball', name: '儿童互动砸球' },
  { folder: '产品/3/全息沙幕', slug: 'holographic-screen-system', name: '全息沙幕系统' },
  { folder: '产品/3/全息沙桌', slug: 'holographic-table-system', name: '全息沙桌系统' },
  { folder: '产品/3/全息舞台', slug: 'holographic-stage-system', name: '全息舞台系统' },
  { folder: '产品/3/全息餐厅', slug: 'holographic-dining-system', name: '全息餐厅系统' }
];

// 复制单个图片文件
function copyImage(sourcePath, destPath) {
  try {
    if (fs.existsSync(sourcePath)) {
      const destDir = path.dirname(destPath);
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
      }
      
      fs.copyFileSync(sourcePath, destPath);
      console.log(`✅ 复制成功: ${path.basename(sourcePath)} -> ${path.basename(destPath)}`);
      return true;
    } else {
      console.log(`❌ 源文件不存在: ${sourcePath}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ 复制失败: ${sourcePath} -> ${error.message}`);
    return false;
  }
}

// 处理单个产品文件夹
function processProductFolder(productInfo) {
  const { folder, slug, name } = productInfo;
  console.log(`\n📁 处理产品: ${name} (${slug})`);
  
  let imageCount = 0;
  let imagePaths = [];
  
  if (!fs.existsSync(folder)) {
    console.log(`❌ 产品文件夹不存在: ${folder}`);
    return { imageCount: 0, imagePaths: [] };
  }
  
  // 1. 复制主图片（未标题-1.jpg）
  const mainImagePath = path.join(folder, '未标题-1.jpg');
  const mainDestPath = path.join(targetDir, `${slug}-main.jpg`);
  if (copyImage(mainImagePath, mainDestPath)) {
    imagePaths.push(`/images/products/${slug}-main.jpg`);
    imageCount++;
  }
  
  // 2. 复制所有产品介绍模板图片
  const templateImages = [];
  for (let i = 1; i <= 10; i++) {
    const templatePath = path.join(folder, `产品介绍模板_${i.toString().padStart(2, '0')}.jpg`);
    if (fs.existsSync(templatePath)) {
      templateImages.push(templatePath);
    }
  }
  
  console.log(`  📷 找到 ${templateImages.length} 张模板图片`);
  
  templateImages.forEach((templatePath, index) => {
    const fileExt = path.extname(templatePath);
    const destPath = path.join(targetDir, `${slug}-${index + 1}${fileExt}`);
    
    if (copyImage(templatePath, destPath)) {
      imagePaths.push(`/images/products/${slug}-${index + 1}${fileExt}`);
      imageCount++;
    }
  });
  
  // 3. 复制其他根目录下的图片文件
  const rootFiles = fs.readdirSync(folder)
    .filter(file => {
      const filePath = path.join(folder, file);
      return fs.statSync(filePath).isFile() && 
             /\.(jpg|jpeg|png|gif)$/i.test(file) &&
             file !== '未标题-1.jpg' &&
             !file.startsWith('产品介绍模板_');
    })
    .sort();
  
  if (rootFiles.length > 0) {
    console.log(`  📷 根目录找到 ${rootFiles.length} 张额外图片`);
    
    rootFiles.forEach((file, index) => {
      const sourcePath = path.join(folder, file);
      const fileExt = path.extname(file);
      const destPath = path.join(targetDir, `${slug}-extra-${index + 1}${fileExt}`);
      
      if (copyImage(sourcePath, destPath)) {
        imagePaths.push(`/images/products/${slug}-extra-${index + 1}${fileExt}`);
        imageCount++;
      }
    });
  }
  
  console.log(`  ✨ ${name} 总共复制了 ${imageCount} 张图片`);
  return { imageCount, imagePaths };
}

// 生成产品描述
function generateProductDescription(name, slug) {
  const descriptions = {
    'interactive-football-system': '互动足球娱乐系统，结合投影技术和体感识别，提供真实的足球游戏体验。适合各年龄段用户，创造沉浸式运动娱乐体验。',
    'ar-motion-trampoline': 'AR增强现实体感蹦床，结合虚拟现实技术与体感运动，为用户提供全新的娱乐健身体验。安全可靠，趣味无穷。',
    'motion-sensing-climbing': '体感攀岩互动系统，通过投影和体感技术，提供安全刺激的攀岩体验。多种难度等级，适合不同年龄段用户挑战。',
    'interactive-ball-smash': '互动砸球游戏系统，通过投影和体感技术，让玩家体验刺激有趣的砸球游戏。支持多人同时游戏，增强互动乐趣。',
    'ar-education-system': 'AR增强现实教育系统，通过虚拟与现实的结合，为学生提供生动有趣的学习体验。支持多学科内容，提升教学效果。',
    'ktv-interactive-system': '智能KTV互动娱乐系统，集成点歌、游戏、社交等多种功能，提升KTV娱乐体验。现代化界面，操作简便。',
    'interactive-all-in-one': '多功能互动一体机，集成触控、显示、计算等功能，适用于教育、商业展示等多种场景。高性能，易维护。',
    '3d-electronic-sandbox': '先进的3D电子沙盘系统，提供沉浸式的地形展示和交互体验，适用于城市规划、教育展示等多种场景。',
    'banquet-hall-system': '宴会厅互动系统，为宴会活动提供震撼的视觉效果和互动体验，营造独特的活动氛围，提升宴会档次。',
    'bowling-interactive-system': '智能保龄球互动娱乐系统，结合传统保龄球运动与现代科技，提供全新的保龄球体验。适合家庭娱乐中心。',
    'children-interactive-beach': '儿童互动沙滩体验系统，通过投影技术创造虚拟海滩环境，让孩子们在安全的室内环境中体验沙滩乐趣。',
    'children-interactive-ball': '专为儿童设计的互动砸球游戏系统，通过体感技术和投影互动，提供安全有趣的游戏体验，促进儿童运动发展。',
    'holographic-screen-system': '全息沙幕投影系统，通过先进的全息技术创造震撼的视觉效果，适用于舞台表演、展览展示等场景。',
    'holographic-table-system': '全息沙桌互动系统，结合沙盘模型与全息投影技术，提供立体的地形展示和交互体验，适用于教育和展示。',
    'holographic-stage-system': '全息舞台表演系统，通过全息投影技术创造梦幻的舞台效果，为演出和活动提供震撼的视觉体验。',
    'holographic-dining-system': '全息餐厅娱乐系统，为餐厅营造独特的用餐氛围，通过全息投影技术提供沉浸式的用餐体验。'
  };
  
  return descriptions[slug] || `${name}，采用先进的互动投影技术，为用户提供沉浸式的娱乐体验。`;
}

// 生成产品特性
function generateProductFeatures(slug) {
  const features = {
    'interactive-football-system': ["足球模拟", "体感控制", "竞技模式", "技能训练", "多人对战"],
    'ar-motion-trampoline': ["AR增强现实", "体感识别", "运动健身", "多人互动", "安全防护"],
    'motion-sensing-climbing': ["体感识别", "投影互动", "安全保护", "难度调节", "成就系统"],
    'interactive-ball-smash': ["体感识别", "投影互动", "多人游戏", "计分系统", "趣味挑战"],
    'ar-education-system': ["AR技术", "教育内容", "互动学习", "多媒体展示", "个性化教学"],
    'ktv-interactive-system': ["智能点歌", "互动游戏", "社交功能", "音效优化", "氛围灯光"],
    'interactive-all-in-one': ["多点触控", "高清显示", "一体化设计", "多媒体支持", "易于安装"],
    '3d-electronic-sandbox': ["3D地形显示", "实时交互", "多点触控", "数据可视化", "教育展示"],
    'banquet-hall-system': ["宴会投影", "氛围营造", "互动体验", "主题切换", "智能控制"],
    'bowling-interactive-system': ["智能计分", "互动投影", "多种游戏模式", "音效系统", "LED灯光"],
    'children-interactive-beach': ["投影沙滩", "互动游戏", "安全材料", "教育内容", "多感官体验"],
    'children-interactive-ball': ["儿童专用", "安全设计", "教育游戏", "体感互动", "成长记录"],
    'holographic-screen-system': ["全息投影", "沙幕效果", "高清显示", "多媒体支持", "远程控制"],
    'holographic-table-system': ["全息显示", "沙桌互动", "地形模拟", "教育应用", "数据展示"],
    'holographic-stage-system': ["全息舞台", "特效投影", "音响系统", "灯光控制", "舞台管理"],
    'holographic-dining-system': ["全息投影", "氛围营造", "互动体验", "主题切换", "智能控制"]
  };
  
  return features[slug] || ["互动投影", "体感识别", "多媒体展示", "用户友好", "高质量体验"];
}

async function uploadAllProducts() {
  console.log('🚀 开始处理所有产品...\n');
  
  let totalImages = 0;
  let processedProducts = 0;
  let allProductData = [];
  
  // 1. 复制所有产品图片
  console.log('📷 第一步：复制所有产品图片');
  productDirectories.forEach(productInfo => {
    const result = processProductFolder(productInfo);
    totalImages += result.imageCount;
    processedProducts++;
    
    // 准备产品数据
    const productData = {
      name: productInfo.name,
      slug: productInfo.slug,
      description: generateProductDescription(productInfo.name, productInfo.slug),
      type: 'interactive_equipment',
      style: '互动,娱乐',
      features: generateProductFeatures(productInfo.slug),
      images: result.imagePaths,
      in_stock: true,
      is_featured: false,
      price: 0
    };
    
    allProductData.push(productData);
  });
  
  console.log(`\n📊 图片复制完成！`);
  console.log(`   - 处理产品数: ${processedProducts}`);
  console.log(`   - 总图片数: ${totalImages}`);
  
  // 2. 上传产品到数据库
  console.log(`\n💾 第二步：上传产品到数据库`);
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    let successCount = 0;
    let skippedCount = 0;
    
    for (const product of allProductData) {
      try {
        console.log(`\n📝 处理产品: ${product.name}`);
        
        // 检查产品是否已存在
        const existingCheck = await client.query('SELECT id FROM products WHERE slug = $1', [product.slug]);
        
        if (existingCheck.rows.length > 0) {
          console.log(`⚠️  产品已存在，跳过: ${product.slug}`);
          skippedCount++;
          continue;
        }
        
        // 插入产品
        const result = await client.query(
          `INSERT INTO products 
           (name, slug, description, type, style, features, images, in_stock, is_featured, price) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
           RETURNING id`,
          [
            product.name,
            product.slug,
            product.description,
            product.type,
            product.style,
            JSON.stringify(product.features),
            JSON.stringify(product.images),
            product.in_stock,
            product.is_featured,
            product.price
          ]
        );
        
        console.log(`✅ 产品创建成功 (ID: ${result.rows[0].id})`);
        console.log(`   📷 图片数量: ${product.images.length}`);
        successCount++;
        
      } catch (error) {
        console.log(`❌ 产品创建失败: ${error.message}`);
      }
    }
    
    console.log(`\n📊 上传结果:`);
    console.log(`   ✅ 成功添加: ${successCount} 个产品`);
    console.log(`   ⚠️  跳过重复: ${skippedCount} 个产品`);
    console.log(`   📦 总处理数: ${allProductData.length} 个产品`);
    
    client.release();
    
  } catch (error) {
    console.error('❌ 数据库操作失败:', error.message);
  } finally {
    await pool.end();
  }
  
  console.log('\n🎉 所有产品处理完成!');
}

uploadAllProducts();
