const { Pool } = require('pg');
const fs = require('fs');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function uploadCompleteProducts() {
  console.log('🚀 开始上传完整产品数据...');
  
  try {
    // 读取完整产品数据
    const productsData = JSON.parse(fs.readFileSync('scripts/complete-products-data.json', 'utf8'));
    console.log(`📦 读取到 ${productsData.length} 个产品`);
    
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    let successCount = 0;
    let skippedCount = 0;
    
    for (const product of productsData) {
      try {
        console.log(`\n📝 处理产品: ${product.name}`);
        
        // 检查产品是否已存在
        const existingCheck = await client.query('SELECT id FROM products WHERE slug = $1', [product.slug]);
        
        if (existingCheck.rows.length > 0) {
          console.log(`⚠️  产品已存在，跳过: ${product.slug}`);
          skippedCount++;
          continue;
        }
        
        // 插入产品
        const result = await client.query(
          `INSERT INTO products
           (name, slug, description, type, style, features, images, in_stock, is_featured, price)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
           RETURNING id`,
          [
            product.name,
            product.slug,
            product.description,
            product.type,
            product.style,
            JSON.stringify(product.features),
            JSON.stringify(product.images),
            product.in_stock,
            product.is_featured,
            product.price
          ]
        );
        
        console.log(`✅ 产品创建成功 (ID: ${result.rows[0].id})`);
        console.log(`   📷 图片数量: ${product.images.length}`);
        console.log(`   🏷️  特色产品: ${product.is_featured ? '是' : '否'}`);
        successCount++;
        
      } catch (error) {
        console.log(`❌ 产品创建失败: ${error.message}`);
      }
    }
    
    console.log(`\n📊 上传结果:`);
    console.log(`   ✅ 成功添加: ${successCount} 个产品`);
    console.log(`   ⚠️  跳过重复: ${skippedCount} 个产品`);
    console.log(`   📦 总处理数: ${productsData.length} 个产品`);
    
    // 验证结果 - 显示所有产品
    const verifyResult = await client.query(
      'SELECT id, name, slug, is_featured FROM products ORDER BY id DESC'
    );
    console.log(`\n📋 数据库中的所有产品 (${verifyResult.rows.length} 个):`);
    verifyResult.rows.forEach((row, index) => {
      const featured = row.is_featured ? '⭐' : '  ';
      console.log(`   ${index + 1}. ${featured} ${row.name} (${row.slug})`);
    });

    // 统计特色产品
    const featuredCount = verifyResult.rows.filter(row => row.is_featured).length;

    console.log(`\n📈 产品统计:`);
    console.log(`   📦 总产品数: ${verifyResult.rows.length}`);
    console.log(`   ⭐ 特色产品: ${featuredCount}`);
    
    client.release();
    console.log('\n🎉 产品上传完成!');
    console.log('\n🔗 下一步: 访问网站查看产品展示效果');
    
  } catch (error) {
    console.error('❌ 上传失败:', error.message);
  } finally {
    await pool.end();
  }
}

uploadCompleteProducts();
