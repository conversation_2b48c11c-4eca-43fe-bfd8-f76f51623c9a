/**
 * 产品自动上传脚本
 * 用于扫描本地产品文件夹，提取图片和信息，然后上传到系统中
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// 配置信息
const config = {
  // 数据库连接信息
  database: {
    connectionString:
      process.env.POSTGRES_URI ||
      'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
    ssl: { rejectUnauthorized: false },
  },
  // 产品源目录，将从这里读取产品图片和信息
  productSourceDir: path.join(process.cwd(), '产品'),
  // 产品图片上传目录
  targetImagesDir: 'public/images/products',
  // 产品类型映射
  productTypes: {
    互动足球: 'trampoline_park',
    互动砸球: 'trampoline_park',
    体感攀岩: 'trampoline_park',
    一体机: 'indoor_playground',
    AR教育: 'indoor_playground',
    宴会厅: 'event_solutions',
    全息餐厅: 'holographic_solutions',
    全息舞台: 'holographic_solutions',
    全息沙桌: 'holographic_solutions',
    全息沙幕: 'holographic_solutions',
    儿童互动砸球: 'indoor_playground',
    儿童互动沙滩: 'indoor_playground',
    保龄球: 'family_entertainment_center',
  },
};

// 创建数据库连接池
const pool = new Pool(config.database);

/**
 * 从目录名称生成产品标题
 * @param {string} dirName 目录名称
 * @returns {string} 产品标题
 */
function generateTitle(dirName) {
  return `${dirName} Interactive Experience`;
}

/**
 * 从目录名称生成产品slug
 * @param {string} dirName 目录名称
 * @returns {string} 产品slug
 */
function generateSlug(dirName) {
  return dirName
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/--+/g, '-')
    .trim();
}

/**
 * 从目录名称生成产品描述
 * @param {string} dirName 目录名称
 * @returns {string} 产品描述
 */
function generateDescription(dirName) {
  return `${dirName} interactive experience provides engaging entertainment for customers of all ages. Our ${dirName.toLowerCase()} solutions combine cutting-edge technology with physical activity, creating memorable experiences for your venue.`;
}

/**
 * 生成产品特点
 * @param {string} dirName 目录名称
 * @returns {string[]} 产品特点列表
 */
function generateFeatures(dirName) {
  const commonFeatures = [
    `Customizable ${dirName} design`,
    'Interactive digital elements',
    'High durability materials',
    'Easy maintenance',
    'Safety compliant construction',
  ];
  return commonFeatures;
}

/**
 * 复制图片到网站public目录
 * @param {string} sourcePath 源图片路径
 * @param {string} destFilename 目标文件名
 * @returns {Promise<string|null>} 成功返回图片URL，失败返回null
 */
async function copyImageToPublic(sourcePath, destFilename) {
  try {
    // 确保目标目录存在
    if (!fs.existsSync(config.targetImagesDir)) {
      fs.mkdirSync(config.targetImagesDir, { recursive: true });
    }

    const destPath = path.join(config.targetImagesDir, destFilename);
    fs.copyFileSync(sourcePath, destPath);

    // 返回相对路径的URL
    return `/images/products/${destFilename}`;
  } catch (error) {
    console.error(`复制图片时发生错误(${sourcePath}):`, error.message);
    return null;
  }
}

/**
 * 创建产品并存入数据库
 * @param {object} productData 产品数据对象
 * @returns {Promise<boolean>} 创建成功返回true，失败返回false
 */
async function createProduct(productData) {
  const client = await pool.connect();
  try {
    // 检查slug是否已存在
    const checkResult = await client.query('SELECT id FROM products WHERE slug = $1', [
      productData.slug,
    ]);

    if (checkResult.rows.length > 0) {
      console.log(`产品已存在，跳过: ${productData.title} (${productData.slug})`);
      return false;
    }

    // 将图片数组转换为第一张图片的URL (因为数据库只有一个image_url字段)
    const imageUrl =
      productData.images && productData.images.length > 0 ? productData.images[0] : null;

    // 插入新产品，注意列名和数据库表的匹配
    const result = await client.query(
      `INSERT INTO products 
       (name, slug, description, price, image_url, category, categories, features, type, style, in_stock) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
       RETURNING id`,
      [
        productData.title, // title -> name
        productData.slug,
        productData.description,
        0.0, // 默认价格为0
        imageUrl, // 使用第一张图片
        productData.categories[0] || null, // 使用第一个分类作为category
        JSON.stringify(productData.categories || []), // 所有分类作为JSON字符串
        JSON.stringify(productData.features || []), // 特点作为JSON字符串
        productData.type || null,
        productData.style || null,
        productData.isPublished || false, // isPublished -> in_stock
      ]
    );

    console.log(`产品创建成功: ${productData.title} (ID: ${result.rows[0].id})`);
    return true;
  } catch (error) {
    console.error(`创建产品时发生错误(${productData.title}):`, error.message);
    if (error.code === '23505') {
      // 唯一约束冲突
      console.error(`产品slug "${productData.slug}" 已存在`);
    }
    return false;
  } finally {
    client.release();
  }
}

/**
 * 处理单个产品目录
 * @param {string} categoryPath 分类路径
 * @param {string} productDir 产品目录名
 */
async function processProductDirectory(categoryPath, productDir) {
  const productPath = path.join(categoryPath, productDir);
  const productType = config.productTypes[productDir] || 'indoor_playground';

  console.log(`正在处理产品: ${productDir}`);

  // 检查是否有images目录
  const imagesDir = path.join(productPath, 'images');
  if (!fs.existsSync(imagesDir) || !fs.statSync(imagesDir).isDirectory()) {
    console.log(`跳过没有images目录的产品: ${productDir}`);
    return;
  }

  // 读取图片文件
  const imageFiles = fs.readdirSync(imagesDir).filter(file => /\.(jpg|jpeg|png|gif)$/i.test(file));

  if (imageFiles.length === 0) {
    console.log(`跳过没有图片的产品: ${productDir}`);
    return;
  }

  // 上传或复制图片
  const imageUrls = [];
  for (const imageFile of imageFiles) {
    const imagePath = path.join(imagesDir, imageFile);
    const fileExtension = path.extname(imageFile);
    const destFilename = `${generateSlug(productDir)}-${imageUrls.length + 1}${fileExtension}`;

    // 如果使用本地存储
    const imageUrl = await copyImageToPublic(imagePath, destFilename);

    if (imageUrl) {
      imageUrls.push(imageUrl);
    }
  }

  if (imageUrls.length === 0) {
    console.log(`没有成功上传任何图片，跳过产品: ${productDir}`);
    return;
  }

  // 创建产品数据
  const productData = {
    title: generateTitle(productDir),
    slug: generateSlug(productDir),
    description: generateDescription(productDir),
    size: '100-500 SQM',
    style: '现代,互动',
    type: productType,
    features: generateFeatures(productDir),
    categories: ['interactive', 'entertainment'],
    images: imageUrls,
    isPublished: true,
    translations: {
      zh: {
        title: productDir,
        description: `${productDir}互动体验为各年龄段的顾客提供沉浸式娱乐。我们的${productDir}解决方案将尖端技术与身体活动相结合，为您的场所创造难忘的体验。`,
        features: [
          `可定制${productDir}设计`,
          '互动数字元素',
          '高耐久性材料',
          '易于维护',
          '符合安全标准',
        ],
      },
    },
  };

  // 创建产品
  await createProduct(productData);
}

/**
 * 主函数
 */
async function main() {
  console.log('开始产品自动上传过程...');

  try {
    // 测试数据库连接
    const client = await pool.connect();
    console.log('数据库连接成功');
    client.release();

    // 检查源目录是否存在
    if (!fs.existsSync(config.productSourceDir)) {
      console.error(`产品源目录不存在: ${config.productSourceDir}`);
      return;
    }

    // 获取产品分类目录 (1, 2, 3)
    const categoryDirs = fs.readdirSync(config.productSourceDir).filter(item => {
      const itemPath = path.join(config.productSourceDir, item);
      return fs.statSync(itemPath).isDirectory();
    });

    for (const categoryDir of categoryDirs) {
      const categoryPath = path.join(config.productSourceDir, categoryDir);

      // 获取该分类下的产品目录
      let productDirs = [];
      try {
        productDirs = fs.readdirSync(categoryPath).filter(item => {
          const itemPath = path.join(categoryPath, item);
          return fs.statSync(itemPath).isDirectory();
        });
      } catch (error) {
        console.error(`读取目录失败: ${categoryPath}`, error);
        continue;
      }

      for (const productDir of productDirs) {
        await processProductDirectory(categoryPath, productDir);
      }
    }

    console.log('产品自动上传完成!');
  } catch (error) {
    console.error('执行脚本时发生错误:', error);
  } finally {
    // 关闭数据库连接池
    await pool.end();
  }
}

// 执行主函数
main();
