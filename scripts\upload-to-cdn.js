// 上传图片到CDN的脚本
const fs = require('fs');
const path = require('path');

// 生成图片URL映射
function generateImageMapping() {
  const imagesDir = './public/images';
  const mapping = {};
  
  function scanDirectory(dir, prefix = '') {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath, prefix + item + '/');
      } else if (isImageFile(item)) {
        const relativePath = '/images/' + prefix + item;
        // 替换为CDN URL
        mapping[relativePath] = `https://your-cdn-domain.com/images/${prefix}${item}`;
      }
    });
  }
  
  scanDirectory(imagesDir);
  
  // 保存映射文件
  fs.writeFileSync('./image-mapping.json', JSON.stringify(mapping, null, 2));
  
  console.log('✅ 图片映射文件已生成: image-mapping.json');
  console.log(`📊 总共 ${Object.keys(mapping).length} 张图片`);
  
  return mapping;
}

function isImageFile(filename) {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
  return imageExtensions.includes(path.extname(filename).toLowerCase());
}

// 生成上传脚本
function generateUploadScript() {
  const script = `#!/bin/bash

# 上传图片到阿里云OSS的脚本
echo "🚀 开始上传图片到CDN..."

# 安装阿里云CLI (如果未安装)
if ! command -v ossutil &> /dev/null; then
    echo "请先安装阿里云OSS工具: https://help.aliyun.com/document_detail/120075.html"
    exit 1
fi

# 配置OSS (请替换为您的配置)
# ossutil config -e your-endpoint -i your-access-key-id -k your-access-key-secret

# 上传图片目录
ossutil cp -r ./public/images/ oss://your-bucket-name/images/ --update

echo "✅ 图片上传完成！"
echo "🌐 CDN地址: https://your-cdn-domain.com/images/"
`;

  fs.writeFileSync('./upload-images.sh', script);
  console.log('✅ 上传脚本已生成: upload-images.sh');
}

// 运行
console.log('🔄 分析项目图片...');
generateImageMapping();
generateUploadScript();

console.log('');
console.log('📋 下一步操作:');
console.log('1. 配置CDN服务 (阿里云OSS/腾讯云COS)');
console.log('2. 运行上传脚本: chmod +x upload-images.sh && ./upload-images.sh');
console.log('3. 更新代码中的图片路径');
console.log('4. 删除本地images目录');
