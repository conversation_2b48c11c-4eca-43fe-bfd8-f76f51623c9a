/**
 * 验证CTA区域统一化脚本
 * 检查所有CTA区域是否都使用了统一的样式和cta-particles
 */
const fs = require('fs');
const path = require('path');

console.log('🎯 验证CTA区域统一化\n');

// 需要检查的文件列表
const filesToCheck = [
  {
    path: 'app/[lang]/pages/about-us/page.tsx',
    description: '关于我们页面',
    expectedClass: 'contact-cta',
    shouldHaveParticles: true
  },
  {
    path: 'app/[lang]/pages/custom-solutions/page.tsx',
    description: '定制解决方案页面',
    expectedClass: 'cta-section',
    shouldHaveParticles: true
  },
  {
    path: 'app/[lang]/pages/how-to-purchase-your-first-indoor-playground/page.tsx',
    description: '购买指南页面',
    expectedClass: 'cta-section premium-cta',
    shouldHaveParticles: true
  },
  {
    path: 'app/[lang]/pages/custom-playground-design/page.tsx',
    description: '定制设计页面',
    expectedClass: 'cta-section premium-cta',
    shouldHaveParticles: true
  },
  {
    path: 'app/[lang]/pages/safe-standard/page.tsx',
    description: '安全标准页面',
    expectedClass: 'cta-section',
    shouldHaveParticles: true
  },
  {
    path: 'app/pages/quality-control/QualityControlContent.tsx',
    description: '质量控制页面',
    expectedClass: 'cta-section',
    shouldHaveParticles: true
  },
  {
    path: 'app/pages/service/ServiceContent.tsx',
    description: '服务页面',
    expectedClass: 'cta-section',
    shouldHaveParticles: true
  },
  {
    path: 'app/pages/how-to-purchase-your-first-indoor-playground/PurchaseGuideContent.tsx',
    description: '购买指南内容页面',
    expectedClass: 'cta-section',
    shouldHaveParticles: true
  },
  {
    path: 'app/pages/custom-playground-design/CustomPlaygroundDesignContent.tsx',
    description: '定制设计内容页面',
    expectedClass: 'cta-section',
    shouldHaveParticles: true
  },

];

let allPassed = true;

console.log('📋 检查各页面CTA区域:\n');

filesToCheck.forEach((file, index) => {
  console.log(`${index + 1}. ${file.description} (${file.path}):`);

  if (!fs.existsSync(file.path)) {
    console.log('   ❌ 文件不存在');
    allPassed = false;
    return;
  }

  const content = fs.readFileSync(file.path, 'utf8');

  // 检查是否有CTA区域
  const hasCTASection = content.includes('cta-section') || content.includes('contact-cta') || content.includes('premium-cta');
  if (!hasCTASection) {
    console.log('   ⚠️  未找到CTA区域');
    return;
  }

  // 检查是否有cta-particles
  const hasParticles = content.includes('cta-particles');
  if (file.shouldHaveParticles && hasParticles) {
    console.log('   ✅ 包含cta-particles');
  } else if (file.shouldHaveParticles && !hasParticles) {
    console.log('   ❌ 缺少cta-particles');
    allPassed = false;
  }

  // 检查CTA区域结构
  const hasContainer = content.includes('<div className="container">') && content.includes('cta-');
  if (hasContainer) {
    console.log('   ✅ 包含正确的容器结构');
  } else {
    console.log('   ⚠️  容器结构可能不正确');
  }

  // 检查按钮样式
  const hasButton = content.includes('btn-primary') || content.includes('custom-btn');
  if (hasButton) {
    console.log('   ✅ 包含CTA按钮');
  } else {
    console.log('   ⚠️  未找到CTA按钮');
  }

  console.log('');
});

// 检查统一样式文件
console.log('📄 检查统一样式文件:\n');

const unifiedCSSPath = 'app/styles/unified-cta.css';
if (fs.existsSync(unifiedCSSPath)) {
  console.log('✅ unified-cta.css 文件存在');

  const cssContent = fs.readFileSync(unifiedCSSPath, 'utf8');

  // 检查关键样式
  const hasBaseStyles = cssContent.includes('.cta-section,') && cssContent.includes('.contact-cta,') && cssContent.includes('.premium-cta');
  const hasParticleStyles = cssContent.includes('.cta-particles');
  const hasResponsiveStyles = cssContent.includes('@media (max-width: 768px)');

  if (hasBaseStyles) {
    console.log('✅ 包含基础CTA样式');
  } else {
    console.log('❌ 缺少基础CTA样式');
    allPassed = false;
  }

  if (hasParticleStyles) {
    console.log('✅ 包含粒子效果样式');
  } else {
    console.log('❌ 缺少粒子效果样式');
    allPassed = false;
  }

  if (hasResponsiveStyles) {
    console.log('✅ 包含响应式样式');
  } else {
    console.log('❌ 缺少响应式样式');
    allPassed = false;
  }
} else {
  console.log('❌ unified-cta.css 文件不存在');
  allPassed = false;
}

// 检查全局样式导入
console.log('\n📦 检查全局样式导入:\n');

const globalCSSPath = 'app/styles/globals.css';
if (fs.existsSync(globalCSSPath)) {
  const globalContent = fs.readFileSync(globalCSSPath, 'utf8');

  if (globalContent.includes("@import './unified-cta.css'")) {
    console.log('✅ unified-cta.css 已在 globals.css 中导入');
  } else {
    console.log('❌ unified-cta.css 未在 globals.css 中导入');
    allPassed = false;
  }
} else {
  console.log('❌ globals.css 文件不存在');
  allPassed = false;
}

// 最终结果
console.log('\n' + '='.repeat(50));
if (allPassed) {
  console.log('🎉 所有CTA区域已成功统一！');
  console.log('✅ 所有页面都使用了统一的CTA样式');
  console.log('✅ 所有CTA区域都包含了cta-particles粒子效果');
  console.log('✅ 统一样式文件已正确配置');
} else {
  console.log('⚠️  CTA区域统一化存在问题，请检查上述错误');
}
console.log('='.repeat(50));
