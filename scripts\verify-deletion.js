require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

// 获取数据库连接参数
const connectionString =
  process.env.DATABASE_URL ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 创建数据库连接池
const pool = new Pool({ connectionString });

async function verifyDeletion() {
  try {
    console.log('正在验证分类删除状态...');
    const categoriesResult = await pool.query(`
      SELECT COUNT(*) as count FROM categories
    `);

    const categoryCount = parseInt(categoriesResult.rows[0].count);
    if (categoryCount === 0) {
      console.log('✅ 成功: 所有分类已被删除！');
    } else {
      console.log(`❌ 警告: 数据库中仍有 ${categoryCount} 个分类`);
    }

    console.log('\n正在验证产品删除状态...');
    const productsResult = await pool.query(`
      SELECT COUNT(*) as count FROM products
    `);

    const productCount = parseInt(productsResult.rows[0].count);
    if (productCount === 0) {
      console.log('✅ 成功: 所有产品已被删除！');
    } else {
      console.log(`❌ 警告: 数据库中仍有 ${productCount} 个产品`);
    }

    console.log('\n验证完成！');
  } catch (error) {
    console.error('验证过程中发生错误:', error);
  } finally {
    await pool.end();
    process.exit(0);
  }
}

console.log('开始验证删除操作...');
verifyDeletion();
