/**
 * 验证热门搜索功能修复的脚本
 */
const fs = require('fs');

console.log('🔍 验证热门搜索功能修复...\n');

// 检查ProductSearch组件的修复
console.log('1. 检查ProductSearch组件修复:\n');

const productSearchPath = 'app/components/ProductSearch.tsx';
if (!fs.existsSync(productSearchPath)) {
  console.log('   ❌ ProductSearch组件文件不存在');
  process.exit(1);
}

const productSearchContent = fs.readFileSync(productSearchPath, 'utf8');

// 检查关键修复点
const checks = [
  {
    name: '防止失焦的mousedown事件',
    pattern: /onMouseDown=\{.*e\.preventDefault\(\).*\}/s,
    description: '确保热门搜索标签有防止失焦的mousedown事件处理'
  },
  {
    name: '阻止事件冒泡',
    pattern: /e\.stopPropagation\(\)/,
    description: '确保点击事件不会冒泡到父元素'
  },
  {
    name: '热门搜索点击处理',
    pattern: /const handleTagClick = \(tag: string\) => \{/,
    description: '确保有专门的热门搜索点击处理函数'
  },
  {
    name: '控制台日志',
    pattern: /console\.log\(['"]热门搜索标签被点击:/,
    description: '确保有调试日志输出'
  },
  {
    name: '点击外部关闭逻辑',
    pattern: /const handleClickOutside = \(event: MouseEvent\) => \{/,
    description: '确保有正确的点击外部关闭逻辑'
  },
  {
    name: '使用onKeyDown替代onKeyPress',
    pattern: /onKeyDown=\{handleKeyDown\}/,
    description: '确保使用了现代的onKeyDown事件'
  }
];

let allChecksPassed = true;

checks.forEach((check, index) => {
  console.log(`   ${index + 1}. ${check.name}:`);
  
  if (check.pattern.test(productSearchContent)) {
    console.log(`      ✅ ${check.description}`);
  } else {
    console.log(`      ❌ ${check.description}`);
    allChecksPassed = false;
  }
  console.log('');
});

// 检查热门搜索标签配置
console.log('2. 检查热门搜索标签配置:\n');

const hotSearchTags = ['互动投影', '数字沙盘', '全息投影', 'AR体验', 'VR设备'];
const hasAllTags = hotSearchTags.every(tag => productSearchContent.includes(tag));

if (hasAllTags) {
  console.log('   ✅ 所有热门搜索标签都已配置');
  console.log(`   📋 标签列表: ${hotSearchTags.join(', ')}`);
} else {
  console.log('   ❌ 部分热门搜索标签缺失');
  allChecksPassed = false;
}

console.log('');

// 检查产品页面中的搜索组件使用
console.log('3. 检查产品页面中的搜索组件使用:\n');

const productPagePath = 'app/[lang]/products/page.tsx';
if (fs.existsSync(productPagePath)) {
  const productPageContent = fs.readFileSync(productPagePath, 'utf8');
  
  if (productPageContent.includes('ProductSearch')) {
    console.log('   ✅ 产品页面正确导入了ProductSearch组件');
  } else {
    console.log('   ❌ 产品页面未使用ProductSearch组件');
    allChecksPassed = false;
  }
  
  if (productPageContent.includes('onSearch={(query) => {')) {
    console.log('   ✅ 产品页面正确配置了搜索回调函数');
  } else {
    console.log('   ❌ 产品页面搜索回调函数配置有问题');
    allChecksPassed = false;
  }
  
  if (productPageContent.includes('setSearchQuery(query)')) {
    console.log('   ✅ 产品页面正确处理搜索查询');
  } else {
    console.log('   ❌ 产品页面搜索查询处理有问题');
    allChecksPassed = false;
  }
} else {
  console.log('   ❌ 产品页面文件不存在');
  allChecksPassed = false;
}

console.log('');

// 检查测试文件
console.log('4. 检查测试文件:\n');

const testFilePath = 'test-hot-search.html';
if (fs.existsSync(testFilePath)) {
  console.log('   ✅ 热门搜索测试文件已创建');
  console.log('   💡 可以在浏览器中打开此文件进行功能测试');
} else {
  console.log('   ⚠️  热门搜索测试文件不存在');
}

console.log('');

// 总结
console.log('📊 修复验证总结:\n');

if (allChecksPassed) {
  console.log('✅ 所有检查都通过了！热门搜索功能修复成功。');
  console.log('');
  console.log('🎯 修复的问题:');
  console.log('   - 热门搜索标签点击时不再失焦');
  console.log('   - 添加了事件冒泡阻止机制');
  console.log('   - 改进了点击外部关闭的逻辑');
  console.log('   - 使用现代的onKeyDown事件');
  console.log('   - 添加了调试日志');
  console.log('');
  console.log('🧪 测试建议:');
  console.log('   1. 在产品页面点击搜索框');
  console.log('   2. 确认热门搜索标签显示');
  console.log('   3. 点击任意热门搜索标签');
  console.log('   4. 确认标签内容填入搜索框');
  console.log('   5. 确认搜索功能正常工作');
} else {
  console.log('❌ 部分检查未通过，请检查上述问题。');
}

console.log('');
console.log('🔗 相关文件:');
console.log(`   - ProductSearch组件: ${productSearchPath}`);
console.log(`   - 产品页面: ${productPagePath}`);
console.log(`   - 测试页面: ${testFilePath}`);
console.log('');
console.log('💡 如果问题仍然存在，请检查浏览器控制台的错误信息。');
