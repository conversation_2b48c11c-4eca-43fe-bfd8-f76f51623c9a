/**
 * 验证页面删除的脚本
 * 确认指定的页面已被成功删除
 */
const fs = require('fs');

console.log('🗑️ 验证页面删除操作...\n');

// 应该被删除的页面列表
const deletedPages = [
  // 第一批删除的页面
  {
    path: 'app/products/100-500-sqm/page.tsx',
    description: '100-500平方米产品页面'
  },
  {
    path: 'app/products/500-1000-sqm/page.tsx',
    description: '500-1000平方米产品页面'
  },
  {
    path: 'app/collections/trampoline-park/page.tsx',
    description: '蹦床公园集合页面'
  },
  {
    path: 'app/[lang]/collections/100-500-sqm/page.tsx',
    description: '多语言版本 - 100-500平方米集合页面'
  },
  {
    path: 'app/[lang]/collections/500-1000-sqm/page.tsx',
    description: '多语言版本 - 500-1000平方米集合页面'
  },
  {
    path: 'app/[lang]/collections/trampoline-park/page.tsx',
    description: '多语言版本 - 蹦床公园集合页面'
  },
  // 第二批删除的页面
  {
    path: 'app/products/indoor-playground/page.tsx',
    description: '室内游乐场产品页面'
  },
  {
    path: 'app/products/trampoline-park/page.tsx',
    description: '蹦床公园产品页面'
  },
  {
    path: 'app/products/1000-plus-sqm/page.tsx',
    description: '1000+平方米产品页面'
  },
  {
    path: 'app/[lang]/collections/indoor-playground/page.tsx',
    description: '多语言版本 - 室内游乐场集合页面'
  },
  {
    path: 'app/[lang]/collections/1000-plus-sqm/page.tsx',
    description: '多语言版本 - 1000+平方米集合页面'
  }
];

// 应该被保留的页面列表（现在所有指定页面都已删除）
const remainingPages = [];

let deletionSuccess = true;
let preservationSuccess = true;

console.log('📋 检查已删除的页面:\n');

deletedPages.forEach(page => {
  console.log(`🗑️ ${page.description} (${page.path}):`);

  if (fs.existsSync(page.path)) {
    console.log(`   ❌ 文件仍然存在 - 删除失败`);
    deletionSuccess = false;
  } else {
    console.log(`   ✅ 文件已成功删除`);
  }

  console.log('');
});

console.log('📋 检查保留的页面:\n');

remainingPages.forEach(page => {
  console.log(`📄 ${page.description} (${page.path}):`);

  if (fs.existsSync(page.path)) {
    console.log(`   ✅ 文件正常保留`);
  } else {
    console.log(`   ❌ 文件意外丢失`);
    preservationSuccess = false;
  }

  console.log('');
});

// 检查空目录
console.log('📁 检查空目录:\n');

const emptyDirectories = [
  'app/products/100-500-sqm',
  'app/products/500-1000-sqm',
  'app/products/indoor-playground',
  'app/products/trampoline-park',
  'app/products/1000-plus-sqm',
  'app/collections/trampoline-park',
  'app/[lang]/collections/100-500-sqm',
  'app/[lang]/collections/500-1000-sqm',
  'app/[lang]/collections/indoor-playground',
  'app/[lang]/collections/trampoline-park',
  'app/[lang]/collections/1000-plus-sqm'
];

emptyDirectories.forEach(dir => {
  if (fs.existsSync(dir)) {
    try {
      const files = fs.readdirSync(dir);
      if (files.length === 0) {
        console.log(`📁 ${dir}: 空目录存在（可以手动删除）`);
      } else {
        console.log(`📁 ${dir}: 目录不为空，包含 ${files.length} 个文件`);
      }
    } catch (error) {
      console.log(`📁 ${dir}: 无法读取目录`);
    }
  } else {
    console.log(`📁 ${dir}: 目录不存在`);
  }
});

// 总结
console.log('\n📊 删除操作总结:');

if (deletionSuccess) {
  console.log('   ✅ 所有指定页面已成功删除');
} else {
  console.log('   ❌ 部分页面删除失败');
}

if (preservationSuccess) {
  console.log('   ✅ 所有应保留的页面都正常存在');
} else {
  console.log('   ❌ 部分应保留的页面意外丢失');
}

console.log('\n🎯 删除效果:');
console.log('   - 100-500平方米产品页面 → 已删除');
console.log('   - 500-1000平方米产品页面 → 已删除');
console.log('   - 蹦床公园集合页面 → 已删除');
console.log('   - 对应的多语言版本页面 → 已删除');
console.log('   - 其他产品页面 → 保持不变');

console.log('\n💡 后续建议:');
console.log('   - 可以手动删除空的目录结构');
console.log('   - 检查导航菜单是否需要更新');
console.log('   - 确认数据库中的相关分类数据');
console.log('   - 测试网站导航功能是否正常');
