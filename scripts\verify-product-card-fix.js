/**
 * 验证产品卡片修复脚本
 */
const fs = require('fs');

console.log('🔍 验证产品卡片修复...\n');

// 1. 检查旧产品页面重定向
console.log('📄 检查旧产品页面重定向:');
const oldProductsPage = 'app/products/page.tsx';

if (fs.existsSync(oldProductsPage)) {
  const content = fs.readFileSync(oldProductsPage, 'utf8');
  
  if (content.includes('router.replace')) {
    console.log('   ✅ 旧产品页面已设置重定向');
  } else {
    console.log('   ❌ 旧产品页面未设置重定向');
  }
  
  if (content.includes('ModernProductCard')) {
    console.log('   ❌ 旧产品页面仍在使用旧组件');
  } else {
    console.log('   ✅ 旧产品页面已移除旧组件');
  }
} else {
  console.log('   ❌ 旧产品页面文件不存在');
}

// 2. 检查新产品页面
console.log('\n📄 检查新产品页面:');
const newProductsPage = 'app/[lang]/products/page.tsx';

if (fs.existsSync(newProductsPage)) {
  const content = fs.readFileSync(newProductsPage, 'utf8');
  
  if (content.includes('ProductGrid')) {
    console.log('   ✅ 新产品页面使用 ProductGrid');
  } else {
    console.log('   ❌ 新产品页面未使用 ProductGrid');
  }
  
  if (content.includes('ModernProductCard')) {
    console.log('   ✅ 新产品页面引用了 ModernProductCard');
  } else {
    console.log('   ⚠️  新产品页面未直接引用 ModernProductCard（通过 ProductGrid 引用）');
  }
} else {
  console.log('   ❌ 新产品页面文件不存在');
}

// 3. 检查 ProductGrid 组件
console.log('\n📄 检查 ProductGrid 组件:');
const productGridFile = 'app/components/ProductGrid.tsx';

if (fs.existsSync(productGridFile)) {
  const content = fs.readFileSync(productGridFile, 'utf8');
  
  if (content.includes('ModernProductCard')) {
    console.log('   ✅ ProductGrid 使用 ModernProductCard');
  } else {
    console.log('   ❌ ProductGrid 未使用 ModernProductCard');
  }
  
  if (content.includes('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8')) {
    console.log('   ✅ ProductGrid 使用正确的网格布局');
  } else {
    console.log('   ❌ ProductGrid 网格布局不正确');
  }
} else {
  console.log('   ❌ ProductGrid 组件文件不存在');
}

// 4. 检查 ModernProductCard 组件
console.log('\n📄 检查 ModernProductCard 组件:');
const modernProductCardFile = 'app/components/ModernProductCard.tsx';

if (fs.existsSync(modernProductCardFile)) {
  const content = fs.readFileSync(modernProductCardFile, 'utf8');
  
  if (content.includes('article className="group cursor-pointer"')) {
    console.log('   ✅ ModernProductCard 使用正确的结构');
  } else {
    console.log('   ❌ ModernProductCard 结构不正确');
  }
  
  if (content.includes('ArrowUpRight')) {
    console.log('   ✅ ModernProductCard 包含箭头图标');
  } else {
    console.log('   ❌ ModernProductCard 缺少箭头图标');
  }
} else {
  console.log('   ❌ ModernProductCard 组件文件不存在');
}

// 5. 检查强制样式覆盖
console.log('\n📄 检查强制样式覆盖:');
const forceStyleFile = 'app/styles/force-modern-product-style.css';

if (fs.existsSync(forceStyleFile)) {
  const content = fs.readFileSync(forceStyleFile, 'utf8');
  
  if (content.includes('display: none !important')) {
    console.log('   ✅ 强制样式包含隐藏旧组件的规则');
  } else {
    console.log('   ❌ 强制样式缺少隐藏旧组件的规则');
  }
  
  if (content.includes('bg-white.rounded-lg.overflow-hidden.shadow-md')) {
    console.log('   ✅ 强制样式针对旧产品卡片样式');
  } else {
    console.log('   ❌ 强制样式未针对旧产品卡片样式');
  }
} else {
  console.log('   ❌ 强制样式文件不存在');
}

// 6. 检查全局样式导入
console.log('\n📄 检查全局样式导入:');
const globalStyleFile = 'app/styles/globals.css';

if (fs.existsSync(globalStyleFile)) {
  const content = fs.readFileSync(globalStyleFile, 'utf8');
  
  if (content.includes("@import './force-modern-product-style.css'")) {
    console.log('   ✅ 全局样式已导入强制样式');
  } else {
    console.log('   ❌ 全局样式未导入强制样式');
  }
} else {
  console.log('   ❌ 全局样式文件不存在');
}

// 7. 检查中间件路由
console.log('\n📄 检查中间件路由:');
const middlewareFile = 'middleware.ts';

if (fs.existsSync(middlewareFile)) {
  const content = fs.readFileSync(middlewareFile, 'utf8');
  
  if (content.includes('locales') && content.includes('redirect')) {
    console.log('   ✅ 中间件包含语言路由重定向');
  } else {
    console.log('   ❌ 中间件缺少语言路由重定向');
  }
} else {
  console.log('   ❌ 中间件文件不存在');
}

console.log('\n🎯 修复总结:');
console.log('1. 旧的 /products 路径现在会重定向到 /zh/products 或 /en/products');
console.log('2. 新的产品页面使用 ModernProductCard 组件');
console.log('3. 强制样式会隐藏任何旧的产品卡片组件');
console.log('4. 中间件确保正确的语言路由');

console.log('\n✅ 验证完成！');
