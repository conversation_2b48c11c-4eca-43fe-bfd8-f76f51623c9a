/**
 * 验证产品链接修改的脚本
 * 检查所有产品卡片是否都链接到产品列表页面而不是具体的产品详情页
 */
const fs = require('fs');

console.log('🔍 验证产品链接修改...\n');

// 需要检查的文件列表
const filesToCheck = [
  {
    path: 'app/components/ModernProductCard.tsx',
    description: 'ModernProductCard组件',
    expectedPattern: 'href={`/${lang}/products`}',
    shouldNotContain: 'href={`/${lang}/products/${getSlug()}`}'
  },


  {
    path: 'app/[lang]/sections/[slug]/page.tsx',
    description: '分类页面',
    expectedPattern: 'href={`/${locale}/products`}',
    shouldNotContain: 'href={`/${locale}/products/${product.slug}`}'
  },
  {
    path: 'app/[lang]/products/ClientProductsPage.tsx',
    description: '客户端产品页面',
    expectedPattern: '保持在产品列表页面',
    shouldNotContain: 'router.push(`/${lang}/products/${slug}`)'
  }
];

let totalIssues = 0;

console.log('📋 检查结果:\n');

filesToCheck.forEach(file => {
  console.log(`📁 ${file.description} (${file.path}):`);

  if (fs.existsSync(file.path)) {
    const content = fs.readFileSync(file.path, 'utf8');

    // 检查是否包含期望的模式
    const hasExpectedPattern = content.includes(file.expectedPattern);
    if (hasExpectedPattern) {
      console.log(`   ✅ 包含期望的模式: ${file.expectedPattern}`);
    } else {
      console.log(`   ❌ 缺少期望的模式: ${file.expectedPattern}`);
      totalIssues++;
    }

    // 检查是否不包含不应该存在的模式
    if (file.shouldNotContain) {
      const hasUnwantedPattern = content.includes(file.shouldNotContain);
      if (!hasUnwantedPattern) {
        console.log(`   ✅ 已移除不需要的模式: ${file.shouldNotContain}`);
      } else {
        console.log(`   ❌ 仍包含不需要的模式: ${file.shouldNotContain}`);
        totalIssues++;
      }
    }

    // 额外检查：查找任何剩余的产品详情页链接
    const productDetailLinks = content.match(/\/products\/[a-z0-9-]+(?!\/)/g);
    if (productDetailLinks && productDetailLinks.length > 0) {
      console.log(`   ⚠️  发现可能的产品详情页链接: ${productDetailLinks.join(', ')}`);
    }

  } else {
    console.log(`   ❌ 文件不存在`);
    totalIssues++;
  }

  console.log('');
});

// 总结
console.log('📊 修改总结:');
if (totalIssues === 0) {
  console.log('   ✅ 所有产品链接已成功修改为指向产品列表页面');
  console.log('   ✅ 用户点击任何产品卡片都会跳转到产品列表页面');
  console.log('   ✅ 不再有产品详情页面的链接');
} else {
  console.log(`   ❌ 发现 ${totalIssues} 个问题需要修复`);
}

console.log('\n🎯 修改效果:');
console.log('   - 首页产品展示区域的产品卡片 → 跳转到产品列表页面');
console.log('   - 产品分类页面的产品卡片 → 跳转到产品列表页面');
console.log('   - 产品集合页面的产品卡片 → 跳转到产品列表页面');
console.log('   - 所有产品相关页面的产品卡片 → 跳转到产品列表页面');
console.log('   - 用户无法访问具体的产品详情页面');
