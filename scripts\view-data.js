// 查看PostgreSQL数据库中的产品和管理员信息
const { Pool } = require('pg');

async function viewData() {
  const connectionString =
    'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

  const pool = new Pool({
    connectionString,
    ssl: { rejectUnauthorized: false },
  });

  try {
    const client = await pool.connect();
    console.log('连接数据库成功\n');

    // 获取表信息
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);

    console.log('数据库中的表:');
    if (tablesResult.rows.length === 0) {
      console.log('没有找到表');
    } else {
      tablesResult.rows.forEach(row => {
        console.log(`- ${row.table_name}`);
      });
    }

    // 获取产品信息
    console.log('\n====== 产品信息 ======');
    const productsResult = await client.query(`
      SELECT id, name, slug, category, price, is_featured 
      FROM products 
      ORDER BY id
    `);

    if (productsResult.rows.length === 0) {
      console.log('没有产品信息');
    } else {
      console.log(`共有 ${productsResult.rows.length} 个产品:\n`);
      productsResult.rows.forEach(product => {
        console.log(`ID: ${product.id}`);
        console.log(`名称: ${product.name}`);
        console.log(`分类: ${product.category}`);
        console.log(`价格: ¥${product.price.toLocaleString('zh-CN')}`);
        console.log(`特色: ${product.is_featured ? '是' : '否'}`);
        console.log('------------------------------');
      });
    }

    // 获取管理员信息
    console.log('\n====== 管理员信息 ======');
    const adminsResult = await client.query(`
      SELECT id, username, email, role, created_at
      FROM admin_users
      ORDER BY id
    `);

    if (adminsResult.rows.length === 0) {
      console.log('没有管理员信息');
    } else {
      console.log(`共有 ${adminsResult.rows.length} 个管理员:\n`);
      adminsResult.rows.forEach(admin => {
        console.log(`ID: ${admin.id}`);
        console.log(`用户名: ${admin.username}`);
        console.log(`邮箱: ${admin.email}`);
        console.log(`角色: ${admin.role}`);
        console.log(`创建时间: ${admin.created_at.toLocaleString('zh-CN')}`);
        console.log('------------------------------');
      });
    }

    client.release();
    await pool.end();
  } catch (error) {
    console.error('错误:', error);
  }
}

viewData().catch(console.error);
