<!DOCTYPE html>
<html>
<head>
    <title>Clear Navigation Cache</title>
</head>
<body>
    <h1>Clear Navigation Cache</h1>
    <button onclick="clearCache()">Clear Cache</button>
    <div id="result"></div>

    <script>
        function clearCache() {
            // 清除导航缓存
            localStorage.removeItem('navigation_data_cache');
            localStorage.removeItem('navigation_data_cache_time');
            
            // 清除其他相关缓存
            localStorage.removeItem('featured_categories_cache');
            localStorage.removeItem('featured_categories_cache_time');
            
            document.getElementById('result').innerHTML = '<p style="color: green;">Cache cleared successfully! Please refresh the main page.</p>';
            
            console.log('Cache cleared');
        }
        
        // 自动清除缓存
        clearCache();
    </script>
</body>
</html>
