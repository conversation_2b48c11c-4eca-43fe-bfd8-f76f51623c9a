const fetch = require('node-fetch');

async function finalTest() {
  try {
    console.log('🎯 最终测试：产品信息区域显示...\n');
    
    // 等待服务器稳定
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试页面
    const response = await fetch('http://localhost:3000/zh/products/motion-sensing-climbing');
    const html = await response.text();
    
    console.log('📄 页面基本信息:');
    console.log(`   状态码: ${response.status}`);
    console.log(`   页面大小: ${(html.length / 1024).toFixed(2)} KB`);
    
    // 详细检查页面内容
    const checks = [
      { name: '产品标题', pattern: /体感攀岩系统/, found: false },
      { name: '面包屑导航', pattern: /首页.*产品.*体感攀岩/, found: false },
      { name: '产品详情容器', pattern: /product-detail-page|product-detail-container/, found: false },
      { name: '产品信息区域标题', pattern: /产品信息|Product Information/, found: false },
      { name: '选项卡导航', pattern: /产品概述.*核心特性.*技术规格.*应用场景/, found: false },
      { name: '产品概述选项卡', pattern: /产品概述|Overview/, found: false },
      { name: '核心特性选项卡', pattern: /核心特性|Features/, found: false },
      { name: '技术规格选项卡', pattern: /技术规格|Specifications/, found: false },
      { name: '应用场景选项卡', pattern: /应用场景|Applications/, found: false },
      { name: '获取报价按钮', pattern: /获取报价|Get Quote/, found: false }
    ];
    
    // 执行检查
    checks.forEach(check => {
      check.found = check.pattern.test(html);
    });
    
    console.log('\n✅ 详细内容检查:');
    checks.forEach(check => {
      console.log(`   ${check.found ? '✅' : '❌'} ${check.name}: ${check.found ? '存在' : '缺失'}`);
    });
    
    // 统计结果
    const foundCount = checks.filter(c => c.found).length;
    const totalCount = checks.length;
    const percentage = Math.round((foundCount / totalCount) * 100);
    
    console.log(`\n📊 完整度评估: ${foundCount}/${totalCount} (${percentage}%)`);
    
    if (percentage >= 90) {
      console.log('🎉 页面内容完整！产品信息区域正常显示！');
    } else if (percentage >= 70) {
      console.log('✅ 页面基本正常，部分功能可能需要优化');
    } else if (percentage >= 50) {
      console.log('⚠️ 页面部分内容缺失，需要进一步检查');
    } else {
      console.log('❌ 页面内容严重缺失，需要修复渲染问题');
    }
    
    // 检查是否有错误
    const hasErrors = html.includes('error') || html.includes('Error') || html.includes('undefined');
    if (hasErrors) {
      console.log('\n⚠️ 页面可能包含错误信息，建议检查浏览器控制台');
    }
    
    // 提供具体的修复建议
    if (foundCount < totalCount) {
      console.log('\n🔧 修复建议:');
      if (!checks[2].found) {
        console.log('   - 检查产品详情页面组件是否正确渲染');
      }
      if (!checks[3].found || !checks[4].found) {
        console.log('   - 检查产品信息区域组件是否正确添加');
      }
      if (!checks[5].found || !checks[6].found || !checks[7].found || !checks[8].found) {
        console.log('   - 检查选项卡组件是否正确实现');
      }
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

finalTest();
