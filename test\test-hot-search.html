<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>热门搜索功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .search-container {
            position: relative;
            margin-bottom: 30px;
        }

        .search-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .search-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            margin-top: 8px;
            padding: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            z-index: 100;
        }

        .suggestions-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }

        .tag-button {
            display: inline-block;
            padding: 8px 16px;
            margin: 4px;
            background: #f3f4f6;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .tag-button:hover {
            background: #dbeafe;
            color: #2563eb;
            transform: translateY(-1px);
        }

        .test-results {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }

        .test-item {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
            background: white;
        }

        .success {
            border-left-color: #10b981;
            background: #f0fdf4;
        }

        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
        }

        .status {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 热门搜索功能测试</h1>
        
        <div class="search-container">
            <input 
                type="text" 
                class="search-input" 
                id="searchInput" 
                placeholder="搜索产品名称、型号或关键词..."
            >
            
            <div id="suggestions" class="suggestions hidden">
                <div class="suggestions-title">热门搜索</div>
                <div>
                    <button class="tag-button" data-tag="互动投影">互动投影</button>
                    <button class="tag-button" data-tag="数字沙盘">数字沙盘</button>
                    <button class="tag-button" data-tag="全息投影">全息投影</button>
                    <button class="tag-button" data-tag="AR体验">AR体验</button>
                    <button class="tag-button" data-tag="VR设备">VR设备</button>
                </div>
            </div>
        </div>

        <div class="test-results">
            <h3>测试结果</h3>
            <div id="testResults"></div>
        </div>
    </div>

    <script>
        const searchInput = document.getElementById('searchInput');
        const suggestions = document.getElementById('suggestions');
        const testResults = document.getElementById('testResults');
        const tagButtons = document.querySelectorAll('.tag-button');

        let testCount = 0;

        function addTestResult(message, isSuccess = true) {
            testCount++;
            const div = document.createElement('div');
            div.className = `test-item ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = `
                <div class="status">${isSuccess ? '✅' : '❌'} 测试 ${testCount}</div>
                <div>${message}</div>
            `;
            testResults.appendChild(div);
        }

        // 输入框焦点事件
        searchInput.addEventListener('focus', () => {
            if (!searchInput.value.trim()) {
                suggestions.classList.remove('hidden');
                addTestResult('输入框获得焦点，热门搜索显示正常');
            }
        });

        // 输入框失焦事件
        searchInput.addEventListener('blur', (e) => {
            // 延迟隐藏，让点击事件有时间触发
            setTimeout(() => {
                suggestions.classList.add('hidden');
                addTestResult('输入框失去焦点，热门搜索隐藏');
            }, 200);
        });

        // 输入事件
        searchInput.addEventListener('input', (e) => {
            if (e.target.value.trim()) {
                suggestions.classList.add('hidden');
                addTestResult(`输入内容: "${e.target.value}"，热门搜索自动隐藏`);
            } else {
                suggestions.classList.remove('hidden');
                addTestResult('清空输入内容，热门搜索重新显示');
            }
        });

        // 热门搜索标签点击事件
        tagButtons.forEach(button => {
            // 防止失焦的mousedown事件
            button.addEventListener('mousedown', (e) => {
                e.preventDefault();
                addTestResult(`mousedown事件触发，防止输入框失焦: ${button.dataset.tag}`);
            });

            // 点击事件
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                const tag = button.dataset.tag;
                searchInput.value = tag;
                suggestions.classList.add('hidden');
                
                addTestResult(`成功点击热门搜索标签: "${tag}"，搜索框已填入内容`);
                
                // 重新聚焦输入框
                setTimeout(() => {
                    searchInput.focus();
                    addTestResult('输入框重新获得焦点');
                }, 100);
            });
        });

        // 点击外部关闭建议
        document.addEventListener('mousedown', (e) => {
            if (!suggestions.contains(e.target) && !searchInput.contains(e.target)) {
                suggestions.classList.add('hidden');
                addTestResult('点击外部区域，热门搜索隐藏');
            }
        });

        // 初始化测试
        addTestResult('热门搜索功能测试初始化完成，请点击搜索框测试功能');
    </script>
</body>
</html>
