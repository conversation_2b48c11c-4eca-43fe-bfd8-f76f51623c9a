// 脚本用于更新数据库schema，添加translations字段到categories表
const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function addTranslationsField() {
  // 连接数据库
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL || process.env.POSTGRES_URI,
  });

  try {
    // 检查数据库结构
    const columnsResult = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'categories'
    `);

    // 获取所有列名
    const columnNames = columnsResult.rows.map(row => row.column_name);
    console.log('现有列名:', columnNames);

    // 检查是否已存在translations字段
    if (columnNames.includes('translations')) {
      console.log('translations字段已存在，无需添加');
    } else {
      // 添加translations字段，类型为jsonb
      console.log('添加translations字段到categories表...');
      await pool.query(`
        ALTER TABLE categories
        ADD COLUMN translations JSONB DEFAULT '{}'::jsonb;
      `);
    }

    // 填充现有记录的translations字段
    console.log('初始化现有记录的translations字段...');

    // 检查是否有name_en字段
    const hasNameEn = columnNames.includes('name_en');

    if (hasNameEn) {
      await pool.query(`
        UPDATE categories
        SET translations = jsonb_build_object(
          'zh', jsonb_build_object('name', name),
          'en', jsonb_build_object('name', COALESCE(name_en, name))
        );
      `);
    } else {
      await pool.query(`
        UPDATE categories
        SET translations = jsonb_build_object(
          'zh', jsonb_build_object('name', name),
          'en', jsonb_build_object('name', name)
        );
      `);
    }

    console.log('数据库schema更新完成');
  } catch (error) {
    console.error('更新数据库schema时出错:', error);
  } finally {
    await pool.end();
  }
}

// 运行函数
addTranslationsField().catch(console.error);
