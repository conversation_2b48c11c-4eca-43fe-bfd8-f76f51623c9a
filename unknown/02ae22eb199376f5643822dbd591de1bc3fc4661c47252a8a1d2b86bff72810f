const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// 数据库连接配置
const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

// 产品文件夹映射
const productFolderMapping = {
  'interactive-football': '产品/1/互动足球',
  'ar-trampoline': '产品/1/AR体感蹦床', 
  'motion-sensing-climbing': '产品/1/体感攀岩',
  'interactive-ball-smashing': '产品/1/互动砸球',
  'ar-education': '产品/1/AR教育',
  'ktv': '产品/1/ktv',
  'all-in-one': '产品/1/一体机',
  '3d-sandbox': '产品/1/3d电子沙盘',
  'bowling': '产品/3/保龄球',
  'children-interactive-beach': '产品/3/儿童互动沙滩',
  'children-interactive-ball': '产品/3/儿童互动砸球',
  'holographic-screen': '产品/3/全息沙幕',
  'holographic-table': '产品/3/全息沙桌',
  'holographic-stage': '产品/3/全息舞台',
  'holographic-dining': '产品/3/全息餐厅'
};

// 复制图片到public目录
function copyImageToPublic(sourcePath, destFilename) {
  try {
    const targetDir = 'public/images/products';
    
    // 确保目标目录存在
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }

    const destPath = path.join(targetDir, destFilename);
    
    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, destPath);
      console.log(`复制图片: ${sourcePath} -> ${destPath}`);
      return `/images/products/${destFilename}`;
    } else {
      console.log(`源图片不存在: ${sourcePath}`);
      return null;
    }
  } catch (error) {
    console.error(`复制图片时发生错误: ${error.message}`);
    return null;
  }
}

// 获取产品文件夹中的图片
function getProductImages(productSlug) {
  const folderPath = productFolderMapping[productSlug];
  if (!folderPath || !fs.existsSync(folderPath)) {
    console.log(`产品文件夹不存在: ${folderPath}`);
    return { mainImage: null, images: [] };
  }

  const images = [];
  let mainImage = null;

  // 检查主图片（未标题-1.jpg）
  const mainImagePath = path.join(folderPath, '未标题-1.jpg');
  if (fs.existsSync(mainImagePath)) {
    const mainImageUrl = copyImageToPublic(mainImagePath, `${productSlug}-main.jpg`);
    if (mainImageUrl) {
      mainImage = mainImageUrl;
      images.push(mainImageUrl);
    }
  }

  // 检查images文件夹中的图片
  const imagesDir = path.join(folderPath, 'images');
  if (fs.existsSync(imagesDir)) {
    const imageFiles = fs.readdirSync(imagesDir)
      .filter(file => /\.(jpg|jpeg|png|gif)$/i.test(file))
      .sort();

    imageFiles.forEach((file, index) => {
      const sourcePath = path.join(imagesDir, file);
      const destFilename = `${productSlug}-${index + 1}.jpg`;
      const imageUrl = copyImageToPublic(sourcePath, destFilename);
      if (imageUrl) {
        images.push(imageUrl);
        // 如果没有主图，使用第一张图片作为主图
        if (!mainImage) {
          mainImage = imageUrl;
        }
      }
    });
  }

  return { mainImage, images };
}

// 更新产品图片
async function updateProductImages() {
  const client = await pool.connect();
  
  try {
    // 获取所有产品
    const result = await client.query('SELECT id, slug, name FROM products ORDER BY id');
    
    for (const product of result.rows) {
      console.log(`\n处理产品: ${product.name} (${product.slug})`);
      
      const { mainImage, images } = getProductImages(product.slug);
      
      if (mainImage && images.length > 0) {
        // 更新数据库
        await client.query(
          'UPDATE products SET image_url = $1, images = $2 WHERE id = $3',
          [mainImage, JSON.stringify(images), product.id]
        );
        
        console.log(`✅ 更新成功: 主图 ${mainImage}, 共 ${images.length} 张图片`);
      } else {
        console.log(`❌ 未找到图片文件`);
      }
    }
    
    console.log('\n🎉 所有产品图片更新完成！');
    
  } catch (error) {
    console.error('更新产品图片时发生错误:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// 执行更新
updateProductImages();
