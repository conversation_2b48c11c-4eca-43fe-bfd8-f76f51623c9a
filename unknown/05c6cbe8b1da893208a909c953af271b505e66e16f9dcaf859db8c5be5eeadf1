require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

// 获取数据库连接参数
const connectionString =
  process.env.DATABASE_URL ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 创建数据库连接池
const pool = new Pool({ connectionString });

// 要更新的产品分类
const productUpdates = [
  {
    slug: 'test-product', // 测试产品的slug
    newCategory: 'indoor-playground', // 新的分类
  },
];

async function updateProductCategories() {
  try {
    console.log('开始更新产品分类...');

    for (const update of productUpdates) {
      // 检查产品是否存在
      const checkResult = await pool.query('SELECT * FROM products WHERE slug = $1', [update.slug]);

      if (checkResult.rows.length === 0) {
        console.log(`⚠️ 产品 "${update.slug}" 不存在，跳过`);
        continue;
      }

      const product = checkResult.rows[0];

      // 检查分类是否存在
      const categoryCheck = await pool.query('SELECT * FROM categories WHERE slug = $1', [
        update.newCategory,
      ]);

      if (categoryCheck.rows.length === 0) {
        console.log(`⚠️ 分类 "${update.newCategory}" 不存在，跳过`);
        continue;
      }

      // 更新产品分类
      const result = await pool.query(
        `UPDATE products SET 
         category = $1,
         updated_at = CURRENT_TIMESTAMP
         WHERE slug = $2
         RETURNING *`,
        [update.newCategory, update.slug]
      );

      if (result.rows.length > 0) {
        console.log(`✅ 成功更新产品 "${product.name}" 的分类为 "${update.newCategory}"`);
      } else {
        console.log(`❌ 更新产品 "${product.name}" 分类失败`);
      }
    }

    console.log('\n所有产品分类更新完成！');
  } catch (error) {
    console.error('更新产品分类失败:', error);
  } finally {
    await pool.end();
  }
}

updateProductCategories();
