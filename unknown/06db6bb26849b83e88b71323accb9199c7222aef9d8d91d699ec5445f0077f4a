---
description:
globs:
alwaysApply: false
---
# Admin Panel and Content Management

## Admin Architecture

The admin panel uses the older Pages Router for its implementation:

- [pages/admin/](mdc:pages/admin/) - Admin pages
- [components/admin/](mdc:components/admin/) - Admin-specific components
- [pages/api/admin/](mdc:pages/api/admin/) - Admin API routes

## Main Admin Sections

1. **Product Management**
   - [pages/admin/products/](mdc:pages/admin/products/) - Product listing and editing
   - [pages/api/products/](mdc:pages/api/products/) - Product API endpoints

2. **Category Management**
   - [pages/admin/categories/](mdc:pages/admin/categories/) - Category management
   - [pages/api/categories/](mdc:pages/api/categories/) - Category API endpoints

3. **Content Management**
   - [pages/admin/content/](mdc:pages/admin/content/) - Content editing
   - [pages/api/content/](mdc:pages/api/content/) - Content API endpoints

4. **User Management**
   - [pages/admin/users/](mdc:pages/admin/users/) - User management
   - [pages/api/users/](mdc:pages/api/users/) - User API endpoints

5. **Settings**
   - [pages/admin/settings/](mdc:pages/admin/settings/) - Admin settings

## Authentication

Admin authentication is handled separately from main site authentication:

- [pages/api/admin/login/](mdc:pages/api/admin/login/) - Admin login API
- [components/admin/LoginForm.tsx](mdc:components/admin/LoginForm.tsx) - Admin login form

## Usage Pattern

The admin panel is built using the Pages Router pattern:

1. Pages make API calls to fetch data
2. Forms submit to API endpoints
3. Client-side state management is used for form handling

Example workflow:

```
1. Admin logs in via /admin/login
2. Admin navigates to /admin/products
3. Products are fetched from /api/products
4. Admin edits a product and submits to /api/products/[id]
5. On success, admin is redirected back to product list
```

When building new admin features, follow this pattern and ensure proper authentication checks are in place for all admin routes and API endpoints.
