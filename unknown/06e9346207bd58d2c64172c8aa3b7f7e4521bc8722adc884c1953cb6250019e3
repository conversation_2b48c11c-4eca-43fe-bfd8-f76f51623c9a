import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../lib/postgresql';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);

  // Check if user is authenticated and is admin
  if (!session || session.user.role !== 'admin') {
    return res.status(403).json({ success: false, message: 'Forbidden' });
  }

  // 获取PostgreSQL连接池
  let pool;
  try {
    pool = await dbConnect();
  } catch (error) {
    console.error('Database connection error:', error);
    return res.status(500).json({
      success: false,
      message: 'Database connection error. Please try again later.',
      data: [], // Return empty array to prevent client-side errors
    });
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      try {
        // 从PostgreSQL获取所有用户（不包含密码）
        const result = await pool.query(`
          SELECT id, username, email, name, role, created_at, updated_at
          FROM users
          ORDER BY created_at DESC
        `);

        const users = result.rows.map(row => ({
          _id: row.id.toString(), // 兼容前端期望的MongoDB风格的_id
          id: row.id,
          username: row.username,
          email: row.email,
          name: row.name,
          role: row.role,
          createdAt: row.created_at,
          updatedAt: row.updated_at,
        }));

        return res.status(200).json({ success: true, data: users });
      } catch (error) {
        console.error('Error fetching users:', error);
        return res.status(500).json({
          success: false,
          message: 'Server error',
          data: [], // Return empty array to prevent client-side errors
        });
      }

    case 'POST':
      try {
        // 插入新用户到PostgreSQL
        const { username, email, password, name, role } = req.body;

        // 检查用户名或邮箱是否已存在
        const existingUser = await pool.query(
          `
          SELECT id FROM users WHERE username = $1 OR email = $2
        `,
          [username, email]
        );

        if (existingUser.rows.length > 0) {
          return res.status(400).json({
            success: false,
            message: 'A user with this email or username already exists',
          });
        }

        // 插入新用户
        const result = await pool.query(
          `
          INSERT INTO users (username, email, password, name, role)
          VALUES ($1, $2, $3, $4, $5)
          RETURNING id, username, email, name, role, created_at, updated_at
        `,
          [username, email, password, name, role || 'user']
        );

        const newUser = {
          _id: result.rows[0].id.toString(),
          id: result.rows[0].id,
          username: result.rows[0].username,
          email: result.rows[0].email,
          name: result.rows[0].name,
          role: result.rows[0].role,
          createdAt: result.rows[0].created_at,
          updatedAt: result.rows[0].updated_at,
        };

        return res.status(201).json({ success: true, data: newUser });
      } catch (error) {
        console.error('Error creating user:', error);
        return res.status(500).json({ success: false, message: 'Server error' });
      }

    default:
      res.setHeader('Allow', ['GET', 'POST']);
      return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }
}
