/**
 * 将public目录复制到.next/standalone目录的脚本
 * 用于解决standalone模式下静态资源加载问题
 */

const { existsSync, mkdirSync, cpSync } = require('fs');
const { join } = require('path');

// 复制public目录到standalone目录
const copyPublicFolder = () => {
  const publicDir = join(__dirname, 'public');
  const standalonePubDir = join(__dirname, '.next/standalone/public');

  if (existsSync(publicDir)) {
    // 确保目标目录存在
    if (!existsSync(standalonePubDir)) {
      mkdirSync(standalonePubDir, { recursive: true });
    }

    // 复制public目录到standalone目录
    cpSync(publicDir, standalonePubDir, { recursive: true });
    console.log('✅ Public folder copied to standalone directory');
  } else {
    console.error('❌ Public folder not found');
  }
};

// 复制.next/static目录到.next/standalone/.next/static目录
const copyStaticFolder = () => {
  const staticDir = join(__dirname, '.next/static');
  const standaloneStaticDir = join(__dirname, '.next/standalone/.next/static');

  if (existsSync(staticDir)) {
    // 确保目标目录存在
    if (!existsSync(standaloneStaticDir)) {
      mkdirSync(standaloneStaticDir, { recursive: true });
    }

    // 复制static目录到standalone目录
    cpSync(staticDir, standaloneStaticDir, { recursive: true });
    console.log('✅ Static folder copied to standalone directory');
  } else {
    console.error('❌ Static folder not found');
  }
};

// 执行复制操作
try {
  copyPublicFolder();
  copyStaticFolder();
  console.log('✅ All folders copied successfully');
} catch (error) {
  console.error('❌ Error copying folders:', error);
}
