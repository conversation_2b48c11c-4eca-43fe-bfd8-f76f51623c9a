/**
 * 为产品表添加全息投影产品相关字段
 */
const { Pool } = require('pg');
require('dotenv').config();

// 数据库连接信息
const connectionString =
  process.env.POSTGRES_URI ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

const pool = new Pool({
  connectionString,
  ssl: { rejectUnauthorized: false },
});

async function addProductFields() {
  let client;

  try {
    client = await pool.connect();
    console.log('数据库连接成功');

    // 检查tech_specs列是否存在
    const checkTechSpecsColumn = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'products' AND column_name = 'tech_specs'
    `);

    if (checkTechSpecsColumn.rows.length === 0) {
      console.log('添加tech_specs列...');
      await client.query(`
        ALTER TABLE products 
        ADD COLUMN tech_specs TEXT
      `);
      console.log('tech_specs列添加成功');
    } else {
      console.log('tech_specs列已存在');
    }

    // 检查key_features列是否存在
    const checkKeyFeaturesColumn = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'products' AND column_name = 'key_features'
    `);

    if (checkKeyFeaturesColumn.rows.length === 0) {
      console.log('添加key_features列...');
      await client.query(`
        ALTER TABLE products 
        ADD COLUMN key_features TEXT DEFAULT '[]'
      `);
      console.log('key_features列添加成功');
    } else {
      console.log('key_features列已存在');
    }

    // 检查applications列是否存在
    const checkApplicationsColumn = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'products' AND column_name = 'applications'
    `);

    if (checkApplicationsColumn.rows.length === 0) {
      console.log('添加applications列...');
      await client.query(`
        ALTER TABLE products 
        ADD COLUMN applications TEXT DEFAULT '[]'
      `);
      console.log('applications列添加成功');
    } else {
      console.log('applications列已存在');
    }

    // 检查installation_options列是否存在
    const checkInstallationOptionsColumn = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'products' AND column_name = 'installation_options'
    `);

    if (checkInstallationOptionsColumn.rows.length === 0) {
      console.log('添加installation_options列...');
      await client.query(`
        ALTER TABLE products 
        ADD COLUMN installation_options TEXT DEFAULT '[]'
      `);
      console.log('installation_options列添加成功');
    } else {
      console.log('installation_options列已存在');
    }

    console.log('产品表修改完成');
  } catch (error) {
    console.error('修改产品表时出错:', error);
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
}

addProductFields().catch(console.error);
