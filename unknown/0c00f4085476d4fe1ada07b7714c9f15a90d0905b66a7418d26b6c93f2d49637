'use client';

import { useEffect } from 'react';

/**
 * 端口重定向组件
 * 
 * 当用户访问错误端口时(例如3000而服务器在3001上)，
 * 自动将用户重定向到正确的端口
 */
const PortRedirector = () => {
  useEffect(() => {
    // 只在客户端执行
    if (typeof window === 'undefined') return;

    // 获取当前URL和路径
    const currentUrl = window.location.href;
    const currentPath = window.location.pathname;
    
    // 检查是否有404错误和端口是否为3000
    const is404 = document.title.includes('404') || document.body.innerHTML.includes('404');
    const isPort3000 = window.location.port === '3000';
    
    // 如果是404错误且端口是3000，尝试端口3001
    if ((is404 || currentPath.endsWith('/admin/login')) && isPort3000) {
      console.log('检测到端口3000上的访问，尝试重定向到端口3001...');
      
      // 构建新的URL，使用端口3001
      const newUrl = currentUrl.replace(':3000', ':3001');
      
      // 重定向到新URL
      window.location.href = newUrl;
    }
  }, []);

  // 此组件不渲染任何内容
  return null;
};

export default PortRedirector; 