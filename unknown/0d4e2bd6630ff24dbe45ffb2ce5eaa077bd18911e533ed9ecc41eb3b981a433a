/**
 * 简单的数据库连接测试
 */

console.log('开始测试...');

const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function test() {
  try {
    console.log('尝试连接数据库...');
    const client = await pool.connect();
    console.log('连接成功!');
    
    const result = await client.query('SELECT NOW()');
    console.log('查询结果:', result.rows[0]);
    
    client.release();
    console.log('连接已释放');
    
  } catch (error) {
    console.error('错误:', error.message);
  } finally {
    await pool.end();
    console.log('连接池已关闭');
  }
}

test();
