require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// 获取数据库连接参数
const connectionString =
  process.env.DATABASE_URL ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 创建数据库连接池
const pool = new Pool({ connectionString });

async function updateProductFields() {
  try {
    console.log('开始更新产品字段...');

    // 1. 检查产品是否存在
    const checkResult = await pool.query('SELECT * FROM products WHERE id = $1', [6]);

    if (checkResult.rows.length === 0) {
      console.log('⚠️ 产品 ID 6 不存在，跳过更新');
      return;
    }

    // 2. 创建公共图片目录（如果不存在）
    const imagesDir = path.join(process.cwd(), 'public', 'images');
    if (!fs.existsSync(imagesDir)) {
      fs.mkdirSync(imagesDir, { recursive: true });
      console.log('✅ 创建图片目录:', imagesDir);
    }

    // 3. 创建简单的占位图像
    const placeholderPath = path.join(imagesDir, 'placeholder.jpg');
    if (!fs.existsSync(placeholderPath)) {
      // 创建一个1x1像素的透明图像作为占位符
      // 实际项目中应使用真实图像，这里只是为了解决404错误
      const placeholderData = Buffer.from([
        0xff, 0xd8, 0xff, 0xe0, 0x00, 0x10, 0x4a, 0x46, 0x49, 0x46, 0x00, 0x01, 0x01, 0x01, 0x00,
        0x48, 0x00, 0x48, 0x00, 0x00, 0xff, 0xdb, 0x00, 0x43, 0x00, 0x08, 0x06, 0x06, 0x07, 0x06,
        0x05, 0x08, 0x07, 0x07, 0x07, 0x09, 0x09, 0x08, 0x0a, 0x0c, 0x14, 0x0d, 0x0c, 0x0b, 0x0b,
        0x0c, 0x19, 0x12, 0x13, 0x0f, 0x14, 0x1d, 0x1a, 0x1f, 0x1e, 0x1d, 0x1a, 0x1c, 0x1c, 0x20,
        0x24, 0x2e, 0x27, 0x20, 0x22, 0x2c, 0x23, 0x1c, 0x1c, 0x28, 0x37, 0x29, 0x2c, 0x30, 0x31,
        0x34, 0x34, 0x34, 0x1f, 0x27, 0x39, 0x3d, 0x38, 0x32, 0x3c, 0x2e, 0x33, 0x34, 0x32, 0xff,
        0xdb, 0x00, 0x43, 0x01, 0x09, 0x09, 0x09, 0x0c, 0x0b, 0x0c, 0x18, 0x0d, 0x0d, 0x18, 0x32,
        0x21, 0x1c, 0x21, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32,
        0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32,
        0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32, 0x32,
        0x32, 0x32, 0x32, 0x32, 0x32, 0xff, 0xc0, 0x00, 0x11, 0x08, 0x00, 0x01, 0x00, 0x01, 0x03,
        0x01, 0x22, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01, 0xff, 0xc4, 0x00, 0x15, 0x00, 0x01,
        0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x08, 0xff, 0xc4, 0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xc4, 0x00, 0x14, 0x01, 0x01,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0xff, 0xc4, 0x00, 0x14, 0x11, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xda, 0x00, 0x0c, 0x03, 0x01, 0x00,
        0x02, 0x11, 0x03, 0x11, 0x00, 0x3f, 0x00, 0xb2, 0xc0, 0x07, 0xff, 0xd9,
      ]);

      fs.writeFileSync(placeholderPath, placeholderData);
      console.log('✅ 创建占位图像:', placeholderPath);
    }

    // 4. 更新产品字段
    const features = ['高品质游戏设备', '安全认证', '定制设计'];

    const result = await pool.query(
      `UPDATE products SET 
       type = $1,
       size = $2,
       features = $3,
       updated_at = CURRENT_TIMESTAMP
       WHERE id = $4
       RETURNING *`,
      ['Indoor Playground', '500平方米', features, 6]
    );

    if (result.rows.length > 0) {
      console.log('✅ 成功更新产品字段:');
      console.log(result.rows[0]);
    } else {
      console.log('❌ 更新产品字段失败');
    }
  } catch (error) {
    console.error('更新产品字段失败:', error);
  } finally {
    await pool.end();
  }
}

updateProductFields();
