/**
 * API工具函数
 * 用于处理API请求URL和端口问题
 */

/**
 * 获取当前运行的应用基础URL
 * 会自动检测当前端口
 */
export function getBaseUrl() {
  // 只在客户端执行
  if (typeof window !== 'undefined') {
    const protocol = window.location.protocol;
    const host = window.location.hostname;
    const port = window.location.port;
    
    // 返回完整的基础URL，包含实际端口
    return `${protocol}//${host}${port ? `:${port}` : ''}`;
  }
  
  // 服务器端，使用环境变量或默认值
  if (process.env.NEXT_PUBLIC_BASE_URL) {
    return process.env.NEXT_PUBLIC_BASE_URL;
  }
  
  // 开发环境默认值
  return process.env.NODE_ENV === 'development' 
    ? 'http://localhost:3001' 
    : 'https://yourdomain.com';
}

/**
 * 构建API URL
 * @param {string} path - API路径，不带前导斜杠
 * @returns {string} 完整的API URL
 */
export function getApiUrl(path) {
  const baseUrl = getBaseUrl();
  const apiPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}/api${apiPath}`;
}

/**
 * 处理API请求，自动处理URL和错误
 * @param {string} path - API路径
 * @param {Object} options - fetch选项
 * @returns {Promise<Object>} 响应数据
 */
export async function fetchApi(path, options = {}) {
  const url = getApiUrl(path);
  
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    // 尝试解析JSON
    const data = await response.json();
    
    // 检查API响应状态
    if (!response.ok) {
      console.error(`API错误 (${response.status}):`, data);
      throw new Error(data.message || `API请求失败: ${response.statusText}`);
    }
    
    return data;
  } catch (error) {
    console.error(`API请求失败 (${path}):`, error);
    throw error;
  }
}

/**
 * 修复API URL，确保使用正确的端口
 * @param {string} url - 原始URL
 * @returns {string} 修复后的URL
 */
export function fixApiUrl(url) {
  if (typeof window === 'undefined') return url;
  
  // 只处理相对URL或同源URL
  if (url.startsWith('/') || url.startsWith(window.location.origin)) {
    const currentPort = window.location.port;
    
    // 如果URL包含端口信息，确保与当前页面端口一致
    if (url.includes(':3000/') && currentPort === '3001') {
      return url.replace(':3000/', ':3001/');
    }
    if (url.includes(':3001/') && currentPort === '3000') {
      return url.replace(':3001/', ':3000/');
    }
  }
  
  return url;
} 