'use client';

import { useEffect, useState } from 'react';

interface SimpleContactButtonProps {
  productName: string;
  onContact: () => void;
}

const SimpleContactButton: React.FC<SimpleContactButtonProps> = ({ productName, onContact }) => {
  return (
    <button
      onClick={onContact}
      className="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-6 rounded-full shadow-md border border-gray-300 transition-all duration-300 flex items-center justify-center"
      style={{
        boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
      }}
    >
      联系购买
    </button>
  );
};

export default SimpleContactButton;
