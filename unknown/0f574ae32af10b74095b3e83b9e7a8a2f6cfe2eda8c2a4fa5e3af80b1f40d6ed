// 产品数据修复脚本
const fs = require('fs');
const path = require('path');

// 确保目录存在
const dataDir = path.join(process.cwd(), 'app', 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 示例产品数据
const products = [
  { 
    id: 4, 
    name: '互动足球墙', 
    slug: 'interactive-soccer-wall', 
    description: '智能互动足球训练系统', 
    image_url: '/images/holographic/holographic-3.jpg',
    category: '互动设备',
    is_featured: true 
  },
  { 
    id: 5, 
    name: '全息沙桌', 
    slug: 'holographic-sand-table', 
    description: '全息投影互动沙桌', 
    image_url: '/images/holographic/holographic-4.jpg',
    category: '互动设备',
    is_featured: true 
  }
];

// 保存产品数据文件
const productsFile = path.join(dataDir, 'products.json');
fs.writeFileSync(productsFile, JSON.stringify(products, null, 2), 'utf8');

console.log(`已生成产品数据文件: ${productsFile}`);
console.log('重启服务器后，产品列表将显示这些测试产品'); 