const fs = require('fs');
const path = require('path');

console.log('🔍 验证动画按钮更新状态\n');

// 需要检查的文件列表
const filesToCheck = [
  {
    path: 'app/[lang]/pages/about-us/page.tsx',
    description: '关于我们页面',
    expectedPattern: 'data-text={t\\(\'about\\.cta\\.button\'',
    shouldHaveAnimatedButton: true
  },
  {
    path: 'app/[lang]/pages/custom-solutions/page.tsx',
    description: '定制解决方案页面',
    expectedPattern: 'data-text={t\\(\'about\\.cta\\.button\'',
    shouldHaveAnimatedButton: true
  },
  {
    path: 'app/[lang]/pages/how-to-purchase-your-first-indoor-playground/page.tsx',
    description: '购买指南页面',
    expectedPattern: 'data-text={t\\(\'purchase_guide\\.cta\\.button\'',
    shouldHaveAnimatedButton: true
  },
  {
    path: 'app/components/AboutSection.tsx',
    description: 'About Section 组件',
    expectedPattern: 'data-text={submitting \\? t\\(\'quote_form\\.submitting\'\\)',
    shouldHaveAnimatedButton: true
  },
  {
    path: 'app/components/CustomSolutions.tsx',
    description: 'Custom Solutions 组件',
    expectedPattern: 'data-text={t\\(\'solutions\\.cta\'',
    shouldHaveAnimatedButton: true
  },
  {
    path: 'app/components/CustomPlayground.tsx',
    description: 'Custom Playground 组件',
    expectedPattern: 'data-text={t\\(\'custom_playground\\.cta_button\'',
    shouldHaveAnimatedButton: true
  }
];

let allPassed = true;

console.log('📄 检查文件中的动画按钮实现:\n');

filesToCheck.forEach((file, index) => {
  console.log(`${index + 1}. ${file.description} (${file.path}):`);
  
  if (!fs.existsSync(file.path)) {
    console.log('   ❌ 文件不存在');
    allPassed = false;
    return;
  }
  
  const content = fs.readFileSync(file.path, 'utf8');
  
  // 检查是否有data-text属性
  const hasDataText = content.includes('data-text=');
  if (file.shouldHaveAnimatedButton && hasDataText) {
    console.log('   ✅ 包含data-text属性');
  } else if (file.shouldHaveAnimatedButton && !hasDataText) {
    console.log('   ❌ 缺少data-text属性');
    allPassed = false;
  }
  
  // 检查是否有字母分割的动画结构
  const hasLetterSplit = content.includes('.split(\'\').map((char, index)');
  if (file.shouldHaveAnimatedButton && hasLetterSplit) {
    console.log('   ✅ 包含字母分割动画结构');
  } else if (file.shouldHaveAnimatedButton && !hasLetterSplit) {
    console.log('   ❌ 缺少字母分割动画结构');
    allPassed = false;
  }
  
  // 检查是否有<i>标签结构
  const hasITags = content.includes('<i key={index}>');
  if (file.shouldHaveAnimatedButton && hasITags) {
    console.log('   ✅ 包含<i>标签动画结构');
  } else if (file.shouldHaveAnimatedButton && !hasITags) {
    console.log('   ❌ 缺少<i>标签动画结构');
    allPassed = false;
  }
  
  // 检查是否使用了统一的btn-primary类
  const hasBtnPrimary = content.includes('btn-primary');
  if (file.shouldHaveAnimatedButton && hasBtnPrimary) {
    console.log('   ✅ 使用btn-primary类');
  } else if (file.shouldHaveAnimatedButton && !hasBtnPrimary) {
    console.log('   ⚠️  可能未使用btn-primary类');
  }
  
  console.log('');
});

// 检查统一样式文件
console.log('📄 检查统一样式文件:\n');

const unifiedCSSPath = 'app/styles/unified-cta.css';
if (fs.existsSync(unifiedCSSPath)) {
  console.log('✅ unified-cta.css 文件存在');
  
  const cssContent = fs.readFileSync(unifiedCSSPath, 'utf8');
  
  // 检查动画按钮样式
  const hasAnimatedStyles = cssContent.includes('display: flex !important') && 
                           cssContent.includes('letter-spacing: 4px !important') &&
                           cssContent.includes('text-transform: uppercase !important');
  const hasBeforeStyles = cssContent.includes('content: attr(data-text)');
  const hasHoverEffects = cssContent.includes('transform: translateY(100%)');
  const hasLetterStyles = cssContent.includes('.btn-primary i,') && 
                         cssContent.includes('transform: translateY(-20px)');
  const hasDelayEffects = cssContent.includes('transition-delay: 0.045s');
  
  if (hasAnimatedStyles) {
    console.log('✅ 包含动画按钮基础样式');
  } else {
    console.log('❌ 缺少动画按钮基础样式');
    allPassed = false;
  }
  
  if (hasBeforeStyles) {
    console.log('✅ 包含::before伪元素样式');
  } else {
    console.log('❌ 缺少::before伪元素样式');
    allPassed = false;
  }
  
  if (hasHoverEffects) {
    console.log('✅ 包含悬停动画效果');
  } else {
    console.log('❌ 缺少悬停动画效果');
    allPassed = false;
  }
  
  if (hasLetterStyles) {
    console.log('✅ 包含字母动画样式');
  } else {
    console.log('❌ 缺少字母动画样式');
    allPassed = false;
  }
  
  if (hasDelayEffects) {
    console.log('✅ 包含字母延迟动画效果');
  } else {
    console.log('❌ 缺少字母延迟动画效果');
    allPassed = false;
  }
  
} else {
  console.log('❌ unified-cta.css 文件不存在');
  allPassed = false;
}

console.log('\n' + '='.repeat(50));
if (allPassed) {
  console.log('🎉 所有动画按钮已成功更新！');
  console.log('✨ 现在所有CTA按钮都具有统一的动画效果');
} else {
  console.log('⚠️  部分动画按钮更新不完整，请检查上述问题');
}
console.log('='.repeat(50));

// 检查测试文件
const testFilePath = 'app/test-animated-buttons.html';
if (fs.existsSync(testFilePath)) {
  console.log('\n📋 测试文件已创建: ' + testFilePath);
  console.log('💡 可以在浏览器中打开此文件来测试动画效果');
} else {
  console.log('\n⚠️  测试文件未找到');
}
