const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function checkProductImages() {
  console.log('🔍 检查数据库中产品图片数量...');
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    // 获取所有产品的图片信息
    const result = await client.query(`
      SELECT id, name, slug, images, is_featured 
      FROM products 
      ORDER BY id
    `);
    
    console.log(`\n📊 数据库中共有 ${result.rows.length} 个产品\n`);
    
    let totalImages = 0;
    let featuredProducts = 0;
    
    result.rows.forEach((product, index) => {
      const featured = product.is_featured ? '⭐' : '  ';
      if (product.is_featured) featuredProducts++;
      
      console.log(`${index + 1}. ${featured} ${product.name}`);
      console.log(`   Slug: ${product.slug}`);
      
      if (product.images) {
        try {
          const images = JSON.parse(product.images);
          console.log(`   📷 图片数量: ${images.length}`);
          console.log(`   📋 图片列表:`);
          images.forEach((img, imgIndex) => {
            console.log(`      ${imgIndex + 1}. ${img}`);
          });
          totalImages += images.length;
        } catch (e) {
          console.log(`   ❌ 图片数据解析错误: ${product.images}`);
        }
      } else {
        console.log(`   ❌ 没有图片数据`);
      }
      console.log('');
    });
    
    console.log(`📈 统计汇总:`);
    console.log(`   📦 总产品数: ${result.rows.length}`);
    console.log(`   ⭐ 特色产品: ${featuredProducts}`);
    console.log(`   📷 总图片数: ${totalImages}`);
    console.log(`   📊 平均每产品: ${(totalImages / result.rows.length).toFixed(1)} 张图片`);
    
    // 按图片数量分组统计
    const imageCountStats = {};
    result.rows.forEach(product => {
      if (product.images) {
        try {
          const images = JSON.parse(product.images);
          const count = images.length;
          imageCountStats[count] = (imageCountStats[count] || 0) + 1;
        } catch (e) {
          imageCountStats[0] = (imageCountStats[0] || 0) + 1;
        }
      } else {
        imageCountStats[0] = (imageCountStats[0] || 0) + 1;
      }
    });
    
    console.log(`\n📊 按图片数量分组:`);
    Object.entries(imageCountStats)
      .sort(([a], [b]) => parseInt(a) - parseInt(b))
      .forEach(([count, products]) => {
        console.log(`   ${count} 张图片: ${products} 个产品`);
      });
    
    client.release();
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    await pool.end();
  }
}

checkProductImages();
