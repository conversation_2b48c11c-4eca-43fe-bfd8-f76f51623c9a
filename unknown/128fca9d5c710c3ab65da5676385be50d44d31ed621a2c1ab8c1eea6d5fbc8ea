/**
 * 创建内容相关的数据库表
 */
const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function createContentTables() {
  const client = await pool.connect();
  
  try {
    console.log('开始创建内容相关表...');
    
    // 创建内容表
    console.log('创建 contents 表...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS contents (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        content TEXT NOT NULL,
        type VARCHAR(50) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // 创建内容翻译表
    console.log('创建 content_translations 表...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS content_translations (
        id SERIAL PRIMARY KEY,
        content_id INTEGER REFERENCES contents(id) ON DELETE CASCADE,
        locale VARCHAR(10) NOT NULL,
        title VARCHAR(255),
        content TEXT,
        UNIQUE(content_id, locale)
      )
    `);
    
    // 创建索引
    console.log('创建索引...');
    await client.query(`
      CREATE INDEX IF NOT EXISTS content_translations_locale_idx 
      ON content_translations(locale)
    `);
    
    await client.query(`
      CREATE INDEX IF NOT EXISTS contents_type_idx 
      ON contents(type)
    `);
    
    console.log('✅ 所有表创建成功');
    
    // 验证表是否存在
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('contents', 'content_translations')
      ORDER BY table_name
    `);
    
    console.log('已创建的表:');
    result.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });
    
    // 检查表结构
    const columnsResult = await client.query(`
      SELECT table_name, column_name, data_type 
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name IN ('contents', 'content_translations')
      ORDER BY table_name, ordinal_position
    `);
    
    console.log('\n表结构:');
    let currentTable = '';
    columnsResult.rows.forEach(row => {
      if (row.table_name !== currentTable) {
        currentTable = row.table_name;
        console.log(`\n${row.table_name}:`);
      }
      console.log(`  - ${row.column_name}: ${row.data_type}`);
    });
    
  } catch (error) {
    console.error('❌ 创建表时出错:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

// 执行脚本
if (require.main === module) {
  createContentTables()
    .then(() => {
      console.log('\n🎉 内容表创建完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { createContentTables };
