# Product Data Flow Architecture

This document outlines the data flow for product-related functionality in our Next.js application.

## Product Pages Flow

```mermaid
flowchart TD
    subgraph Database
        PostgreSQL[(PostgreSQL)]
    end

    subgraph ServerComponents["Server Components"]
        ProductsPage["Products List Page\n(Server Component)"]
        ProductDetailPage["Product Detail Page\n(Server Component)"]
        CategoryPage["Category Page\n(Server Component)"]
    end

    subgraph ClientComponents["Client Components"]
        ContactForm["Contact Form\n(Client Component)"]
    end

    subgraph DataAccess["Data Access Layer"]
        DBQuery["Database Query Functions"]
        getDictionary["getDictionary\n(i18n Functions)"]
    end

    PostgreSQL -- "Direct Query via\nlib/db.js" --> DBQuery
    DBQuery -- "Product Data" --> ProductsPage
    DBQuery -- "Product Details" --> ProductDetailPage
    DBQuery -- "Filtered Products" --> CategoryPage

    getDictionary -- "Translations" --> ProductsPage
    getDictionary -- "Translations" --> ProductDetailPage
    getDictionary -- "Translations" --> CategoryPage

    ProductDetailPage -- "Render" --> ContactForm

    User((User)) -- "Visit /products" --> ProductsPage
    User -- "Visit /products/[slug]" --> ProductDetailPage
    User -- "Visit /products/category" --> CategoryPage
    User -- "Submit Form" --> ContactForm
```

## API Routes for Products

```mermaid
flowchart TD
    subgraph Database
        PostgreSQL[(PostgreSQL)]
    end

    subgraph AppRouter["App Router API Routes"]
        ProductsAPI["GET /api/products\napp/api/products/route.ts"]
        ProductBySlugAPI["GET /api/products/by-slug/[slug]\napp/api/products/by-slug/[slug]/route.ts"]
    end

    subgraph Components["Components (Server)"]
        ServerDataFetch["Server-side Data Fetching\n(Direct DB Query)"]
    end

    PostgreSQL -- "Query via lib/db.js" --> ProductsAPI
    PostgreSQL -- "Query via lib/db.js" --> ProductBySlugAPI
    PostgreSQL -- "Direct Query" --> ServerDataFetch

    ClientApp["Client Application\n(Not Used - Legacy)"] -. "Fetch Request\n(Legacy Method)" .-> ProductsAPI
    ClientApp -. "Fetch Request\n(Legacy Method)" .-> ProductBySlugAPI
```

## Translation and Internationalization Flow

```mermaid
flowchart TD
    subgraph Dictionaries["Dictionary Files"]
        ENDict["app/dictionaries/en.json"]
        ZHDict["app/dictionaries/zh.json"]
    end

    subgraph ServerComponents["Server Components"]
        ProductsPage["Products List Page"]
        ProductDetailPage["Product Detail Page"]
        CategoryPage["Category Page"]
    end

    subgraph I18NUtils["Internationalization Utilities"]
        getDictionary["getDictionary Function\napp/utils/i18n.ts"]
    end

    RequestWithLang["Request with [lang] parameter"] --> getDictionary
    getDictionary -- "Load Dictionary Based on lang" --> ENDict
    getDictionary -- "Load Dictionary Based on lang" --> ZHDict
    getDictionary -- "Provide Translations" --> ProductsPage
    getDictionary -- "Provide Translations" --> ProductDetailPage
    getDictionary -- "Provide Translations" --> CategoryPage
```

The architecture has been updated to use server components for all product-related pages, which eliminates hydration errors caused by client/server text mismatch. The ContactForm component remains a client component since it requires interactivity but is now properly isolated and dynamically imported.
