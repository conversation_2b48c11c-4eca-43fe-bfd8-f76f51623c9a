/**
 * 将根目录下的JavaScript脚本文件整理到scripts目录
 */
const fs = require('fs');
const path = require('path');

// 日志函数
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m', // 青色
    success: '\x1b[32m', // 绿色
    warning: '\x1b[33m', // 黄色
    error: '\x1b[31m', // 红色
  };

  console.log(`${colors[type]}[${type.toUpperCase()}]\x1b[0m ${message}`);
}

// 要排除的文件名（不需要移动的JS文件）
const excludeFiles = [
  'next.config.js',
  'next-static-export.config.js',
  'postcss.config.js',
  'tailwind.config.js',
  'next-env.d.ts',
  'middleware.ts',
  'middleware-utf8.ts',
];

// 获取根目录下的所有JS文件
function getJsFilesInRoot() {
  const rootDir = process.cwd();
  const files = fs.readdirSync(rootDir);

  return files.filter(file => {
    // 过滤出JS文件，但排除特定的配置文件
    const isJs = file.endsWith('.js') || file.endsWith('.mjs') || file.endsWith('.ts');
    const isExcluded = excludeFiles.includes(file);
    const isDirectory = fs.statSync(path.join(rootDir, file)).isDirectory();

    return isJs && !isExcluded && !isDirectory;
  });
}

// 移动文件到scripts目录
function moveFileToScripts(fileName) {
  const rootDir = process.cwd();
  const sourceFile = path.join(rootDir, fileName);
  const targetFile = path.join(rootDir, 'scripts', fileName);

  try {
    // 读取源文件内容
    const content = fs.readFileSync(sourceFile, 'utf8');

    // 写入目标文件
    fs.writeFileSync(targetFile, content);

    // 删除源文件
    fs.unlinkSync(sourceFile);

    log(`Moved "${fileName}" to scripts/`, 'success');
    return true;
  } catch (error) {
    log(`Failed to move "${fileName}": ${error.message}`, 'error');
    return false;
  }
}

// 更新package.json中的脚本路径
function updatePackageJsonScripts() {
  const rootDir = process.cwd();
  const packageJsonPath = path.join(rootDir, 'package.json');

  try {
    // 读取package.json
    const packageJsonContent = fs.readFileSync(packageJsonPath, 'utf8');
    const packageJson = JSON.parse(packageJsonContent);

    if (!packageJson.scripts) {
      log('No scripts found in package.json', 'warning');
      return false;
    }

    let updated = false;

    // 更新脚本路径
    for (const [scriptName, scriptCommand] of Object.entries(packageJson.scripts)) {
      // 查找那些直接执行根目录JS文件的脚本
      if (scriptCommand.includes('node ') && !scriptCommand.includes('node scripts/')) {
        const parts = scriptCommand.split(' ');
        for (let i = 0; i < parts.length; i++) {
          const part = parts[i];
          if (part.endsWith('.js') || part.endsWith('.mjs') || part.endsWith('.ts')) {
            // 排除那些可能指向node_modules或其他目录的路径
            if (!part.includes('/') && !part.includes('\\')) {
              parts[i] = `scripts/${part}`;
              updated = true;
            }
          }
        }

        packageJson.scripts[scriptName] = parts.join(' ');
      }
    }

    if (updated) {
      // 写回package.json
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
      log('Updated script paths in package.json', 'success');
    } else {
      log('No script paths needed updating in package.json', 'info');
    }

    return true;
  } catch (error) {
    log(`Failed to update package.json: ${error.message}`, 'error');
    return false;
  }
}

// 主函数
async function main() {
  log('Starting script organization...', 'info');

  // 获取根目录下的JS文件
  const jsFiles = getJsFilesInRoot();
  log(`Found ${jsFiles.length} JavaScript files to organize`, 'info');

  if (jsFiles.length === 0) {
    log('No files to organize!', 'success');
    return;
  }

  // 移动每个文件
  const movedFiles = [];

  for (const file of jsFiles) {
    const success = moveFileToScripts(file);
    if (success) {
      movedFiles.push(file);
    }
  }

  log(`Successfully moved ${movedFiles.length}/${jsFiles.length} files to scripts/`, 'success');

  // 更新package.json中的脚本路径
  updatePackageJsonScripts();

  log('Script organization completed!', 'success');
}

// 执行主函数
main().catch(error => {
  log(`Script organization failed: ${error.message}`, 'error');
  process.exit(1);
});
