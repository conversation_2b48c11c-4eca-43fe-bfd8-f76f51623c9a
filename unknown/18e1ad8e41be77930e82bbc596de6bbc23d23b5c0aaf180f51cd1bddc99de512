'use client';

import { useState } from 'react';
import {
  TranslatedHeading2,
  TranslatedParagraph,
  TranslatedLabel,
  useTranslatedText,
} from './TranslatedText';

// 这只是一个示例，展示如何使用TranslatedText组件
export default function ContactFormExample() {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');

  // 使用useTranslatedText获取placeholder文本
  const namePlaceholder = useTranslatedText('contact.form.name_placeholder', 'Your Name');
  const emailPlaceholder = useTranslatedText('contact.form.email_placeholder', 'Your Email');
  const submitText = useTranslatedText('contact.form.submit', 'Get My Free Quote');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // 处理表单提交
    console.log('Form submitted', { name, email });
  };

  return (
    <div className="contact-form-example">
      <TranslatedHeading2
        id="contact.form.title"
        fallback="Get Your Free Quote"
        className="form-title"
      />

      <TranslatedParagraph
        id="contact.form.subtitle"
        fallback="Fill out the form below and our team will get back to you within 24 hours."
        className="form-subtitle"
      />

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <TranslatedLabel id="contact.form.name" fallback="Full Name *" htmlFor="name" />
          <input
            type="text"
            id="name"
            value={name}
            onChange={e => setName(e.target.value)}
            required
            placeholder={namePlaceholder}
          />
        </div>

        <div className="form-group">
          <TranslatedLabel id="contact.form.email" fallback="Email Address *" htmlFor="email" />
          <input
            type="email"
            id="email"
            value={email}
            onChange={e => setEmail(e.target.value)}
            required
            placeholder={emailPlaceholder}
          />
        </div>

        <button type="submit" className="btn-primary">
          {submitText}
        </button>
      </form>
    </div>
  );
}
