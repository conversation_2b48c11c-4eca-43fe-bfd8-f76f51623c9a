import { getDictionary } from '../../../utils/i18n';
import { query } from '../../../../lib/db';
import { Metadata } from 'next';

// 定义产品接口
interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  price: number;
  category: string;
  image_url: string;
  is_featured: boolean;
  in_stock: boolean;
  created_at: string;
  updated_at: string;
}

// 从数据库获取产品详情
async function getProduct(slug: string): Promise<Product | null> {
  try {
    // 查询产品详情
    const result = await query(`SELECT * FROM products WHERE slug = $1 LIMIT 1`, [slug]);

    // 如果没有找到产品
    if (result.rows.length === 0) {
      return null;
    }

    // 返回产品详情
    return result.rows[0];
  } catch (error) {
    console.error(`通过slug获取产品失败: ${slug}`, error);
    return null;
  }
}

// 生成动态元数据
export async function generateMetadata({
  params,
}: {
  params: { slug: string; lang: string };
}): Promise<Metadata> {
  const { slug, lang } = params;
  const dict = await getDictionary(lang);
  const product = await getProduct(slug);

  if (!product) {
    return {
      title: dict.common?.product_not_found || 'Product not found',
    };
  }

  return {
    title: `${product.name} | ${dict.common?.products || 'Products'}`,
    description: product.description,
  };
}

export default function ProductLayout({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}
