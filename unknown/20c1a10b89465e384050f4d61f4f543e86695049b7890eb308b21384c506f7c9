/**
 * 高对比度修复脚本
 * 将所有CSS文件中已弃用的-ms-high-contrast替换为现代的forced-colors标准
 */
const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 替换规则
const replacements = [
  {
    from: /@media\s+\(\s*-ms-high-contrast\s*:\s*active\s*\)/g,
    to: '@media (forced-colors: active)'
  },
  {
    from: /@media\s+\(\s*-ms-high-contrast\s*:\s*none\s*\)/g,
    to: '@media (forced-colors: none)'
  },
  {
    from: /@media\s+\(\s*-ms-high-contrast\s*:\s*black-on-white\s*\)/g,
    to: '@media (forced-colors: active)'
  },
  {
    from: /@media\s+\(\s*-ms-high-contrast\s*:\s*white-on-black\s*\)/g,
    to: '@media (forced-colors: active)'
  }
];

// 要扫描的目录
const targetDirs = [
  'app/styles',
  'styles',
  'public/styles',
  'components'
];

// 要扫描的文件类型
const fileTypes = ['css', 'scss', 'sass', 'less', 'js', 'jsx', 'ts', 'tsx'];

console.log('🔍 开始扫描CSS文件...');
let modifiedFilesCount = 0;
let totalReplacementsCount = 0;

// 处理每个目录
targetDirs.forEach(dir => {
  const dirPath = path.join(process.cwd(), dir);
  
  if (!fs.existsSync(dirPath)) {
    console.log(`⚠️ 目录不存在: ${dir}`);
    return;
  }
  
  // 构建glob模式
  const pattern = `${dir}/**/*.{${fileTypes.join(',')}}`;
  
  // 获取所有匹配的文件
  const files = glob.sync(pattern);
  console.log(`在 ${dir} 中找到 ${files.length} 个文件`);
  
  // 处理每个文件
  files.forEach(file => {
    try {
      const filePath = path.join(process.cwd(), file);
      const content = fs.readFileSync(filePath, 'utf8');
      let newContent = content;
      let fileModified = false;
      
      // 应用所有替换规则
      replacements.forEach(rule => {
        const matches = content.match(rule.from) || [];
        if (matches.length > 0) {
          newContent = newContent.replace(rule.from, rule.to);
          fileModified = true;
          totalReplacementsCount += matches.length;
          console.log(`✅ 在 ${file} 中替换了 ${matches.length} 处 -ms-high-contrast`);
        }
      });
      
      // 如果文件被修改，写回文件
      if (fileModified) {
        fs.writeFileSync(filePath, newContent, 'utf8');
        modifiedFilesCount++;
      }
    } catch (error) {
      console.error(`❌ 处理文件 ${file} 时出错:`, error);
    }
  });
});

// 创建内联脚本以动态修复运行时加载的样式
const createInlineFixScript = () => {
  // 创建用于前端的内联脚本
  const inlineScript = `
/**
 * 修复高对比度模式
 * 动态替换页面上所有样式表中的-ms-high-contrast
 */
(function() {
  // 替换规则
  const replacements = [
    {
      from: /@media\\s+\\(\\s*-ms-high-contrast\\s*:\\s*active\\s*\\)/g,
      to: '@media (forced-colors: active)'
    },
    {
      from: /@media\\s+\\(\\s*-ms-high-contrast\\s*:\\s*none\\s*\\)/g,
      to: '@media (forced-colors: none)'
    },
    {
      from: /@media\\s+\\(\\s*-ms-high-contrast\\s*:\\s*black-on-white\\s*\\)/g,
      to: '@media (forced-colors: active)'
    },
    {
      from: /@media\\s+\\(\\s*-ms-high-contrast\\s*:\\s*white-on-black\\s*\\)/g,
      to: '@media (forced-colors: active)'
    }
  ];

  // 处理所有样式表
  function fixStylesheets() {
    const styleSheets = document.styleSheets;
    
    for (let i = 0; i < styleSheets.length; i++) {
      try {
        const sheet = styleSheets[i];
        
        // 跳过非同源样式表（CORS限制）
        if (!sheet.href || sheet.href.startsWith(window.location.origin) || sheet.href.startsWith('/')) {
          const rules = sheet.cssRules || sheet.rules;
          
          if (rules) {
            for (let j = 0; j < rules.length; j++) {
              const rule = rules[j];
              
              // 检查并替换@media规则
              if (rule.type === CSSRule.MEDIA_RULE && rule.conditionText && rule.conditionText.includes('-ms-high-contrast')) {
                for (const replacement of replacements) {
                  if (replacement.from.test(rule.conditionText)) {
                    // 由于无法直接修改conditionText，我们需要删除旧规则并添加新规则
                    const cssText = rule.cssText.replace(replacement.from, replacement.to);
                    sheet.deleteRule(j);
                    sheet.insertRule(cssText, j);
                    console.log('已动态修复高对比度媒体查询');
                    break;
                  }
                }
              }
            }
          }
        }
      } catch (e) {
        // 跨域样式表会抛出安全错误，我们忽略它们
        console.warn('无法处理样式表:', e);
      }
    }
  }

  // 拦截CSS API
  const originalInsertRule = CSSStyleSheet.prototype.insertRule;
  CSSStyleSheet.prototype.insertRule = function(rule, index) {
    // 检查规则是否包含-ms-high-contrast
    let newRule = rule;
    
    for (const replacement of replacements) {
      if (replacement.from.test(rule)) {
        newRule = rule.replace(replacement.from, replacement.to);
        break;
      }
    }
    
    // 调用原始方法
    return originalInsertRule.call(this, newRule, index);
  };

  // 处理动态添加的样式
  const observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        // 检查是否是样式元素
        if (node.nodeName === 'STYLE' || node.nodeName === 'LINK' && node.rel === 'stylesheet') {
          setTimeout(fixStylesheets, 0);
        }
      });
    });
  });

  // 开始观察DOM变化
  observer.observe(document.documentElement, {
    childList: true,
    subtree: true
  });

  // 初始处理
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', fixStylesheets);
  } else {
    fixStylesheets();
  }
})();
`;

  const scriptPath = path.join(process.cwd(), 'public', 'js', 'high-contrast-fix.js');
  
  // 确保目录存在
  const scriptDir = path.dirname(scriptPath);
  if (!fs.existsSync(scriptDir)) {
    fs.mkdirSync(scriptDir, { recursive: true });
  }
  
  // 写入脚本文件
  fs.writeFileSync(scriptPath, inlineScript, 'utf8');
  console.log(`✅ 已创建内联修复脚本: public/js/high-contrast-fix.js`);
};

// 创建内联修复脚本
createInlineFixScript();

// 输出结果
console.log('\n🎉 高对比度修复完成!');
console.log(`修改了 ${modifiedFilesCount} 个文件，共替换 ${totalReplacementsCount} 处 -ms-high-contrast`);
console.log('已生成内联修复脚本以处理动态加载的样式');
console.log('\n请将以下脚本添加到_document.js或页面头部:');
console.log('<script src="/js/high-contrast-fix.js"></script>'); 