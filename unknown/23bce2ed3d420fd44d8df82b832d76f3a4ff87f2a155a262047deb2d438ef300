/**
 * 现代高对比度修复
 * 使用最新的forced-colors标准替代已弃用的-ms-high-contrast
 */

/* 高对比度模式激活时的通用样式 */
@media (forced-colors: active) {
  /* 确保按钮边框可见 */
  button, 
  [role="button"],
  .button,
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    border: 1px solid ButtonText;
    color: ButtonText;
    background-color: ButtonFace;
  }
  
  /* 确保链接可见 */
  a {
    color: LinkText;
    text-decoration: underline;
  }
  
  /* 确保图标可见 */
  svg, 
  img[role="presentation"] {
    forced-color-adjust: none;
  }
  
  /* 修复焦点轮廓 */
  :focus {
    outline: 2px solid CanvasText;
  }
  
  /* 修复表单元素 */
  input, textarea, select {
    border: 1px solid CanvasText;
    background-color: Canvas;
    color: CanvasText;
  }
  
  /* 修复复选框和单选按钮 */
  input[type="checkbox"],
  input[type="radio"] {
    border: 1px solid CanvasText;
    background-color: Canvas;
  }
  
  /* 修复选择下拉菜单 */
  select {
    background-color: Canvas;
    color: CanvasText;
    border: 1px solid CanvasText;
  }
}

/* 常见的-ms-high-contrast替代方案 */
@media (forced-colors: active) {
  /* 替代 forced-colors: active */
  .ms-high-contrast-active {
    /* 这里放置您的高对比度模式样式 */
  }
}

@media (forced-colors: none) {
  /* 替代 forced-colors: none */
  .ms-high-contrast-none {
    /* 这里放置您的非高对比度模式样式 */
  }
}

/* 阻止微软自动翻译器添加的高对比度样式 */
.ms-translator-high-contrast-styles {
  display: none !important;
}

/* 空规则覆盖旧的-ms-high-contrast媒体查询，不影响正常样式 */
@media (forced-colors: active),
       (forced-colors: black-on-white),
       (forced-colors: white-on-black),
       (forced-colors: none) {
  /* 空规则覆盖所有旧的-ms-high-contrast样式 */
} 