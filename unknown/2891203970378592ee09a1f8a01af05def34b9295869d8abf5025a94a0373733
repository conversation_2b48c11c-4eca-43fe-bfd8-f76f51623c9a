/**
 * 检查产品数据
 */
const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

// 数据库连接配置
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

async function checkProducts() {
  const client = await pool.connect();

  try {
    console.log('检查产品数据...');

    // 检查产品表结构
    const tableInfo = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'products'
      ORDER BY ordinal_position
    `);

    console.log('\n产品表结构:');
    tableInfo.rows.forEach(col => {
      console.log(`- ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });

    // 检查产品数量
    const countResult = await client.query('SELECT COUNT(*) as count FROM products');
    console.log(`\n产品总数: ${countResult.rows[0].count}`);

    // 检查前几个产品
    const products = await client.query(`
      SELECT id, title, slug, type, size, is_published, created_at
      FROM products
      ORDER BY created_at DESC
      LIMIT 5
    `);

    console.log('\n最新的5个产品:');
    if (products.rows.length === 0) {
      console.log('没有找到产品数据');
    } else {
      products.rows.forEach(p => {
        console.log(`- ID: ${p.id}, 标题: ${p.title || 'N/A'}, Slug: ${p.slug || 'N/A'}, 类型: ${p.type || 'N/A'}, 发布: ${p.is_published}`);
      });
    }

  } catch (error) {
    console.error('❌ 检查产品时出错:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// 执行脚本
checkProducts()
  .then(() => {
    console.log('\n✅ 产品检查完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  });
