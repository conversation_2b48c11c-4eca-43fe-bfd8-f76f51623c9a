// 快速检查表单问题
const fs = require('fs');

console.log('=== 表单居中问题检查 ===\n');

// 1. 检查产品页面结构
const productPage = fs.readFileSync('./app/[lang]/products/page.tsx', 'utf8');
console.log('1. 产品页面结构检查:');

if (productPage.includes('w-full flex justify-center')) {
  console.log('   ✅ 外层容器使用了 flex justify-center');
} else {
  console.log('   ❌ 外层容器缺少 flex justify-center');
}

if (productPage.includes('max-w-4xl')) {
  console.log('   ✅ 设置了最大宽度限制');
} else {
  console.log('   ❌ 缺少最大宽度限制');
}

if (productPage.includes('products-page-quote-form')) {
  console.log('   ✅ 使用了正确的CSS类名');
} else {
  console.log('   ❌ 缺少CSS类名');
}

// 2. 检查CSS样式
console.log('\n2. CSS样式检查:');

const globalFormCss = fs.readFileSync('./app/styles/global-quote-form.css', 'utf8');
if (globalFormCss.includes('.products-page-quote-form.quote-form-container')) {
  console.log('   ✅ 产品页面特定样式存在');
  if (globalFormCss.includes('margin: 3rem auto !important')) {
    console.log('   ✅ 设置了正确的居中样式');
  } else {
    console.log('   ❌ 居中样式不正确');
  }
} else {
  console.log('   ❌ 产品页面特定样式不存在');
}

// 3. 检查可能的冲突
const productsCss = fs.readFileSync('./app/styles/products.css', 'utf8');
console.log('\n3. 可能的CSS冲突检查:');

// 查找任何 transform 属性
if (productsCss.includes('transform:') && productsCss.includes('products-page-quote-form')) {
  console.log('   ⚠️ 发现可能的transform冲突');
} else {
  console.log('   ✅ 没有transform冲突');
}

// 查找任何 position 属性
if (productsCss.includes('position: absolute') && productsCss.includes('products-page-quote-form')) {
  console.log('   ⚠️ 发现可能的position冲突');
} else {
  console.log('   ✅ 没有position冲突');
}

console.log('\n=== 检查完成 ===');
console.log('\n建议：');
console.log('1. 在浏览器中按F12打开开发者工具');
console.log('2. 右键点击表单，选择"检查元素"');
console.log('3. 查看Applied Styles中是否有margin: auto的设置');
console.log('4. 检查是否有其他样式覆盖了居中设置'); 