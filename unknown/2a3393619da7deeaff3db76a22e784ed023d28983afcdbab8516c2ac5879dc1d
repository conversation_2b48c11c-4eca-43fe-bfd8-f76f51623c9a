import React from 'react';
import NewProductDetail from '../../../components/NewProductDetail';

// 示例产品数据
const sampleProduct = {
  id: 1,
  name: '互动沙盘游乐设备',
  slug: 'interactive-sandbox',
  description: '这是一款适合儿童互动娱乐的沙盘游乐设备，采用先进的投影技术和感应系统，为儿童提供沉浸式的游戏体验。',
  category: '互动设备',
  image_url: '/images/products/3d-sandbox-1.jpg',
  images: [
    '/images/products/3d-sandbox-1.jpg',
    '/images/products/产品介绍模板_02.jpg',
    '/images/products/产品介绍模板_03.jpg',
    '/images/products/产品介绍模板_04.jpg',
  ],
  detail_images: [
    '/images/products/产品介绍模板_01.jpg',
    '/images/products/产品介绍模板_02.jpg',
    '/images/products/产品介绍模板_03.jpg',
    '/images/products/产品介绍模板_04.jpg',
  ]
};

// 产品详情原型页面
export default function ProductPrototypePage({ params }: { params: { lang: string } }) {
  const { lang } = params;
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">产品详情页原型</h1>
      <NewProductDetail product={sampleProduct} lang={lang} />
    </div>
  );
} 