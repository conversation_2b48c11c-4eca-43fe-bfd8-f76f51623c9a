[{"file": "app/[lang]/products/page.tsx", "count": 22, "examples": [";\n\n// 产品列表页面\nimport { useState, useEffect } from ", ";\n\n// 默认字典值 - 添加必要的键以避免未定义错误\nconst defaultDictionary: Dictionary = {\n  products: {\n    title: ", "全部产品", "浏览我们的完整产品系列", "我们提供多种创新产品解决方案"]}, {"file": "app/[lang]/pages/about-us/page.tsx", "count": 9, "examples": [";\n\nexport default function AboutUsPage() {\n  const { t, locale } = useLanguage();\n\n  // 滚动渐入效果\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      entries => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            entry.target.classList.add(", ">01</div>\n                <h3>3D全息投影</h3>\n                <div className=", "></div>\n                <p>无需特殊眼镜即可呈现立体悬浮影像，为您创造真实的三维视觉体验</p>\n              </div>\n            </motion.div>\n\n            <motion.div\n              className=", ">02</div>\n                <h3>裸眼5D互动投影</h3>\n                <div className=", "></div>\n                <p>结合动作感应与全息成像，让观众无需任何设备即可与投影内容自然互动</p>\n              </div>\n            </motion.div>\n\n            <motion.div\n              className="]}]