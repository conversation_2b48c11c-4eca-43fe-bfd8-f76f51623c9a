'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';

export default function FixProductsPage() {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [testSlug, setTestSlug] = useState('ar-trampoline');
  
  // 测试产品详情页面
  const testProductDetailPage = async () => {
    try {
      window.open(`/zh/products/${testSlug}`, '_blank');
    } catch (err) {
      console.error(err);
    }
  };
  
  // 检查并修复产品API路由
  const fixProductsAPI = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);
      
      // 调用诊断API
      const res = await fetch('/api/test-product?slug=' + testSlug);
      const data = await res.json();
      
      // 分析API响应
      if (data.api && data.api.data && data.api.data.success) {
        setSuccess(true);
        alert('产品API路由正常工作，无需修复');
      } else {
        if (window.confirm('检测到产品API可能存在问题，是否尝试修复？')) {
          // 这里需要后端实现实际的修复逻辑
          const fixRes = await fetch('/api/fix-products-route', { method: 'POST' });
          const fixData = await fixRes.json();
          
          if (fixData.success) {
            setSuccess(true);
            alert('修复成功！请重新测试产品详情页');
          } else {
            setError(fixData.error || '修复失败');
          }
        }
      }
      
      setLoading(false);
    } catch (err: any) {
      setError(err.message || '发生未知错误');
      setLoading(false);
    }
  };
  
  // 清除缓存
  const clearCache = async () => {
    try {
      setLoading(true);
      const res = await fetch('/api/cache/clear', { method: 'POST' });
      const data = await res.json();
      
      if (data.success) {
        alert('缓存已清除');
      } else {
        alert('清除缓存失败: ' + (data.error || '未知错误'));
      }
      
      setLoading(false);
    } catch (err: any) {
      alert('清除缓存出错: ' + err.message);
      setLoading(false);
    }
  };
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">产品详情页修复工具</h1>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p><strong>错误:</strong> {error}</p>
        </div>
      )}
      
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <p><strong>成功:</strong> 操作完成</p>
        </div>
      )}
      
      <div className="mb-8 bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">测试产品详情页</h2>
        
        <div className="mb-4">
          <label className="block text-gray-700 mb-2">产品Slug:</label>
          <input 
            type="text" 
            value={testSlug} 
            onChange={(e) => setTestSlug(e.target.value)}
            className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="输入要测试的产品Slug"
          />
        </div>
        
        <div className="flex flex-wrap gap-4">
          <button 
            onClick={testProductDetailPage}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
            disabled={loading}
          >
            测试详情页
          </button>
          
          <button 
            onClick={fixProductsAPI}
            className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 transition-colors"
            disabled={loading}
          >
            检查并修复API
          </button>
          
          <button 
            onClick={clearCache}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
            disabled={loading}
          >
            清除缓存
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">常见问题分析</h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold text-blue-800">1. 找不到产品数据</h3>
              <p className="text-gray-700 mt-1">可能原因: API路由错误、数据库连接问题或缓存问题</p>
              <p className="text-gray-600 text-sm mt-1">解决方案: 检查API路由、清除缓存、检查数据库连接</p>
            </div>
            
            <div>
              <h3 className="font-semibold text-blue-800">2. 页面无法加载</h3>
              <p className="text-gray-700 mt-1">可能原因: 组件渲染错误、路径参数处理不当</p>
              <p className="text-gray-600 text-sm mt-1">解决方案: 查看浏览器控制台错误、检查组件代码</p>
            </div>
            
            <div>
              <h3 className="font-semibold text-blue-800">3. 页面样式异常</h3>
              <p className="text-gray-700 mt-1">可能原因: CSS冲突、响应式布局问题</p>
              <p className="text-gray-600 text-sm mt-1">解决方案: 检查CSS类名、调整响应式断点</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">调试指南</h2>
          
          <div className="space-y-4 text-gray-700">
            <p>1. 使用浏览器开发者工具检查网络请求，特别关注对 <code>/api/products/by-slug/[slug]</code> 的请求</p>
            <p>2. 查看浏览器控制台日志，寻找JavaScript错误或警告</p>
            <p>3. 测试使用不同的产品Slug值，某些产品可能有特殊字符导致问题</p>
            <p>4. 尝试在隐私模式/无痕模式下测试，排除浏览器缓存影响</p>
            <p>5. 检查服务器端控制台日志，查找API处理过程中的错误</p>
          </div>
          
          <div className="mt-6">
            <Link 
              href="/debug"
              className="text-blue-600 hover:underline inline-flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              返回调试主页
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
} 