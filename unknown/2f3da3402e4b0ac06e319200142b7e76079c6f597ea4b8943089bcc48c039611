/* 产品详情页样式修复 */

/* 修复产品详情页的整体布局 */
.product-detail-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
}

/* 产品详情页容器修复 */
.product-detail-container {
  position: relative;
  z-index: 1;
  background-color: white;
}

/* 产品详情页修复类 */
.product-detail-fix {
  margin-top: 450px !important;
  position: relative;
  z-index: 1;
}

/* 强制覆盖Header Section的padding */
.product-detail-fix .bg-gray-50 .container {
  padding-top: 12rem !important;
  padding-bottom: 12rem !important;
}

/* 更具体的选择器来确保样式生效 */
.product-detail-container .bg-gray-50 .container.mx-auto {
  padding-top: 12rem !important;
  padding-bottom: 12rem !important;
}

/* 顶部导航区域优化 */
.product-detail-navigation {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px 24px;
  margin: -8px -8px 32px -8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}

/* 面包屑导航优化 */
.breadcrumbs {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 0;
  flex-wrap: wrap;
}

.breadcrumbs a {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  padding: 4px 8px;
  border-radius: 6px;
  white-space: nowrap;
}

.breadcrumbs a:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
}

.breadcrumbs .separator {
  color: #d1d5db;
  font-weight: 400;
  margin: 0 4px;
}

.breadcrumbs .current {
  color: #374151;
  font-weight: 600;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 产品展示区域间距修复 */
.product-showcase-section {
  margin-top: 80px;
  margin-bottom: 80px;
  padding: 0 16px;
}

/* 产品大图区域布局优化 - 全屏展示，左右不留空间 */
.product-large-images-section {
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #f0f4f8 0%, #e2e8f0 50%, #f8fafc 100%);
  padding: 60px 0 40px 0;
  margin-top: 60px;
  /* 修复图片溢出问题 - 使用更简单可靠的全屏方法 */
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  position: relative;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.6);
  overflow-x: hidden; /* 防止水平滚动 */
  overflow-y: visible;
}

/* 产品展示区域内容容器 - 移除左右边距，实现真正全屏 */
.product-large-images-section .max-w-7xl {
  max-width: none !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100% !important;
}

/* 产品展示标题区域保持居中，但移除左右边距 */
.product-large-images-section .product-showcase-header {
  padding: 0 !important;
  margin-bottom: 48px;
  text-align: center;
  position: relative;
  z-index: 2;
}

/* 移除产品大图之间的间距 */
.product-large-images-section .space-y-12 {
  gap: 0 !important;
  display: block !important;
}

.product-large-images-section .space-y-12 > div {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

/* 覆盖原有的产品大图展示样式 */
.product-large-gallery {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.product-large-gallery .space-y-12 > div {
  margin-bottom: 0 !important;
}

/* 确保图片容器紧密连接并全屏显示 */
.product-detail-image-container {
  border-radius: 0 !important;
  margin-bottom: 0 !important;
  width: 100% !important; /* 改为100%而不是100vw，避免溢出 */
  margin-left: 0 !important;
  margin-right: 0 !important;
  max-width: none !important;
  overflow: hidden; /* 确保内容不会溢出容器 */
  position: relative;
}

/* 产品大图展示组件全屏样式 */
.product-large-images-section .product-large-gallery {
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 产品大图展示的每个图片项目全屏 */
.product-large-images-section .space-y-12 > div,
.product-large-images-section .product-large-gallery > div {
  width: 100% !important; /* 改为100%而不是100vw，避免溢出 */
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  max-width: none !important;
  overflow: hidden; /* 防止内容溢出 */
}

/* 添加微妙的装饰效果 */
.product-large-images-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.3), transparent);
  z-index: 1;
}

.product-large-images-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  z-index: 1;
}

/* 产品信息卡片样式 */
.product-info-card {
  background: white;
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  width: 100%;
  height: fit-content;
  position: relative;
  z-index: 2;
}

/* 产品特点网格优化 */
.product-features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 24px;
}

.product-feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
  font-size: 15px;
}

.product-feature-item:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

/* 缩略图区域 */
.product-thumbnails-section {
  margin-top: 24px;
}

/* 缩略图网格优化 */
.product-thumbnails-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding: 16px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.product-thumbnail-item {
  position: relative;
  aspect-ratio: 4/3;
  border-radius: 12px;
  overflow: hidden;
  border: 3px solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.product-thumbnail-item.active {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3), 0 8px 24px rgba(59, 130, 246, 0.2);
  /* 移除放大效果 */
}

.product-thumbnail-item:hover {
  border-color: #60a5fa;
  /* 移除放大效果 */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.product-thumbnail-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

/* 移除缩略图悬停放大效果 */

/* 缩略图统一标识器已移除 - 不再显示数字 */

/* 隐藏图片计数器 */
.product-detail-image-container .absolute.bottom-6.left-6,
.product-large-images-section .absolute.bottom-6.left-6,
div[class*="bottom-6"][class*="left-6"][class*="bg-white"],
.absolute.bottom-6.left-6.bg-white\/90,
.group .absolute[class*="bottom-6"][class*="left-6"] {
  display: none !important;
}

/* 隐藏所有包含图片计数格式的元素 */
div:contains("/ 6"),
div:contains("1 /"),
div:contains("2 /"),
div:contains("3 /"),
div:contains("4 /"),
div:contains("5 /"),
div:contains("6 /") {
  display: none !important;
}

/* 缩略图加载效果 */
.product-thumbnail-item img {
  opacity: 0;
  animation: fadeInImage 0.5s ease-out forwards;
}

@keyframes fadeInImage {
  to {
    opacity: 1;
  }
}

/* 兼容其他缩略图组件样式 */
.thumbnails-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
  margin-top: 16px;
  padding: 12px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.thumbnail-item {
  position: relative;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;
  background: white;
}

.thumbnail-item.border-blue-500 {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
  /* 移除选中状态放大效果 */
}

.thumbnail-item:hover {
  border-color: #60a5fa;
  /* 移除放大效果 */
}

/* 产品标题区域优化 */
.product-title-section {
  margin-bottom: 32px;
}

.product-main-title {
  font-size: clamp(1.8rem, 4vw, 3rem);
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.product-category-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 联系按钮优化 */
.product-contact-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px 32px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border-radius: 16px;
  font-weight: 600;
  font-size: 16px;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
  width: 100%;
}

.product-contact-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
}



/* 服务承诺区域 */
.product-service-promises {
  display: flex;
  gap: 24px;
  margin-top: 24px;
  flex-wrap: wrap;
}

.service-promise-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 14px;
}

/* 响应式优化 */
@media (max-width: 1024px) {
  .product-thumbnails-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 14px;
    padding: 14px;
  }

  .product-features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .product-detail-fix {
    margin-top: 300px !important;
    padding-top: 16px !important;
  }

  .product-showcase-section {
    margin-top: 40px;
    margin-bottom: 40px;
  }

  .product-large-images-section {
    padding: 30px 0;
  }

  .product-info-card {
    padding: 24px;
    border-radius: 16px;
  }

  .product-thumbnails-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 12px;
    margin-top: 16px;
  }

  .product-thumbnail-item {
    aspect-ratio: 1/1;
  }

  .product-service-promises {
    flex-direction: column;
    gap: 16px;
  }

  .product-contact-button {
    width: 100%;
    padding: 14px 24px;
  }

  /* 移动端导航区域优化 */
  .product-detail-navigation {
    padding: 16px 20px;
    margin: -8px -8px 24px -8px;
    border-radius: 8px;
  }

  .breadcrumbs {
    font-size: 13px;
    gap: 6px;
  }

  .breadcrumbs a {
    padding: 3px 6px;
  }

  .breadcrumbs .current {
    max-width: 150px;
  }


}

@media (max-width: 640px) {
  .product-features-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .product-thumbnails-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    padding: 10px;
  }

  .product-thumbnail-item {
    aspect-ratio: 1/1;
    border-width: 2px;
  }

  .product-thumbnail-item.active::after {
    width: 16px;
    height: 16px;
    font-size: 10px;
    top: 2px;
    right: 2px;
  }

  .product-feature-item {
    padding: 12px;
    font-size: 14px;
  }
}

/* 产品大图容器 - 全屏显示 */
.product-detail-image-container {
  position: relative;
  width: 100% !important; /* 改为100%而不是100vw，避免溢出 */
  aspect-ratio: 16/9;
  min-height: 400px;
  max-height: 600px;
  border-radius: 0 !important;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  margin-left: 0 !important;
  margin-right: 0 !important;
  max-width: none !important;
  overflow: hidden; /* 确保内容不会溢出容器 */
}

.product-detail-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

/* 移除产品详情图片悬停放大效果 */

/* 产品展示标题区域 */
.product-showcase-header {
  text-align: center;
  margin-bottom: 48px;
  padding: 0 16px;
  position: relative;
  z-index: 2;
}

.product-showcase-title {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 16px;
}

.product-showcase-divider {
  width: 96px;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  margin: 0 auto 16px;
  border-radius: 2px;
}

.product-showcase-description {
  color: #6b7280;
  font-size: 1.125rem;
  max-width: 32rem;
  margin: 0 auto;
  line-height: 1.6;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-slide-in-left {
  opacity: 0;
  animation: slideInFromLeft 0.5s ease-out forwards;
}

.animate-slide-in-right {
  opacity: 0;
  animation: slideInFromRight 0.5s ease-out forwards;
}

/* 导航区域动画 */
.product-detail-navigation {
  animation: fadeInUp 0.4s ease-out forwards;
  opacity: 0;
  animation-delay: 0.1s;
}

/* 延迟动画 */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }

/* 背景图片上的导航样式 - 移除白色背景 */
.product-detail-navigation-overlay {
  background: transparent;
  backdrop-filter: none;
  border-radius: 0;
  padding: 1.5rem 2rem;
  box-shadow: none;
  border: none;
  max-width: 1200px;
  margin: 0 auto;
}

.breadcrumbs-overlay {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  flex-wrap: wrap;
}

.breadcrumbs-overlay a {
  color: white;
  text-decoration: none;
  transition: all 0.2s ease;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  background: rgba(0, 0, 0, 0.7);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.breadcrumbs-overlay a:hover {
  color: white;
  background: rgba(0, 0, 0, 0.85);
  transform: translateY(-1px);
  border-color: rgba(255, 255, 255, 0.3);
}

.breadcrumbs-overlay .separator {
  color: white;
  margin: 0 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  font-weight: 600;
  font-size: 1.1rem;
}

.breadcrumbs-overlay .current {
  color: white;
  font-weight: 700;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  padding: 0.375rem 0.75rem;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
}



/* 移动端覆盖样式调整 */
@media (max-width: 768px) {
  .product-detail-navigation-overlay {
    padding: 1rem 1.5rem;
    margin: 0 1rem;
    border-radius: 8px;
  }

  .breadcrumbs-overlay {
    font-size: 0.875rem;
    gap: 0.375rem;
  }

  .breadcrumbs-overlay .current {
    max-width: 150px;
  }

  /* 移动端也确保全屏显示 */
  .product-large-images-section .max-w-7xl {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .product-detail-image-container {
    width: 100% !important; /* 改为100%而不是100vw，避免溢出 */
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}

/* 通用全屏产品展示样式 - 覆盖所有可能的容器限制 */
.product-large-images-section * {
  box-sizing: border-box;
}

/* 确保所有产品展示相关的容器都能全屏 */
.product-large-images-section .container,
.product-large-images-section .mx-auto,
.product-large-images-section [class*="max-w"],
.product-large-images-section [class*="container"] {
  max-width: none !important;
  width: 100% !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* 产品详情图片组件全屏样式 */
.product-large-images-section .w-full {
  width: 100% !important; /* 改为100%而不是100vw，避免溢出 */
  max-width: none !important;
}

/* 产品展示区域的直接子元素全屏 */
.product-large-images-section > div {
  width: 100% !important;
  max-width: none !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* 强制覆盖ProductLargeGallery组件的容器样式 */
.product-large-images-section div[class*="max-w-7xl"],
.product-large-images-section div[class*="mx-auto"],
.product-large-images-section div[class*="px-"] {
  max-width: none !important;
  width: 100% !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* 强制覆盖space-y-12类的样式 */
.product-large-images-section .space-y-12 {
  gap: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 强制覆盖group类的样式，确保全屏 */
.product-large-images-section .group {
  width: 100% !important; /* 改为100%而不是100vw，避免溢出 */
  max-width: none !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  transform: none !important;
  overflow: hidden; /* 防止内容溢出 */
}

/* 移除group的hover效果，因为我们要全屏显示 */
.product-large-images-section .group:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* 防止页面整体产生水平滚动 */
body {
  overflow-x: hidden;
}

/* 确保产品详情页面容器不会溢出 */
.product-detail-page {
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
}

/* 确保图片不会超出容器边界 - 合并图片样式 */
.product-large-images-section img {
  width: 100% !important;
  max-width: 100% !important;
  height: auto;
  display: block;
  object-fit: cover;
}

/* 强制修复所有可能导致溢出的元素 */
.product-large-images-section,
.product-large-images-section *,
.product-large-images-section *::before,
.product-large-images-section *::after {
  box-sizing: border-box !important;
}

/* 确保没有元素会超出视口宽度 */
.product-large-images-section * {
  max-width: 100% !important;
}

/* 特别处理可能的问题元素 */
.product-large-images-section .space-y-12,
.product-large-images-section .group,
.product-large-images-section .product-detail-image-container,
.product-large-images-section .product-large-gallery {
  contain: layout style !important;
}