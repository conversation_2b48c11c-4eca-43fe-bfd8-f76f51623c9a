const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function createFormSubmissionsTable() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {
    console.log('连接到数据库...');
    
    // 创建表单提交表
    console.log('创建 form_submissions 表...');
    await pool.query(`
      CREATE TABLE IF NOT EXISTS form_submissions (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        phone VARCHAR(50),
        country VARCHAR(100),
        playground_size VARCHAR(100),
        product VARCHAR(255),
        message TEXT NOT NULL,
        status VARCHAR(50) DEFAULT 'new',
        is_read BOOLEAN DEFAULT FALSE,
        admin_notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // 创建索引
    console.log('创建索引...');
    await pool.query(`
      CREATE INDEX IF NOT EXISTS form_submissions_status_idx ON form_submissions(status)
    `);
    
    await pool.query(`
      CREATE INDEX IF NOT EXISTS form_submissions_created_at_idx ON form_submissions(created_at)
    `);
    
    await pool.query(`
      CREATE INDEX IF NOT EXISTS form_submissions_is_read_idx ON form_submissions(is_read)
    `);
    
    console.log('✅ form_submissions 表创建成功');
    
    // 验证表是否存在
    const result = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'form_submissions'
    `);
    
    if (result.rows.length > 0) {
      console.log('✅ 表验证成功: form_submissions 表已存在');
      
      // 检查表结构
      const columns = await pool.query(`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'form_submissions' 
        ORDER BY ordinal_position
      `);
      
      console.log('表结构:');
      columns.rows.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      });
    } else {
      console.log('❌ 表验证失败: form_submissions 表不存在');
    }
    
  } catch (error) {
    console.error('❌ 创建表失败:', error);
  } finally {
    await pool.end();
  }
}

// 运行脚本
createFormSubmissionsTable();
