/**
 * 将app/public目录中的内容合并到根目录的public目录中
 */
const fs = require('fs');
const path = require('path');

// 日志函数
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m', // 青色
    success: '\x1b[32m', // 绿色
    warning: '\x1b[33m', // 黄色
    error: '\x1b[31m', // 红色
  };

  console.log(`${colors[type]}[${type.toUpperCase()}]\x1b[0m ${message}`);
}

// 确保目录存在
function ensureDirectoryExists(directory) {
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
    log(`Created directory: ${directory}`, 'success');
  }
}

// 复制文件
function copyFile(source, target, overwrite = false) {
  try {
    if (fs.existsSync(target) && !overwrite) {
      log(`File already exists: ${target}`, 'warning');
      return false;
    }

    fs.copyFileSync(source, target);
    log(`Copied: ${source} -> ${target}`, 'success');
    return true;
  } catch (error) {
    log(`Failed to copy ${source} to ${target}: ${error.message}`, 'error');
    return false;
  }
}

// 递归复制目录内容
function copyDirectoryContents(sourceDir, targetDir, overwrite = false) {
  if (!fs.existsSync(sourceDir)) {
    log(`Source directory not found: ${sourceDir}`, 'error');
    return false;
  }

  ensureDirectoryExists(targetDir);

  const items = fs.readdirSync(sourceDir);

  for (const item of items) {
    const sourcePath = path.join(sourceDir, item);
    const targetPath = path.join(targetDir, item);

    if (fs.statSync(sourcePath).isDirectory()) {
      ensureDirectoryExists(targetPath);
      copyDirectoryContents(sourcePath, targetPath, overwrite);
    } else {
      copyFile(sourcePath, targetPath, overwrite);
    }
  }

  return true;
}

// 更新Next.js组件中的引用路径
function updateImageReferences() {
  log('Updating image references in components...', 'info');

  const componentsDir = path.join(process.cwd(), 'app', 'components');
  const pagesDir = path.join(process.cwd(), 'app', 'pages');

  // 处理组件目录
  if (fs.existsSync(componentsDir)) {
    updatePathsInDirectory(componentsDir);
  }

  // 处理页面目录
  if (fs.existsSync(pagesDir)) {
    updatePathsInDirectory(pagesDir);
  }

  log('Image references updated', 'success');
}

// 在目录中更新文件引用路径
function updatePathsInDirectory(directory) {
  const items = fs.readdirSync(directory);

  for (const item of items) {
    const itemPath = path.join(directory, item);

    if (fs.statSync(itemPath).isDirectory()) {
      updatePathsInDirectory(itemPath);
    } else if (
      item.endsWith('.js') ||
      item.endsWith('.jsx') ||
      item.endsWith('.ts') ||
      item.endsWith('.tsx')
    ) {
      updatePathsInFile(itemPath);
    }
  }
}

// 更新文件中的路径引用
function updatePathsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');

    // 替换引用app/public的路径
    const newContent = content.replace(/['"]\/app\/public\//g, '"/');

    if (content !== newContent) {
      fs.writeFileSync(filePath, newContent);
      log(`Updated references in ${filePath}`, 'success');
    }
  } catch (error) {
    log(`Failed to update references in ${filePath}: ${error.message}`, 'error');
  }
}

// 主函数：合并public目录
async function normalizePublicFolders() {
  const rootDir = process.cwd();
  const appPublicDir = path.join(rootDir, 'app', 'public');
  const publicDir = path.join(rootDir, 'public');

  if (!fs.existsSync(appPublicDir)) {
    log('app/public directory not found!', 'warning');
    return false;
  }

  if (!fs.existsSync(publicDir)) {
    log('public directory not found, creating it...', 'warning');
    ensureDirectoryExists(publicDir);
  }

  log('Merging app/public into public directory...', 'info');

  // 复制目录内容
  const success = copyDirectoryContents(appPublicDir, publicDir);

  if (success) {
    // 更新组件中的引用路径
    updateImageReferences();

    // 创建备份目录
    const backupDir = path.join(rootDir, 'app', 'public_backup');
    ensureDirectoryExists(backupDir);

    // 复制app/public内容到备份目录
    copyDirectoryContents(appPublicDir, backupDir, true);

    // 创建临时的空文件，标记原目录已经被移动
    fs.writeFileSync(
      path.join(appPublicDir, 'MOVED_TO_ROOT_PUBLIC.txt'),
      'The contents of this directory have been moved to the root public/ directory.\n' +
        'A backup is available in app/public_backup/.\n' +
        'This directory can be safely deleted after verifying that everything works correctly.\n'
    );

    log('Public directories have been normalized!', 'success');
    log('A backup of app/public has been created at app/public_backup', 'info');
    log(
      'After verifying that everything works correctly, you can safely delete app/public',
      'info'
    );

    return true;
  }

  log('Failed to normalize public directories', 'error');
  return false;
}

// 执行主函数
normalizePublicFolders().catch(error => {
  log(`Normalization failed: ${error.message}`, 'error');
  process.exit(1);
});
