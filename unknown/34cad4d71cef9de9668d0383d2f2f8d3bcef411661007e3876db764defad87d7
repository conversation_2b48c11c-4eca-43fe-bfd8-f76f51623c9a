// 补充缺失的英文翻译
const fs = require('fs');

console.log('🌐 开始补充英文翻译...\n');

// 函数工具
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => {
    if (!current[key]) current[key] = {};
    return current[key];
  }, obj);
  target[lastKey] = value;
}

try {
  // 读取文件
  const zhDict = JSON.parse(fs.readFileSync('app/dictionaries/zh.json', 'utf8'));
  const enDict = JSON.parse(fs.readFileSync('app/dictionaries/en.json', 'utf8'));
  const missingKeys = JSON.parse(fs.readFileSync('scripts/missing-dictionary-keys.json', 'utf8'));
  
  console.log(`📊 发现 ${missingKeys.missingInEnglish.length} 个缺失的英文翻译键`);
  
  // 基础翻译映射
  const translations = {
    // Header & Navigation
    'header.company_name': 'Guangzhou Junsheng Amusement Equipment',
    'nav.home': 'Home',
    'nav.products': 'Products',
    'nav.about': 'About',
    'nav.contact': 'Contact',
    'nav.blog': 'Blog',
    'nav.pages': 'Pages',
    'nav.admin': 'Admin',
    
    // Hero section
    'hero.title': 'Indoor Playground Equipment',
    'hero.subtitle': 'Professional manufacturer with 15+ years experience',
    'hero.description': 'Creating safe, fun, and innovative playground solutions for children worldwide',
    'hero.cta.primary': 'Get Quote',
    'hero.cta.secondary': 'View Products',
    
    // Common
    'common.learn_more': 'Learn More',
    'common.contact_us': 'Contact Us',
    'common.get_quote': 'Get Quote',
    'common.view_all': 'View All',
    'common.read_more': 'Read More',
    'common.submit': 'Submit',
    'common.send': 'Send',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.close': 'Close',
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.warning': 'Warning',
    'common.info': 'Info',
    
    // Footer
    'footer.quick_links': 'Quick Links',
    'footer.products': 'Products',
    'footer.services': 'Services',
    'footer.follow_us': 'Follow Us',
    'footer.newsletter': 'Newsletter',
    'footer.newsletter_desc': 'Subscribe to get updates on new products and offers',
    'footer.email_placeholder': 'Enter your email',
    'footer.subscribe': 'Subscribe',
    'footer.copyright': '© 2024 Guangzhou Junsheng. All rights reserved.',
    'footer.privacy': 'Privacy Policy',
    'footer.terms': 'Terms of Service',
    
    // Products page
    'products.title': 'Our Products',
    'products.subtitle': 'Explore our wide range of indoor playground equipment',
    'products.filter.all': 'All Products',
    'products.filter.indoor': 'Indoor Playground',
    'products.filter.trampoline': 'Trampoline Park',
    'products.filter.size_small': '100-500 sqm',
    'products.filter.size_medium': '500-1000 sqm',
    'products.filter.size_large': '1000+ sqm',
    'products.no_results': 'No products found',
    'products.view_details': 'View Details',
    'products.request_quote': 'Request Quote',
    
    // Product details
    'product.specifications': 'Specifications',
    'product.features': 'Features',
    'product.gallery': 'Gallery',
    'product.related': 'Related Products',
    'product.inquiry': 'Product Inquiry',
    'product.size': 'Size',
    'product.age_range': 'Age Range',
    'product.capacity': 'Capacity',
    'product.material': 'Material',
    'product.warranty': 'Warranty',
    'product.certification': 'Certification',
    
    // About page
    'about.title': 'About Us',
    'about.subtitle': 'Leading manufacturer of indoor playground equipment',
    'about.image_alt': 'About Us - Indoor Playground Equipment',
    'about.company_name': 'Junsheng Amusement Equipment',
    'about.mission.title': 'Our Mission',
    'about.mission.paragraph1': 'We are dedicated to creating innovative and safe indoor playground equipment that brings joy to children worldwide.',
    'about.mission.paragraph2': 'With over 15 years of experience, we combine creativity, safety, and fun to deliver exceptional play experiences.',
    'about.stats.projects': 'Projects Completed',
    'about.stats.experience': 'Years of Experience',
    'about.stats.safety': 'Safety Record',
    'about.cta.description': 'Ready to create an amazing indoor playground?',
    
    // Contact page
    'contact.title': 'Contact Us',
    'contact.subtitle': 'Get in touch with our team',
    'contact.description': 'Get in touch with us for your indoor playground needs',
    'contact.office': 'Office',
    'contact.address': 'Address',
    'contact.phone': 'Phone',
    'contact.email': 'Email',
    'contact.email_us': 'Email Us',
    'contact.call_us': 'Call Us',
    'contact.working_hours': 'Working Hours',
    'contact.hours': 'Monday - Friday: 9:00 AM - 6:00 PM',
    'contact.form.title': 'Send us a message',
    'contact.form.subtitle': "We'll get back to you within 24 hours",
    'contact.form.name': 'Name',
    'contact.form.name_placeholder': 'Your Name',
    'contact.form.email': 'Email',
    'contact.form.email_placeholder': 'Your Email',
    'contact.form.phone': 'Phone',
    'contact.form.phone_placeholder': 'Your Phone Number',
    'contact.form.country': 'Country',
    'contact.form.country_placeholder': 'Your Country',
    'contact.form.message': 'Message',
    'contact.form.message_placeholder': 'Tell us about your project...',
    'contact.form.playground_size': 'Playground Size',
    'contact.form.size_option1': '100-500 sqm',
    'contact.form.size_option2': '500-1000 sqm',
    'contact.form.size_option3': '1000+ sqm',
    'contact.form.size_option4': 'Not sure yet',
    'contact.form.submit': 'Send Message',
    'contact.form.sending': 'Sending...',
    'contact.form.success': 'Message sent successfully!',
    'contact.form.error': 'Failed to send message. Please try again.',
    
    // Quote form
    'quote.title': 'Get a Free Quote',
    'quote.subtitle': 'Fill out the form below and we\'ll get back to you with a custom quote',
    'quote.form.project_type': 'Project Type',
    'quote.form.budget': 'Budget Range',
    'quote.form.timeline': 'Project Timeline',
    'quote.form.location': 'Project Location',
    'quote.form.additional_info': 'Additional Information',
    
    // Blog
    'blog.title': 'Blog & News',
    'blog.subtitle': 'Latest updates and industry insights',
    'blog.categories': 'Categories',
    'blog.recent_posts': 'Recent Posts',
    'blog.read_time': 'min read',
    'blog.by_author': 'By',
    'blog.published_on': 'Published on',
    'blog.share': 'Share',
    'blog.related_posts': 'Related Posts',
    
    // Services
    'services.design': 'Custom Design',
    'services.manufacturing': 'Manufacturing',
    'services.installation': 'Installation',
    'services.maintenance': 'Maintenance',
    'services.consultation': 'Consultation',
    'services.warranty': 'Warranty Support'
  };
  
  // 应用翻译
  let addedCount = 0;
  let needsManualTranslation = [];
  
  missingKeys.missingInEnglish.forEach(key => {
    const zhValue = getNestedValue(zhDict, key);
    
    if (translations[key]) {
      setNestedValue(enDict, key, translations[key]);
      addedCount++;
    } else if (zhValue) {
      // 记录需要人工翻译的键
      needsManualTranslation.push({
        key,
        chinese: zhValue
      });
    }
  });
  
  // 补充缺失的中文键
  missingKeys.missingInChinese.forEach(key => {
    const enValue = getNestedValue(enDict, key);
    if (enValue) {
      if (key === 'custom_playground.subtitle') {
        setNestedValue(zhDict, key, '将您的独特愿景变为现实，我们提供定制设计服务');
      } else if (key === 'custom_playground.cta') {
        setNestedValue(zhDict, key, '开始定制设计');
      } else if (key === 'footer.products') {
        setNestedValue(zhDict, key, '产品');
      }
    }
  });
  
  // 保存更新后的字典
  fs.writeFileSync('app/dictionaries/en.json', JSON.stringify(enDict, null, 2), 'utf8');
  fs.writeFileSync('app/dictionaries/zh.json', JSON.stringify(zhDict, null, 2), 'utf8');
  
  console.log(`\n✅ 翻译添加完成！`);
  console.log(`- 成功添加 ${addedCount} 个英文翻译`);
  console.log(`- 还有 ${needsManualTranslation.length} 个键需要人工翻译`);
  console.log(`- 补充了 ${missingKeys.missingInChinese.length} 个中文翻译`);
  
  // 保存需要人工翻译的列表
  if (needsManualTranslation.length > 0) {
    fs.writeFileSync(
      'scripts/needs-manual-translation.json',
      JSON.stringify(needsManualTranslation, null, 2),
      'utf8'
    );
    console.log(`\n📝 需要人工翻译的键已保存到: scripts/needs-manual-translation.json`);
    console.log('前5个需要翻译的示例:');
    needsManualTranslation.slice(0, 5).forEach(item => {
      console.log(`- ${item.key}: "${item.chinese}"`);
    });
  }
  
} catch (error) {
  console.error('❌ 错误:', error.message);
  console.error(error.stack);
}

console.log('\n🎯 下一步建议:');
console.log('1. 检查 scripts/needs-manual-translation.json 完成剩余翻译');
console.log('2. 运行硬编码文本修复脚本');
console.log('3. 测试语言切换功能'); 