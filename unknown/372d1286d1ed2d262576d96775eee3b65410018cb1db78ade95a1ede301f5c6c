'use client';

import { useLanguage } from './components/LanguageProvider';
import Link from 'next/link';

export default function NotFound() {
  const { locale } = useLanguage();

  return (
    <div className="container mx-auto py-20 text-center">
      <div className="not-found-content">
        <h1 className="text-6xl font-bold mb-4" suppressHydrationWarning>
          404
        </h1>
        <h2 className="text-2xl font-medium mb-4" suppressHydrationWarning>
          页面不存在
        </h2>
        <p className="mb-8" suppressHydrationWarning>
          抱歉，您请求的页面不存在或已被移除。
        </p>
        <Link href={`/${locale}`} className="btn-primary inline-block" suppressHydrationWarning>
          返回首页
        </Link>
      </div>
    </div>
  );
}
