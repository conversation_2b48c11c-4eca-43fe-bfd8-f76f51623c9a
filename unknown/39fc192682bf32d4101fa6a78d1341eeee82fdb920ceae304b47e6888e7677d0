// 启动项目并自动打开调试页面
const { spawn } = require('child_process');
const open = require('open');

console.log('🚀 启动Next.js开发服务器...');

// 启动Next.js开发服务器
const nextProcess = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  shell: true
});

// 监听进程退出
nextProcess.on('close', (code) => {
  console.log(`Next.js进程已退出，退出码: ${code}`);
});

// 设置超时等待服务器启动
setTimeout(async () => {
  try {
    console.log('📊 打开调试页面...');
    
    // 打开浏览器访问调试页面
    await open('http://localhost:3000/debug');
    
    // 打开产品测试页面
    await open('http://localhost:3000/zh/products/ar-trampoline');
    
    console.log('✅ 调试页面已在浏览器中打开');
    console.log('✅ 产品测试页面已在浏览器中打开');
    console.log('');
    console.log('🔍 测试步骤:');
    console.log('1. 检查产品详情页是否正常显示');
    console.log('2. 如果不正常，访问调试页面进行诊断和修复');
    console.log('3. 可以访问 /debug/fix-products 页面进行更深入的修复');
    console.log('');
    console.log('💡 调试提示:');
    console.log('- 查看浏览器控制台的网络请求和日志');
    console.log('- 检查服务器端控制台的日志输出');
    console.log('- 如果页面缓存问题，在URL后添加?t=时间戳参数');
  } catch (error) {
    console.error('打开浏览器失败:', error);
  }
}, 5000); // 等待5秒让服务器启动 