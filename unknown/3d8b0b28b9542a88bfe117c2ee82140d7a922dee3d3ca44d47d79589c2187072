/* 主页英雄区域样式 */
.hero-section {
  position: relative;
  overflow: hidden;
  min-height: 80vh;
  display: flex;
  align-items: center;
  background-color: #f8fafc;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 600px;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  background: linear-gradient(90deg, #2563eb, #7e22ce);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.5;
  margin-bottom: 2rem;
  color: #4b5563;
}

.hero-cta {
  display: flex;
  gap: 1rem;
}

.hero-image {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 50%;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.hero-overlay {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.8) 40%, rgba(255,255,255,0) 100%);
  z-index: 1;
}

.hero-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

.hero-stats {
  display: flex;
  margin-top: 3rem;
  gap: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2563eb;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #6b7280;
  font-size: 0.875rem;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.125rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 70vh;
  }
  
  .hero-image {
    width: 100%;
    opacity: 0.15;
  }
  
  .hero-overlay {
    background: rgba(255,255,255,0.8);
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-content {
    max-width: 100%;
    text-align: center;
    padding: 0 1rem;
  }
  
  .hero-cta {
    justify-content: center;
  }
  
  .hero-stats {
    flex-direction: column;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    min-height: 60vh;
    padding: 4rem 0;
  }
  
  .hero-title {
    font-size: 1.75rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .hero-cta {
    flex-direction: column;
  }
} 