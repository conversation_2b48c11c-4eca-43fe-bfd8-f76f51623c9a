# 重启开发服务器并测试产品显示
Write-Host "🔄 重启开发服务器并测试产品显示" -ForegroundColor Green

# 1. 停止现有的 Node.js 进程
Write-Host "`n1. 停止现有的 Node.js 进程..." -ForegroundColor Yellow
Get-Process | Where-Object {$_.ProcessName -eq "node"} | Stop-Process -Force
Start-Sleep -Seconds 2

# 2. 清理 Next.js 缓存
Write-Host "`n2. 清理 Next.js 缓存..." -ForegroundColor Yellow
if (Test-Path ".next") {
    Remove-Item -Recurse -Force ".next"
    Write-Host "   ✅ 已删除 .next 目录"
} else {
    Write-Host "   ℹ️  .next 目录不存在"
}

if (Test-Path "node_modules/.cache") {
    Remove-Item -Recurse -Force "node_modules/.cache"
    Write-Host "   ✅ 已删除 node_modules/.cache"
} else {
    Write-Host "   ℹ️  node_modules/.cache 不存在"
}

# 3. 测试 API
Write-Host "`n3. 测试 API..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/api/products?published=true" -Headers @{"Accept"="application/json"} -TimeoutSec 5
    $data = $response.Content | ConvertFrom-Json
    if ($data.success -and $data.products.Count -gt 0) {
        Write-Host "   ✅ API 正常，返回 $($data.products.Count) 个产品" -ForegroundColor Green
    } else {
        Write-Host "   ❌ API 返回空数据" -ForegroundColor Red
    }
} catch {
    Write-Host "   ⚠️  API 测试失败，可能服务器未运行: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 4. 启动开发服务器
Write-Host "`n4. 启动开发服务器..." -ForegroundColor Yellow
Write-Host "   请在新的终端窗口中运行: npm run dev" -ForegroundColor Cyan
Write-Host "   或者按 Ctrl+C 停止此脚本，然后运行 npm run dev" -ForegroundColor Cyan

# 5. 等待服务器启动
Write-Host "`n5. 等待服务器启动..." -ForegroundColor Yellow
Write-Host "   服务器启动后，请访问以下链接测试:" -ForegroundColor Cyan
Write-Host "   - 产品页面: http://localhost:3000/zh/products" -ForegroundColor White
Write-Host "   - 重定向测试: http://localhost:3000/products" -ForegroundColor White
Write-Host "   - API 测试: http://localhost:3000/api/products?published=true" -ForegroundColor White

# 6. 浏览器缓存清理提示
Write-Host "`n6. 浏览器缓存清理:" -ForegroundColor Yellow
Write-Host "   请在浏览器中按 Ctrl+Shift+R 强制刷新页面" -ForegroundColor Cyan
Write-Host "   或者打开开发者工具 (F12) -> Network 标签 -> 勾选 'Disable cache'" -ForegroundColor Cyan

# 7. 调试提示
Write-Host "`n7. 如果问题仍然存在:" -ForegroundColor Yellow
Write-Host "   - 打开浏览器开发者工具 (F12)" -ForegroundColor White
Write-Host "   - 查看 Console 标签页是否有错误信息" -ForegroundColor White
Write-Host "   - 查看 Network 标签页，确认 API 请求是否成功" -ForegroundColor White
Write-Host "   - 检查 Elements 标签页，确认 ProductGrid 组件是否正确渲染" -ForegroundColor White

Write-Host "`n✅ 脚本执行完成！" -ForegroundColor Green
Write-Host "现在请启动开发服务器并测试产品页面。" -ForegroundColor Green
