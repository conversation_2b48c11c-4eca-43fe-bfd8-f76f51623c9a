'use client';

import { usePathname } from 'next/navigation';
import QuoteForm from './QuoteForm';
import HydrationErrorBoundary from './HydrationErrorBoundary';

export default function QuoteFormConditional() {
  // 使用usePathname钩子获取当前路径
  const pathname = usePathname();

  // 检查是否是不需要显示表单的页面
  const shouldHideForm =
    // 首页不需要表单
    !pathname ||
    pathname === '/' ||
    pathname === '/en' ||
    pathname === '/zh' ||
    pathname.includes('/home') ||
    // 联系我们页面不需要表单
    pathname.includes('/contact-us') ||
    // 以下路径也不显示表单
    pathname.includes('/about-us') ||
    pathname.includes('/admin') ||
    // 防止产品列表页重复显示表单 - 修复逻辑
    pathname.endsWith('/products') ||
    pathname === '/zh/products' ||
    pathname === '/en/products' ||
    // 指南页面不需要表单
    pathname.includes('/how-to-purchase-your-first-holographic-system') ||
    pathname.includes('/custom-playground-design');

  // 如果是不需要表单的页面，不显示表单
  if (shouldHideForm) {

    return null;
  }

  // 如果需要表单，显示表单
  return (
    <HydrationErrorBoundary>
      <QuoteForm />
    </HydrationErrorBoundary>
  );
}
