// 深度检查语言切换功能
const fs = require('fs');
const path = require('path');
const glob = require('glob');

console.log('🔍 开始深度检查语言切换功能...\n');

// 收集检查结果
const issues = [];
const warnings = [];
const successes = [];
const hardcodedTexts = [];

// 1. 检查语言切换组件
console.log('🔄 检查语言切换组件...');
const langSwitcherFiles = [
  'app/components/LanguageSwitcher.tsx',
  'app/components/Header.tsx',
  'app/components/Navigation.tsx'
];

langSwitcherFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    if (content.includes('usePathname') && content.includes('router.push')) {
      successes.push(`✅ ${file} 包含语言切换逻辑`);
    } else {
      warnings.push(`⚠️ ${file} 可能缺少语言切换逻辑`);
    }
  }
});

// 2. 检查字典文件
console.log('\n📚 检查字典文件...');
const dictionaries = {
  zh: null,
  en: null
};

// 检查字典目录
if (fs.existsSync('app/dictionaries')) {
  ['zh.json', 'en.json'].forEach(dictFile => {
    const dictPath = path.join('app/dictionaries', dictFile);
    if (fs.existsSync(dictPath)) {
      try {
        dictionaries[dictFile.replace('.json', '')] = JSON.parse(fs.readFileSync(dictPath, 'utf8'));
        successes.push(`✅ ${dictFile} 字典文件存在且有效`);
      } catch (error) {
        issues.push(`❌ ${dictFile} 字典文件解析失败: ${error.message}`);
      }
    } else {
      issues.push(`❌ 缺少字典文件: ${dictPath}`);
    }
  });
} else {
  issues.push('❌ 字典目录不存在: app/dictionaries');
}

// 3. 比较字典键的完整性
console.log('\n🔑 检查字典键的完整性...');
if (dictionaries.zh && dictionaries.en) {
  const zhKeys = getAllKeys(dictionaries.zh);
  const enKeys = getAllKeys(dictionaries.en);
  
  // 检查缺失的键
  const missingInEn = zhKeys.filter(key => !enKeys.includes(key));
  const missingInZh = enKeys.filter(key => !zhKeys.includes(key));
  
  if (missingInEn.length > 0) {
    issues.push(`❌ 英文字典缺少 ${missingInEn.length} 个键: ${missingInEn.slice(0, 5).join(', ')}${missingInEn.length > 5 ? '...' : ''}`);
  }
  
  if (missingInZh.length > 0) {
    issues.push(`❌ 中文字典缺少 ${missingInZh.length} 个键: ${missingInZh.slice(0, 5).join(', ')}${missingInZh.length > 5 ? '...' : ''}`);
  }
  
  if (missingInEn.length === 0 && missingInZh.length === 0) {
    successes.push('✅ 字典键完全匹配');
  }
}

// 4. 检查页面组件中的硬编码文本
console.log('\n📄 检查页面中的硬编码文本...');
const pagePatterns = [
  'app/[lang]/**/*.tsx',
  'app/[lang]/**/*.jsx',
  'app/components/**/*.tsx',
  'app/components/**/*.jsx'
];

const hardcodedPatterns = [
  // 中文硬编码
  /[\u4e00-\u9fa5]+/g,
  // 英文硬编码（排除常见的代码关键字）
  /(?<!\/\/.*|\/\*.*|import.*|export.*|const.*|let.*|var.*|function.*|class.*|interface.*|type.*)['"]((?!className|href|src|alt|id|key|type|name|value|placeholder|onClick|onChange|onSubmit)[A-Z][a-zA-Z\s]{3,})['"]/g,
  // 三元运算符中的文本
  /locale\s*===?\s*['"]zh['"]\s*\?\s*['"]([^'"]+)['"]\s*:\s*['"]([^'"]+)['"]/g
];

pagePatterns.forEach(pattern => {
  const files = glob.sync(pattern);
  files.forEach(file => {
    const content = fs.readFileSync(file, 'utf8');
    const relativePath = path.relative(process.cwd(), file);
    
    // 跳过一些特殊文件
    if (relativePath.includes('test') || relativePath.includes('spec')) {
      return;
    }
    
    // 检查是否使用了 t() 函数
    if (!content.includes('const { t }') && !content.includes('const t =')) {
      if (content.match(/[\u4e00-\u9fa5]/) || content.match(/[A-Z][a-zA-Z\s]{5,}/)) {
        warnings.push(`⚠️ ${relativePath} 可能未使用国际化函数`);
      }
    }
    
    // 检查硬编码文本
    hardcodedPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches && matches.length > 0) {
        matches.forEach(match => {
          // 过滤掉一些误报
          if (!match.includes('console.log') && 
              !match.includes('error') && 
              !match.includes('loading') &&
              !match.includes('className')) {
            hardcodedTexts.push({
              file: relativePath,
              text: match.substring(0, 50) + (match.length > 50 ? '...' : '')
            });
          }
        });
      }
    });
  });
});

// 5. 检查路由处理
console.log('\n🛣️ 检查路由语言参数处理...');
const routeFiles = glob.sync('app/[lang]/**/page.tsx');
routeFiles.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  const relativePath = path.relative(process.cwd(), file);
  
  if (content.includes('params.lang') || content.includes('params?.lang')) {
    successes.push(`✅ ${relativePath} 正确处理语言参数`);
  } else if (!content.includes('use client')) {
    warnings.push(`⚠️ ${relativePath} 可能未处理语言参数`);
  }
});

// 6. 检查中间件
console.log('\n🔧 检查中间件语言处理...');
if (fs.existsSync('middleware.ts') || fs.existsSync('middleware.js')) {
  const middlewareFile = fs.existsSync('middleware.ts') ? 'middleware.ts' : 'middleware.js';
  const content = fs.readFileSync(middlewareFile, 'utf8');
  
  if (content.includes('locale') && (content.includes('/zh') || content.includes('/en'))) {
    successes.push('✅ 中间件包含语言处理逻辑');
  } else {
    issues.push('❌ 中间件缺少语言处理逻辑');
  }
}

// 7. 检查特定组件的国际化
console.log('\n🧩 检查特定组件的国际化...');
const criticalComponents = [
  { file: 'app/components/Header.tsx', keys: ['navigation.home', 'navigation.products'] },
  { file: 'app/components/Footer.tsx', keys: ['footer.company', 'footer.contact'] },
  { file: 'app/components/QuoteForm.tsx', keys: ['quote.title', 'quote.submit'] },
  { file: 'app/components/ProductGrid.tsx', keys: ['products.view_details'] }
];

criticalComponents.forEach(({ file, keys }) => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    let hasI18n = false;
    
    keys.forEach(key => {
      if (content.includes(`t('${key}')`) || content.includes(`t("${key}")`)) {
        hasI18n = true;
      }
    });
    
    if (hasI18n) {
      successes.push(`✅ ${file} 使用了国际化`);
    } else {
      warnings.push(`⚠️ ${file} 可能缺少国际化实现`);
    }
  }
});

// 生成报告
console.log('\n' + '='.repeat(60));
console.log('📊 语言切换功能检查报告\n');

console.log(`✅ 成功项: ${successes.length}`);
console.log(`⚠️ 警告项: ${warnings.length}`);
console.log(`❌ 问题项: ${issues.length}`);
console.log(`📝 硬编码文本: ${hardcodedTexts.length} 处`);

if (issues.length > 0) {
  console.log('\n❌ 发现的问题:');
  issues.forEach(issue => console.log(`  ${issue}`));
}

if (warnings.length > 0) {
  console.log('\n⚠️ 警告:');
  warnings.slice(0, 10).forEach(warning => console.log(`  ${warning}`));
  if (warnings.length > 10) {
    console.log(`  ... 还有 ${warnings.length - 10} 个警告`);
  }
}

if (hardcodedTexts.length > 0) {
  console.log('\n📝 硬编码文本示例:');
  hardcodedTexts.slice(0, 10).forEach(({ file, text }) => {
    console.log(`  ${file}: "${text}"`);
  });
  if (hardcodedTexts.length > 10) {
    console.log(`  ... 还有 ${hardcodedTexts.length - 10} 处硬编码文本`);
  }
}

console.log('\n💡 建议:');
console.log('1. 确保所有页面组件都使用 t() 函数进行文本国际化');
console.log('2. 检查并补充缺失的字典键');
console.log('3. 将硬编码文本移至字典文件');
console.log('4. 确保路由切换时正确传递语言参数');
console.log('5. 测试每个页面的语言切换功能');

console.log('\n' + '='.repeat(60));

// 辅助函数：递归获取所有键
function getAllKeys(obj, prefix = '') {
  let keys = [];
  
  for (const key in obj) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      keys = keys.concat(getAllKeys(obj[key], fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
} 