'use client';

import Image from 'next/image';
import Link from 'next/link';
import { generatePlaceholderSVG } from '../../utils/imagePlaceholder';

export default function BusinessConsultingContent() {
  return (
    <main className="main-content">
      <section className="page-header">
        <div className="container">
          <h1 className="page-title">Business Consulting</h1>
          <div className="breadcrumbs">
            <Link href="/">Home</Link> &gt; <Link href="/service">Service</Link> &gt;{' '}
            <span>Business Consulting</span>
          </div>
        </div>
      </section>

      <section className="consulting-intro">
        <div className="container">
          <div className="content-grid">
            <div className="content-image">
              <Image
                src={generatePlaceholderSVG(600, 400, 'Business Consulting')}
                alt="Indoor Playground Business Consulting"
                width={600}
                height={400}
              />
            </div>
            <div className="content-text">
              <h2>Expert Guidance for Playground Business Success</h2>
              <p>
                Starting or growing an indoor playground business involves numerous challenges and
                decisions. At Infinity Playground Park, we offer comprehensive business consulting
                services to help you navigate the complexities of the indoor playground industry.
              </p>
              <p>
                Our team of experienced consultants has helped establish hundreds of successful
                indoor playground businesses worldwide. We provide data-driven insights, industry
                best practices, and practical strategies to maximize your business potential.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="consulting-services">
        <div className="container">
          <h2 className="section-title">Our Consulting Services</h2>

          <div className="services-grid">
            <div className="service-card">
              <div className="service-image">
                <Image
                  src={generatePlaceholderSVG(400, 300, 'Business Planning')}
                  alt="Business Planning"
                  width={400}
                  height={300}
                />
              </div>
              <div className="service-content">
                <h3>Business Planning</h3>
                <p>
                  Comprehensive business plan development to set a solid foundation for your
                  playground business.
                </p>
                <ul className="service-features">
                  <li>Market analysis and feasibility studies</li>
                  <li>Financial projections and budgeting</li>
                  <li>Business model optimization</li>
                  <li>Funding strategy development</li>
                </ul>
              </div>
            </div>

            <div className="service-card">
              <div className="service-image">
                <Image
                  src={generatePlaceholderSVG(400, 300, 'Location Selection')}
                  alt="Location Selection"
                  width={400}
                  height={300}
                />
              </div>
              <div className="service-content">
                <h3>Location Selection</h3>
                <p>
                  Expert guidance on choosing the ideal location for your indoor playground
                  business.
                </p>
                <ul className="service-features">
                  <li>Demographic analysis</li>
                  <li>Foot traffic assessment</li>
                  <li>Competitor proximity evaluation</li>
                  <li>Space requirement planning</li>
                </ul>
              </div>
            </div>

            <div className="service-card">
              <div className="service-image">
                <Image
                  src={generatePlaceholderSVG(400, 300, 'Operational Strategy')}
                  alt="Operational Strategy"
                  width={400}
                  height={300}
                />
              </div>
              <div className="service-content">
                <h3>Operational Strategy</h3>
                <p>
                  Development of efficient operational procedures to optimize your business
                  performance.
                </p>
                <ul className="service-features">
                  <li>Staffing and training plans</li>
                  <li>Standard operating procedures</li>
                  <li>Safety protocols and risk management</li>
                  <li>Technology integration</li>
                </ul>
              </div>
            </div>

            <div className="service-card">
              <div className="service-image">
                <Image
                  src={generatePlaceholderSVG(400, 300, 'Marketing Strategy')}
                  alt="Marketing Strategy"
                  width={400}
                  height={300}
                />
              </div>
              <div className="service-content">
                <h3>Marketing Strategy</h3>
                <p>Custom marketing strategies to attract customers and build your brand.</p>
                <ul className="service-features">
                  <li>Brand development</li>
                  <li>Digital marketing planning</li>
                  <li>Customer acquisition strategies</li>
                  <li>Loyalty program development</li>
                </ul>
              </div>
            </div>

            <div className="service-card">
              <div className="service-image">
                <Image
                  src={generatePlaceholderSVG(400, 300, 'Revenue Optimization')}
                  alt="Revenue Optimization"
                  width={400}
                  height={300}
                />
              </div>
              <div className="service-content">
                <h3>Revenue Optimization</h3>
                <p>Strategies to maximize revenue streams and improve profitability.</p>
                <ul className="service-features">
                  <li>Pricing strategy development</li>
                  <li>Additional revenue stream identification</li>
                  <li>Food & beverage program optimization</li>
                  <li>Special events and program planning</li>
                </ul>
              </div>
            </div>

            <div className="service-card">
              <div className="service-image">
                <Image
                  src={generatePlaceholderSVG(400, 300, 'Staff Training')}
                  alt="Staff Training"
                  width={400}
                  height={300}
                />
              </div>
              <div className="service-content">
                <h3>Staff Training</h3>
                <p>Comprehensive training programs for your staff to ensure excellent service.</p>
                <ul className="service-features">
                  <li>Customer service training</li>
                  <li>Safety and emergency procedures</li>
                  <li>Operational training</li>
                  <li>Leadership development</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="consulting-process">
        <div className="container">
          <h2 className="section-title">Our Consulting Process</h2>

          <div className="process-timeline">
            <div className="timeline-item">
              <div className="timeline-number">1</div>
              <div className="timeline-content">
                <h3>Initial Consultation</h3>
                <p>
                  We begin with a detailed discussion to understand your vision, goals, and specific
                  challenges for your playground business.
                </p>
              </div>
            </div>

            <div className="timeline-item">
              <div className="timeline-number">2</div>
              <div className="timeline-content">
                <h3>Business Assessment</h3>
                <p>
                  For existing businesses, we conduct a comprehensive assessment to identify
                  strengths, weaknesses, and opportunities for improvement.
                </p>
              </div>
            </div>

            <div className="timeline-item">
              <div className="timeline-number">3</div>
              <div className="timeline-content">
                <h3>Strategy Development</h3>
                <p>
                  Based on our findings, we develop customized strategies and action plans tailored
                  to your specific business needs.
                </p>
              </div>
            </div>

            <div className="timeline-item">
              <div className="timeline-number">4</div>
              <div className="timeline-content">
                <h3>Implementation Support</h3>
                <p>
                  We provide guidance and support during the implementation phase to ensure
                  strategies are executed effectively.
                </p>
              </div>
            </div>

            <div className="timeline-item">
              <div className="timeline-number">5</div>
              <div className="timeline-content">
                <h3>Ongoing Evaluation</h3>
                <p>
                  We offer continuous evaluation and refinement of strategies to adapt to changing
                  market conditions and business needs.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="case-studies">
        <div className="container">
          <h2 className="section-title">Success Stories</h2>

          <div className="case-grid">
            <div className="case-item">
              <div className="case-image">
                <Image
                  src={generatePlaceholderSVG(400, 300, 'Success Story 1')}
                  alt="Jungle Adventure Park Success Story"
                  width={400}
                  height={300}
                />
              </div>
              <div className="case-content">
                <h3>Jungle Adventure Park</h3>
                <p>
                  A new indoor playground that achieved 85% capacity within three months of opening
                  through our comprehensive business consulting services.
                </p>
                <p className="case-results">
                  <strong>Results:</strong> 30% higher revenue than projected, successful marketing
                  strategy leading to 15,000 visitors in the first quarter.
                </p>
              </div>
            </div>

            <div className="case-item">
              <div className="case-image">
                <Image
                  src={generatePlaceholderSVG(400, 300, 'Success Story 2')}
                  alt="KidZone Playground Success Story"
                  width={400}
                  height={300}
                />
              </div>
              <div className="case-content">
                <h3>KidZone Playground</h3>
                <p>
                  An existing playground business that was struggling with profitability. Our
                  consulting services helped identify operational inefficiencies and implement new
                  revenue strategies.
                </p>
                <p className="case-results">
                  <strong>Results:</strong> 40% increase in revenue within six months, 25% reduction
                  in operational costs, introduction of three new successful revenue streams.
                </p>
              </div>
            </div>

            <div className="case-item">
              <div className="case-image">
                <Image
                  src={generatePlaceholderSVG(400, 300, 'Success Story 3')}
                  alt="FunWorld Family Center Success Story"
                  width={400}
                  height={300}
                />
              </div>
              <div className="case-content">
                <h3>FunWorld Family Center</h3>
                <p>
                  A family entertainment center looking to expand their business with an indoor
                  playground. Our team provided guidance throughout the entire expansion process.
                </p>
                <p className="case-results">
                  <strong>Results:</strong> Successful expansion completed on time and under budget,
                  50% increase in overall business revenue, significant increase in customer
                  retention.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="consulting-team">
        <div className="container">
          <h2 className="section-title">Our Consulting Team</h2>

          <div className="team-content">
            <div className="team-image">
              <Image
                src={generatePlaceholderSVG(500, 400, 'Consulting Team')}
                alt="Our Business Consulting Team"
                width={500}
                height={400}
              />
            </div>
            <div className="team-info">
              <h3>Industry Experts</h3>
              <p>
                Our consulting team consists of experienced professionals with backgrounds in indoor
                playground operations, business management, marketing, and finance. Many of our
                consultants have owned or managed successful indoor playground businesses
                themselves.
              </p>
              <p>
                This real-world experience combined with our collective knowledge of the industry
                allows us to provide practical, effective advice that generates real results for
                your business.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="cta-section cta-particles">
        <div className="container">
          <div className="cta-content">
            <h2>准备好讨论您的全息定制解决方案？</h2>
            <p>今天就联系我们的团队，探索我们如何为您的需求创造完美的全息解决方案。</p>
            <Link
              href="/contact-us"
              className="btn-primary"
              data-text="立即联系我们"
            >
              {'立即联系我们'.split('').map((char, index) => (
                <i key={index}>{char === ' ' ? '\u00A0' : char}</i>
              ))}
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
