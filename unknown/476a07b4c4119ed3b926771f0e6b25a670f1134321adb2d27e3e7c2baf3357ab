// 测试表单重复问题修复
const fs = require('fs');

console.log('🔧 测试表单重复问题修复\n');

// 检查QuoteFormConditional逻辑
const conditionalFormPath = './app/components/QuoteFormConditional.tsx';
const conditionalContent = fs.readFileSync(conditionalFormPath, 'utf8');

console.log('1. QuoteFormConditional 路径检测逻辑:');

// 检查是否包含简化后的逻辑
if (conditionalContent.includes("pathname === '/zh/products'") && 
    conditionalContent.includes("pathname === '/en/products'")) {
  console.log('   ✅ 路径检测逻辑已简化');
} else {
  console.log('   ❌ 路径检测逻辑需要修复');
}

// 检查是否移除了复杂的正则表达式
if (!conditionalContent.includes('pathname.match(/') && 
    !conditionalContent.includes('!pathname.includes(\'/products/\')')) {
  console.log('   ✅ 移除了复杂的正则表达式');
} else {
  console.log('   ❌ 仍包含复杂的正则表达式');
}

// 检查CSS样式简化
console.log('\n2. CSS样式简化检查:');
const productsCssPath = './app/styles/products.css';
const productsContent = fs.readFileSync(productsCssPath, 'utf8');

// 检查是否简化了CSS规则
if (!productsContent.includes('margin: 2rem auto !important') && 
    !productsContent.includes('quote-form-container ~ .quote-form-container')) {
  console.log('   ✅ CSS样式已简化，移除了冲突规则');
} else {
  console.log('   ❌ CSS样式仍有复杂规则');
}

// 检查是否保留了基本样式
if (productsContent.includes('.products-page-quote-form')) {
  console.log('   ✅ 保留了基本的产品页面表单样式类');
} else {
  console.log('   ❌ 缺少基本的产品页面表单样式类');
}

// 检查全局样式
console.log('\n3. 全局表单样式检查:');
const globalFormCssPath = './app/styles/global-quote-form.css';
const globalContent = fs.readFileSync(globalFormCssPath, 'utf8');

if (globalContent.includes('margin: 2rem auto !important') &&
    globalContent.includes('margin: 3rem auto !important')) {
  console.log('   ✅ 全局表单样式保持居中设置');
} else {
  console.log('   ❌ 全局表单样式缺少居中设置');
}

console.log('\n' + '='.repeat(50));
console.log('🎯 修复总结:');
console.log('1. 简化了QuoteFormConditional的路径检测逻辑');
console.log('2. 移除了CSS中的冲突规则');
console.log('3. 保留了基础的居中样式');
console.log('4. 确保产品页面只显示一个表单');
console.log('\n🌐 测试建议:');
console.log('- 访问 /zh/products 和 /en/products');
console.log('- 多次刷新页面测试表单位置一致性');
console.log('- 检查浏览器控制台是否显示"不显示全局表单"消息');
console.log('='.repeat(50)); 