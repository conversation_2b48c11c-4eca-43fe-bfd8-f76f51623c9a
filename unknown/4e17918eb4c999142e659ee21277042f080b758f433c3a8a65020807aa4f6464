// 项目健康检查脚本
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 开始项目健康检查...\n');

// 检查结果收集
const issues = [];
const warnings = [];
const successes = [];

// 1. 检查依赖项
console.log('📦 检查依赖项...');
try {
  // 检查package.json是否存在
  if (fs.existsSync('package.json')) {
    successes.push('✅ package.json 文件存在');
    
    // 检查node_modules
    if (fs.existsSync('node_modules')) {
      successes.push('✅ node_modules 目录存在');
      
      // 检查关键依赖
      const criticalDeps = ['next', 'react', 'react-dom', 'framer-motion'];
      criticalDeps.forEach(dep => {
        if (fs.existsSync(`node_modules/${dep}`)) {
          successes.push(`✅ ${dep} 已安装`);
        } else {
          issues.push(`❌ 缺少关键依赖: ${dep}`);
        }
      });
    } else {
      issues.push('❌ node_modules 目录不存在，需要运行 npm install');
    }
  } else {
    issues.push('❌ package.json 文件不存在');
  }
} catch (error) {
  issues.push(`❌ 检查依赖项时出错: ${error.message}`);
}

// 2. 检查环境变量
console.log('\n🔐 检查环境变量...');
if (fs.existsSync('.env.local')) {
  successes.push('✅ .env.local 文件存在');
  
  // 检查必要的环境变量
  const envContent = fs.readFileSync('.env.local', 'utf8');
  const requiredEnvVars = [
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL'
  ];
  
  requiredEnvVars.forEach(envVar => {
    if (envContent.includes(envVar)) {
      successes.push(`✅ ${envVar} 已配置`);
    } else {
      warnings.push(`⚠️ 环境变量 ${envVar} 可能未配置`);
    }
  });
} else {
  issues.push('❌ .env.local 文件不存在');
}

// 3. 检查目录结构
console.log('\n📁 检查目录结构...');
const requiredDirs = [
  'app',
  'app/[lang]',
  'app/api',
  'app/components',
  'app/styles',
  'public',
  'lib',
  'models'
];

requiredDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    successes.push(`✅ ${dir} 目录存在`);
  } else {
    issues.push(`❌ 缺少目录: ${dir}`);
  }
});

// 4. 检查关键文件
console.log('\n📄 检查关键文件...');
const criticalFiles = [
  'next.config.js',
  'tailwind.config.js',
  'tsconfig.json',
  'middleware.ts',
  'app/layout.tsx'
];

criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    successes.push(`✅ ${file} 文件存在`);
  } else {
    issues.push(`❌ 缺少文件: ${file}`);
  }
});

// 5. 检查Next.js缓存
console.log('\n💾 检查Next.js缓存...');
if (fs.existsSync('.next')) {
  const cacheSize = getDirectorySize('.next');
  if (cacheSize > 500 * 1024 * 1024) { // 500MB
    warnings.push(`⚠️ .next 缓存目录较大 (${(cacheSize / 1024 / 1024).toFixed(2)}MB)，建议清理`);
  } else {
    successes.push(`✅ .next 缓存目录正常 (${(cacheSize / 1024 / 1024).toFixed(2)}MB)`);
  }
} else {
  successes.push('✅ .next 缓存目录不存在（首次运行）');
}

// 6. 检查TypeScript错误
console.log('\n🔧 检查TypeScript配置...');
try {
  execSync('npx tsc --noEmit', { stdio: 'pipe' });
  successes.push('✅ TypeScript 编译无错误');
} catch (error) {
  const errorOutput = error.stdout ? error.stdout.toString() : '';
  if (errorOutput.includes('error')) {
    warnings.push('⚠️ 存在TypeScript编译错误');
  }
}

// 7. 检查端口占用
console.log('\n🌐 检查端口占用...');
const checkPort = (port) => {
  try {
    execSync(`netstat -ano | findstr :${port}`, { stdio: 'pipe' });
    return true;
  } catch {
    return false;
  }
};

if (checkPort(3000)) {
  warnings.push('⚠️ 端口 3000 已被占用');
} else {
  successes.push('✅ 端口 3000 可用');
}

// 8. 检查数据库连接文件
console.log('\n🗄️ 检查数据库配置...');
if (fs.existsSync('lib/db.js') || fs.existsSync('lib/db.ts')) {
  successes.push('✅ 数据库连接文件存在');
} else {
  warnings.push('⚠️ 未找到数据库连接文件');
}

// 生成报告
console.log('\n' + '='.repeat(60));
console.log('📊 项目健康检查报告\n');

console.log(`✅ 成功项: ${successes.length}`);
console.log(`⚠️ 警告项: ${warnings.length}`);
console.log(`❌ 问题项: ${issues.length}`);

if (issues.length > 0) {
  console.log('\n❌ 发现的问题:');
  issues.forEach(issue => console.log(`  ${issue}`));
}

if (warnings.length > 0) {
  console.log('\n⚠️ 警告:');
  warnings.forEach(warning => console.log(`  ${warning}`));
}

console.log('\n💡 建议的修复步骤:');
if (issues.some(i => i.includes('node_modules'))) {
  console.log('1. 运行 npm install 安装依赖');
}
if (issues.some(i => i.includes('.env.local'))) {
  console.log('2. 创建 .env.local 文件并配置环境变量');
}
if (warnings.some(w => w.includes('.next 缓存'))) {
  console.log('3. 运行 npm run clean 清理缓存');
}
if (warnings.some(w => w.includes('端口 3000'))) {
  console.log('4. 关闭占用端口 3000 的程序或使用其他端口');
}

console.log('\n' + '='.repeat(60));

// 辅助函数：计算目录大小
function getDirectorySize(dirPath) {
  let size = 0;
  
  function calculateSize(currentPath) {
    try {
      const stats = fs.statSync(currentPath);
      if (stats.isFile()) {
        size += stats.size;
      } else if (stats.isDirectory()) {
        const files = fs.readdirSync(currentPath);
        files.forEach(file => {
          calculateSize(path.join(currentPath, file));
        });
      }
    } catch (error) {
      // 忽略无法访问的文件
    }
  }
  
  calculateSize(dirPath);
  return size;
}

// 返回状态码
process.exit(issues.length > 0 ? 1 : 0); 