---
description:
globs:
alwaysApply: false
---
# 开发工作流程

本项目的开发工作流程指南。

## 项目设置
1. 安装依赖：`npm install`
2. 设置环境变量：参考 [.env.local.fixed](mdc:.env.local.fixed)
3. 数据库初始化：使用 `/app/api/db-init/` 进行初始化

## 开发命令
- `npm run dev` - 启动开发服务器
- `npm run build` - 构建生产版本
- `npm run start` - 运行生产版本

## 代码规范
- 遵循 [.eslintrc.json](mdc:.eslintrc.json) 中的规则
- 使用 TypeScript 进行类型检查
- 使用 Tailwind CSS 进行样式设计

## 目录结构规范
- 按功能组织文件和目录
- 页面组件应放在相应的路由目录下
- 共享组件应放在 `/app/components/` 或 `/components/` 目录
- API路由应放在相应的 `/app/api/` 或 `/pages/api/` 目录

## 国际化
- 使用 `/app/dictionaries/` 中的翻译资源
- 根据 `[lang]` 参数加载对应语言的内容
- 确保所有用户可见的文本都支持国际化

## 部署流程
1. 运行 `npm run build` 构建生产版本
2. 确保环境变量正确设置
3. 运行 `npm run start` 启动生产服务器
