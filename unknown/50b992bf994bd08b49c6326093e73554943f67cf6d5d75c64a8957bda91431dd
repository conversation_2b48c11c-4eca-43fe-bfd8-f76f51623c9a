const { Pool } = require('pg');
const fs = require('fs');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function uploadProducts() {
  console.log('🚀 开始上传新产品...');
  
  try {
    // 读取产品数据
    const productsData = JSON.parse(fs.readFileSync('scripts/new-products-data.json', 'utf8'));
    console.log(`📦 读取到 ${productsData.length} 个产品`);
    
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    let successCount = 0;
    
    for (const product of productsData) {
      try {
        console.log(`\n📝 添加产品: ${product.name}`);
        
        // 检查产品是否已存在
        const existingCheck = await client.query('SELECT id FROM products WHERE slug = $1', [product.slug]);
        
        if (existingCheck.rows.length > 0) {
          console.log(`⚠️  产品已存在，跳过: ${product.slug}`);
          continue;
        }
        
        // 插入产品
        const result = await client.query(
          `INSERT INTO products 
           (name, slug, description, type, style, features, images, in_stock, is_featured, price) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
           RETURNING id`,
          [
            product.name,
            product.slug,
            product.description,
            product.type,
            product.style,
            JSON.stringify(product.features),
            JSON.stringify(product.images),
            product.in_stock,
            product.is_featured,
            product.price
          ]
        );
        
        console.log(`✅ 产品创建成功 (ID: ${result.rows[0].id})`);
        successCount++;
        
      } catch (error) {
        console.log(`❌ 产品创建失败: ${error.message}`);
      }
    }
    
    console.log(`\n📊 上传结果: 成功添加 ${successCount} 个产品`);
    
    // 验证结果
    const verifyResult = await client.query('SELECT name, slug FROM products ORDER BY id DESC LIMIT 10');
    console.log('\n📋 最新产品列表:');
    verifyResult.rows.forEach(row => {
      console.log(`  - ${row.name} (${row.slug})`);
    });
    
    client.release();
    console.log('\n🎉 产品上传完成!');
    
  } catch (error) {
    console.error('❌ 上传失败:', error.message);
  } finally {
    await pool.end();
  }
}

uploadProducts();
