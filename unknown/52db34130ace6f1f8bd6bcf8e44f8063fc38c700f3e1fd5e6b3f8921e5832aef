/**
 * 修复API路由文件中的导入路径问题
 */
const fs = require('fs');
const path = require('path');

// 日志函数
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m', // 青色
    success: '\x1b[32m', // 绿色
    warning: '\x1b[33m', // 黄色
    error: '\x1b[31m', // 红色
  };

  console.log(`${colors[type]}[${type.toUpperCase()}]\x1b[0m ${message}`);
}

// 获取所有API文件
function getAllApiFiles(directory) {
  const files = [];

  function traverseDirectory(currentPath) {
    const entries = fs.readdirSync(currentPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(currentPath, entry.name);

      if (entry.isDirectory()) {
        traverseDirectory(fullPath);
      } else if (entry.isFile() && entry.name === 'route.ts') {
        files.push(fullPath);
      }
    }
  }

  traverseDirectory(directory);
  return files;
}

// 修复导入路径
function fixImportPaths(filePath, content) {
  // 需要修复的路径映射
  const pathMappings = [
    // 修复db导入
    {
      search: /from ['"]\.\.\/\.\.\/\.\.\/lib\/db['"]/g,
      replace: "from '../../../../../../../lib/db'",
    },
    {
      search: /from ['"]\.\.\/\.\.\/lib\/db['"]/g,
      replace: "from '../../../../../../lib/db'",
    },
    {
      search: /from ['"]\.\.\/lib\/db['"]/g,
      replace: "from '../../../../../lib/db'",
    },
    // 修复mongodb导入
    {
      search: /from ['"]\.\.\/\.\.\/\.\.\/lib\/mongodb['"]/g,
      replace: "from '../../../../../../../lib/mongodb'",
    },
    {
      search: /from ['"]\.\.\/\.\.\/lib\/mongodb['"]/g,
      replace: "from '../../../../../../lib/mongodb'",
    },
    {
      search: /from ['"]\.\.\/lib\/mongodb['"]/g,
      replace: "from '../../../../../lib/mongodb'",
    },
    // 修复postgresql导入
    {
      search: /from ['"]\.\.\/\.\.\/\.\.\/lib\/postgresql['"]/g,
      replace: "from '../../../../../../../lib/postgresql'",
    },
    {
      search: /from ['"]\.\.\/\.\.\/lib\/postgresql['"]/g,
      replace: "from '../../../../../../lib/postgresql'",
    },
    {
      search: /from ['"]\.\.\/lib\/postgresql['"]/g,
      replace: "from '../../../../../lib/postgresql'",
    },
    // 修复模型导入
    {
      search: /from ['"]\.\.\/\.\.\/\.\.\/models\/([A-Za-z]+)['"]/g,
      replace: "from '../../../../../../../models/$1'",
    },
    {
      search: /from ['"]\.\.\/\.\.\/models\/([A-Za-z]+)['"]/g,
      replace: "from '../../../../../../models/$1'",
    },
    {
      search: /from ['"]\.\.\/models\/([A-Za-z]+)['"]/g,
      replace: "from '../../../../../models/$1'",
    },
    // 修复auth导入
    {
      search: /from ['"]\.\.\/auth\/\[\.\.\.[a-zA-Z]+\]['"]/g,
      replace: "from '../../../../auth/[...nextauth]/route'",
    },
    {
      search: /from ['"]\.\/auth\/\[\.\.\.[a-zA-Z]+\]['"]/g,
      replace: "from '../auth/[...nextauth]/route'",
    },
  ];

  let modifiedContent = content;
  let changed = false;

  for (const mapping of pathMappings) {
    if (mapping.search.test(modifiedContent)) {
      modifiedContent = modifiedContent.replace(mapping.search, mapping.replace);
      changed = true;
    }
  }

  // 替换 NextApiRequest/NextApiResponse 为 NextRequest/NextResponse
  if (modifiedContent.includes('NextApiRequest') || modifiedContent.includes('NextApiResponse')) {
    modifiedContent = modifiedContent
      .replace(/NextApiRequest/g, 'NextRequest')
      .replace(/NextApiResponse/g, 'NextResponse');
    changed = true;
  }

  // 修复表单数据处理
  if (
    modifiedContent.includes('parseFormAsync(req,') ||
    modifiedContent.includes('parseFormAsync(req ,')
  ) {
    modifiedContent = modifiedContent
      .replace(/parseFormAsync\(req,/g, 'parseFormAsync(request,')
      .replace(/parseFormAsync\(req ,/g, 'parseFormAsync(request ,');
    changed = true;
  }

  return { modifiedContent, changed };
}

// 主函数
async function main() {
  log('Starting API import path fixing...', 'info');

  // 项目根目录
  const rootDir = process.cwd();
  const apiDir = path.join(rootDir, 'app', 'api');

  // 获取所有API文件
  const apiFiles = getAllApiFiles(apiDir);
  log(`Found ${apiFiles.length} API route files`, 'info');

  let fixedCount = 0;
  let skippedCount = 0;

  // 处理每个文件
  for (const filePath of apiFiles) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const { modifiedContent, changed } = fixImportPaths(filePath, content);

      if (changed) {
        fs.writeFileSync(filePath, modifiedContent, 'utf8');
        log(`Fixed import paths in: ${filePath}`, 'success');
        fixedCount++;
      } else {
        log(`No changes needed for: ${filePath}`, 'info');
        skippedCount++;
      }
    } catch (error) {
      log(`Error processing ${filePath}: ${error.message}`, 'error');
    }
  }

  // 显示结果
  log(`Operation completed.`, 'success');
  log(`Fixed files: ${fixedCount}`, 'success');
  log(`Skipped files: ${skippedCount}`, 'info');

  log(`Please restart the development server to apply changes.`, 'info');
}

// 执行主函数
main().catch(error => {
  log(`An error occurred: ${error.message}`, 'error');
  process.exit(1);
});
