import { NextResponse } from 'next/server';
import dbConnect from '@/lib/postgresql.js';
import bcrypt from 'bcryptjs';

// 在App Router中，使用HTTP方法名称作为导出函数
export async function POST() {
  try {
    console.log('Connecting to database...');
    const pool = await dbConnect();
    console.log('Database connected');

    // 获取一个客户端连接
    const client = await pool.connect();

    try {
      // 检查是否已存在管理员用户
      const adminResult = await client.query('SELECT id FROM users WHERE role = $1 LIMIT 1', [
        'admin',
      ]);

      if (adminResult.rows.length > 0) {
        return NextResponse.json(
          {
            success: false,
            message: 'Admin user already exists',
            userId: adminResult.rows[0].id,
          },
          { status: 200 }
        );
      }

      // 创建默认管理员用户
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('Admin@123', salt);

      const insertResult = await client.query(
        `INSERT INTO users (username, email, password_hash, role) 
         VALUES ($1, $2, $3, $4) RETURNING id`,
        ['admin', '<EMAIL>', hashedPassword, 'admin']
      );

      return NextResponse.json(
        {
          success: true,
          message: 'Default admin user created successfully',
          userId: insertResult.rows[0].id,
        },
        { status: 201 }
      );
    } finally {
      // 确保客户端连接被释放
      client.release();
    }
  } catch (error) {
    console.error('Error creating default admin:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to create default admin user',
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
