// 修复app/[lang]/admin/组件导入路径问题
const fs = require('fs');
const path = require('path');

// 主要需要修复的文件夹
const adminDir = './app/[lang]/admin';

// 修复路径映射
const pathFixes = [
  {
    pattern: /from\s+["']..\/..\/..\/..\/components\/admin\/Layout["']/g,
    replacement: 'from "../../../components/admin/Layout"',
    description: '修复Layout组件路径'
  },
  {
    pattern: /from\s+["']..\/..\/..\/..\/components\/admin\/AuthGuard["']/g,
    replacement: 'from "../../../components/admin/AuthGuard"',
    description: '修复AuthGuard组件路径'
  }
];

// 递归处理目录
function processDirectory(dirPath) {
  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const item of items) {
      const itemPath = path.join(dirPath, item.name);

      if (item.isDirectory()) {
        processDirectory(itemPath);
      } else if (item.isFile() && (item.name.endsWith('.tsx') || item.name.endsWith('.jsx'))) {
        fixFile(itemPath);
      }
    }
  } catch (error) {
    console.error(`处理目录时出错: ${dirPath}`, error);
  }
}

// 修复单个文件
function fixFile(filePath) {
  try {
    console.log(`检查文件: ${filePath}`);
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    for (const { pattern, replacement, description } of pathFixes) {
      if (pattern.test(content)) {
        console.log(`  ${description}: ${filePath}`);
        content = content.replace(pattern, replacement);
        modified = true;
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
    } else {
      console.log(`  无需修改: ${filePath}`);
    }
  } catch (error) {
    console.error(`处理文件时出错: ${filePath}`, error);
  }
}

// 执行主函数
console.log('开始修复组件导入路径问题...');
processDirectory(adminDir);
console.log('完成！'); 