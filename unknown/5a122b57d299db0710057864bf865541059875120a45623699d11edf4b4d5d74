'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import AdminLayout from '../../../components/admin/Layout';
import AuthGuard from '../../../components/admin/AuthGuard';



export default function Page({ params }: { params: { lang: string } }) {
  const { data: session } = useSession();
  const [localSession, setLocalSession] = useState<any>(null);
  const [stats, setStats] = useState({
    products: 0,
    content: 0,
    users: 0,
  });
  const [adminLang, setAdminLang] = useState('zh'); // 管理界面语言
  const [isClientLoaded, setIsClientLoaded] = useState(false);

  // 简化的翻译函数
  const t = (key: string) => {
    const currentUser = localSession?.user || session?.user;
    const translations: { [key: string]: string } = {
      welcome: `欢迎, ${currentUser?.name || 'Admin'}!`,
      manage_description: '管理您的跨境电商网站内容和产品。',
      back_to_website: '返回网站',
      loading_stats: '加载统计信息...',
      products: '产品',
      content_items: '内容项目',
      users: '用户',
      manage: '管理',
      quick_actions: '快捷操作',
      add_new_product: '添加新产品',
      add_new_content: '添加新内容',
      add_new_user: '添加新用户',
      error_fetching_data: '获取数据时出错。显示的是缓存数据。',
    };

    return translations[key] || key;
  };

  // Effect 1: Client-side initialization (runs once)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Set admin language
      const storedAdminLang = localStorage.getItem('ADMIN_LANG') || 'zh';
      setAdminLang(storedAdminLang);



      // Check local session
      const adminSession = localStorage.getItem('admin_session');
      if (adminSession) {
        try {
          const sessionData = JSON.parse(adminSession);
          if (new Date(sessionData.expires) > new Date()) {
            setLocalSession(sessionData);
          } else {
            localStorage.removeItem('admin_session');
          }
        } catch (error) {
          localStorage.removeItem('admin_session');
        }
      }



      setIsClientLoaded(true);
    }
  }, []); // Empty dependency array - runs only once

  // Set default stats
  useEffect(() => {
    if (isClientLoaded) {
      const currentUser = localSession?.user || session?.user;
      setStats({
        products: 25,
        content: 12,
        users: currentUser?.role?.includes('admin') ? 8 : 0,
      });
    }
  }, [session, localSession, isClientLoaded]);

  const getCurrentUser = () => {
    if (localSession?.user) {
      return {
        ...localSession.user,
        source: '本地登录',
        sessionType: 'localStorage'
      };
    }
    
    if (session?.user) {
      return {
        ...session.user,
        source: 'NextAuth登录',
        sessionType: 'nextauth'
      };
    }
    
    return null;
  };

  const currentUser = getCurrentUser();

  const statCards = [
    {
      title: t('products'),
      count: stats.products,
      icon: '📦',
      link: `/${params.lang}/admin/products`,
      color: 'bg-blue-500',
    },
    {
      title: t('content_items'),
      count: stats.content,
      icon: '📝',
      link: `/${params.lang}/admin/content`,
      color: 'bg-green-500',
    },
  ];

  // Add users card for admin only
  if (currentUser?.role?.includes('admin')) {
    statCards.push({
      title: t('users'),
      count: stats.users,
      icon: '👥',
      link: `/${params.lang}/admin/users`,
      color: 'bg-purple-500',
    });
  }



  // 防止hydration错误：等待客户端加载完成
  if (!isClientLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-xl text-gray-600">加载中...</div>
      </div>
    );
  }

  return (
    <AuthGuard>
      <AdminLayout title={adminLang === 'zh' ? '控制面板' : 'Dashboard'}>
        <div className="space-y-6">
          {/* 欢迎信息 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              欢迎回到管理后台
            </h2>
            {currentUser ? (
              <div className="space-y-2">
                <p className="text-gray-600">
                  用户: <span className="font-semibold">{currentUser.name || currentUser.username}</span>
                </p>
                <p className="text-gray-600">
                  角色: <span className="font-semibold">{currentUser.role}</span>
                </p>
                <p className="text-gray-600">
                  登录方式: <span className="font-semibold">{currentUser.source}</span>
                </p>
              </div>
            ) : (
              <p className="text-red-600">未检测到用户信息</p>
            )}
          </div>

          {/* 权限测试区域 */}
          <div className="bg-blue-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-900 mb-4">权限测试</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <a
                href={`/${params.lang}/admin/products`}
                className="block p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow"
              >
                <h4 className="font-semibold text-gray-900">产品管理</h4>
                <p className="text-sm text-gray-600">管理所有产品</p>
              </a>
              
              <a
                href={`/${params.lang}/admin/content`}
                className="block p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow"
              >
                <h4 className="font-semibold text-gray-900">内容管理</h4>
                <p className="text-sm text-gray-600">管理网站内容</p>
              </a>
              
              <a
                href={`/${params.lang}/admin/users`}
                className="block p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow"
              >
                <h4 className="font-semibold text-gray-900">用户管理</h4>
                <p className="text-sm text-gray-600">管理用户权限</p>
              </a>
            </div>
          </div>


        </div>
      </AdminLayout>
    </AuthGuard>
  );
}
