/**
 * 将Pages Router页面迁移到App Router的自动化脚本
 */
const fs = require('fs');
const path = require('path');

// 日志函数
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m', // 青色
    success: '\x1b[32m', // 绿色
    warning: '\x1b[33m', // 黄色
    error: '\x1b[31m', // 红色
  };

  console.log(`${colors[type]}[${type.toUpperCase()}]\x1b[0m ${message}`);
}

// 创建目录函数
function ensureDirectoryExists(directory) {
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
    log(`Created directory: ${directory}`, 'success');
  }
}

// 获取页面路由文件
function getPageRouteFiles() {
  const pagesDir = path.join(process.cwd(), 'pages');
  const results = [];

  function traverse(dir, routePath = '') {
    if (!fs.existsSync(dir)) return;

    const files = fs.readdirSync(dir);

    for (const file of files) {
      const fullPath = path.join(dir, file);
      const stats = fs.statSync(fullPath);

      // 跳过API目录和特殊文件
      if (file === 'api' || file.startsWith('_')) continue;

      if (stats.isDirectory()) {
        // 递归处理子目录
        traverse(fullPath, path.join(routePath, file));
      } else if (
        stats.isFile() &&
        (file.endsWith('.js') ||
          file.endsWith('.jsx') ||
          file.endsWith('.ts') ||
          file.endsWith('.tsx'))
      ) {
        // 处理页面文件
        const ext = path.extname(file);
        const baseName = path.basename(file, ext);

        // 跳过非页面文件
        if (baseName.startsWith('_')) continue;

        let finalRoutePath = routePath;

        // 处理index文件
        if (baseName !== 'index') {
          finalRoutePath = path.join(routePath, baseName);
        }

        results.push({
          file: fullPath,
          routePath: finalRoutePath,
          fileName: file,
        });
      }
    }
  }

  traverse(pagesDir);
  return results;
}

// 转换Pages页面代码为App Router格式
function convertPageCode(content) {
  // 替换getServerSideProps
  let newContent = content;

  // 检测是否使用了getServerSideProps
  if (content.includes('export async function getServerSideProps')) {
    log('Detected getServerSideProps, converting to Server Component...', 'info');

    // 移除getServerSideProps函数
    newContent = newContent.replace(
      /export\s+async\s+function\s+getServerSideProps[^{]*{[\s\S]*?return\s*{[\s\S]*?props\s*:([^}]*)}[\s\S]*?}/g,
      (match, propsGroup) => {
        // 提取props对象
        return `// Server Component data fetching
// Converted from getServerSideProps
const props = ${propsGroup.trim()};`;
      }
    );

    // 添加'use server'指令
    if (!newContent.includes("'use server'") && !newContent.includes('"use server"')) {
      newContent = "'use server';\n" + newContent;
    }
  }

  // 检测是否使用了getStaticProps
  if (content.includes('export async function getStaticProps')) {
    log('Detected getStaticProps, converting to Server Component...', 'info');

    // 移除getStaticProps函数
    newContent = newContent.replace(
      /export\s+async\s+function\s+getStaticProps[^{]*{[\s\S]*?return\s*{[\s\S]*?props\s*:([^}]*)}[\s\S]*?}/g,
      (match, propsGroup) => {
        // 提取props对象
        return `// Server Component data fetching
// Converted from getStaticProps
const props = ${propsGroup.trim()};`;
      }
    );

    // 添加'use server'指令
    if (!newContent.includes("'use server'") && !newContent.includes('"use server"')) {
      newContent = "'use server';\n" + newContent;
    }
  }

  // 检测是否使用了getStaticPaths
  if (content.includes('export async function getStaticPaths')) {
    log('Detected getStaticPaths, converting to generateStaticParams...', 'info');

    // 替换getStaticPaths为generateStaticParams
    newContent = newContent.replace(
      /export\s+async\s+function\s+getStaticPaths[^{]*{[\s\S]*?return\s*{[\s\S]*?paths\s*:([\s\S]*?),[\s\S]*?fallback\s*:([^}]*)}[\s\S]*?}/g,
      (match, pathsGroup, fallbackGroup) => {
        // 提取paths数组并转换为generateStaticParams格式
        return `// Converted from getStaticPaths
export async function generateStaticParams() {
  // Original paths: ${pathsGroup.trim()}
  // Original fallback: ${fallbackGroup.trim()}
  const paths = ${pathsGroup.trim()};
  
  // Convert paths format to generateStaticParams format
  return paths.map(path => {
    // If path is a string, convert to object with segment
    if (typeof path === 'string') {
      const segments = path.split('/').filter(Boolean);
      const lastSegment = segments[segments.length - 1];
      return { slug: lastSegment };
    }
    
    // If path is already an object with params
    return path.params;
  });
}`;
      }
    );
  }

  // 处理默认导出
  if (newContent.includes('export default function')) {
    // 替换为命名组件导出
    newContent = newContent.replace(
      /export\s+default\s+function\s+(\w+)/,
      'export default function Page'
    );
  }

  return newContent;
}

// 迁移单个页面
function migratePage(pageInfo) {
  const { file, routePath, fileName } = pageInfo;

  // 目标路径在app/[lang]下
  const targetDir = path.join(process.cwd(), 'app', '[lang]', ...routePath.split(path.sep));
  const targetFile = path.join(targetDir, 'page' + path.extname(fileName));

  // 确保目标目录存在
  ensureDirectoryExists(targetDir);

  // 读取源文件
  const content = fs.readFileSync(file, 'utf8');

  // 转换代码
  const newContent = convertPageCode(content);

  // 写入新文件
  fs.writeFileSync(targetFile, newContent);
  log(`Migrated: ${file} -> ${targetFile}`, 'success');

  return true;
}

// 主函数
async function main() {
  log('Starting Pages Router migration...', 'info');

  // 获取页面路由文件
  const pageRoutes = getPageRouteFiles();
  log(`Found ${pageRoutes.length} pages to migrate`, 'info');

  if (pageRoutes.length === 0) {
    log('No pages to migrate!', 'success');
    return;
  }

  // 迁移每个页面
  let successCount = 0;

  for (const pageRoute of pageRoutes) {
    try {
      const success = migratePage(pageRoute);
      if (success) successCount++;
    } catch (error) {
      log(`Failed to migrate ${pageRoute.file}: ${error.message}`, 'error');
    }
  }

  log(
    `Migration completed. Successfully migrated: ${successCount}/${pageRoutes.length}`,
    'success'
  );

  if (successCount === pageRoutes.length) {
    log('All pages were successfully migrated!', 'success');
    log('Next steps:', 'info');
    log('1. Check the migrated pages and make any necessary adjustments', 'info');
    log('2. Test the migrated pages to ensure they work correctly', 'info');
    log('3. Update any internal links to point to the new routes', 'info');
    log('4. When ready, you can safely remove the old pages', 'info');
  } else {
    log('Some pages failed to migrate. Check the errors above and fix them manually.', 'warning');
  }
}

// 执行主函数
main().catch(error => {
  log(`Migration failed: ${error.message}`, 'error');
  process.exit(1);
});
