import { Pool, PoolClient } from 'pg';

// Define our connection cache type
interface ConnectionCache {
  pool: Pool | null;
  client: PoolClient | null;
  isConnected: boolean;
}

// Global connection cache
const globalWithPg = global as typeof global & {
  pgCache: ConnectionCache;
};

// Initialize cache
if (!globalWithPg.pgCache) {
  globalWithPg.pgCache = {
    pool: null,
    client: null,
    isConnected: false,
  };
}

// Get PostgreSQL connection parameters from environment variables
const POSTGRES_URI =
  process.env.POSTGRES_URI ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';
const POSTGRES_HOST = process.env.POSTGRES_HOST || 'localhost';
const POSTGRES_DATABASE = process.env.POSTGRES_DATABASE || 'infinity_playground';
const POSTGRES_USER = process.env.POSTGRES_USER || 'postgres';
const POSTGRES_PASSWORD = process.env.POSTGRES_PASSWORD || 'postgres';
const POSTGRES_PORT = parseInt(process.env.POSTGRES_PORT || '5432', 10);

// 最大重试次数
const MAX_RETRIES = 3;
// 重试延迟（毫秒）
const RETRY_DELAY = 1000;

/**
 * 延迟函数
 */
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Connect to PostgreSQL database with retry mechanism
 */
export async function dbConnect(retryCount = 0): Promise<Pool> {
  // If we have an existing pool and it's connected, return it
  if (globalWithPg.pgCache.pool && globalWithPg.pgCache.isConnected) {
    return globalWithPg.pgCache.pool;
  }

  try {
    // Create connection pool with improved settings
    const pool = new Pool({
      connectionString: POSTGRES_URI || undefined,
      host: !POSTGRES_URI ? POSTGRES_HOST : undefined,
      database: !POSTGRES_URI ? POSTGRES_DATABASE : undefined,
      user: !POSTGRES_URI ? POSTGRES_USER : undefined,
      password: !POSTGRES_URI ? POSTGRES_PASSWORD : undefined,
      port: !POSTGRES_URI ? POSTGRES_PORT : undefined,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
      max: 20, // Maximum number of clients in the pool
      idleTimeoutMillis: 60000, // How long a client is allowed to remain idle before being closed (1 minute)
      connectionTimeoutMillis: 30000, // 增加连接超时时间至30秒
      keepAlive: true, // Enable TCP keepalive
      keepAliveInitialDelayMillis: 30000, // Delay before TCP keepalive (30 seconds)
    });

    // Add error handler to the pool
    pool.on('error', err => {
      console.error('Unexpected error on idle client', err);
      globalWithPg.pgCache.isConnected = false;
    });

    console.log('Attempting to connect to PostgreSQL with:', {
      host: !POSTGRES_URI ? POSTGRES_HOST : '(from connection string)',
      database: !POSTGRES_URI ? POSTGRES_DATABASE : '(from connection string)',
      port: !POSTGRES_URI ? POSTGRES_PORT : '(from connection string)',
      retry: retryCount > 0 ? `Retry attempt ${retryCount}/${MAX_RETRIES}` : 'Initial connection',
    });

    // Test the connection
    const client = await pool.connect();
    await client.query('SELECT 1'); // Test query to ensure connection is working
    client.release();

    console.log('PostgreSQL connected successfully');
    globalWithPg.pgCache.pool = pool;
    globalWithPg.pgCache.isConnected = true;
    return pool;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(
      `PostgreSQL connection error (attempt ${retryCount + 1}/${MAX_RETRIES + 1}):`,
      error
    );

    // Implement retry mechanism
    if (retryCount < MAX_RETRIES) {
      console.log(`Retrying connection in ${RETRY_DELAY}ms...`);
      await delay(RETRY_DELAY * (retryCount + 1)); // Exponential backoff
      return dbConnect(retryCount + 1);
    }

    // After max retries, create mock data mode or throw error
    console.error('Failed to connect to PostgreSQL after multiple attempts. Using mock data mode.');
    globalWithPg.pgCache.isConnected = false;

    // Instead of throwing, you could return a pool that will use mock data
    // For now, we'll throw to maintain compatibility with existing code
    throw new Error(
      `Failed to connect to PostgreSQL after ${MAX_RETRIES + 1} attempts: ${errorMessage}`
    );
  }
}

/**
 * Get a client from the connection pool
 */
export async function getClient(): Promise<PoolClient> {
  const pool = await dbConnect();
  return await pool.connect();
}

/**
 * Check if we're connected to the database
 */
export function isConnected(): boolean {
  return globalWithPg.pgCache?.isConnected || false;
}

export default dbConnect;
