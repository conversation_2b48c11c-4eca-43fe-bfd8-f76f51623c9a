'use client';

import { useState, useEffect } from 'react';
import { useLanguage } from './LanguageProvider';

interface FormData {
  name: string;
  email: string;
  phone: string;
  country: string;
  message: string;
  playground_size: string;
  product?: string;
}

interface ContactFormProps {
  productName?: string;
}

export default function ContactForm({ productName }: ContactFormProps) {
  const { t } = useLanguage();
  const [isClient, setIsClient] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    country: '',
    message: productName ? `I am interested in: ${productName}` : '',
    playground_size: '100-500 sqm',
    product: productName || '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState('');

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitError('');

    try {
      // 提交表单数据到API
      const response = await fetch('/api/form-submissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        console.log('Form submitted successfully:', result);
        setSubmitSuccess(true);
        // 清空表单
        setFormData({
          name: '',
          email: '',
          phone: '',
          country: '',
          message: '',
          playground_size: '100-500 sqm',
          product: '',
        });
      } else {
        throw new Error(result.message || 'Submission failed');
      }
    } catch (error) {
      setSubmitError(
        t('contact.form.error', { fallback: 'Submission failed, please try again later' })
      );
      console.error('Submit error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isClient) {
    return (
      <div className="contact-form-container">
        <h2>Get Your Free Quote</h2>
        <p>Fill out the form below and our team will get back to you within 24 hours.</p>
        <div className="loading-placeholder">
          <div className="form-skeleton">
            <div className="skeleton-line"></div>
            <div className="skeleton-line"></div>
            <div className="skeleton-line"></div>
            <div className="skeleton-button"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="contact-form-container">
      <h2>
        {t('contact.form.title', { fallback: 'Get Your Free Quote' })}
      </h2>
      <p>
        {t('contact.form.subtitle', {
          fallback: 'Fill out the form below and our team will get back to you within 24 hours.',
        })}
      </p>

      {submitSuccess ? (
        <div className="success-message">
          <i className="fas fa-check-circle"></i>
          <h3>
            {t('contact.form.success.title', { fallback: 'Thank You!' })}
          </h3>
          <p>
            {t('contact.form.success.message', {
              fallback: 'Your message has been sent successfully. We will contact you shortly.',
            })}
          </p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="contact-form">
          {productName && (
            <div className="form-group product-inquiry mb-4">
              <div className="bg-blue-50 p-3 rounded-lg border border-blue-100 flex items-start">
                <div className="shrink-0 text-blue-500 mr-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-blue-800">
                    {t('contact.form.product_inquiry', { fallback: 'Product Inquiry' })}:
                  </p>
                  <p className="font-medium text-blue-900">{productName}</p>
                </div>
              </div>
            </div>
          )}

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="name">
                {t('contact.form.name', { fallback: 'Full Name *' })}
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                placeholder={t('contact.form.name_placeholder', { fallback: 'Your Name' })}
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">
                {t('contact.form.email', { fallback: 'Email Address *' })}
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                placeholder={t('contact.form.email_placeholder', { fallback: 'Your Email' })}
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="phone">
                {t('contact.form.phone', { fallback: 'Phone Number' })}
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder={t('contact.form.phone_placeholder', { fallback: 'Your Phone' })}
              />
            </div>

            <div className="form-group">
              <label htmlFor="country">
                {t('contact.form.country', { fallback: 'Country *' })}
              </label>
              <input
                type="text"
                id="country"
                name="country"
                value={formData.country}
                onChange={handleChange}
                required
                placeholder={t('contact.form.country_placeholder', { fallback: 'Your Country' })}
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="playground_size">
              {t('contact.form.playground_size', { fallback: 'Playground Size' })}
            </label>
            <select
              id="playground_size"
              name="playground_size"
              value={formData.playground_size}
              onChange={handleChange}
            >
              <option value="100-500 sqm">
                {t('contact.form.size_option1', { fallback: '100-500 sqm' })}
              </option>
              <option value="500-1000 sqm">
                {t('contact.form.size_option2', { fallback: '500-1000 sqm' })}
              </option>
              <option value="1000+ sqm">
                {t('contact.form.size_option3', { fallback: '1000+ sqm' })}
              </option>
              <option value="custom">
                {t('contact.form.size_option4', { fallback: 'Custom Size' })}
              </option>
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="message">
              {t('contact.form.message', { fallback: 'Your Message *' })}
            </label>
            <textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleChange}
              required
              placeholder={t('contact.form.message_placeholder', {
                fallback: 'Please describe your project or inquiry',
              })}
              rows={5}
            ></textarea>
          </div>

          {submitError && (
            <div className="error-message">
              {submitError}
            </div>
          )}

          <button type="submit" className="btn-primary" disabled={isSubmitting}>
            <span>
              {isSubmitting
                ? t('contact.form.sending', { fallback: 'Sending...' })
                : t('contact.form.submit', { fallback: 'Get My Free Quote' })}
            </span>
          </button>
        </form>
      )}
    </div>
  );
}
