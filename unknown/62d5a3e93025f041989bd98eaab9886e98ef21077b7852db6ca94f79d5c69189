import { NextRequest, NextResponse } from 'next/server';
import { query } from '../../../lib/db';
import { sendFormNotification } from '../../../lib/email';

// 提交表单数据
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, phone, country, playground_size, product, message } = body;

    // 验证必填字段
    if (!name || !message) {
      return NextResponse.json(
        { success: false, message: 'Name and message are required' },
        { status: 400 }
      );
    }

    // 插入表单数据到数据库
    const result = await query(`
      INSERT INTO form_submissions (name, email, phone, country, playground_size, product, message)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id, created_at
    `, [name, email, phone, country, playground_size, product, message]);

    if (result.rows && result.rows.length > 0) {
      const submissionData = {
        id: result.rows[0].id,
        name,
        email,
        phone,
        country,
        playground_size,
        product,
        message,
        created_at: result.rows[0].created_at
      };

      // 发送邮件通知（异步，不阻塞响应）
      sendFormNotification(submissionData).catch(error => {
        console.error('邮件通知发送失败:', error);
      });

      return NextResponse.json({
        success: true,
        message: 'Form submitted successfully',
        data: {
          id: result.rows[0].id,
          created_at: result.rows[0].created_at
        }
      });
    } else {
      throw new Error('Failed to insert form submission');
    }
  } catch (error) {
    console.error('Error submitting form:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to submit form' },
      { status: 500 }
    );
  }
}

// 获取表单提交列表（管理员用）
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');
    const isRead = searchParams.get('is_read');
    
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereConditions = [];
    let queryParams = [];
    let paramIndex = 1;

    if (status) {
      whereConditions.push(`status = $${paramIndex}`);
      queryParams.push(status);
      paramIndex++;
    }

    if (isRead !== null && isRead !== undefined) {
      whereConditions.push(`is_read = $${paramIndex}`);
      queryParams.push(isRead === 'true');
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 获取总数
    const countResult = await query(`
      SELECT COUNT(*) as total FROM form_submissions ${whereClause}
    `, queryParams);

    const total = parseInt(countResult.rows[0].total);

    // 获取分页数据
    const dataResult = await query(`
      SELECT 
        id, name, email, phone, country, playground_size, product, message,
        status, is_read, admin_notes, created_at, updated_at
      FROM form_submissions 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `, [...queryParams, limit, offset]);

    return NextResponse.json({
      success: true,
      data: dataResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching form submissions:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch form submissions' },
      { status: 500 }
    );
  }
}
