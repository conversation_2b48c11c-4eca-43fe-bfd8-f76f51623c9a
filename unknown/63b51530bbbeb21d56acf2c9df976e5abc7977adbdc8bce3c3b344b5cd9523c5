/**
 * 检查产品详情页样式冲突的脚本
 */
const fs = require('fs');
const path = require('path');

console.log('🔍 检查产品详情页样式冲突...\n');

// 要检查的CSS文件列表（按导入顺序）
const cssFiles = [
  'app/styles/tailwind.css',
  'app/styles/globals.css',
  'app/styles/header-dark.css',
  'app/styles/products.css',
  'app/styles/quality-control.css',
  'app/styles/banner-fix.css',
  'app/styles/ms-fix.css',
  'app/styles/high-contrast-override.css',
  'app/styles/ms-high-contrast-blocker.css',
  'app/styles/ms-high-contrast-killer.css',
  'app/styles/ms-translator-blocker.css',
  'app/styles/product-detail-fix.css',
  'app/styles/hero.css',
  'app/styles/home-page.css',
  'app/styles/home-page-fix.css',
  'app/styles/custom-overrides.css',
  'app/styles/global-quote-form.css',
  'app/styles/loading-fix.css',
  'app/styles/top-space-fix.css'
];

// 要检查的关键样式属性
const keyStyles = [
  'margin-top',
  'height',
  'product-detail-fix',
  'product-detail-container',
  '.w-full.absolute',
  '.bg-white.relative'
];

console.log('📋 CSS文件导入顺序:');
cssFiles.forEach((file, index) => {
  const exists = fs.existsSync(file);
  console.log(`${index + 1}. ${file} ${exists ? '✅' : '❌'}`);
});

console.log('\n🔍 检查关键样式冲突:\n');

// 检查每个CSS文件中的关键样式
cssFiles.forEach((file, index) => {
  if (!fs.existsSync(file)) return;
  
  const content = fs.readFileSync(file, 'utf8');
  const fileName = path.basename(file);
  
  console.log(`📄 ${fileName}:`);
  
  // 检查产品详情页相关样式
  if (content.includes('.product-detail-fix')) {
    const marginTopMatch = content.match(/\.product-detail-fix[^}]*margin-top:\s*([^;!]+)/);
    if (marginTopMatch) {
      console.log(`   📌 .product-detail-fix margin-top: ${marginTopMatch[1].trim()}`);
    }
  }
  
  if (content.includes('.product-detail-container')) {
    const containerMatch = content.match(/\.product-detail-container[^}]*margin-top:\s*([^;!]+)/);
    if (containerMatch) {
      console.log(`   📌 .product-detail-container margin-top: ${containerMatch[1].trim()}`);
    }
  }
  
  // 检查背景图片高度设置
  if (content.includes('height:') && content.includes('450px')) {
    console.log(`   📌 发现450px高度设置`);
  }
  
  if (content.includes('height:') && content.includes('300px')) {
    console.log(`   📌 发现300px高度设置`);
  }
  
  // 检查媒体查询中的样式
  const mediaQueries = content.match(/@media[^{]*\{[^}]*\}/g);
  if (mediaQueries) {
    mediaQueries.forEach(query => {
      if (query.includes('product-detail') && query.includes('margin-top')) {
        console.log(`   📱 媒体查询中的margin-top设置`);
      }
    });
  }
  
  console.log('');
});

console.log('🎯 重点检查loading-fix.css和product-detail-fix.css的冲突:\n');

// 详细检查这两个关键文件
const loadingFixPath = 'app/styles/loading-fix.css';
const productDetailFixPath = 'app/styles/product-detail-fix.css';

if (fs.existsSync(loadingFixPath)) {
  const loadingFixContent = fs.readFileSync(loadingFixPath, 'utf8');
  console.log('📄 loading-fix.css 中的关键样式:');
  
  // 桌面端样式
  const desktopMatch = loadingFixContent.match(/@media \(min-width: 768px\)[^}]*\{([^}]*)\}/s);
  if (desktopMatch) {
    console.log('   🖥️ 桌面端样式:');
    if (desktopMatch[1].includes('height: 450px')) {
      console.log('      ✅ 背景图片高度: 450px');
    }
    if (desktopMatch[1].includes('margin-top: 450px')) {
      console.log('      ✅ 内容区域margin-top: 450px');
    }
  }
  
  // 移动端样式
  const mobileMatch = loadingFixContent.match(/@media \(max-width: 767px\)[^}]*\{([^}]*)\}/s);
  if (mobileMatch) {
    console.log('   📱 移动端样式:');
    if (mobileMatch[1].includes('height: 300px')) {
      console.log('      ✅ 背景图片高度: 300px');
    }
    if (mobileMatch[1].includes('margin-top: 300px')) {
      console.log('      ✅ 内容区域margin-top: 300px');
    }
  }
}

if (fs.existsSync(productDetailFixPath)) {
  const productDetailContent = fs.readFileSync(productDetailFixPath, 'utf8');
  console.log('\n📄 product-detail-fix.css 中的关键样式:');
  
  // 基础样式
  const baseMatch = productDetailContent.match(/\.product-detail-fix[^}]*\{([^}]*)\}/);
  if (baseMatch) {
    console.log('   🖥️ 基础样式:');
    if (baseMatch[1].includes('margin-top: 450px')) {
      console.log('      ✅ margin-top: 450px');
    }
  }
  
  // 移动端样式
  const mobileMatch = productDetailContent.match(/@media \(max-width: 768px\)[^}]*\.product-detail-fix[^}]*\{([^}]*)\}/s);
  if (mobileMatch) {
    console.log('   📱 移动端样式:');
    if (mobileMatch[1].includes('margin-top: 300px')) {
      console.log('      ✅ margin-top: 300px');
    }
  }
}

console.log('\n💡 建议:');
console.log('1. loading-fix.css 在 product-detail-fix.css 之后导入，具有更高优先级');
console.log('2. 确保两个文件中的样式值保持一致');
console.log('3. 如果修改不生效，可能需要清除浏览器缓存');
console.log('4. 检查是否有内联样式覆盖CSS样式');
