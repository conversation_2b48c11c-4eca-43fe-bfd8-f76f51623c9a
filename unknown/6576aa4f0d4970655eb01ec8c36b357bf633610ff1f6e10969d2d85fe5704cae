import { Metadata } from 'next';
import HeroSlider from './components/HeroSlider';
import ServicesFeatures from './components/ServicesFeatures';
import CustomSolutions from './components/CustomSolutions';
import HolographicFeatures from './components/HolographicFeatures';
import FactoryShowcase from './components/FactoryShowcase';
import AdvantageShowcase from './components/AdvantageShowcase';
import GlobalCases from './components/GlobalCases';
import AboutSection from './components/AboutSection';

export const metadata: Metadata = {
  title: 'Guangzhou Junsheng Technology | 3D Holographic & Interactive Projection Solutions',
  description:
    'Explore cutting-edge 3D holographic projection, naked-eye 5D interactive projection, immersive restaurants, and technology exhibition halls from Guangzhou Junsheng Technology. We provide innovative visual solutions to clients in over 80 countries.',
  keywords:
    'holographic projection, interactive projection, immersive restaurant, exhibition hall, 3D hologram, 5D interactive',
};

export default function Home() {
  return (
    <>
      <HeroSlider />
      <ServicesFeatures />
      <CustomSolutions />
      <HolographicFeatures />
      <FactoryShowcase />
      <AdvantageShowcase />
      <GlobalCases />
      <AboutSection />
    </>
  );
}
