/* Tailwind CSS 指令 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义全局样式 */
@layer components {
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  .form-input {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm;
  }
  
  .form-error {
    @apply mt-1 text-sm text-red-600;
  }
  
  .form-group {
    @apply mb-4;
  }
  
  .btn {
    @apply rounded-md px-4 py-2 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
  }
  
  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  /* 动画效果 */
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-in;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
} 