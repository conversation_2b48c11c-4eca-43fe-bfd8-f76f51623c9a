/**
 * 产品自动上传脚本 (简化版)
 * 用于扫描本地产品文件夹，提取图片和信息，
 * 将图片复制到public目录并生成产品JSON文件以便手动导入
 */

const fs = require('fs');
const path = require('path');

// 配置信息
const config = {
  // 产品源目录，将从这里读取产品图片和信息
  productSourceDir:
    'D:/AIGC-dm/Cross-border E-commerce Website Project/information/nextjs-app/产品',
  // 产品图片上传目录
  targetImagesDir: 'public/images/products',
  // 产品数据输出文件
  outputJsonFile: 'scripts/products-data.json',
  // 产品类型映射
  productTypes: {
    互动足球: 'trampoline_park',
    互动砸球: 'trampoline_park',
    体感攀岩: 'trampoline_park',
    一体机: 'indoor_playground',
    AR教育: 'indoor_playground',
    保龄球: 'family_entertainment_center',
    儿童互动沙滩: 'indoor_playground',
    儿童互动砸球: 'indoor_playground',
    全息沙幕: 'family_entertainment_center',
    全息沙桌: 'family_entertainment_center',
    全息舞台: 'family_entertainment_center',
    全息餐厅: 'family_entertainment_center',
  },
};

/**
 * 从目录名称生成产品标题
 * @param {string} dirName 目录名称
 * @returns {string} 产品标题
 */
function generateTitle(dirName) {
  return `${dirName} Interactive Experience`;
}

/**
 * 从目录名称生成产品slug
 * @param {string} dirName 目录名称
 * @returns {string} 产品slug
 */
function generateSlug(dirName) {
  // 处理中文转拼音或直接转换为有效slug
  // 简单实现，对于中文字符，可能需要更复杂的拼音转换
  return (
    dirName
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/--+/g, '-')
      .trim() || `product-${Date.now()}`
  );
}

/**
 * 从目录名称生成产品描述
 * @param {string} dirName 目录名称
 * @returns {string} 产品描述
 */
function generateDescription(dirName) {
  return `${dirName} interactive experience provides engaging entertainment for customers of all ages. Our ${dirName.toLowerCase()} solutions combine cutting-edge technology with physical activity, creating memorable experiences for your venue.`;
}

/**
 * 生成产品特点
 * @param {string} dirName 目录名称
 * @returns {string[]} 产品特点列表
 */
function generateFeatures(dirName) {
  const commonFeatures = [
    `Customizable ${dirName} design`,
    'Interactive digital elements',
    'High durability materials',
    'Easy maintenance',
    'Safety compliant construction',
  ];
  return commonFeatures;
}

/**
 * 复制图片到网站public目录
 * @param {string} sourcePath 源图片路径
 * @param {string} destFilename 目标文件名
 * @returns {Promise<string|null>} 成功返回图片URL，失败返回null
 */
function copyImageToPublic(sourcePath, destFilename) {
  try {
    // 确保目标目录存在
    if (!fs.existsSync(config.targetImagesDir)) {
      fs.mkdirSync(config.targetImagesDir, { recursive: true });
    }

    const destPath = path.join(config.targetImagesDir, destFilename);
    fs.copyFileSync(sourcePath, destPath);

    // 返回相对路径的URL
    return `/images/products/${destFilename}`;
  } catch (error) {
    console.error(`复制图片时发生错误(${sourcePath}):`, error.message);
    return null;
  }
}

/**
 * 处理单个产品目录，生成产品数据
 * @param {string} categoryPath 分类路径
 * @param {string} productDir 产品目录名
 * @returns {Object|null} 产品数据对象或null（如果处理失败）
 */
function processProductDirectory(categoryPath, productDir) {
  const productPath = path.join(categoryPath, productDir);
  const productType = config.productTypes[productDir] || 'indoor_playground';

  console.log(`正在处理产品: ${productDir}`);

  // 检查是否有images目录
  const imagesDir = path.join(productPath, 'images');
  if (!fs.existsSync(imagesDir) || !fs.statSync(imagesDir).isDirectory()) {
    console.log(`跳过没有images目录的产品: ${productDir}`);
    return null;
  }

  // 读取图片文件
  const imageFiles = fs.readdirSync(imagesDir).filter(file => /\.(jpg|jpeg|png|gif)$/i.test(file));

  if (imageFiles.length === 0) {
    console.log(`跳过没有图片的产品: ${productDir}`);
    return null;
  }

  // 获取产品slug
  const productSlug = generateSlug(productDir);

  // 复制图片
  const imageUrls = [];
  for (const imageFile of imageFiles) {
    const imagePath = path.join(imagesDir, imageFile);
    const fileExtension = path.extname(imageFile);
    const destFilename = `${productSlug}-${imageUrls.length + 1}${fileExtension}`;

    const imageUrl = copyImageToPublic(imagePath, destFilename);

    if (imageUrl) {
      imageUrls.push(imageUrl);
      console.log(`  - 已复制图片: ${imageUrl}`);
    }
  }

  if (imageUrls.length === 0) {
    console.log(`没有成功复制任何图片，跳过产品: ${productDir}`);
    return null;
  }

  // 创建产品数据
  return {
    title: generateTitle(productDir),
    slug: productSlug,
    description: generateDescription(productDir),
    size: '100-500 SQM',
    style: '现代,互动',
    type: productType,
    features: generateFeatures(productDir),
    categories: ['interactive', 'entertainment'],
    images: imageUrls,
    isPublished: true,
    translations: {
      zh: {
        title: productDir,
        description: `${productDir}互动体验为各年龄段的顾客提供沉浸式娱乐。我们的${productDir}解决方案将尖端技术与身体活动相结合，为您的场所创造难忘的体验。`,
        features: [
          `可定制${productDir}设计`,
          '互动数字元素',
          '高耐久性材料',
          '易于维护',
          '符合安全标准',
        ],
      },
    },
  };
}

/**
 * 主函数
 */
function main() {
  console.log('开始产品自动处理过程...');

  try {
    // 检查源目录是否存在
    if (!fs.existsSync(config.productSourceDir)) {
      console.error(`产品源目录不存在: ${config.productSourceDir}`);
      return;
    }

    // 收集所有产品数据
    const allProducts = [];

    // 获取产品分类目录 (1, 2, 3)
    const categoryDirs = fs.readdirSync(config.productSourceDir).filter(item => {
      const itemPath = path.join(config.productSourceDir, item);
      return fs.statSync(itemPath).isDirectory();
    });

    for (const categoryDir of categoryDirs) {
      const categoryPath = path.join(config.productSourceDir, categoryDir);

      // 获取该分类下的产品目录
      const productDirs = fs.readdirSync(categoryPath).filter(item => {
        const itemPath = path.join(categoryPath, item);
        return fs.statSync(itemPath).isDirectory();
      });

      for (const productDir of productDirs) {
        const productData = processProductDirectory(categoryPath, productDir);
        if (productData) {
          allProducts.push(productData);
        }
      }
    }

    // 将产品数据输出到JSON文件
    fs.writeFileSync(config.outputJsonFile, JSON.stringify(allProducts, null, 2));

    console.log(`产品处理完成! 共生成 ${allProducts.length} 个产品数据。`);
    console.log(`产品JSON数据已保存到: ${config.outputJsonFile}`);
    console.log('下一步:');
    console.log('1. 启动Next.js应用');
    console.log('2. 登录管理后台');
    console.log('3. 进入开发者控制台，执行下面的代码导入产品:');
    console.log(`
async function importProducts() {
  const response = await fetch('/api/products-import', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      importToken: 'your-secure-token',
      products: ${JSON.stringify(
        allProducts.length > 3
          ? '[...前3个产品示例...]'
          : JSON.stringify(allProducts.slice(0, 3), null, 2)
      )}
    })
  });
  return await response.json();
}

importProducts().then(console.log).catch(console.error);
`);
  } catch (error) {
    console.error('执行脚本时发生错误:', error);
  }
}

// 执行主函数
main();
