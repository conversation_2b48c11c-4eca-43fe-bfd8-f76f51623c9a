/**
 * Product Card Fix
 * 
 * This CSS file fixes issues with product cards and ensures they are clickable.
 */

/* Make product cards more clickable */
.product-card {
  cursor: pointer !important;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
  z-index: 1;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Ensure the entire card is clickable */
.product-card a {
  display: block;
  position: relative;
  width: 100%;
  height: 100%;
  text-decoration: none;
  color: inherit;
}

/* Fix for product image container */
.product-image-container {
  overflow: hidden;
  border-radius: 8px;
}

/* Ensure overlay is visible on hover */
.product-overlay {
  opacity: 0;
  transition: opacity 0.3s ease;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 8px;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

/* Make the view details button more prominent */
.view-details-btn {
  background: #ffffff;
  color: #333;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.product-card:hover .view-details-btn {
  transform: scale(1.05);
}

/* Fix for product title */
.product-title {
  margin-top: 12px;
  font-weight: 500;
  font-size: 16px;
  color: #333;
  text-align: center;
}

/* Add a direct click handler for mobile devices */
.product-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}

/* Fix for product grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 30px;
}
