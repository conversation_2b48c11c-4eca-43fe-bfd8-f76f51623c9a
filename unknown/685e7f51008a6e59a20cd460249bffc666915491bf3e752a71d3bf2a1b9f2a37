# 邮件通知配置指南

本文档介绍如何配置邮件通知功能，当用户提交表单时自动发送邮件通知给管理员。

## 功能概述

- ✅ 用户提交表单时自动发送邮件通知
- ✅ 支持多种邮件服务（SMTP、SendGrid、Mailgun）
- ✅ 美观的HTML邮件模板
- ✅ 管理后台邮件设置页面
- ✅ 测试邮件功能
- ✅ 备用方案（控制台日志）

## 配置方式

### 方式一：SMTP 邮件服务（推荐）

在 `.env.local` 文件中配置以下环境变量：

```env
# SMTP Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=跨境电商网站
ADMIN_EMAIL=<EMAIL>
```

#### 常用邮件服务商配置

**Gmail:**
- SMTP_HOST: `smtp.gmail.com`
- SMTP_PORT: `587`
- 需要开启两步验证并生成应用密码

**QQ邮箱:**
- SMTP_HOST: `smtp.qq.com`
- SMTP_PORT: `587`
- 需要开启SMTP服务并获取授权码

**163邮箱:**
- SMTP_HOST: `smtp.163.com`
- SMTP_PORT: `587`
- 需要开启SMTP服务并获取授权码

**企业邮箱:**
- 请联系您的邮件服务提供商获取SMTP设置

### 方式二：SendGrid API

如果您使用 SendGrid 服务：

```env
SENDGRID_API_KEY=SG.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
FROM_EMAIL=<EMAIL>
FROM_NAME=跨境电商网站
ADMIN_EMAIL=<EMAIL>
```

### 方式三：Mailgun API

如果您使用 Mailgun 服务：

```env
MAILGUN_API_KEY=your-mailgun-api-key
MAILGUN_DOMAIN=your-domain.mailgun.org
FROM_EMAIL=<EMAIL>
FROM_NAME=跨境电商网站
ADMIN_EMAIL=<EMAIL>
```

## 设置步骤

### 1. Gmail 设置步骤

1. 登录您的 Gmail 账户
2. 进入 Google 账户设置
3. 开启两步验证
4. 生成应用密码：
   - 进入"安全性" > "应用密码"
   - 选择"邮件"和您的设备
   - 生成16位应用密码
5. 在 `.env.local` 中使用生成的应用密码

### 2. SendGrid 设置步骤

1. 注册 SendGrid 账户
2. 验证您的发件人邮箱
3. 创建 API Key：
   - 进入 Settings > API Keys
   - 创建新的 API Key
   - 选择 "Full Access" 权限
4. 在 `.env.local` 中配置 API Key

### 3. Mailgun 设置步骤

1. 注册 Mailgun 账户
2. 添加并验证您的域名
3. 获取 API Key：
   - 进入 Settings > API Keys
   - 复制 Private API Key
4. 在 `.env.local` 中配置 API Key 和域名

## 测试邮件功能

1. 配置完成后，重启应用
2. 登录管理后台：`/zh/admin/login`
3. 进入邮件设置页面：`/zh/admin/email-settings`
4. 点击"发送测试邮件"按钮
5. 检查您的邮箱是否收到测试邮件

## 邮件模板

系统会发送包含以下信息的邮件：

- 客户姓名
- 联系邮箱
- 联系电话
- 国家/地区
- 场地大小
- 感兴趣的产品
- 客户留言
- 提交时间
- 提交ID
- 管理后台链接

## 故障排除

### 常见问题

**1. Gmail 提示"用户名或密码错误"**
- 确保开启了两步验证
- 使用应用密码而不是账户密码
- 检查SMTP设置是否正确

**2. QQ邮箱/163邮箱发送失败**
- 确保开启了SMTP服务
- 使用授权码而不是登录密码
- 检查端口设置（通常是587）

**3. SendGrid 发送失败**
- 确保API Key有效
- 确保发件人邮箱已验证
- 检查SendGrid账户状态

**4. 邮件进入垃圾箱**
- 添加发件人到白名单
- 检查邮件内容是否触发垃圾邮件过滤器
- 考虑使用专业的邮件服务

### 调试方法

1. 查看服务器控制台日志
2. 使用测试邮件功能
3. 检查环境变量配置
4. 验证邮件服务商设置

## 安全建议

1. **使用应用密码**：不要使用账户密码，使用专门的应用密码
2. **环境变量保护**：不要将邮件密码提交到代码仓库
3. **定期更换密码**：定期更换邮件服务密码
4. **监控邮件发送**：监控邮件发送量，防止滥用

## 备用方案

如果邮件发送失败，系统会：

1. 尝试多种邮件服务（SMTP → SendGrid → Mailgun）
2. 将通知信息记录到服务器控制台
3. 在管理后台显示表单提交记录

## 支持的邮件服务

- ✅ SMTP（Gmail、QQ、163、企业邮箱等）
- ✅ SendGrid
- ✅ Mailgun
- 🔄 可扩展支持其他邮件服务

## 相关文件

- `/lib/email.ts` - 主要邮件发送逻辑
- `/lib/simple-email.ts` - 简化邮件发送（不依赖外部库）
- `/app/api/form-submissions/route.ts` - 表单提交API
- `/app/api/test-email/route.ts` - 测试邮件API
- `/app/[lang]/admin/email-settings/page.tsx` - 邮件设置页面

## 更新日志

- 2024-01-XX: 初始版本，支持SMTP和SendGrid
- 2024-01-XX: 添加Mailgun支持
- 2024-01-XX: 添加邮件设置管理页面
- 2024-01-XX: 添加测试邮件功能
