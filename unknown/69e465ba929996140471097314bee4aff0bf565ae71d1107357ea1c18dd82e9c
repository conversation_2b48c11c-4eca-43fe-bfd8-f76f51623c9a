import { getServerSession } from 'next-auth/next';
import { redirect } from 'next/navigation';
import { authOptions } from '@/app/api/auth/[...nextauth]/route.js';
import CacheCleaner from '@/components/admin/CacheCleaner';

export const metadata = {
  title: '系统设置 - 管理后台',
  description: '系统设置和维护功能'
};

export default async function AdminSettingsPage({ params }) {
  // 验证用户身份
  const session = await getServerSession(authOptions);

  // 如果用户未登录或不是管理员，重定向到登录页面
  if (!session || session.user.role !== 'admin') {
    redirect(`/${params.lang}/admin/login`);
  }

  return (
    <div className="admin-container">
      <div className="admin-header">
        <h1 className="admin-title">系统设置</h1>
        <p className="admin-description">管理系统设置和维护功能</p>
      </div>

      <div className="grid grid-cols-1 gap-6 mt-6">
        {/* 缓存管理 */}
        <CacheCleaner />

        {/* 可以添加其他设置模块 */}
      </div>
    </div>
  );
}