/**
 * 上传互动砸球产品到数据库
 */
const fs = require('fs');
const path = require('path');

// 导入数据库连接
const { query } = require('../lib/db.js');

async function uploadInteractiveBallProduct() {
  console.log('🎯 开始上传互动砸球产品...\n');

  try {
    // 1. 首先复制图片文件到public目录
    const sourceDir = '产品/1/互动砸球';
    const targetDir = 'public/images/products/interactive-ball';

    console.log('📁 复制产品图片...');

    // 确保目标目录存在
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
      console.log(`   ✅ 创建目录: ${targetDir}`);
    }

    // 获取源目录中的所有图片文件
    const sourceFiles = fs.readdirSync(sourceDir);
    const imageFiles = sourceFiles.filter(file =>
      file.toLowerCase().endsWith('.jpg') ||
      file.toLowerCase().endsWith('.jpeg') ||
      file.toLowerCase().endsWith('.png')
    );

    console.log(`   📸 找到 ${imageFiles.length} 个图片文件`);

    // 复制图片文件并重命名
    const copiedImages = [];
    imageFiles.forEach((file, index) => {
      const sourcePath = path.join(sourceDir, file);
      const ext = path.extname(file);
      const newFileName = `interactive-ball-${index + 1}${ext}`;
      const targetPath = path.join(targetDir, newFileName);

      try {
        fs.copyFileSync(sourcePath, targetPath);
        const webPath = `/images/products/interactive-ball/${newFileName}`;
        copiedImages.push(webPath);
        console.log(`   ✅ 复制: ${file} -> ${newFileName}`);
      } catch (error) {
        console.log(`   ❌ 复制失败: ${file} - ${error.message}`);
      }
    });

    if (copiedImages.length === 0) {
      throw new Error('没有成功复制任何图片文件');
    }

    // 2. 准备产品数据
    const productData = {
      name: '互动砸球系统',
      slug: 'interactive-ball-system',
      description: '创新的互动砸球游戏系统，结合投影技术和体感识别，为玩家提供沉浸式的互动体验。通过投掷球体击中投影目标，实现虚拟与现实的完美结合，适合各年龄段用户参与。',
      price: 88000.00, // 添加价格字段
      type: 'interactive_projection',
      size: '3m x 4m',
      style: '现代,互动',
      category: '互动投影',
      categories: JSON.stringify(['互动投影', '体感游戏', '投影互动']),
      features: JSON.stringify([
        '高精度体感识别技术',
        '多人同时游戏支持',
        '丰富的游戏场景选择',
        '实时得分统计系统',
        '安全防护设计',
        '易于安装和维护'
      ]),
      key_features: JSON.stringify([
        '高精度体感识别',
        '多人互动支持',
        '丰富游戏场景',
        '实时得分系统'
      ]),
      applications: JSON.stringify([
        '游乐场',
        '商场娱乐区',
        '儿童乐园',
        '体验馆'
      ]),
      installation_options: JSON.stringify([
        '吊装式安装',
        '地面投影',
        '墙面投影'
      ]),
      images: JSON.stringify(copiedImages),
      image_url: copiedImages[0], // 使用第一张图片作为主图
      is_featured: false,
      in_stock: true
    };

    console.log('\n📝 准备产品数据:');
    console.log(`   名称: ${productData.name}`);
    console.log(`   Slug: ${productData.slug}`);
    console.log(`   类型: ${productData.type}`);
    console.log(`   尺寸: ${productData.size}`);
    console.log(`   图片数量: ${productData.images.length}`);

    // 3. 检查产品是否已存在
    console.log('\n🔍 检查产品是否已存在...');
    const existingProduct = await query(
      'SELECT id, name FROM products WHERE slug = $1',
      [productData.slug]
    );

    if (existingProduct.rows.length > 0) {
      console.log(`   ⚠️  产品已存在 (ID: ${existingProduct.rows[0].id})`);
      console.log('   是否要更新现有产品？(Y/n)');

      // 这里我们直接更新，实际使用时可以添加用户确认
      const updateResult = await query(
        `UPDATE products SET
         name = $1,
         description = $2,
         price = $3,
         type = $4,
         size = $5,
         style = $6,
         category = $7,
         categories = $8,
         features = $9,
         key_features = $10,
         applications = $11,
         installation_options = $12,
         images = $13,
         image_url = $14,
         is_featured = $15,
         in_stock = $16,
         updated_at = CURRENT_TIMESTAMP
         WHERE slug = $17
         RETURNING id`,
        [
          productData.name,
          productData.description,
          productData.price,
          productData.type,
          productData.size,
          productData.style,
          productData.category,
          productData.categories,
          productData.features,
          productData.key_features,
          productData.applications,
          productData.installation_options,
          productData.images,
          productData.image_url,
          productData.is_featured,
          productData.in_stock,
          productData.slug
        ]
      );

      console.log(`   ✅ 产品更新成功 (ID: ${updateResult.rows[0].id})`);
    } else {
      // 4. 插入新产品
      console.log('\n💾 插入新产品到数据库...');
      const insertResult = await query(
        `INSERT INTO products
         (name, slug, description, price, type, size, style, category, categories, features, key_features, applications, installation_options, images, image_url, is_featured, in_stock)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
         RETURNING id`,
        [
          productData.name,
          productData.slug,
          productData.description,
          productData.price,
          productData.type,
          productData.size,
          productData.style,
          productData.category,
          productData.categories,
          productData.features,
          productData.key_features,
          productData.applications,
          productData.installation_options,
          productData.images,
          productData.image_url,
          productData.is_featured,
          productData.in_stock
        ]
      );

      console.log(`   ✅ 产品创建成功 (ID: ${insertResult.rows[0].id})`);
    }

    // 5. 验证产品是否正确插入
    console.log('\n🔍 验证产品数据...');
    const verifyResult = await query(
      'SELECT * FROM products WHERE slug = $1',
      [productData.slug]
    );

    if (verifyResult.rows.length > 0) {
      const product = verifyResult.rows[0];
      console.log('   ✅ 产品验证成功:');
      console.log(`      ID: ${product.id}`);
      console.log(`      名称: ${product.name}`);
      console.log(`      价格: ¥${product.price}`);
      console.log(`      库存状态: ${product.in_stock ? '有库存' : '缺货'}`);
      console.log(`      图片数量: ${JSON.parse(product.images || '[]').length}`);
    } else {
      throw new Error('产品验证失败');
    }

    console.log('\n🎉 互动砸球产品上传完成！');
    console.log('\n📋 后续步骤:');
    console.log('1. 访问产品页面查看效果: http://localhost:3000/zh/products');
    console.log('2. 检查产品详情页: http://localhost:3000/zh/products/interactive-ball-system');
    console.log('3. 如需修改产品信息，可以通过管理后台进行编辑');

  } catch (error) {
    console.error('\n❌ 上传失败:', error.message);
    console.error('错误详情:', error);
    process.exit(1);
  }
}

// 执行上传
if (require.main === module) {
  uploadInteractiveBallProduct()
    .then(() => {
      console.log('\n✅ 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { uploadInteractiveBallProduct };
