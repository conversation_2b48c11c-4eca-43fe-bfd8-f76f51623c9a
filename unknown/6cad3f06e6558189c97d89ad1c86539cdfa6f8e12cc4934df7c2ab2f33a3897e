'use client';

import Link from 'next/link';
import MarketingSupportServices from '../../components/MarketingSupportServices';
import MarketingSupportProcess from '../../components/MarketingSupportProcess';

export default function MarketingSupportServicesPage() {
  return (
    <main className="main-content">
      <section className="page-header">
        <div className="container">
          <h1 className="page-title">我们的营销支持服务</h1>
          <div className="breadcrumbs">
            <Link href="/">首页</Link> &gt; <span>营销支持服务</span>
          </div>
        </div>
      </section>

      <MarketingSupportServices />

      <MarketingSupportProcess />

      <section className="cta-section cta-particles">
        <div className="container">
          <div className="cta-content">
            <h2>准备好讨论您的全息定制解决方案？</h2>
            <p>今天就联系我们的团队，探索我们如何为您的需求创造完美的全息解决方案。</p>
            <Link
              href="/pages/contact-us"
              className="btn-primary"
              data-text="立即联系我们"
            >
              {'立即联系我们'.split('').map((char, index) => (
                <i key={index}>{char === ' ' ? '\u00A0' : char}</i>
              ))}
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
