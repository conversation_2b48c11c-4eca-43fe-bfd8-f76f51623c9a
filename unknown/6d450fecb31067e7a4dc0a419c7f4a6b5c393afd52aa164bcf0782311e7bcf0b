/**
 * 全面替换所有文件中的-ms-high-contrast为现代标准
 */
const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

async function fixAllHighContrast() {
  console.log('开始全面修复高对比度样式...');

  // 扩大搜索范围，包括所有可能的样式文件和组件文件
  const files = await glob('{app,styles,components,src,pages}/**/*.{css,scss,js,jsx,ts,tsx}');
  console.log(`找到 ${files.length} 个文件需要检查`);

  let fixedCount = 0;
  let filesFixed = 0;
  
  for (const file of files) {
    let content = fs.readFileSync(file, 'utf8');
    let original = content;
    
    // 替换所有-ms-high-contrast媒体查询
    const replaced = content.replace(
      /@media\s+\(\s*-ms-high-contrast\s*:\s*active\s*\)/g,
      '@media (forced-colors: active)'
    );
    
    // 替换CSS中的-ms-high-contrast属性
    let finalContent = replaced.replace(
      /-ms-high-contrast\s*:/g,
      'forced-colors:'
    );
    
    // 替换JS/TS字符串中的-ms-high-contrast
    finalContent = finalContent.replace(
      /['"`]-ms-high-contrast['"`]\s*:/g, 
      '\'forced-colors\':"'
    );
    
    // 替换驼峰式命名的forcedColors属性
    finalContent = finalContent.replace(
      /forcedColors\s*:/g,
      'forcedColors:'
    );
    
    if (finalContent !== original) {
      fs.writeFileSync(file, finalContent, 'utf8');
      console.log(`✓ 修复文件: ${file}`);
      filesFixed++;
      fixedCount += (original.match(/-ms-high-contrast|forcedColors/g) || []).length;
    }
  }
  
  console.log(`修复完成! 共修复了 ${filesFixed} 个文件中的 ${fixedCount} 处高对比度样式`);
}

// 此外，创建强制覆盖高对比度样式的CSS文件
function createOverrideStylesheet() {
  const overrideCSS = `
/* 强制覆盖高对比度样式，确保兼容性 */
@media (forced-colors: active) {
  * {
    /* 确保所有元素使用正确的强制色彩模式属性 */
    forced-colors: active;
  }
  
  /* 修复特定高对比度模式下的问题 */
  a {
    forced-colors: auto;
  }
  
  button, input, select, textarea {
    forced-colors: auto;
  }
}
`;

  fs.writeFileSync(path.join('styles', 'high-contrast-fix.css'), overrideCSS);
  console.log('✓ 创建了高对比度覆盖样式文件: styles/high-contrast-fix.css');
}

async function run() {
  try {
    await fixAllHighContrast();
    createOverrideStylesheet();
    console.log('全部修复完成!');
  } catch (error) {
    console.error('修复过程中出现错误:', error);
  }
}

run(); 