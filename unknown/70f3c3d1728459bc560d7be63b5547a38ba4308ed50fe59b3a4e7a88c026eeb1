// 补充所有260个缺失的英文翻译
const fs = require('fs');

console.log('🌐 开始补充所有缺失的英文翻译...\n');

// 函数工具
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => {
    if (!current[key]) current[key] = {};
    return current[key];
  }, obj);
  target[lastKey] = value;
}

try {
  // 读取文件
  const zhDict = JSON.parse(fs.readFileSync('app/dictionaries/zh.json', 'utf8'));
  const enDict = JSON.parse(fs.readFileSync('app/dictionaries/en.json', 'utf8'));
  const missingKeys = JSON.parse(fs.readFileSync('scripts/missing-dictionary-keys.json', 'utf8'));
  
  console.log(`📊 发现 ${missingKeys.missingInEnglish.length} 个缺失的英文翻译键`);
  
  // 从needs-manual-translation.json读取需要翻译的项
  const needsTranslation = JSON.parse(fs.readFileSync('scripts/needs-manual-translation.json', 'utf8'));
  
  // 创建一个映射表，包含所有翻译
  const allTranslations = {
    // About page
    'about.image_alt': 'About Us - Indoor Playground Equipment',
    'about.company_name': 'Junsheng Amusement Equipment',
    'about.mission.title': 'Our Mission',
    'about.mission.paragraph1': 'We are dedicated to creating innovative and safe indoor playground equipment that brings joy to children worldwide.',
    'about.mission.paragraph2': 'With over 15 years of experience, we combine creativity, safety, and fun to deliver exceptional play experiences.',
    'about.stats.projects': 'Projects Completed',
    'about.stats.experience': 'Years of Experience',
    'about.stats.safety': 'Safety Record',
    'about.cta.description': 'Ready to create an amazing indoor playground?',
    
    // Products - Indoor
    'products.indoor.description': 'Explore our innovative indoor playground solutions designed for children of all ages, creating engaging play experiences. Each playground can be customized to fit your space and theme requirements.',
    'products.indoor.features.title': 'Indoor Playground Features',
    'products.indoor.features.themes.title': 'Themed Environments',
    'products.indoor.features.themes.description': 'Immersive themed play areas that spark imagination and create unforgettable experiences.',
    'products.indoor.features.multi.title': 'Multi-Level Play Structures',
    'products.indoor.features.multi.description': 'Exciting vertical play spaces featuring slides, climbing equipment, and interactive elements.',
    'products.indoor.features.toddler.title': 'Toddler Areas',
    'products.indoor.features.toddler.description': 'Safe, age-appropriate play zones designed specifically for younger children.',
    'products.indoor.features.interactive.title': 'Interactive Elements',
    'products.indoor.features.interactive.description': 'Engaging activities that promote learning through play and physical development.',
    'products.indoor.cta.title': 'Ready to Create Your Indoor Playground?',
    'products.indoor.cta.description': 'Contact our team today to discuss your custom indoor playground project.',
    'products.indoor.cta.button': 'Start Now',
    
    // Products - Trampoline
    'products.trampoline.description': 'Explore our customizable trampoline park solutions designed for maximum fun and safety. Our trampoline parks provide thrilling experiences for visitors of all ages.',
    'products.trampoline.features.title': 'Trampoline Park Features',
    'products.trampoline.features.jump.title': 'Jump Zones',
    'products.trampoline.features.jump.description': 'Multiple jumping areas with professional-grade trampolines for all skill levels.',
    'products.trampoline.features.sports.title': 'Trampoline Sports',
    'products.trampoline.features.sports.description': 'Trampoline basketball, dodgeball, and other exciting sports activities.',
    'products.trampoline.features.foam.title': 'Foam Pit Areas',
    'products.trampoline.features.foam.description': 'Safe landing zones filled with foam blocks for practicing flips and tricks.',
    'products.trampoline.features.safety.title': 'Safety Systems',
    'products.trampoline.features.safety.description': 'Comprehensive safety features including padded surfaces, nets, and professional-grade equipment.',
    'products.trampoline.cta.title': 'Ready to Build Your Trampoline Park?',
    'products.trampoline.cta.description': 'Contact our team today to discuss your custom trampoline park project.',
    'products.trampoline.cta.button': 'Start Now'
  };
  
  // 添加Products - Small翻译
  Object.assign(allTranslations, {
    'products.small.description': 'Our 100-500 sqm playground solutions are perfect for smaller venues looking to maximize play value in limited space. These compact yet feature-rich designs offer excellent return on investment for shopping malls, family entertainment centers, and retail locations.',
    'products.small.benefits.title': 'Benefits of 100-500 sqm Playgrounds',
    'products.small.benefits.cost.title': 'Lower Initial Investment',
    'products.small.benefits.cost.description': 'Smaller footprint means reduced startup costs while still delivering a complete play experience.',
    'products.small.benefits.location.title': 'More Location Options',
    'products.small.benefits.location.description': 'Fits in various commercial spaces, opening up more potential venue choices.',
    'products.small.benefits.roi.title': 'Faster Return on Investment',
    'products.small.benefits.roi.description': 'Lower operating costs and overhead typically result in quicker ROI.',
    'products.small.benefits.efficient.title': 'Space Efficiency',
    'products.small.benefits.efficient.description': 'Our designs maximize play value per square meter through clever multi-level structures.',
    'products.small.cta.title': 'Perfect Solution for Your Small Space?',
    'products.small.cta.description': 'Contact our team today to discuss how we can create an amazing playground in your 100-500 sqm space.',
    'products.small.cta.button': 'Get Your Custom Quote'
  });
  
  // 添加Products - Medium翻译
  Object.assign(allTranslations, {
    'products.medium.description': 'Our 500-1000 sqm playground solutions offer the perfect balance of space and functionality, ideal for dedicated family entertainment centers and larger commercial venues. These medium-sized playgrounds provide diverse play experiences while maintaining operational efficiency.',
    'products.medium.benefits.title': 'Benefits of 500-1000 sqm Playgrounds',
    'products.medium.benefits.capacity.title': 'Optimal Capacity',
    'products.medium.benefits.capacity.description': 'Accommodate more visitors simultaneously while maintaining comfortable play environments.',
    'products.medium.benefits.variety.title': 'Greater Play Variety',
    'products.medium.benefits.variety.description': 'Room for more diverse play elements, themed zones, and specialized activities.',
    'products.medium.benefits.party.title': 'Enhanced Party Facilities',
    'products.medium.benefits.party.description': 'Space for multiple party areas, increasing revenue potential from celebrations and events.',
    'products.medium.benefits.balance.title': 'Balanced Investment',
    'products.medium.benefits.balance.description': 'Perfect balance between investment size and revenue potential for sustainable business.',
    'products.medium.cta.title': 'The Ideal Size for Your Entertainment Center?',
    'products.medium.cta.description': 'Contact our team today to explore our 500-1000 sqm playground solutions tailored to your needs.',
    'products.medium.cta.button': 'Discuss Your Project'
  });
  
  // 添加Products - Large翻译
  Object.assign(allTranslations, {
    'products.large.description': 'Our 1000+ sqm playground solutions represent the ultimate in indoor play experiences. These large-scale entertainment destinations offer comprehensive play options, multiple attractions, and the capacity to become regional entertainment destinations.',
    'products.large.benefits.title': 'Benefits of 1000+ sqm Playgrounds',
    'products.large.benefits.destination.title': 'Destination Appeal',
    'products.large.benefits.destination.description': 'Become a regional entertainment destination attracting visitors from wider geographic areas.',
    'products.large.benefits.attractions.title': 'Multiple Attractions',
    'products.large.benefits.attractions.description': 'Space for diverse play zones, multiple attractions, and complementary entertainment options.',
    'products.large.benefits.amenities.title': 'Extended Amenities',
    'products.large.benefits.amenities.description': 'Room for full-service restaurants, cafes, retail areas, and extensive seating for parents.',
    'products.large.benefits.revenue.title': 'Maximum Revenue Potential',
    'products.large.benefits.revenue.description': 'Higher capacity and longer visit durations translate to greater revenue opportunities.',
    'products.large.cta.title': 'Planning a Major Entertainment Destination?',
    'products.large.cta.description': 'Contact our team today to discuss your vision for a large-scale indoor playground or entertainment center.',
    'products.large.cta.button': 'Start Your Large Project'
  });
  
  // 添加Factory翻译
  Object.assign(allTranslations, {
    'factory.title_prefix': 'Factory Tour',
    'factory.slider_label': 'Production Facility Views',
    'factory.info_btn': 'View Quality & Certification Info',
    'factory.info_btn_aria': 'Show factory information',
    'factory.prev_slide_aria': 'Previous slide',
    'factory.next_slide_aria': 'Next slide',
    'factory.slide_dot_aria': 'Go to slide {{index}}',
    'factory.info_card.header': 'Quality Assurance & Manufacturing Excellence',
    'factory.info_card.description': 'Our production facility is equipped with industry-leading equipment and precision instruments, operating strictly according to ISO9001 quality management system to ensure every product meets international standards.',
    'factory.info_card.cert_title': 'International Quality Certifications',
    'factory.stats.years': 'Years of Industry Experience',
    'factory.stats.products': 'Products Delivered',
    'factory.certifications.ce': 'CE European Safety Standards',
    'factory.certifications.iso': 'ISO 9001:2015 Quality Management',
    'factory.slides.slide1': 'Our main production workshop',
    'factory.slides.slide2': 'High-precision optical equipment',
    'factory.slides.slide3': 'Product quality testing area',
    'factory.slides.slide4': 'R&D and design department'
  });
  
  // 添加Safety Standards翻译
  Object.assign(allTranslations, {
    'safe_standard.intro.paragraph2': 'Our commitment to safety begins at the design stage and continues through manufacturing, installation, and beyond. Every component, material, and feature is carefully engineered to meet or exceed international safety standards.',
    'safe_standard.standards.title': 'International Safety Standards',
    'safe_standard.standards.astm': 'Standard Consumer Safety Performance Specification for Playground Equipment for Public Use (USA)',
    'safe_standard.standards.en': 'European Standards for Playground Equipment and Impact Absorbing Playground Surfacing',
    'safe_standard.standards.iso': 'International Quality Management System Standards',
    'safe_standard.standards.ce': 'European Conformity for Health, Safety, and Environmental Protection Standards',
    'safe_standard.standards.tuv': 'German Technical Inspection Association Safety and Quality Certification',
    'safe_standard.standards.csa': 'Canadian Standards Association Guidelines for Children\'s Play Spaces and Equipment',
    'safe_standard.features.title': 'Safety Features in Our Equipment',
    'safe_standard.features.image_alt': 'Safety features of Guangzhou Junsheng Technology equipment',
    'safe_standard.features.rounded_edges.title': 'Rounded Edges',
    'safe_standard.features.rounded_edges.description': 'All components feature rounded edges and corners to prevent injuries.',
    'safe_standard.features.non_toxic.title': 'Non-Toxic Materials',
    'safe_standard.features.non_toxic.description': 'We use only non-toxic, lead-free materials and paints that are safe for children.',
    'safe_standard.features.anti_slip.title': 'Anti-Slip Surfaces',
    'safe_standard.features.anti_slip.description': 'Platforms and steps feature anti-slip surfaces to prevent falls.',
    'safe_standard.features.impact_absorbing.title': 'Impact Absorbing Flooring',
    'safe_standard.features.impact_absorbing.description': 'Our flooring systems are designed to cushion falls and reduce injury risk.',
    'safe_standard.features.secure.title': 'Secure Connections',
    'safe_standard.features.secure.description': 'All components are securely connected using tamper-proof hardware.',
    'safe_standard.features.spacing.title': 'Proper Spacing',
    'safe_standard.features.spacing.description': 'Equipment is properly spaced to prevent overcrowding and collisions.',
    'safe_standard.features.enclosed.title': 'Enclosed Climbing Areas',
    'safe_standard.features.enclosed.description': 'Elevated platforms feature barriers to prevent falls.',
    'safe_standard.features.age_appropriate.title': 'Age-Appropriate Design',
    'safe_standard.features.age_appropriate.description': 'Equipment is designed with age-appropriate features and challenges.',
    'safe_standard.cta.title': 'Ready to Build a Safe Play Environment?',
    'safe_standard.cta.description': 'Contact us today to learn how our safety-first approach can benefit your playground project.',
    'safe_standard.cta.button': 'Contact Our Safety Experts'
  });
  
  // 添加Marketing Support翻译
  Object.assign(allTranslations, {
    'marketing_support.intro.paragraph2': 'That\'s why we offer comprehensive marketing support to help you attract customers, build your brand, and maximize your business potential. Our team of marketing experts will work with you from pre-opening through ongoing operations.',
    'marketing_support.services.title': 'Our Marketing Support Services',
    'marketing_support.services.brand.title': 'Brand Development',
    'marketing_support.services.brand.item1': 'Logo design assistance',
    'marketing_support.services.brand.item2': 'Brand identity development',
    'marketing_support.services.brand.item3': 'Color scheme and theme recommendations',
    'marketing_support.services.brand.item4': 'Signage design support',
    'marketing_support.services.digital.title': 'Digital Marketing',
    'marketing_support.services.digital.item1': 'Website design recommendations',
    'marketing_support.services.digital.item2': 'Social media strategy guidance',
    'marketing_support.services.digital.item3': 'Online advertising templates',
    'marketing_support.services.digital.item4': 'SEO optimization tips',
    'marketing_support.services.promotional.title': 'Promotional Support',
    'marketing_support.services.promotional.item1': 'Grand opening event planning',
    'marketing_support.services.promotional.item2': 'Promotional material templates',
    'marketing_support.services.promotional.item3': 'Seasonal marketing campaigns',
    'marketing_support.services.promotional.item4': 'Partnership opportunity guidance',
    'marketing_support.services.opening.title': 'Pre-Opening Support',
    'marketing_support.services.opening.item1': 'Marketing timeline development',
    'marketing_support.services.opening.item2': 'Community outreach strategies',
    'marketing_support.services.opening.item3': 'Media relations guidance',
    'marketing_support.services.opening.item4': 'Soft opening recommendations',
    'marketing_support.process.title': 'Marketing Support Process',
    'marketing_support.process.step1.title': 'Analysis',
    'marketing_support.process.step1.description': 'We analyze your local market and competitive landscape',
    'marketing_support.process.step2.title': 'Strategy',
    'marketing_support.process.step2.description': 'Develop a customized marketing strategy for your business',
    'marketing_support.process.step3.title': 'Implementation',
    'marketing_support.process.step3.description': 'Provide tools and templates for marketing execution',
    'marketing_support.process.step4.title': 'Training',
    'marketing_support.process.step4.description': 'Train your team on effective marketing techniques',
    'marketing_support.process.step5.title': 'Ongoing Support',
    'marketing_support.process.step5.description': 'Continue to provide guidance as your business grows',
    'marketing_support.cta.title': 'Ready to Market Your Playground Successfully?',
    'marketing_support.cta.description': 'Contact us today to learn about our comprehensive marketing support services.',
    'marketing_support.cta.button': 'Get Marketing Support'
  });
  
  // 添加更多翻译...
  Object.assign(allTranslations, {
    'customPlayground.title': 'Custom Indoor Playground Design',
    'customPlayground.subtitle': 'Opening your first playground? Let us provide you with a custom solution!',
    'customPlayground.cta': 'Get Your Custom Solution',
    'custom_playground.subtitle': 'Opening your first playground? Let us provide you with a custom solution!',
    'custom_playground.cta': 'Get Your Custom Solution',
    'contact.description': 'Get in touch with us for your indoor playground needs',
    'contact.office': 'Office',
    'contact.email_us': 'Email Us',
    'contact.call_us': 'Call Us',
    'contact.working_hours': 'Working Hours',
    'contact.hours': 'Monday - Friday: 9:00 AM - 6:00 PM',
    'contact.form.title': 'Send us a message',
    'contact.form.subtitle': 'We\'ll get back to you within 24 hours',
    'contact.form.name_placeholder': 'Your Name',
    'contact.form.email_placeholder': 'Your Email',
    'contact.form.phone': 'Phone',
    'contact.form.phone_placeholder': 'Your Phone Number',
    'contact.form.country': 'Country',
    'contact.form.country_placeholder': 'Your Country',
    'contact.form.playground_size': 'Playground Size',
    'contact.form.size_option1': '100-500 sqm',
    'contact.form.size_option2': '500-1000 sqm',
    'contact.form.size_option3': '1000+ sqm',
    'contact.form.size_option4': 'Not sure yet',
    'contact.form.message_placeholder': 'Tell us about your project...',
    'contact.form.sending': 'Sending...'
  });
  
  // 应用翻译
  let addedCount = 0;
  let failedKeys = [];

  missingKeys.missingInEnglish.forEach(key => {
    if (allTranslations[key]) {
      setNestedValue(enDict, key, allTranslations[key]);
      addedCount++;
    } else {
      const zhValue = getNestedValue(zhDict, key);
      if (zhValue) {
        failedKeys.push({ key, chinese: zhValue });
      }
    }
  });

  // 补充中文字典缺失的键
  missingKeys.missingInChinese.forEach(key => {
    if (key === 'custom_playground.subtitle') {
      setNestedValue(zhDict, key, '将您的独特愿景变为现实，我们提供定制设计服务');
    } else if (key === 'custom_playground.cta') {
      setNestedValue(zhDict, key, '开始定制设计');
    } else if (key === 'footer.products') {
      setNestedValue(zhDict, key, '产品');
    }
  });

  // 保存更新后的字典
  fs.writeFileSync('app/dictionaries/en.json', JSON.stringify(enDict, null, 2), 'utf8');
  fs.writeFileSync('app/dictionaries/zh.json', JSON.stringify(zhDict, null, 2), 'utf8');

  console.log(`\n✅ 翻译补充完成！`);
  console.log(`- 成功添加 ${addedCount} 个英文翻译`);
  console.log(`- 还有 ${failedKeys.length} 个键需要翻译`);
  console.log(`- 补充了 ${missingKeys.missingInChinese.length} 个中文翻译`);

  if (failedKeys.length > 0) {
    fs.writeFileSync(
      'scripts/remaining-translations-detailed.json',
      JSON.stringify(failedKeys, null, 2),
      'utf8'
    );
    console.log(`\n📝 剩余需要翻译的键已保存到: scripts/remaining-translations-detailed.json`);
    console.log('前5个需要翻译的示例:');
    failedKeys.slice(0, 5).forEach(item => {
      console.log(`- ${item.key}: "${item.chinese}"`);
    });
  }

} catch (error) {
  console.error('❌ 错误:', error.message);
  console.error(error.stack);
}

console.log('\n✨ 国际化改进进度：');
console.log('1. ✅ 检测缺失翻译键');
console.log('2. ✅ 补充部分英文翻译');
console.log('3. ⏭️ 下一步：补充剩余翻译');
console.log('4. ⏭️ 修复硬编码文本');
console.log('5. ⏭️ 测试语言切换功能'); 