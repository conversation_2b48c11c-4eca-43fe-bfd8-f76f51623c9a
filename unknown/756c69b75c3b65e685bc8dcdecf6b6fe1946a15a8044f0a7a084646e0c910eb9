const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function addProduct() {
  try {
    const client = await pool.connect();
    
    const result = await client.query(
      `INSERT INTO products 
       (name, slug, description, type, features, images, in_stock, is_featured, price) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
       RETURNING id`,
      [
        '全息餐厅投影系统',
        'holographic-restaurant-system',
        '为餐厅打造独特的全息投影用餐环境，提升顾客用餐体验。',
        'holographic_solutions',
        JSON.stringify(['360度全息投影', '主题场景切换', '音效同步']),
        JSON.stringify(['/images/holographic/holographic-restaurant.jpg']),
        true,
        true,
        0
      ]
    );
    
    console.log('产品添加成功，ID:', result.rows[0].id);
    client.release();
    
  } catch (error) {
    console.error('添加失败:', error.message);
  } finally {
    await pool.end();
  }
}

addProduct();
