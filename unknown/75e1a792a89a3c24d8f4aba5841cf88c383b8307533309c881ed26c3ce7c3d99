'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  MagnifyingGlassPlusIcon,
  XMarkIcon,
  PlayIcon,
  PauseIcon,
} from '@heroicons/react/24/outline';

interface ProductImageGalleryProps {
  images: string[];
  videos?: string[]; // 新增视频数组
  productName: string;
}

// 辅助函数：检查URL是否为视频
const isVideoUrl = (url: string): boolean => {
  return (
    url.endsWith('.mp4') ||
    url.endsWith('.webm') ||
    url.endsWith('.ogg') ||
    url.includes('youtube.com') ||
    url.includes('youtu.be') ||
    url.includes('vimeo.com')
  );
};

// 辅助函数：从YouTube/Vimeo URL获取嵌入URL
const getEmbedUrl = (url: string): string => {
  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    // 处理YouTube链接
    const videoId = url.includes('youtu.be')
      ? url.split('/').pop()
      : url.split('v=')[1]?.split('&')[0];
    return `https://www.youtube.com/embed/${videoId}`;
  } else if (url.includes('vimeo.com')) {
    // 处理Vimeo链接
    const videoId = url.split('/').pop();
    return `https://player.vimeo.com/video/${videoId}`;
  }
  // 如果是直接视频文件，返回原始URL
  return url;
};

const ProductImageGallery: React.FC<ProductImageGalleryProps> = ({
  images,
  videos = [],
  productName,
}) => {
  // 合并媒体数组：先放图片，再放视频
  const allMedia = [...images, ...videos];
  const [selectedMediaIndex, setSelectedMediaIndex] = useState(0);
  const [showModal, setShowModal] = useState(false);
  const [zoomActive, setZoomActive] = useState(false);
  const [zoomCoords, setZoomCoords] = useState({ x: 0, y: 0 });
  const [isPlaying, setIsPlaying] = useState(false);
  const mainImageRef = useRef<HTMLDivElement>(null);
  const thumbnailsRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  const selectedMedia = allMedia[selectedMediaIndex];
  const isCurrentMediaVideo = videos.includes(selectedMedia) || isVideoUrl(selectedMedia);

  useEffect(() => {
    setSelectedMediaIndex(0); // 媒体列表变更时重置索引
    setZoomActive(false);
    setIsPlaying(false);
  }, [images, videos]);

  // 当选择缩略图变化时，自动滚动到可视区域
  useEffect(() => {
    if (thumbnailsRef.current) {
      const thumbnailItems = thumbnailsRef.current.children;
      if (thumbnailItems[selectedMediaIndex]) {
        thumbnailItems[selectedMediaIndex].scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
        });
      }
    }

    // 处理视频播放状态
    if (isCurrentMediaVideo) {
      setZoomActive(false); // 视频模式下禁用缩放
      setIsPlaying(false);
    }
  }, [selectedMediaIndex, isCurrentMediaVideo]);

  const handleThumbnailClick = (index: number) => {
    setSelectedMediaIndex(index);
    setZoomActive(false); // 切换媒体时取消缩放
    setIsPlaying(false); // 切换媒体时暂停播放
  };

  const handlePrevMedia = () => {
    setSelectedMediaIndex(prevIndex => (prevIndex === 0 ? allMedia.length - 1 : prevIndex - 1));
    setZoomActive(false);
    setIsPlaying(false);
  };

  const handleNextMedia = () => {
    setSelectedMediaIndex(prevIndex => (prevIndex === allMedia.length - 1 ? 0 : prevIndex + 1));
    setZoomActive(false);
    setIsPlaying(false);
  };

  const openModal = () => {
    if (!isCurrentMediaVideo || !isPlaying) {
      // 非视频或视频未播放时才打开模态窗
      setShowModal(true);
    }
  };

  const closeModal = () => {
    setShowModal(false);
    setZoomActive(false);
    setIsPlaying(false);
  };

  const handleMouseMoveZoom = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!zoomActive || isCurrentMediaVideo) return;
    const { left, top, width, height } = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - left) / width) * 100;
    const y = ((e.clientY - top) / height) * 100;
    setZoomCoords({ x, y });
  };

  const toggleZoom = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (isCurrentMediaVideo) return; // 视频模式下禁用缩放
    e.stopPropagation(); // 阻止点击事件传播
    setZoomActive(!zoomActive);
  };

  const toggleVideoPlay = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation(); // 阻止点击事件传播
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleVideoEnded = () => {
    setIsPlaying(false);
  };

  const handleKeyNavigation = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowLeft') {
      handlePrevMedia();
    } else if (e.key === 'ArrowRight') {
      handleNextMedia();
    } else if (e.key === 'Escape') {
      if (showModal) closeModal();
      else setZoomActive(false);
    } else if (e.key === ' ' && isCurrentMediaVideo) {
      // 空格键播放/暂停视频
      e.preventDefault();
      if (videoRef.current) {
        if (isPlaying) {
          videoRef.current.pause();
        } else {
          videoRef.current.play();
        }
        setIsPlaying(!isPlaying);
      }
    }
  };

  if (!allMedia || allMedia.length === 0) {
    return (
      <div className="w-full h-[400px] md:h-[500px] bg-gray-200 flex items-center justify-center rounded-lg">
        <p className="text-gray-500">No media available</p>
      </div>
    );
  }

  // 渲染视频播放器
  const renderVideo = (url: string, fullscreen = false) => {
    // 检查是否为外部视频平台链接
    if (url.includes('youtube.com') || url.includes('youtu.be') || url.includes('vimeo.com')) {
      const embedUrl = getEmbedUrl(url);
      return (
        <div
          className={`relative w-full h-full overflow-hidden ${fullscreen ? 'aspect-video' : ''}`}
        >
          <iframe
            src={`${embedUrl}?autoplay=${isPlaying ? 1 : 0}&mute=0&controls=1`}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            className="absolute inset-0 w-full h-full"
            title={`${productName} video`}
          />
        </div>
      );
    }

    // 本地视频文件
    return (
      <video
        ref={videoRef}
        src={url}
        className={`w-full h-full object-contain ${fullscreen ? 'max-h-[80vh]' : ''}`}
        controls={fullscreen} // 全屏模式显示控制器
        playsInline
        onEnded={handleVideoEnded}
        onClick={e => !fullscreen && e.stopPropagation()} // 防止点击视频时触发父元素点击事件
        poster={images[0]} // 使用第一张图片作为封面
      />
    );
  };

  return (
    <div
      className="product-gallery flex flex-col md:flex-row gap-4 w-full"
      onKeyDown={handleKeyNavigation}
      tabIndex={0}
    >
      {/* 缩略图区域 - 垂直(MD+)/水平(SM) */}
      <div
        ref={thumbnailsRef}
        className="thumbnails-container flex md:flex-col gap-3 md:w-20 order-2 md:order-1 pb-2 md:pb-0 md:pr-2"
      >
        {allMedia.map((media, index) => {
          const isVideo = videos.includes(media) || isVideoUrl(media);
          return (
            <div
              key={index}
              className={`cursor-pointer rounded-md overflow-hidden shadow-sm relative w-16 h-16 md:w-full md:h-auto md:aspect-square flex-shrink-0
                        ${
                          selectedMediaIndex === index
                            ? 'ring-2 ring-blue-500 shadow-md transform scale-[1.03] z-10'
                            : 'border border-gray-200 hover:border-gray-300'
                        } transition-all duration-200 ease-in-out`}
              onClick={() => handleThumbnailClick(index)}
            >
              {isVideo ? (
                <>
                  {/* 视频缩略图 */}
                  <div className="absolute inset-0 bg-black/30 flex items-center justify-center z-10">
                    <PlayIcon className="w-6 h-6 text-white" />
                  </div>
                  {/* 如果是外部平台视频，使用第一张图片作为缩略图 */}
                  <Image
                    src={images[0] || '/images/video-thumbnail.jpg'}
                    alt={`${productName} video thumbnail ${index + 1}`}
                    fill
                    style={{ objectFit: 'cover' }}
                    sizes="(max-width: 768px) 64px, 80px"
                    className="transition-transform duration-200"
                  />
                </>
              ) : (
                <Image
                  src={media}
                  alt={`${productName} thumbnail ${index + 1}`}
                  fill
                  style={{ objectFit: 'cover' }}
                  sizes="(max-width: 768px) 64px, 80px"
                  className="transition-transform duration-200 hover:scale-105"
                />
              )}
              {selectedMediaIndex === index && (
                <div className="absolute inset-0 bg-blue-500 bg-opacity-10"></div>
              )}
            </div>
          );
        })}
      </div>

      {/* 主媒体展示区 - Order 1 on SM, Order 2 on MD+ */}
      <div
        ref={mainImageRef}
        className="main-image-container relative w-full md:flex-1 aspect-square md:aspect-[4/3] order-1 md:order-2 rounded-lg overflow-hidden border border-gray-200 shadow-sm group bg-white"
      >
        {isCurrentMediaVideo ? (
          // 视频内容展示
          <div className="w-full h-full relative" onClick={openModal}>
            {renderVideo(selectedMedia)}

            {/* 视频播放/暂停按钮 */}
            <button
              onClick={toggleVideoPlay}
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20 p-4 bg-black/40 hover:bg-black/60 rounded-full transition-all duration-200"
              aria-label={isPlaying ? 'Pause video' : 'Play video'}
            >
              {isPlaying ? (
                <PauseIcon className="w-8 h-8 text-white" />
              ) : (
                <PlayIcon className="w-8 h-8 text-white" />
              )}
            </button>

            {/* 全屏按钮 */}
            <button
              onClick={e => {
                e.stopPropagation();
                openModal();
              }}
              className="absolute bottom-3 right-3 z-10 p-2 bg-white/80 hover:bg-white rounded-full shadow-md transition-all duration-200 opacity-0 group-hover:opacity-100"
              aria-label="View in fullscreen"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-700"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 0h-4m4 0l-5-5"
                />
              </svg>
            </button>
          </div>
        ) : (
          // 图片内容展示
          <div
            className={`w-full h-full relative cursor-${zoomActive ? 'zoom-out' : 'zoom-in'}`}
            onClick={!zoomActive ? openModal : () => setZoomActive(false)}
            onMouseMove={handleMouseMoveZoom}
            onMouseLeave={() => setZoomActive(false)}
          >
            <Image
              src={selectedMedia}
              alt={`${productName} - ${selectedMediaIndex + 1}`}
              fill
              style={{
                objectFit: zoomActive ? 'cover' : 'contain',
                transformOrigin: zoomActive ? `${zoomCoords.x}% ${zoomCoords.y}%` : 'center center',
                transform: zoomActive ? 'scale(2.5)' : 'scale(1)',
                transition: zoomActive ? 'none' : 'transform 0.3s ease-out',
              }}
              priority
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 600px"
              className="select-none"
            />

            {/* 图片缩放提示覆盖层 */}
            {!zoomActive && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 hover:bg-opacity-10 transition-all duration-300">
                <div className="bg-white bg-opacity-0 group-hover:bg-opacity-80 p-3 rounded-full transform scale-90 group-hover:scale-100 transition-all duration-300">
                  <MagnifyingGlassPlusIcon className="w-6 h-6 text-gray-400 group-hover:text-gray-700" />
                </div>
              </div>
            )}

            {/* 缩放按钮 */}
            <button
              onClick={toggleZoom}
              className="absolute top-3 right-3 z-10 p-2 bg-white/80 hover:bg-white rounded-full shadow-md transition-all duration-200 opacity-0 group-hover:opacity-100 transform translate-y-1 group-hover:translate-y-0"
              aria-label={zoomActive ? 'Zoom out' : 'Zoom in'}
            >
              {zoomActive ? (
                <XMarkIcon className="w-5 h-5 text-gray-700" />
              ) : (
                <MagnifyingGlassPlusIcon className="w-5 h-5 text-gray-700" />
              )}
            </button>
          </div>
        )}

        {/* 导航箭头 - 适用于图片和视频 */}
        {allMedia.length > 1 && (
          <>
            <button
              onClick={handlePrevMedia}
              className="absolute top-1/2 left-2 transform -translate-y-1/2 z-10 p-2 bg-white/80 hover:bg-white rounded-full shadow-md transition-all duration-200 opacity-0 group-hover:opacity-100 hover:scale-110 disabled:opacity-30"
              aria-label="Previous media"
            >
              <ChevronLeftIcon className="w-5 h-5 text-gray-700" />
            </button>
            <button
              onClick={handleNextMedia}
              className="absolute top-1/2 right-2 transform -translate-y-1/2 z-10 p-2 bg-white/80 hover:bg-white rounded-full shadow-md transition-all duration-200 opacity-0 group-hover:opacity-100 hover:scale-110 disabled:opacity-30"
              aria-label="Next media"
            >
              <ChevronRightIcon className="w-5 h-5 text-gray-700" />
            </button>
          </>
        )}


      </div>

      {/* 全屏模态框 - 适用于图片和视频 */}
      {showModal && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/90 p-4"
          onClick={closeModal}
          onKeyDown={e => {
            if (e.key === 'Escape') closeModal();
            else if (e.key === 'ArrowLeft') handlePrevMedia();
            else if (e.key === 'ArrowRight') handleNextMedia();
          }}
          tabIndex={0}
        >
          <div
            className="relative max-w-4xl w-full h-[80vh] bg-transparent rounded-lg overflow-hidden"
            onClick={e => e.stopPropagation()}
          >
            {isCurrentMediaVideo ? (
              // 全屏视频播放器
              renderVideo(selectedMedia, true)
            ) : (
              // 全屏图片
              <Image
                src={selectedMedia}
                alt={`${productName} - fullscreen`}
                fill
                style={{ objectFit: 'contain' }}
                sizes="100vw"
                className="select-none"
                priority
              />
            )}

            <button
              onClick={closeModal}
              className="absolute top-4 right-4 z-50 p-2 bg-white/80 hover:bg-white rounded-full shadow-md transition-colors duration-200"
              aria-label="Close media viewer"
            >
              <XMarkIcon className="w-6 h-6 text-gray-700" />
            </button>

            {allMedia.length > 1 && (
              <>
                <button
                  onClick={e => {
                    e.stopPropagation();
                    handlePrevMedia();
                  }}
                  className="absolute top-1/2 left-4 transform -translate-y-1/2 z-50 p-3 bg-white/80 hover:bg-white rounded-full shadow-md transition-colors duration-200"
                  aria-label="Previous media in modal"
                >
                  <ChevronLeftIcon className="w-6 h-6 text-gray-700" />
                </button>
                <button
                  onClick={e => {
                    e.stopPropagation();
                    handleNextMedia();
                  }}
                  className="absolute top-1/2 right-4 transform -translate-y-1/2 z-50 p-3 bg-white/80 hover:bg-white rounded-full shadow-md transition-colors duration-200"
                  aria-label="Next media in modal"
                >
                  <ChevronRightIcon className="w-6 h-6 text-gray-700" />
                </button>

                {/* 缩略图导航 */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-50 flex gap-2 bg-black/40 p-2 rounded-lg overflow-x-auto max-w-[90%]">
                  {allMedia.map((media, index) => {
                    const isVideo = videos.includes(media) || isVideoUrl(media);
                    return (
                      <div
                        key={`modal-thumb-${index}`}
                        className={`w-12 h-12 cursor-pointer rounded-md overflow-hidden relative ${selectedMediaIndex === index ? 'ring-2 ring-white' : 'opacity-70 hover:opacity-100'}`}
                        onClick={e => {
                          e.stopPropagation();
                          handleThumbnailClick(index);
                        }}
                      >
                        {isVideo ? (
                          <>
                            <div className="absolute inset-0 bg-black/30 flex items-center justify-center z-10">
                              <PlayIcon className="w-4 h-4 text-white" />
                            </div>
                            <Image
                              src={images[0] || '/images/video-thumbnail.jpg'}
                              alt={`Video thumbnail ${index + 1}`}
                              width={48}
                              height={48}
                              style={{ objectFit: 'cover', width: '100%', height: '100%' }}
                            />
                          </>
                        ) : (
                          <Image
                            src={media}
                            alt={`Thumbnail ${index + 1}`}
                            width={48}
                            height={48}
                            style={{ objectFit: 'cover', width: '100%', height: '100%' }}
                          />
                        )}
                      </div>
                    );
                  })}
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductImageGallery;
