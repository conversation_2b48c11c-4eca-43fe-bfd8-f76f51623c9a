'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '../../../../components/admin/Layout';
import AuthGuard from '../../../../components/admin/AuthGuard';

interface FormSubmission {
  id: number;
  name: string;
  email: string;
  phone: string;
  country: string;
  playground_size: string;
  product: string;
  message: string;
  status: string;
  is_read: boolean;
  admin_notes: string;
  created_at: string;
  updated_at: string;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export default function FormNotificationsPage() {
  const [submissions, setSubmissions] = useState<FormSubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });
  const [selectedSubmission, setSelectedSubmission] = useState<FormSubmission | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [statusFilter, setStatusFilter] = useState('');
  const [readFilter, setReadFilter] = useState('');

  // 获取表单提交列表
  const fetchSubmissions = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });

      if (statusFilter) params.append('status', statusFilter);
      if (readFilter) params.append('is_read', readFilter);

      const response = await fetch(`/api/form-submissions?${params}`);
      const result = await response.json();

      if (result.success) {
        setSubmissions(result.data);
        setPagination(result.pagination);
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError('Failed to fetch form submissions');
      console.error('Error fetching submissions:', err);
    } finally {
      setLoading(false);
    }
  };

  // 更新表单提交状态
  const updateSubmission = async (id: number, updates: Partial<FormSubmission>) => {
    try {
      const response = await fetch(`/api/form-submissions/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      const result = await response.json();

      if (result.success) {
        // 更新本地状态
        setSubmissions(prev => 
          prev.map(sub => 
            sub.id === id ? { ...sub, ...updates } : sub
          )
        );
        
        if (selectedSubmission && selectedSubmission.id === id) {
          setSelectedSubmission({ ...selectedSubmission, ...updates });
        }
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError('Failed to update submission');
      console.error('Error updating submission:', err);
    }
  };

  // 标记为已读
  const markAsRead = async (id: number) => {
    await updateSubmission(id, { is_read: true });
  };

  // 删除表单提交
  const deleteSubmission = async (id: number) => {
    if (!confirm('确定要删除这个表单提交吗？')) return;

    try {
      const response = await fetch(`/api/form-submissions/${id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        setSubmissions(prev => prev.filter(sub => sub.id !== id));
        setShowModal(false);
        setSelectedSubmission(null);
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError('Failed to delete submission');
      console.error('Error deleting submission:', err);
    }
  };

  // 查看详情
  const viewDetails = async (submission: FormSubmission) => {
    setSelectedSubmission(submission);
    setShowModal(true);
    
    // 如果未读，标记为已读
    if (!submission.is_read) {
      await markAsRead(submission.id);
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800';
      case 'processing': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  useEffect(() => {
    fetchSubmissions();
  }, [pagination.page, statusFilter, readFilter]);

  return (
    <AuthGuard adminOnly>
      <AdminLayout title="表单通知">
        <div className="space-y-6">
          {/* 筛选器 */}
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="flex flex-wrap gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  状态筛选
                </label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="">全部状态</option>
                  <option value="new">新提交</option>
                  <option value="processing">处理中</option>
                  <option value="completed">已完成</option>
                  <option value="rejected">已拒绝</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  阅读状态
                </label>
                <select
                  value={readFilter}
                  onChange={(e) => setReadFilter(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="">全部</option>
                  <option value="false">未读</option>
                  <option value="true">已读</option>
                </select>
              </div>

              <div className="flex items-end">
                <button
                  onClick={() => {
                    setStatusFilter('');
                    setReadFilter('');
                    setPagination(prev => ({ ...prev, page: 1 }));
                  }}
                  className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
                >
                  清除筛选
                </button>
              </div>
            </div>
          </div>

          {/* 统计信息 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900">总提交数</h3>
              <p className="text-2xl font-bold text-blue-600">{pagination.total}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900">未读消息</h3>
              <p className="text-2xl font-bold text-red-600">
                {submissions.filter(s => !s.is_read).length}
              </p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900">新提交</h3>
              <p className="text-2xl font-bold text-green-600">
                {submissions.filter(s => s.status === 'new').length}
              </p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900">处理中</h3>
              <p className="text-2xl font-bold text-yellow-600">
                {submissions.filter(s => s.status === 'processing').length}
              </p>
            </div>
          </div>

          {/* 表单提交列表 */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">表单提交记录</h2>
            </div>

            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-2 text-gray-600">加载中...</p>
              </div>
            ) : error ? (
              <div className="p-8 text-center text-red-600">
                错误: {error}
              </div>
            ) : submissions.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                暂无表单提交记录
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        姓名
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        邮箱
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        电话
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        提交时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {submissions.map((submission) => (
                      <tr 
                        key={submission.id}
                        className={`hover:bg-gray-50 ${!submission.is_read ? 'bg-blue-50' : ''}`}
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {submission.name}
                              </div>
                              {!submission.is_read && (
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                  未读
                                </span>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {submission.email || '未提供'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {submission.phone}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getStatusColor(submission.status)}`}>
                            {submission.status === 'new' && '新提交'}
                            {submission.status === 'processing' && '处理中'}
                            {submission.status === 'completed' && '已完成'}
                            {submission.status === 'rejected' && '已拒绝'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(submission.created_at)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => viewDetails(submission)}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            查看详情
                          </button>
                          <button
                            onClick={() => deleteSubmission(submission.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            删除
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {/* 分页 */}
            {pagination.totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  显示 {((pagination.page - 1) * pagination.limit) + 1} 到{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
                  共 {pagination.total} 条记录
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                    disabled={pagination.page === 1}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    上一页
                  </button>
                  <span className="px-3 py-1 text-sm">
                    第 {pagination.page} 页，共 {pagination.totalPages} 页
                  </span>
                  <button
                    onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                    disabled={pagination.page === pagination.totalPages}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    下一页
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 详情模态框 */}
        {showModal && selectedSubmission && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    表单提交详情
                  </h3>
                  <button
                    onClick={() => setShowModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <i className="fas fa-times"></i>
                  </button>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">姓名</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedSubmission.name}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">邮箱</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedSubmission.email || '未提供'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">电话</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedSubmission.phone}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">国家</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedSubmission.country}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">场地大小</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedSubmission.playground_size}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">产品</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedSubmission.product}</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">留言</label>
                    <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{selectedSubmission.message}</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">状态</label>
                      <select
                        value={selectedSubmission.status}
                        onChange={(e) => {
                          const newStatus = e.target.value;
                          updateSubmission(selectedSubmission.id, { status: newStatus });
                        }}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      >
                        <option value="new">新提交</option>
                        <option value="processing">处理中</option>
                        <option value="completed">已完成</option>
                        <option value="rejected">已拒绝</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">提交时间</label>
                      <p className="mt-1 text-sm text-gray-900">{formatDate(selectedSubmission.created_at)}</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">管理员备注</label>
                    <textarea
                      value={selectedSubmission.admin_notes || ''}
                      onChange={(e) => {
                        setSelectedSubmission({
                          ...selectedSubmission,
                          admin_notes: e.target.value
                        });
                      }}
                      onBlur={(e) => {
                        updateSubmission(selectedSubmission.id, { admin_notes: e.target.value });
                      }}
                      rows={3}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                      placeholder="添加管理员备注..."
                    />
                  </div>
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    onClick={() => setShowModal(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                  >
                    关闭
                  </button>
                  <button
                    onClick={() => deleteSubmission(selectedSubmission.id)}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                  >
                    删除
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </AdminLayout>
    </AuthGuard>
  );
}
