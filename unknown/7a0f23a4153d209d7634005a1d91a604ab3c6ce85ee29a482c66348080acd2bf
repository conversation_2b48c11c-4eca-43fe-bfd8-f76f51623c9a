require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

// 获取数据库连接参数
const connectionString =
  process.env.DATABASE_URL ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 创建数据库连接池
const pool = new Pool({ connectionString });

async function addTestCategory() {
  try {
    // 定义测试分类
    const testCategory = {
      name: '测试分类',
      slug: 'test-category',
      description: '这是一个用于测试的分类',
      type: 'product_type',
      is_active: true,
      is_featured: true,
      featured_type: 'product',
      order_num: 1,
    };

    console.log('添加测试分类到数据库...');
    console.log(testCategory);

    // 插入分类
    const result = await pool.query(
      `
      INSERT INTO categories 
      (name, slug, description, type, is_active, is_featured, featured_type, order_num, created_at, updated_at)
      VALUES 
      ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING id
    `,
      [
        testCategory.name,
        testCategory.slug,
        testCategory.description,
        testCategory.type,
        testCategory.is_active,
        testCategory.is_featured,
        testCategory.featured_type,
        testCategory.order_num,
      ]
    );

    if (result.rows.length > 0) {
      console.log(`✅ 成功添加测试分类，ID: ${result.rows[0].id}`);

      // 添加测试产品
      console.log('\n添加测试产品到数据库...');

      const testProduct = {
        name: '测试产品',
        slug: 'test-product',
        description: '这是一个用于测试的产品',
        price: 1000,
        category: testCategory.slug,
        image_url: '/images/placeholder.jpg',
        is_featured: true,
        in_stock: true,
      };

      console.log(testProduct);

      const productResult = await pool.query(
        `
        INSERT INTO products
        (name, slug, description, price, category, image_url, is_featured, in_stock, created_at, updated_at)
        VALUES
        ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id
      `,
        [
          testProduct.name,
          testProduct.slug,
          testProduct.description,
          testProduct.price,
          testProduct.category,
          testProduct.image_url,
          testProduct.is_featured,
          testProduct.in_stock,
        ]
      );

      if (productResult.rows.length > 0) {
        console.log(`✅ 成功添加测试产品，ID: ${productResult.rows[0].id}`);
      }

      console.log('\n现在请刷新前端页面查看分类和产品是否显示！');
      console.log('如果不显示，尝试以下步骤:');
      console.log('1. 重启开发服务器: npm run dev');
      console.log('2. 清除浏览器缓存');
      console.log('3. 检查前端代码是否正确获取和显示分类');
    }
  } catch (error) {
    console.error('添加测试分类失败:', error);
  } finally {
    await pool.end();
  }
}

addTestCategory();
