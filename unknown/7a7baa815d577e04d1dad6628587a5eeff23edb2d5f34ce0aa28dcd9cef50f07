// 产品页面间距组件，用于确保页面内容与顶部有适当间距
'use client';

export interface ProductPageSpacerProps {
  height?: number;
  children?: React.ReactNode; // 使children可选
}

export default function ProductPageSpacer({ height = 120, children }: ProductPageSpacerProps) {
  return (
    <div 
      style={{ 
        height: `${height}px`,
        width: '100%'
      }}
      className="product-page-spacer"
    >
      {children}
    </div>
  );
} 