/**
 * 清除Next.js缓存的命令行脚本
 * 
 * 用法: 
 * node scripts/clear-cache.js
 * node scripts/clear-cache.js --all
 */
const fs = require('fs');
const path = require('path');
const rimraf = require('rimraf');
const { exec } = require('child_process');

// 是否删除所有缓存（包括node_modules）
const deleteAll = process.argv.includes('--all');

// 缓存目录
const cacheDirectories = [
  '.next/cache',          // Next.js 缓存
  '.next/server/pages',   // 服务端渲染页面缓存
  '.next/static',         // 静态资源缓存
];

if (deleteAll) {
  // 添加node_modules到删除列表
  cacheDirectories.push('node_modules/.cache'); // Webpack缓存
}

// 删除每个缓存目录
console.log('🧹 开始清除Next.js缓存...');

let deletedCount = 0;
let errorCount = 0;

// 删除指定的目录
cacheDirectories.forEach(dir => {
  const fullPath = path.join(process.cwd(), dir);
  
  try {
    if (fs.existsSync(fullPath)) {
      console.log(`正在删除 ${dir}...`);
      
      if (dir === 'node_modules/.cache') {
        // 对于node_modules/.cache，保留特定目录
        const cacheDir = path.join(process.cwd(), 'node_modules/.cache');
        fs.readdirSync(cacheDir).forEach(subdir => {
          const subdirPath = path.join(cacheDir, subdir);
          if (fs.statSync(subdirPath).isDirectory()) {
            rimraf.sync(subdirPath);
          }
        });
      } else {
        // 对于其他目录，直接删除
        rimraf.sync(fullPath);
      }
      
      deletedCount++;
      console.log(`✅ 已删除 ${dir}`);
    } else {
      console.log(`⚠️ 目录不存在: ${dir}`);
    }
  } catch (error) {
    console.error(`❌ 删除 ${dir} 时出错:`, error);
    errorCount++;
  }
});

// 清除浏览器和API缓存
console.log('🔄 重启Next.js服务并清除API缓存...');

// 停止可能正在运行的Next.js开发服务器
const isWindows = process.platform === 'win32';
if (isWindows) {
  exec('taskkill /F /IM node.exe', (error) => {
    if (error) {
      console.log('⚠️ 无正在运行的Node进程或无法结束进程');
    } else {
      console.log('✅ 已结束所有Node进程');
    }
    
    // 输出结果
    console.log('\n🧹 缓存清理完成!');
    console.log(`✅ 成功删除 ${deletedCount} 个缓存目录`);
    if (errorCount > 0) {
      console.log(`❌ ${errorCount} 个目录删除失败`);
    }
    
    console.log('\n🚀 现在可以重新运行开发服务器:');
    console.log('npm run dev');
  });
} else {
  exec('pkill -f "node.*next"', (error) => {
    if (error) {
      console.log('⚠️ 无正在运行的Next.js进程或无法结束进程');
    } else {
      console.log('✅ 已结束Next.js进程');
    }
    
    // 输出结果
    console.log('\n🧹 缓存清理完成!');
    console.log(`✅ 成功删除 ${deletedCount} 个缓存目录`);
    if (errorCount > 0) {
      console.log(`❌ ${errorCount} 个目录删除失败`);
    }
    
    console.log('\n🚀 现在可以重新运行开发服务器:');
    console.log('npm run dev');
  });
} 