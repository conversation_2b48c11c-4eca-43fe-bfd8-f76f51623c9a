// 生成不同颜色的占位图SVG
export function generatePlaceholderSVG(
  width: number,
  height: number,
  text: string,
  bgColor: string = '#0a59f7',
  textColor: string = 'white'
): string {
  // 创建安全的文本（转义特殊字符）
  const safeText = text.replace(/[<>&"']/g, c => {
    return (
      {
        '<': '&lt;',
        '>': '&gt;',
        '&': '&amp;',
        '"': '&quot;',
        "'": '&#39;',
      }[c] || c
    );
  });

  // 创建SVG字符串
  const svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="${bgColor}" />
    <text 
      x="50%" 
      y="50%" 
      font-family="Arial, sans-serif" 
      font-size="${Math.min(width, height) * 0.08}" 
      fill="${textColor}" 
      text-anchor="middle" 
      dominant-baseline="middle"
    >
      ${safeText}
    </text>
  </svg>`;

  // 转换为Base64
  const base64 = Buffer.from(svg).toString('base64');
  return `data:image/svg+xml;base64,${base64}`;
}

// 轮播图占位符 - 使用更加美观的渐变色
export const sliderPlaceholders = [
  generatePlaceholderSVG(1920, 550, 'Infinity Playground Products', '#0a59f7'),
  generatePlaceholderSVG(1920, 550, 'Custom Playground Design', '#1d4ed8'),
  generatePlaceholderSVG(1920, 550, 'Indoor Playground Solutions', '#3b82f6'),
];

// 解决方案占位符 - 使用柔和的色彩
export const solutionPlaceholders = [
  generatePlaceholderSVG(290, 200, '730 SQM Solution', '#0a59f7'),
  generatePlaceholderSVG(290, 200, '240 SQM Solution', '#1e40af'),
  generatePlaceholderSVG(290, 200, '2300 SQM Solution', '#3b82f6'),
  generatePlaceholderSVG(290, 200, '1200 SQM Solution', '#60a5fa'),
  generatePlaceholderSVG(290, 200, '400 SQM Solution', '#2563eb'),
  generatePlaceholderSVG(290, 200, '800 SQM Solution', '#1d4ed8'),
  generatePlaceholderSVG(290, 200, '600 SQM Solution', '#3b82f6'),
  generatePlaceholderSVG(290, 200, '375 SQM Solution', '#60a5fa'),
];

// 特性占位符 - 使用协调的蓝色渐变
export const featurePlaceholders = [
  generatePlaceholderSVG(580, 380, 'Playful Colors', '#0a59f7'),
  generatePlaceholderSVG(580, 380, 'Safety-Focused Surfaces', '#1d4ed8'),
  generatePlaceholderSVG(580, 380, 'Foam & PVC Materials', '#2563eb'),
  generatePlaceholderSVG(580, 380, 'Steel & Wood Materials', '#3b82f6'),
  generatePlaceholderSVG(580, 380, 'Dynamic Shapes', '#60a5fa'),
  generatePlaceholderSVG(580, 380, 'Creative Curves', '#93c5fd'),
];
