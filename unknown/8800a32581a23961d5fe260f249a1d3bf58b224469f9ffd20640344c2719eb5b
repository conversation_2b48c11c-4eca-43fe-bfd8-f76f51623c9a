"use client";

import { useEffect } from 'react';

/**
 * HighContrastFixer组件
 * 
 * 这个组件解决Microsoft Edge中-ms-high-contrast属性弃用的问题，
 * 通过在应用启动时注入一个脚本来拦截相关API调用并替换为现代标准。
 */
export default function HighContrastFixer() {
  useEffect(() => {
    // 避免重复初始化
    if (window.__highContrastFixerInitialized) return;
    window.__highContrastFixerInitialized = true;

    // 创建并注入改进版修复脚本
    const script = document.createElement('script');
    script.id = 'high-contrast-fixer-script';
    script.innerHTML = `
      (function() {
        'use strict';
        
        // 存储原始的CSS API方法
        const originalSetProperty = CSSStyleDeclaration.prototype.setProperty;
        const originalGetPropertyValue = CSSStyleDeclaration.prototype.getPropertyValue;
        const originalRemoveProperty = CSSStyleDeclaration.prototype.removeProperty;
        const originalGetComputedStyle = window.getComputedStyle;
        const originalMatchMedia = window.matchMedia;
        
        // 属性映射
        const propertyMap = {
          "forced-colors": 'forced-colors',
          "forced-color-adjust": 'forced-color-adjust',
          'forcedColors': 'forcedColors',
          'forcedColorsAdjust': 'forcedColorAdjust'
        };
        
        // 重写setProperty方法
        CSSStyleDeclaration.prototype.setProperty = function(propertyName, value, priority) {
          if (propertyName in propertyMap) {
            return originalSetProperty.call(this, propertyMap[propertyName], value, priority);
          }
          return originalSetProperty.call(this, propertyName, value, priority);
        };
        
        // 重写getPropertyValue方法
        CSSStyleDeclaration.prototype.getPropertyValue = function(propertyName) {
          if (propertyName in propertyMap) {
            console.log(\`[High Contrast Fix] 重定向CSS属性获取: \${propertyName} → \${propertyMap[propertyName]}\`);
            return originalGetPropertyValue.call(this, propertyMap[propertyName]);
          }
          return originalGetPropertyValue.call(this, propertyName);
        };
        
        // 重写removeProperty方法
        CSSStyleDeclaration.prototype.removeProperty = function(propertyName) {
          if (propertyName in propertyMap) {
            return originalRemoveProperty.call(this, propertyMap[propertyName]);
          }
          return originalRemoveProperty.call(this, propertyName);
        };
        
        // 重写matchMedia方法
        window.matchMedia = function(query) {
          if (typeof query === 'string') {
            // 替换-ms-high-contrast媒体查询
            query = query.replace(/\\(\\s*-ms-high-contrast\\s*:\\s*[^\\)]+\\)/g, '(forced-colors: active)');
          }
          return originalMatchMedia.call(window, query);
        };
        
        // 替换已存在的CSS规则
        function processCSSRules() {
          try {
            for (let i = 0; i < document.styleSheets.length; i++) {
              const sheet = document.styleSheets[i];
              
              try {
                // 跳过无法访问的样式表（如跨域样式表）
                if (!sheet.cssRules) continue;
                
                // 处理每条规则
                for (let j = 0; j < sheet.cssRules.length; j++) {
                  const rule = sheet.cssRules[j];
                  
                  // 处理媒体查询规则
                  if (rule.type === CSSRule.MEDIA_RULE) {
                    if (rule.media && rule.media.mediaText.includes('-ms-high-contrast')) {
                      const newMediaText = rule.media.mediaText.replace(
                        /\\(\\s*-ms-high-contrast\\s*:\\s*[^\\)]+\\)/g,
                        '(forced-colors: active)'
                      );
                      
                      // 创建新规则
                      const cssText = rule.cssText.replace(rule.media.mediaText, newMediaText);
                      
                      try {
                        // 删除旧规则并插入新规则
                        sheet.deleteRule(j);
                        sheet.insertRule(cssText, j);
                      } catch (e) {
                        // 某些环境下可能无法修改规则
                        console.debug('[High Contrast Fix] 无法替换媒体查询规则', e);
                      }
                    }
                  }
                }
              } catch (e) {
                // 跨域样式表可能会引发安全错误
                console.debug('[High Contrast Fix] 无法处理样式表', e);
              }
            }
          } catch (e) {
            console.debug('[High Contrast Fix] 处理CSS规则出错', e);
          }
        }
        
        // 添加全局覆盖样式
        function addOverrideStyles() {
          const styleId = 'high-contrast-override-styles';
          if (document.getElementById(styleId)) return;
          
          const style = document.createElement('style');
          style.id = styleId;
          style.textContent = \`
            /* 防止-ms-high-contrast媒体查询生效 */
            @media (forced-colors: active), (forced-colors: none) {
              * { 
                forced-color-adjust: none !important;
                forced-color-adjust: none !important;
              }
            }
            
            /* 现代高对比度模式下保持适当的样式 */
            @media (forced-colors: active) {
              /* 恢复关键交互元素的对比度 */
              a, button, input, select, textarea, [role="button"], .btn {
                forced-color-adjust: auto !important;
              }
            }
          \`;
          
          document.head.appendChild(style);
        }
        
        // 初始化函数
        function init() {
          addOverrideStyles();
          processCSSRules();
          
          // 设置MutationObserver监听新加载的样式表
          if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver(function(mutations) {
              let styleChanged = false;
              
              for (const mutation of mutations) {
                if (mutation.type === 'childList') {
                  const addedNodes = Array.from(mutation.addedNodes);
                  styleChanged = addedNodes.some(node => 
                    node.nodeName === 'STYLE' || 
                    (node.nodeName === 'LINK' && node.rel === 'stylesheet')
                  );
                  
                  if (styleChanged) break;
                }
              }
              
              if (styleChanged) {
                // 当样式表变化时重新处理规则
                setTimeout(processCSSRules, 0);
              }
            });
            
            observer.observe(document.head, { childList: true, subtree: true });
            observer.observe(document.body, { childList: true, subtree: true });
          }
        }
        
        // 如果DOM已经准备好，立即初始化；否则等待DOM内容加载
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', init);
        } else {
          init();
        }
        
        // 在window加载完成后再次运行，确保捕获所有动态加载的资源
        window.addEventListener('load', processCSSRules);
      })();
    `;
    
    // 将脚本插入到页面
    document.head.appendChild(script);
    
  }, []);

  // 组件不渲染任何内容
  return null;
} 