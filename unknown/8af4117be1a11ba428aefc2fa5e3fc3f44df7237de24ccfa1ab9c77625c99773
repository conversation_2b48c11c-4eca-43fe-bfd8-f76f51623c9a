import type { Metadata } from 'next';
import { i18n } from '../../utils/i18n';
// import { getDictionary } from '../../utils/i18n'; // Removed unused import

// 引入组件
import HeroSlider from '../../components/HeroSlider';
import AdvantageShowcase from '../../components/AdvantageShowcase';
import HolographicFeatures from '../../components/HolographicFeatures';
import FactoryShowcase from '../../components/FactoryShowcase';
import CustomPlayground from '../../components/CustomPlayground';
import GlobalCases from '../../components/GlobalCases';
import AboutSection from '../../components/AboutSection';
import FloatingButtons from '../../components/FloatingButtons';

// 导入样式
import '../../styles/home-page.css';

// Generate metadata based on locale
export async function generateMetadata({
  params: { lang }, // Destructure lang directly
}: {
  params: { lang: string };
}): Promise<Metadata> {
  const locale = lang || i18n.defaultLocale;
  // const dictionary = await getDictionary(locale); // Removed unused dictionary

  return {
    title:
      locale === 'zh'
        ? '广州钧盛科技有限公司 - 3D全息投影|裸眼5D互动|沉浸式餐厅|科技展厅'
        : 'Guangzhou Junsheng - 3D Holographic & Immersive Projection Solutions',
    description:
      locale === 'zh'
        ? '专业提供3D全息投影、裸眼5D互动投影、户外地面投影、沉浸式餐厅、墙面投影、宴会厅及科技展厅解决方案。'
        : 'Professional provider of 3D holographic projection, naked-eye 5D interactive projection, immersive restaurants, and tech exhibition hall solutions.',
  };
}

// Generate static params for all supported locales
export function generateStaticParams() {
  return i18n.locales.map(locale => ({ lang: locale }));
}

export default async function HomePage() {
  // const locale = params.lang; // Removed unused locale, params itself is kept in signature for now

  return (
    <main className="home-page">
      {/* 全屏Hero区域 */}
      <section className="hero-section">
        <HeroSlider />
      </section>

      {/* 页面分隔 */}
      <div className="section-spacer"></div>

      {/* 企业优势展示 */}
      <section className="section-container">
        <AdvantageShowcase />
      </section>

      {/* 页面分隔 */}
      <div className="section-spacer"></div>

      {/* 全息技术特点展示 */}
      <section className="section-features bg-light">
        <div className="section-pattern section-pattern-top"></div>
        <HolographicFeatures />
        <div className="section-pattern section-pattern-bottom"></div>
      </section>

      {/* 页面分隔 */}
      <div className="section-spacer"></div>

      {/* 工厂展示区域 */}
      <section className="section-container">
        <FactoryShowcase />
      </section>

      {/* 页面分隔 */}
      <div className="section-spacer"></div>

      {/* 定制方案区域 */}
      <section className="section-custom bg-dark">
        <div className="section-pattern section-pattern-top section-pattern-dark"></div>
        <CustomPlayground />
        <div className="section-pattern section-pattern-bottom section-pattern-dark"></div>
      </section>

      {/* 页面分隔 */}
      <div className="section-spacer"></div>

      {/* 全球案例展示 */}
      <section className="section-container">
        <GlobalCases />
      </section>

      {/* 页面分隔 */}
      <div className="section-spacer"></div>

      {/* 关于我们区域 */}
      <section className="section-about bg-light">
        <div className="section-pattern section-pattern-top"></div>
        <AboutSection />
      </section>

      {/* 悬浮联系按钮 */}
      <FloatingButtons />
    </main>
  );
}
