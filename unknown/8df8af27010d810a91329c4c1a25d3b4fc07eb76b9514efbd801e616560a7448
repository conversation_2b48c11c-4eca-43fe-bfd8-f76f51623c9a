'use client';

import React, { ReactNode } from 'react';

interface ProductPageSpacerProps {
  children?: ReactNode;
  height?: number;
}

/**
 * 产品页面顶部间隔组件
 * 用于在产品详情页顶部创建空间，确保内容不会与导航栏重叠
 * 默认高度为150px，可通过props调整
 */
const ProductPageSpacer: React.FC<ProductPageSpacerProps> = ({
  children,
  height = 150
}) => {
  return (
    <div
      className="product-page-spacer"
      style={{ height: `${height}px`, width: '100%' }}
    >
      {children}
    </div>
  );
};

export default ProductPageSpacer;
