const fs = require('fs');
const path = require('path');

// 确保目标目录存在
const targetDir = 'public/images/products';
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// 产品图片复制映射
const imageCopyMap = [
  // 互动足球
  {
    src: '产品/1/互动足球/未标题-1.jpg',
    dest: 'public/images/products/interactive-football.jpg'
  },
  // AR体感蹦床
  {
    src: '产品/1/AR体感蹦床/未标题-1.jpg',
    dest: 'public/images/products/ar-trampoline.jpg'
  },
  // 体感攀岩
  {
    src: '产品/1/体感攀岩/未标题-1.jpg',
    dest: 'public/images/products/motion-climbing.jpg'
  },
  // 互动砸球
  {
    src: '产品/1/互动砸球/未标题-1.jpg',
    dest: 'public/images/products/interactive-ball.jpg'
  },
  // AR教育
  {
    src: '产品/1/AR教育/未标题-1.jpg',
    dest: 'public/images/products/ar-education.jpg'
  },
  // KTV
  {
    src: '产品/1/ktv/未标题-1.jpg',
    dest: 'public/images/products/ktv-system.jpg'
  },
  // 一体机
  {
    src: '产品/1/一体机/未标题-1.jpg',
    dest: 'public/images/products/all-in-one.jpg'
  },
  // 3D电子沙盘
  {
    src: '产品/1/3d电子沙盘/未标题-1.jpg',
    dest: 'public/images/products/3d-sandbox.jpg'
  },
  // 保龄球
  {
    src: '产品/3/保龄球/未标题-1.jpg',
    dest: 'public/images/products/bowling.jpg'
  },
  // 儿童互动沙滩
  {
    src: '产品/3/儿童互动沙滩/未标题-1.jpg',
    dest: 'public/images/products/children-beach.jpg'
  },
  // 儿童互动砸球
  {
    src: '产品/3/儿童互动砸球/未标题-1.jpg',
    dest: 'public/images/products/children-ball.jpg'
  },
  // 全息沙幕
  {
    src: '产品/3/全息沙幕/未标题-1.jpg',
    dest: 'public/images/products/holographic-screen.jpg'
  },
  // 全息沙桌
  {
    src: '产品/3/全息沙桌/未标题-1.jpg',
    dest: 'public/images/products/holographic-table.jpg'
  },
  // 全息舞台
  {
    src: '产品/3/全息舞台/未标题-1.jpg',
    dest: 'public/images/products/holographic-stage.jpg'
  },
  // 全息餐厅
  {
    src: '产品/3/全息餐厅/未标题-1.jpg',
    dest: 'public/images/products/holographic-dining.jpg'
  }
];

// 复制图片
let successCount = 0;
let failCount = 0;

console.log('开始复制产品图片...\n');

imageCopyMap.forEach(({ src, dest }) => {
  try {
    if (fs.existsSync(src)) {
      fs.copyFileSync(src, dest);
      console.log(`✅ 复制成功: ${src} -> ${dest}`);
      successCount++;
    } else {
      console.log(`❌ 源文件不存在: ${src}`);
      failCount++;
    }
  } catch (error) {
    console.log(`❌ 复制失败: ${src} -> ${error.message}`);
    failCount++;
  }
});

console.log(`\n复制完成！成功: ${successCount}, 失败: ${failCount}`);

// 如果有失败的，尝试使用images文件夹中的第一张图片
if (failCount > 0) {
  console.log('\n尝试使用images文件夹中的图片...\n');
  
  const fallbackMap = [
    {
      src: '产品/1/互动足球/images/产品介绍模板_01.jpg',
      dest: 'public/images/products/interactive-football.jpg'
    },
    {
      src: '产品/1/AR体感蹦床/images/产品介绍模板_01.jpg',
      dest: 'public/images/products/ar-trampoline.jpg'
    },
    {
      src: '产品/1/体感攀岩/images/产品介绍模板_01.jpg',
      dest: 'public/images/products/motion-climbing.jpg'
    },
    {
      src: '产品/1/互动砸球/images/产品介绍模板_01.jpg',
      dest: 'public/images/products/interactive-ball.jpg'
    },
    {
      src: '产品/1/AR教育/images/产品介绍模板_01.jpg',
      dest: 'public/images/products/ar-education.jpg'
    },
    {
      src: '产品/1/ktv/images/产品介绍模板_01.jpg',
      dest: 'public/images/products/ktv-system.jpg'
    },
    {
      src: '产品/1/一体机/images/产品介绍模板_01.jpg',
      dest: 'public/images/products/all-in-one.jpg'
    },
    {
      src: '产品/1/3d电子沙盘/images/产品介绍模板_01.jpg',
      dest: 'public/images/products/3d-sandbox.jpg'
    }
  ];
  
  fallbackMap.forEach(({ src, dest }) => {
    try {
      if (fs.existsSync(src) && !fs.existsSync(dest)) {
        fs.copyFileSync(src, dest);
        console.log(`✅ 备用复制成功: ${src} -> ${dest}`);
      }
    } catch (error) {
      console.log(`❌ 备用复制失败: ${src} -> ${error.message}`);
    }
  });
}
