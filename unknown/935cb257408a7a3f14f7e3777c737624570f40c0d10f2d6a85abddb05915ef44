# 产品上传工具集合

本目录包含了完整的产品上传工具集合，支持通过命令行快速上传产品到数据库。

## 🚀 快速开始

### 1. 快速测试（推荐新手）
```bash
node scripts/quick-start-upload.js
```
这个工具会自动检查环境并创建一个测试产品，验证上传功能是否正常。

### 2. 单个产品上传（推荐）
```bash
node scripts/api-upload-product.js
```
交互式界面，逐步引导您输入产品信息。

### 3. 批量产品上传（推荐）
```bash
node scripts/api-batch-upload.js scripts/sample-products.json
```
从JSON文件批量上传多个产品。

## 📁 工具文件列表

### 主要上传工具
| 文件名 | 类型 | 推荐度 | 描述 |
|--------|------|--------|------|
| `quick-start-upload.js` | 测试工具 | ⭐⭐⭐⭐⭐ | 快速测试上传功能 |
| `api-upload-product.js` | API工具 | ⭐⭐⭐⭐⭐ | API交互式单产品上传 |
| `api-batch-upload.js` | API工具 | ⭐⭐⭐⭐⭐ | API批量产品上传 |
| `command-upload-product.js` | 数据库工具 | ⭐⭐⭐ | 直连数据库交互式上传 |
| `single-upload-product.js` | 数据库工具 | ⭐⭐⭐ | 直连数据库快速上传 |
| `batch-upload-products.js` | 数据库工具 | ⭐⭐⭐ | 直连数据库批量上传 |

### 示例和测试文件
| 文件名 | 描述 |
|--------|------|
| `sample-products.json` | 示例产品数据文件 |
| `quick-test-upload.js` | 简单的数据库上传测试 |
| `test-upload-connection.js` | 数据库连接测试 |
| `simple-test.js` | 基础数据库连接测试 |

### 文档文件
| 文件名 | 描述 |
|--------|------|
| `product-upload-guide.md` | 详细使用指南 |
| `README-upload-tools.md` | 本文件，工具概览 |

## 🎯 使用场景

### 场景1：第一次使用，想测试功能
```bash
# 1. 启动Next.js应用
npm run dev

# 2. 运行快速测试
node scripts/quick-start-upload.js
```

### 场景2：上传单个产品
```bash
# 1. 启动Next.js应用
npm run dev

# 2. 运行交互式上传工具
node scripts/api-upload-product.js
```

### 场景3：批量上传多个产品
```bash
# 1. 准备产品数据JSON文件（可参考sample-products.json）
# 2. 启动Next.js应用
npm run dev

# 3. 运行批量上传工具
node scripts/api-batch-upload.js your-products.json
```

### 场景4：开发环境，需要直接操作数据库
```bash
# 确保数据库连接正常，然后运行
node scripts/command-upload-product.js
```

## 🔧 环境要求

### API工具（推荐）
- Node.js 16+
- Next.js应用运行中 (`npm run dev`)
- 网络连接正常

### 数据库工具
- Node.js 16+
- PostgreSQL数据库连接
- 环境变量配置正确

## 📊 工具对比

| 特性 | API工具 | 数据库工具 |
|------|---------|------------|
| 安全性 | ✅ 高 | ⚠️ 中等 |
| 易用性 | ✅ 简单 | ⚠️ 需要配置 |
| 依赖性 | Next.js应用 | 数据库连接 |
| 错误处理 | ✅ 完善 | ⚠️ 基础 |
| 推荐程度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

## 🚨 注意事项

1. **推荐使用API工具**：更安全、更可靠、更易用
2. **确保应用运行**：API工具需要Next.js应用运行
3. **数据备份**：批量上传前建议备份数据库
4. **测试先行**：生产环境使用前先在开发环境测试
5. **slug唯一性**：确保产品slug不重复

## 🆘 故障排除

### 问题1：API工具连接失败
```bash
# 检查Next.js应用是否运行
curl http://localhost:3000/api/admin/products

# 如果失败，启动应用
npm run dev
```

### 问题2：数据库工具连接失败
```bash
# 测试数据库连接
node scripts/simple-test.js
```

### 问题3：产品创建失败
- 检查必需字段是否填写
- 检查slug是否已存在
- 查看错误信息进行调试

## 📚 更多信息

- 详细使用说明：查看 `product-upload-guide.md`
- 示例数据格式：查看 `sample-products.json`
- API文档：查看项目中的API路由文件

## 🤝 贡献

如果您发现问题或有改进建议，请：
1. 查看现有工具是否满足需求
2. 参考现有代码风格
3. 添加适当的错误处理
4. 更新相关文档
