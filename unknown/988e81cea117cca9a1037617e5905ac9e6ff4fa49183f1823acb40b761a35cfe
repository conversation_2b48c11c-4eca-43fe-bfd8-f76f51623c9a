import React from 'react';
import Link from 'next/link';

interface AnimatedButtonProps {
  href: string;
  text: string;
  className?: string;
}

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({ 
  href, 
  text, 
  className = 'btn-primary' 
}) => {
  // 将文本转换为字母数组，保留空格
  const letters = text.split('').map((char, index) => {
    if (char === ' ') {
      return <i key={index}>&nbsp;</i>;
    }
    return <i key={index}>{char}</i>;
  });

  return (
    <Link 
      href={href} 
      className={className}
      data-text={text.toUpperCase()}
    >
      {letters}
    </Link>
  );
};

// 用于生成静态HTML的工具函数
export const generateAnimatedButtonHTML = (text: string, href: string, className: string = 'btn-primary') => {
  const letters = text.split('').map((char, index) => {
    if (char === ' ') {
      return `<i>&nbsp;</i>`;
    }
    return `<i>${char}</i>`;
  });

  return `<a href="${href}" class="${className}" data-text="${text.toUpperCase()}">${letters.join('')}</a>`;
};

export default AnimatedButton;
