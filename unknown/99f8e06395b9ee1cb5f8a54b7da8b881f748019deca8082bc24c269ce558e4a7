'use client';

import { useEffect } from 'react';
import { fixApiUrl } from '@/lib/apiUtils';

/**
 * API端口修复组件
 * 
 * 此组件劫持全局fetch和XMLHttpRequest API，
 * 自动将API请求的端口号修复为当前页面使用的端口号
 */
const ApiPortFixer = () => {
  useEffect(() => {
    // 只在客户端执行
    if (typeof window === 'undefined') return;
    
    // 保存原始fetch函数
    const originalFetch = window.fetch;
    
    // 重写fetch函数
    window.fetch = function(url, options) {
      // 修复URL中的端口号
      const fixedUrl = fixApiUrl(url);
      
      // 调用原始fetch
      return originalFetch.call(this, fixedUrl, options);
    };
    
    // 保存原始XMLHttpRequest.open方法
    const originalXhrOpen = XMLHttpRequest.prototype.open;
    
    // 重写XMLHttpRequest.open方法
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
      // 修复URL中的端口号
      const fixedUrl = fixApiUrl(url);
      
      // 调用原始open方法
      return originalXhrOpen.call(this, method, fixedUrl, ...args);
    };
    
    // 添加端口检测日志
    console.log(`[API端口修复] 当前使用端口: ${window.location.port || '80/443'}`);
    
    // 清理函数
    return () => {
      // 恢复原始函数
      window.fetch = originalFetch;
      XMLHttpRequest.prototype.open = originalXhrOpen;
    };
  }, []);
  
  // 此组件不渲染任何内容
  return null;
};

export default ApiPortFixer; 