/* 设计流程样式 */
.design-process {
  padding: 60px 0;
}

.design-process .section-title {
  text-align: center;
  margin-bottom: 40px;
  color: #0a59f7;
  font-size: 32px;
}

.process-steps {
  margin-top: 40px;
}

.step-item {
  margin-bottom: 40px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.step-image {
  width: 100%;
  height: 400px;
  position: relative;
  overflow: hidden;
  max-width: 600px;
  margin: 0 auto;
}

.step-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.step-content-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 30px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  z-index: 2;
}

.step-number {
  font-size: 36px;
  font-weight: 700;
  color: #0a59f7;
  background-color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.step-content-overlay h3 {
  font-size: 24px;
  margin-bottom: 15px;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.step-content-overlay p {
  font-size: 16px;
  line-height: 1.6;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .step-image {
    height: 300px;
  }

  .step-content-overlay {
    padding: 20px;
  }

  .step-number {
    width: 50px;
    height: 50px;
    font-size: 28px;
  }

  .step-content-overlay h3 {
    font-size: 20px;
  }

  .step-content-overlay p {
    font-size: 14px;
  }
}
