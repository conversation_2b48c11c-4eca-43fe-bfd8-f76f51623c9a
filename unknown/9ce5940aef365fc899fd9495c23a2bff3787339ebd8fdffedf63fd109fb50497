'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface SimpleProductGalleryProps {
  images: string[];
  productName: string;
}

const SimpleProductGallery: React.FC<SimpleProductGalleryProps> = ({ images, productName }) => {
  const [selectedImage, setSelectedImage] = useState<string>(images[0] || '');

  useEffect(() => {
    if (images.length > 0) {
      setSelectedImage(images[0]);
    }
  }, [images]);

  if (!images || images.length === 0) {
    return (
      <div className="w-full aspect-[16/9] bg-gray-100 flex items-center justify-center">
        <p className="text-gray-400">暂无图片</p>
      </div>
    );
  }

  return (
    <div className="w-full border-[2px] border-black p-1 flex gap-1">
      {/* 左侧缩略图列表 */}
      <div className="w-1/4 flex flex-col gap-1">
        {images.map((image, index) => (
          <div
            key={index}
            className={`w-full border border-gray-200 cursor-pointer hover:border-gray-500 transition-all ${
              selectedImage === image ? 'border-black' : ''
            }`}
            onClick={() => setSelectedImage(image)}
          >
            <div className="relative aspect-square">
              <Image
                src={image}
                alt={`${productName} thumbnail ${index + 1}`}
                fill
                sizes="100px"
                style={{ objectFit: 'cover' }}
                className="select-none"
              />
            </div>
          </div>
        ))}
      </div>

      {/* 右侧主图显示区 */}
      <div className="w-3/4 relative aspect-video bg-white">
        <Image
          src={selectedImage}
          alt={`${productName} - selected image`}
          fill
          sizes="(max-width: 768px) 100vw, 600px"
          style={{ objectFit: 'contain' }}
          priority
          className="select-none"
        />
      </div>
    </div>
  );
};

export default SimpleProductGallery;
