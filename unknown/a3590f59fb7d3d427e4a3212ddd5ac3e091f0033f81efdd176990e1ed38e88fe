'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from "next/navigation";
import { useSession } from 'next-auth/react';
import { useForm } from 'react-hook-form';
import AdminLayout from '../../../../../components/admin/Layout';

interface UserFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: 'admin' | 'editor';
}

export default function Page() {
  const router = useRouter();
  const { data: session } = useSession();
  const params = useParams<{ id: string }>();
  const id = params.id;
  const isNew = id === 'new';

  const [loading, setLoading] = useState(!isNew);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<UserFormData>({
    defaultValues: {
      role: 'editor',
    },
  });

  // Watch password for confirmation validation
  const password = watch('password', '');

  useEffect(() => {
    // Redirect if not admin
    if (session && session.user.role !== 'admin') {
      router.push('/[lang]/admin');
    }
  }, [session, router]);

  useEffect(() => {
    const fetchUser = async () => {
      if (isNew) {
        setLoading(false);
        return;
      }

      try {
        const res = await fetch(`/api/users/${id}`);

        if (!res.ok) {
          throw new Error('Failed to fetch user');
        }

        const data = await res.json();

        if (data.success) {
          const user = data.data;

          // Set form values
          setValue('username', user.username);
          setValue('email', user.email);
          setValue('role', user.role);
        } else {
          throw new Error(data.message || 'Failed to fetch user');
        }
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('An unknown error occurred while fetching the user.');
        }
        console.error('Error fetching user:', err);
      } finally {
        setLoading(false);
      }
    };

    if (id && session?.user.role === 'admin') {
      fetchUser();
    }
  }, [id, isNew, setValue, session]);

  const onSubmit = async (data: UserFormData) => {
    setSaving(true);
    setError(null);

    try {
      // Prepare data for API
      const userData: Record<string, string> = {
        username: data.username,
        email: data.email,
        role: data.role,
      };

      // Add password if provided
      if (data.password) {
        userData['password'] = data.password;
      }

      // Determine if creating or updating
      const url = isNew ? '/api/users' : `/api/users/${id}`;
      const method = isNew ? 'POST' : 'PUT';

      const res = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to save user');
      }

      const responseData = await res.json();

      if (responseData.success) {
        // Redirect to users list
        router.push('/[lang]/admin/users');
      } else {
        throw new Error(responseData.message || 'Failed to save user');
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred while saving the user.');
      }
      console.error('Error saving user:', err);
    } finally {
      setSaving(false);
    }
  };

  // If not admin, don't render the page
  if (session && session.user.role !== 'admin') {
    return null;
  }

  return (
    <AdminLayout title={isNew ? 'Add New User' : 'Edit User'}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-800">{isNew ? 'Add New User' : 'Edit User'}</h2>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-xl text-gray-500">Loading user...</div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-6">
          {error && <div className="bg-red-100 text-red-700 p-4 rounded-md mb-6">{error}</div>}

          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Username <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  className={`w-full px-4 py-2 border rounded-md ${
                    errors.username ? 'border-red-500' : 'border-gray-300'
                  }`}
                  {...register('username', { required: 'Username is required' })}
                />
                {errors.username && (
                  <p className="mt-1 text-sm text-red-500">{errors.username.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  className={`w-full px-4 py-2 border rounded-md ${
                    errors.email ? 'border-red-500' : 'border-gray-300'
                  }`}
                  {...register('email', {
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Invalid email address',
                    },
                  })}
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-500">{errors.email.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Password {isNew && <span className="text-red-500">*</span>}
                </label>
                <input
                  type="password"
                  className={`w-full px-4 py-2 border rounded-md ${
                    errors.password ? 'border-red-500' : 'border-gray-300'
                  }`}
                  {...register('password', {
                    required: isNew ? 'Password is required' : false,
                    minLength: {
                      value: 6,
                      message: 'Password must be at least 6 characters',
                    },
                  })}
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-red-500">{errors.password.message}</p>
                )}
                {!isNew && (
                  <p className="mt-1 text-sm text-gray-500">
                    Leave blank to keep the current password
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Confirm Password {isNew && <span className="text-red-500">*</span>}
                </label>
                <input
                  type="password"
                  className={`w-full px-4 py-2 border rounded-md ${
                    errors.confirmPassword ? 'border-red-500' : 'border-gray-300'
                  }`}
                  {...register('confirmPassword', {
                    required: isNew ? 'Please confirm your password' : false,
                    validate: value =>
                      !value || !password || value === password || 'Passwords do not match',
                  })}
                />
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm text-red-500">{errors.confirmPassword.message}</p>
                )}
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Role <span className="text-red-500">*</span>
              </label>
              <select
                className={`w-full px-4 py-2 border rounded-md ${
                  errors.role ? 'border-red-500' : 'border-gray-300'
                }`}
                {...register('role', { required: 'Role is required' })}
              >
                <option value="editor">Editor</option>
                <option value="admin">Admin</option>
              </select>
              {errors.role && <p className="mt-1 text-sm text-red-500">{errors.role.message}</p>}
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => router.push('/[lang]/admin/users')}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={saving}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400"
              >
                {saving ? 'Saving...' : 'Save User'}
              </button>
            </div>
          </form>
        </div>
      )}
    </AdminLayout>
  );
}
