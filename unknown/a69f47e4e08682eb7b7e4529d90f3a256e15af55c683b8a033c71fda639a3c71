'use client';

import { useEffect, useState } from 'react';

/**
 * LoadingFix组件
 * 
 * 用于解决页面刷新过程中样式重叠的问题
 * 通过在页面加载过程中添加和移除特定的CSS类来控制内容的可见性
 */
export default function LoadingFix() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // 添加js-loading类到body，在CSS完全加载前隐藏内容
    document.body.classList.add('js-loading');

    // 页面加载完成后的处理
    const handleLoad = () => {
      // 短暂延迟以确保所有CSS已应用
      setTimeout(() => {
        // 移除js-loading类，显示内容
        document.body.classList.remove('js-loading');
        document.body.classList.add('js-loading-complete');
        document.body.classList.add('loaded');
        setIsLoaded(true);
      }, 300);
    };

    // 如果页面已经加载完成，直接执行handleLoad
    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      // 否则监听load事件
      window.addEventListener('load', handleLoad);
    }

    // 清理函数
    return () => {
      window.removeEventListener('load', handleLoad);
    };
  }, []);

  // 添加一个额外的修复，专门针对产品页面
  useEffect(() => {
    if (!isLoaded) return;

    // 检查当前页面是否是产品页面
    const isProductPage = window.location.pathname.includes('/products/');
    
    if (isProductPage) {
      // 获取产品详情容器
      const productDetailContainer = document.querySelector('.product-detail-container');
      
      if (productDetailContainer) {
        // 确保产品详情容器有正确的样式
        productDetailContainer.classList.add('product-detail-fix');
        
        // 获取顶部banner图片
        const topBanner = productDetailContainer.querySelector('.w-full.absolute');
        
        if (topBanner) {
          // 确保banner图片在正确的位置
          (topBanner as HTMLElement).style.zIndex = '0';
        }
        
        // 获取内容区域
        const contentArea = productDetailContainer.querySelector('.bg-white.relative');
        
        if (contentArea) {
          // 确保内容区域在banner图片上方
          (contentArea as HTMLElement).style.zIndex = '1';
          (contentArea as HTMLElement).style.position = 'relative';
        }
      }
    }
  }, [isLoaded]);

  // 这个组件不渲染任何可见内容
  return null;
}
