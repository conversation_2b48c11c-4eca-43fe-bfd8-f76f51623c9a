import { NextApiRequest, NextApiResponse } from 'next';
import mongoose from 'mongoose';

// 检查MongoDB连接的简单端点
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // 获取环境变量
  const mongoUri = process.env.MONGODB_URI;

  return res.status(200).json({
    environment: process.env.NODE_ENV,
    mongodb: {
      uri: mongoUri ? mongoUri.replace(/:([^:@]+)@/, ':***@') : 'Not set', // 隐藏密码部分
      mongoose_ready: mongoose.connection.readyState,
      mongoose_states: {
        0: 'disconnected',
        1: 'connected',
        2: 'connecting',
        3: 'disconnecting',
        99: 'uninitialized',
      },
    },
    nextauth: {
      url: process.env.NEXTAUTH_URL || 'Not set',
      secret: process.env.NEXTAUTH_SECRET ? '***' : 'Not set', // 不显示实际密钥
    },
    env_vars: Object.keys(process.env).filter(
      key => key.startsWith('MONGODB_') || key.startsWith('NEXTAUTH_')
    ),
  });
}
