// Simple script to show tables in the Neon PostgreSQL database
const { Pool } = require('pg');

async function showTables() {
  const connectionString =
    'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

  const pool = new Pool({
    connectionString,
    ssl: { rejectUnauthorized: false },
  });

  try {
    const client = await pool.connect();
    console.log('Connected to database successfully');

    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);

    console.log('\nTables in database:');
    if (result.rows.length === 0) {
      console.log('No tables found');
    } else {
      result.rows.forEach(row => {
        console.log(`- ${row.table_name}`);
      });
    }

    client.release();
    await pool.end();
  } catch (error) {
    console.error('Error:', error);
  }
}

showTables().catch(console.error);
