/* 管理员登录页面专用样式 - 使用高优先级选择器覆盖全局样式 */

/* 登录容器样式 */
body .admin-login-container {
  min-height: 100vh !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 2rem 1rem !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 9999 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
  box-sizing: border-box !important;
  margin: 0 !important;
  overflow: auto !important;
}

body .admin-login-container .admin-login-card {
  background: white !important;
  border-radius: 1rem !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  padding: 2rem !important;
  width: 100% !important;
  max-width: 400px !important;
  transform: translateY(0) !important;
  transition: all 0.3s ease !important;
  box-sizing: border-box !important;
  font-family: inherit !important;
  display: block !important;
  position: relative !important;
}

.admin-login-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.admin-login-container .admin-login-header {
  text-align: center !important;
  margin-bottom: 2rem !important;
  display: block !important;
}

.admin-login-container .admin-login-logo {
  width: 80px !important;
  height: 80px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 auto 1rem !important;
  color: white !important;
  font-size: 2rem !important;
  box-sizing: border-box !important;
}

.admin-login-container .admin-login-title {
  font-size: 1.875rem !important;
  font-weight: 700 !important;
  color: #1f2937 !important;
  margin-bottom: 0.5rem !important;
  font-family: inherit !important;
  line-height: 1.2 !important;
  margin-top: 0 !important;
}

.admin-login-container .admin-login-subtitle {
  color: #6b7280 !important;
  font-size: 0.875rem !important;
  font-family: inherit !important;
  line-height: 1.4 !important;
  margin: 0 !important;
}

.admin-login-container .form-group {
  margin-bottom: 1.5rem !important;
  display: block !important;
  box-sizing: border-box !important;
}

.admin-login-container .form-label {
  display: block !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  color: #374151 !important;
  margin-bottom: 0.5rem !important;
  font-family: inherit !important;
  line-height: 1.4 !important;
}

.admin-login-container .form-input {
  width: 100% !important;
  padding: 0.75rem 1rem !important;
  border: 1px solid #d1d5db !important;
  border-radius: 0.5rem !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
  background-color: #f9fafb !important;
  box-sizing: border-box !important;
  font-family: inherit !important;
  outline: none !important;
}

.admin-login-container .form-input:focus {
  outline: none !important;
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  background-color: white !important;
}

.admin-login-container .form-input.error {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.admin-login-container .form-error {
  color: #ef4444 !important;
  font-size: 0.75rem !important;
  margin-top: 0.25rem !important;
  font-family: inherit !important;
}

.admin-login-container .btn-primary {
  width: 100% !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border: none !important;
  padding: 0.75rem 1rem !important;
  border-radius: 0.5rem !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
  box-sizing: border-box !important;
  font-family: inherit !important;
  outline: none !important;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.language-toggle {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  padding: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 1.25rem;
  transition: all 0.2s ease;
}

.language-toggle:hover {
  background: white;
  transform: scale(1.1);
}

.admin-login-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  color: #6b7280;
  font-size: 0.75rem;
}

.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}



/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .admin-login-container {
    padding: 1rem;
  }

  .admin-login-card {
    padding: 1.5rem;
  }

  .admin-login-title {
    font-size: 1.5rem;
  }
}

/* 确保图标正确显示 */
.admin-login-container .fas,
.admin-login-container .fa,
.admin-login-container i[class*="fa-"] {
  font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
  font-weight: 900 !important;
  font-style: normal !important;
  display: inline-block !important;
  text-rendering: auto !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

.admin-login-container .fab {
  font-family: "Font Awesome 6 Brands" !important;
  font-weight: 400 !important;
}

.admin-login-container .far {
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 400 !important;
}

/* 特定图标样式 */
.admin-login-container .fa-shield-alt:before {
  content: "\f3ed" !important;
}

.admin-login-container .fa-user:before {
  content: "\f007" !important;
}

.admin-login-container .fa-lock:before {
  content: "\f023" !important;
}

.admin-login-container .fa-sign-in-alt:before {
  content: "\f2f6" !important;
}

.admin-login-container .fa-spinner:before {
  content: "\f110" !important;
}

.admin-login-container .fa-info-circle:before {
  content: "\f05a" !important;
}

.admin-login-container .fa-exclamation-triangle:before {
  content: "\f071" !important;
}

/* 旋转动画 */
.admin-login-container .fa-spin {
  animation: fa-spin 2s infinite linear !important;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 工具类 */
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }

/* 简化的样式重置 */
body .admin-login-container * {
  box-sizing: border-box !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
}
