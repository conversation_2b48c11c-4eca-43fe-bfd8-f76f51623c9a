import { NextResponse } from 'next/server';
import { createProductsTable  } from '@/lib/db-examples';

export async function GET() {
  try {
    const result = await createProductsTable();

    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: 500 });
    }
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error', error: String(error) },
      { status: 500 }
    );
  }
}
