const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function fixSolutionMenuItems() {
  // 连接数据库
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL || process.env.POSTGRES_URI,
  });

  try {
    // 查询所有与解决方案相关的分类
    console.log('查询现有的解决方案相关分类...');
    const result = await pool.query(`
      SELECT id, name, slug, is_featured, featured_type
      FROM categories
      WHERE slug IN ('100-500-sqm', '500-1000-sqm', '1000-plus-sqm')
    `);

    console.log(`找到 ${result.rows.length} 个解决方案相关分类:`);
    console.log(result.rows);

    // 更新这些分类，使其显示在解决方案导航菜单中
    for (const item of result.rows) {
      console.log(`更新分类 ${item.name} (ID: ${item.id})...`);

      await pool.query(
        `
        UPDATE categories
        SET 
          is_featured = true,
          featured_type = 'solution',
          featured_order = CASE 
            WHEN slug = '100-500-sqm' THEN 1
            WHEN slug = '500-1000-sqm' THEN 2
            WHEN slug = '1000-plus-sqm' THEN 3
            ELSE featured_order
          END,
          translations = CASE 
            WHEN slug = '100-500-sqm' THEN '{"zh":{"name":"100-500平方米"},"en":{"name":"100-500 sqm"}}'::jsonb
            WHEN slug = '500-1000-sqm' THEN '{"zh":{"name":"500-1000平方米"},"en":{"name":"500-1000 sqm"}}'::jsonb
            WHEN slug = '1000-plus-sqm' THEN '{"zh":{"name":"1000+平方米"},"en":{"name":"1000+ sqm"}}'::jsonb
            ELSE translations
          END
        WHERE id = $1
      `,
        [item.id]
      );
    }

    // 如果没有找到任何记录，创建新的解决方案菜单项
    if (result.rows.length === 0) {
      console.log('未找到任何解决方案相关分类，创建新的菜单项...');

      // 准备要添加的解决方案菜单项
      const solutionItems = [
        {
          name: '100-500平方米',
          slug: '100-500-sqm',
          description: '小型游乐场解决方案',
          type: 'size_range',
          is_active: true,
          order_num: 1,
          is_featured: true,
          featured_order: 1,
          featured_type: 'solution',
          translations: {
            zh: { name: '100-500平方米' },
            en: { name: '100-500 sqm' },
          },
        },
        {
          name: '500-1000平方米',
          slug: '500-1000-sqm',
          description: '中型游乐场解决方案',
          type: 'size_range',
          is_active: true,
          order_num: 2,
          is_featured: true,
          featured_order: 2,
          featured_type: 'solution',
          translations: {
            zh: { name: '500-1000平方米' },
            en: { name: '500-1000 sqm' },
          },
        },
      ];

      // 添加菜单项
      for (const item of solutionItems) {
        await pool.query(
          `INSERT INTO categories 
          (name, slug, description, type, is_active, order_num, is_featured, featured_order, featured_type, translations) 
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
          [
            item.name,
            item.slug,
            item.description,
            item.type,
            item.is_active,
            item.order_num,
            item.is_featured,
            item.featured_order,
            item.featured_type,
            JSON.stringify(item.translations),
          ]
        );

        console.log(`成功创建菜单项: ${item.name}`);
      }
    }

    console.log('解决方案导航菜单项修复完成');

    // 确认更新后的结果
    const finalCheck = await pool.query(`
      SELECT id, name, slug, is_featured, featured_type, featured_order, translations
      FROM categories
      WHERE is_featured = true AND featured_type = 'solution'
      ORDER BY featured_order
    `);

    console.log('更新后的解决方案导航菜单项:');
    console.log(finalCheck.rows);
  } catch (error) {
    console.error('修复解决方案导航菜单项时出错:', error);
  } finally {
    await pool.end();
  }
}

// 运行函数
fixSolutionMenuItems().catch(console.error);
