const fs = require('fs');
const { createCanvas } = require('canvas');

// 创建一个函数生成纹理图案
function createPatternOverlay() {
  const width = 200;
  const height = 200;
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  // 设置背景为透明
  ctx.clearRect(0, 0, width, height);

  // 绘制微妙的网格纹理
  ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
  ctx.lineWidth = 0.5;

  // 绘制水平线
  for (let y = 0; y <= height; y += 20) {
    ctx.beginPath();
    ctx.moveTo(0, y);
    ctx.lineTo(width, y);
    ctx.stroke();
  }

  // 绘制垂直线
  for (let x = 0; x <= width; x += 20) {
    ctx.beginPath();
    ctx.moveTo(x, 0);
    ctx.lineTo(x, height);
    ctx.stroke();
  }

  // 添加一些随机小点
  ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';
  for (let i = 0; i < 100; i++) {
    const x = Math.random() * width;
    const y = Math.random() * height;
    const size = Math.random() * 2 + 1;

    ctx.beginPath();
    ctx.arc(x, y, size, 0, Math.PI * 2);
    ctx.fill();
  }

  // 将Canvas导出为PNG
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync('./public/images/solutions/pattern-overlay.png', buffer);

  console.log('Pattern overlay created successfully!');
}

// 创建一个函数生成微妙的背景图案
function createSubtlePattern() {
  const width = 100;
  const height = 100;
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  // 设置背景为透明
  ctx.clearRect(0, 0, width, height);

  // 添加微妙的点状图案
  ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';

  for (let i = 0; i < 50; i++) {
    const x = Math.random() * width;
    const y = Math.random() * height;
    const size = Math.random() * 1.5 + 0.5;

    ctx.beginPath();
    ctx.arc(x, y, size, 0, Math.PI * 2);
    ctx.fill();
  }

  // 将Canvas导出为PNG
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync('./public/images/solutions/subtle-pattern.png', buffer);

  console.log('Subtle pattern created successfully!');
}

// 执行生成函数
try {
  createPatternOverlay();
  createSubtlePattern();
} catch (error) {
  console.error('Error creating patterns:', error);
}
