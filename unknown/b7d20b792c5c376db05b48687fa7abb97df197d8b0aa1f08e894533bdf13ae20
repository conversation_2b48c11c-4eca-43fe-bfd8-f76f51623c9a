const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

// 要删除的产品slug列表
const productsToDelete = [
  'interactive-football-system',    // 互动足球系统
  'ar-motion-trampoline',          // AR体感蹦床
  'interactive-ball-smash',        // 互动砸球游戏
  'children-interactive-beach',    // 儿童互动沙滩
  'holographic-stage-system'       // 全息舞台系统
];

// 要删除的图片文件列表
const imagesToDelete = [
  // 互动足球系统 - 7张图片
  'interactive-football-main.jpg',
  'interactive-football-1.jpg',
  'interactive-football-2.jpg',
  'interactive-football-3.jpg',
  'interactive-football-4.jpg',
  'interactive-football-5.jpg',
  'interactive-football-6.jpg',
  
  // AR体感蹦床 - 5张图片
  'ar-trampoline-main.jpg',
  'ar-trampoline-extra-1.jpg',
  'ar-trampoline-extra-2.jpg',
  'ar-trampoline-extra-3.jpg',
  'ar-trampoline-extra-4.jpg',
  
  // 互动砸球游戏 - 5张图片
  'interactive-ball-main.jpg',
  'interactive-ball-extra-1.jpg',
  'interactive-ball-extra-2.jpg',
  'interactive-ball-extra-3.jpg',
  'interactive-ball-extra-4.jpg',
  
  // 儿童互动沙滩 - 7张图片
  'children-beach-main.jpg',
  'children-beach-extra-1.jpg',
  'children-beach-extra-2.jpg',
  'children-beach-extra-3.jpg',
  'children-beach-extra-4.jpg',
  'children-beach-extra-5.jpg',
  'children-beach-extra-6.jpg',
  
  // 全息舞台系统 - 6张图片
  'holographic-stage-main.jpg',
  'holographic-stage-1.jpg',
  'holographic-stage-2.jpg',
  'holographic-stage-3.jpg',
  'holographic-stage-4.jpg',
  'holographic-stage-5.jpg'
];

async function deleteSpecificProducts() {
  console.log('🗑️  开始删除指定产品和图片...');
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    // 1. 首先显示要删除的产品信息
    console.log('\n📋 要删除的产品:');
    for (const slug of productsToDelete) {
      const result = await client.query('SELECT id, name, slug FROM products WHERE slug = $1', [slug]);
      if (result.rows.length > 0) {
        const product = result.rows[0];
        console.log(`   ${product.id}. ${product.name} (${product.slug})`);
      } else {
        console.log(`   ❌ 未找到产品: ${slug}`);
      }
    }
    
    // 2. 删除数据库中的产品
    console.log('\n🗑️  删除数据库产品...');
    let deletedCount = 0;
    
    for (const slug of productsToDelete) {
      try {
        const result = await client.query('DELETE FROM products WHERE slug = $1 RETURNING id, name', [slug]);
        if (result.rows.length > 0) {
          const product = result.rows[0];
          console.log(`   ✅ 已删除: ${product.name} (ID: ${product.id})`);
          deletedCount++;
        } else {
          console.log(`   ⚠️  未找到: ${slug}`);
        }
      } catch (error) {
        console.log(`   ❌ 删除失败: ${slug} - ${error.message}`);
      }
    }
    
    // 3. 删除图片文件
    console.log('\n🗑️  删除图片文件...');
    const imagesDir = 'public/images/products';
    let deletedImages = 0;
    
    for (const imageFile of imagesToDelete) {
      const imagePath = path.join(imagesDir, imageFile);
      try {
        if (fs.existsSync(imagePath)) {
          fs.unlinkSync(imagePath);
          console.log(`   ✅ 已删除图片: ${imageFile}`);
          deletedImages++;
        } else {
          console.log(`   ⚠️  图片不存在: ${imageFile}`);
        }
      } catch (error) {
        console.log(`   ❌ 删除图片失败: ${imageFile} - ${error.message}`);
      }
    }
    
    // 4. 验证删除结果
    console.log('\n📊 删除结果验证...');
    const remainingProducts = await client.query('SELECT COUNT(*) as total FROM products');
    const remainingFeatured = await client.query('SELECT COUNT(*) as total FROM products WHERE is_featured = true');
    
    console.log(`\n📈 删除统计:`);
    console.log(`   🗑️  删除产品数: ${deletedCount}`);
    console.log(`   🗑️  删除图片数: ${deletedImages}`);
    console.log(`   📦 剩余产品数: ${remainingProducts.rows[0].total}`);
    console.log(`   ⭐ 剩余特色产品: ${remainingFeatured.rows[0].total}`);
    
    // 5. 显示剩余产品列表
    const remainingList = await client.query(`
      SELECT id, name, slug, is_featured 
      FROM products 
      ORDER BY id
    `);
    
    console.log(`\n📋 剩余产品列表:`);
    remainingList.rows.forEach((product, index) => {
      const featured = product.is_featured ? '⭐' : '  ';
      console.log(`   ${index + 1}. ${featured} ${product.name} (${product.slug})`);
    });
    
    // 6. 检查剩余图片
    console.log(`\n📷 剩余图片文件:`);
    if (fs.existsSync(imagesDir)) {
      const remainingImages = fs.readdirSync(imagesDir)
        .filter(file => /\.(jpg|jpeg|png|gif)$/i.test(file))
        .sort();
      
      console.log(`   总计: ${remainingImages.length} 张图片`);
      remainingImages.forEach(img => {
        console.log(`   📷 ${img}`);
      });
    }
    
    client.release();
    console.log('\n🎉 删除操作完成!');
    
  } catch (error) {
    console.error('❌ 删除失败:', error.message);
  } finally {
    await pool.end();
  }
}

deleteSpecificProducts();
