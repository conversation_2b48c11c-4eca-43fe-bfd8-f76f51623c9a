import { NextApiRequest, NextApiResponse } from 'next';
import dbConnect from '../../lib/postgresql';
import bcrypt from 'bcryptjs';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests for security
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    console.log('Connecting to database...');
    const pool = await dbConnect();
    console.log('Database connected');

    // 获取一个客户端连接
    const client = await pool.connect();

    try {
      // 检查是否已存在管理员用户
      const adminResult = await client.query('SELECT id FROM users WHERE role = $1 LIMIT 1', [
        'admin',
      ]);

      if (adminResult.rows.length > 0) {
        return res.status(200).json({
          success: false,
          message: 'Admin user already exists',
          userId: adminResult.rows[0].id,
        });
      }

      // 创建默认管理员用户
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('Admin@123', salt);

      const insertResult = await client.query(
        `INSERT INTO users (username, email, password_hash, role) 
         VALUES ($1, $2, $3, $4) RETURNING id`,
        ['admin', '<EMAIL>', hashedPassword, 'admin']
      );

      return res.status(201).json({
        success: true,
        message: 'Default admin user created successfully',
        userId: insertResult.rows[0].id,
      });
    } finally {
      // 确保客户端连接被释放
      client.release();
    }
  } catch (error) {
    console.error('Error creating default admin:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create default admin user',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
