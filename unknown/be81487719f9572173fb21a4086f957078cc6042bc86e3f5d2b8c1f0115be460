/**
 * 测试新的Footer组件
 */
const fs = require('fs');

console.log('🔍 测试新的Footer组件...\n');

// 检查Footer组件文件
const footerPath = 'app/components/Footer.tsx';
if (fs.existsSync(footerPath)) {
  const content = fs.readFileSync(footerPath, 'utf8');
  
  console.log('📄 Footer组件检查:');
  
  // 检查导入
  if (content.includes('framer-motion')) {
    console.log('   ✅ 导入了 framer-motion');
  } else {
    console.log('   ❌ 未导入 framer-motion');
  }
  
  if (content.includes('lucide-react')) {
    console.log('   ✅ 导入了 lucide-react 图标');
  } else {
    console.log('   ❌ 未导入 lucide-react 图标');
  }
  
  // 检查动画
  if (content.includes('motion.div')) {
    console.log('   ✅ 使用了 motion 动画');
  } else {
    console.log('   ❌ 未使用 motion 动画');
  }
  
  // 检查背景动画
  if (content.includes('bg-gradient-to-br')) {
    console.log('   ✅ 使用了渐变背景');
  } else {
    console.log('   ❌ 未使用渐变背景');
  }
  
  // 检查悬停效果
  if (content.includes('whileHover')) {
    console.log('   ✅ 包含悬停动画效果');
  } else {
    console.log('   ❌ 缺少悬停动画效果');
  }
  
  // 检查社交图标
  if (content.includes('Facebook') && content.includes('Twitter')) {
    console.log('   ✅ 包含社交媒体图标');
  } else {
    console.log('   ❌ 缺少社交媒体图标');
  }
  
  // 检查联系信息
  if (content.includes('MapPin') && content.includes('Mail') && content.includes('Phone')) {
    console.log('   ✅ 包含联系信息图标');
  } else {
    console.log('   ❌ 缺少联系信息图标');
  }
  
  // 检查公司描述
  if (content.includes('广州俊盛科技有限公司')) {
    console.log('   ✅ 包含公司描述');
  } else {
    console.log('   ❌ 缺少公司描述');
  }
  
  // 检查版权信息
  if (content.includes('© 2025 Guangzhou Junsheng Technology Co., Ltd.')) {
    console.log('   ✅ 包含版权信息');
  } else {
    console.log('   ❌ 缺少版权信息');
  }
  
  console.log('\n📊 组件统计:');
  const lines = content.split('\n').length;
  console.log(`   总行数: ${lines}`);
  
  const motionElements = (content.match(/motion\./g) || []).length;
  console.log(`   Motion 元素数量: ${motionElements}`);
  
  const animationVariants = (content.match(/Variants/g) || []).length;
  console.log(`   动画变体数量: ${animationVariants}`);
  
} else {
  console.log('❌ Footer组件文件不存在');
}

// 检查依赖
console.log('\n📦 依赖检查:');
const packageJsonPath = 'package.json';
if (fs.existsSync(packageJsonPath)) {
  const packageContent = fs.readFileSync(packageJsonPath, 'utf8');
  const packageJson = JSON.parse(packageContent);
  
  if (packageJson.dependencies['framer-motion']) {
    console.log(`   ✅ framer-motion: ${packageJson.dependencies['framer-motion']}`);
  } else {
    console.log('   ❌ 未安装 framer-motion');
  }
  
  if (packageJson.dependencies['lucide-react']) {
    console.log(`   ✅ lucide-react: ${packageJson.dependencies['lucide-react']}`);
  } else {
    console.log('   ❌ 未安装 lucide-react');
  }
} else {
  console.log('   ❌ package.json 不存在');
}

// 检查logo文件
console.log('\n🖼️  Logo文件检查:');
const logoFiles = [
  '/images/junsheng-logo.svg',
  '/logo.png',
  '/logo.svg'
];

logoFiles.forEach(logoPath => {
  const fullPath = `public${logoPath}`;
  if (fs.existsSync(fullPath)) {
    console.log(`   ✅ ${logoPath} 存在`);
  } else {
    console.log(`   ❌ ${logoPath} 不存在`);
  }
});

console.log('\n🎯 测试建议:');
console.log('1. 打开浏览器访问 http://localhost:3000/zh');
console.log('2. 滚动到页面底部查看新的Footer');
console.log('3. 测试悬停效果和动画');
console.log('4. 检查响应式布局（调整浏览器窗口大小）');
console.log('5. 测试社交媒体图标的悬停效果');

console.log('\n✅ Footer组件测试完成！');
