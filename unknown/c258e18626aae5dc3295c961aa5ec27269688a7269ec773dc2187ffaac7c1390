// 修复中文目录名的脚本
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 目录映射 - 从中文路径到英文路径
const directoryMap = {
  产品: 'products-assets', // 主目录
  '产品/1': 'products-assets/category-1', // 子目录
  '产品/2': 'products-assets/category-2',
  '产品/3': 'products-assets/category-3',
  // 添加更多目录映射
};

// 创建新目录
function createDirectories() {
  console.log('创建英文目录...');

  Object.values(directoryMap).forEach(dir => {
    if (!fs.existsSync(dir)) {
      console.log(`- 创建: ${dir}`);
      fs.mkdirSync(dir, { recursive: true });
    } else {
      console.log(`- 已存在: ${dir}`);
    }
  });
}

// 复制文件到新目录
function copyFiles() {
  console.log('\n复制文件到新目录...');

  // 处理每个映射的目录
  Object.entries(directoryMap).forEach(([srcDir, destDir]) => {
    if (!fs.existsSync(srcDir)) {
      console.log(`源目录不存在: ${srcDir}`);
      return;
    }

    // 获取源目录中的所有文件和子目录
    const items = fs.readdirSync(srcDir, { withFileTypes: true });

    // 处理每个条目
    items.forEach(item => {
      const srcPath = path.join(srcDir, item.name);
      const destPath = path.join(destDir, item.name);

      if (item.isDirectory()) {
        // 检查这个子目录是否在我们的映射中
        if (!Object.keys(directoryMap).includes(srcPath)) {
          // 如果不在映射中，自动创建对应的英文目录
          console.log(`- 创建子目录: ${destPath}`);
          fs.mkdirSync(destPath, { recursive: true });

          // 递归复制子目录内容
          const subItems = fs.readdirSync(srcPath, { withFileTypes: true });
          subItems.forEach(subItem => {
            const subSrcPath = path.join(srcPath, subItem.name);
            const subDestPath = path.join(destPath, subItem.name);

            if (subItem.isDirectory()) {
              console.log(`  - 创建子目录: ${subDestPath}`);
              fs.mkdirSync(subDestPath, { recursive: true });

              // 使用shell命令复制目录内容
              try {
                console.log(`  - 复制目录内容: ${subSrcPath} -> ${subDestPath}`);
                execSync(`cp -R "${subSrcPath}"/* "${subDestPath}"/ || echo "No files to copy"`);
              } catch (error) {
                console.log(`  - 目录为空或复制出错: ${subSrcPath}`);
              }
            } else {
              console.log(`  - 复制文件: ${subItem.name}`);
              fs.copyFileSync(subSrcPath, subDestPath);
            }
          });
        }
      } else {
        // 复制文件
        console.log(`- 复制文件: ${item.name}`);
        fs.copyFileSync(srcPath, destPath);
      }
    });
  });
}

// 验证新目录结构
function verifyDirectories() {
  console.log('\n验证新目录结构...');

  let success = true;

  Object.entries(directoryMap).forEach(([srcDir, destDir]) => {
    if (!fs.existsSync(destDir)) {
      console.log(`❌ 目标目录不存在: ${destDir}`);
      success = false;
      return;
    }

    if (!fs.existsSync(srcDir)) {
      console.log(`⚠️ 源目录不存在: ${srcDir}`);
      return;
    }

    // 比较源目录和目标目录中的文件数量
    const srcFiles = fs.readdirSync(srcDir);
    const destFiles = fs.readdirSync(destDir);

    console.log(`- ${srcDir}: ${srcFiles.length} 个文件`);
    console.log(`- ${destDir}: ${destFiles.length} 个文件`);

    if (srcFiles.length > destFiles.length) {
      console.log(`❌ 文件数量不匹配: ${srcDir} -> ${destDir}`);
      success = false;
    } else {
      console.log(`✓ 文件数量匹配或目标更多: ${srcDir} -> ${destDir}`);
    }
  });

  return success;
}

// 主函数
async function main() {
  try {
    console.log('开始修复中文目录...\n');

    // 步骤1: 创建英文目录
    createDirectories();

    // 步骤2: 复制文件到新目录
    copyFiles();

    // 步骤3: 验证新目录结构
    const verified = verifyDirectories();

    console.log('\n总结:');
    if (verified) {
      console.log('✅ 所有目录已成功转换为英文名称');
      console.log('\n后续步骤:');
      console.log('1. 检查新目录确保所有文件已正确复制');
      console.log('2. 更新代码中的文件路径引用');
      console.log('3. 删除原中文目录 (手动操作更安全)');
    } else {
      console.log('⚠️ 目录转换过程中发现问题，请检查上面的错误信息');
    }
  } catch (error) {
    console.error('出错了:', error);
  }
}

// 执行主函数
main();
