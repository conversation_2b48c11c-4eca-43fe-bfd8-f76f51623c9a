---
description:
globs:
alwaysApply: false
---
# 路由和国际化

本项目支持多语言路由，通过动态路由参数 `[lang]` 实现。

## 路由结构
- `/app/[lang]/` - 多语言内容的根目录
  - `/app/[lang]/home/<USER>
  - `/app/[lang]/products/` - 产品页面
  - `/app/[lang]/collections/` - 产品集合
  - `/app/[lang]/blog/` - 博客内容
  - `/app/[lang]/pages/` - 其他静态页面

## 语言支持
- `/app/en/` - 英文内容
- `/app/zh/` - 中文内容

## 字典配置
- `/app/dictionaries/` - 多语言词汇和翻译资源

## API路由
- `/app/api/` - API路由(App Router)
- `/pages/api/` - API路由(Pages Router)

## 动态路由
- `/products/[slug]/` - 产品详情页
- `/blog/[slug]/` - 博客文章
- `/collections/[slug]/` - 产品集合详情
