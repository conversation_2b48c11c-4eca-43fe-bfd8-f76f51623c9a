/**
 * 创建测试内容数据
 */
const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

// 数据库连接配置
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

const testContent = [
  {
    title: '首页横幅标题',
    slug: 'homepage-banner-title',
    content: '欢迎来到我们的互动娱乐设备展示平台',
    type: 'homepage',
    translations: {
      en: {
        title: 'Homepage Banner Title',
        content: 'Welcome to our Interactive Entertainment Equipment Platform'
      },
      zh: {
        title: '首页横幅标题',
        content: '欢迎来到我们的互动娱乐设备展示平台'
      }
    }
  },
  {
    title: '首页副标题',
    slug: 'homepage-subtitle',
    content: '探索最新的互动技术，体验未来娱乐方式',
    type: 'homepage',
    translations: {
      en: {
        title: 'Homepage Subtitle',
        content: 'Explore the latest interactive technology and experience future entertainment'
      },
      zh: {
        title: '首页副标题',
        content: '探索最新的互动技术，体验未来娱乐方式'
      }
    }
  },
  {
    title: '关于我们',
    slug: 'about-us',
    content: '我们是一家专注于互动娱乐设备研发和销售的公司，致力于为客户提供最优质的产品和服务。',
    type: 'about',
    translations: {
      en: {
        title: 'About Us',
        content: 'We are a company focused on the development and sales of interactive entertainment equipment, committed to providing customers with the highest quality products and services.'
      },
      zh: {
        title: '关于我们',
        content: '我们是一家专注于互动娱乐设备研发和销售的公司，致力于为客户提供最优质的产品和服务。'
      }
    }
  },
  {
    title: '联系方式',
    slug: 'contact-info',
    content: '电话：+86 123-4567-8900\n邮箱：<EMAIL>\n地址：中国上海市浦东新区',
    type: 'contact',
    translations: {
      en: {
        title: 'Contact Information',
        content: 'Phone: +86 123-4567-8900\nEmail: <EMAIL>\nAddress: Pudong New Area, Shanghai, China'
      },
      zh: {
        title: '联系方式',
        content: '电话：+86 123-4567-8900\n邮箱：<EMAIL>\n地址：中国上海市浦东新区'
      }
    }
  },
  {
    title: '服务条款',
    slug: 'terms-of-service',
    content: '本服务条款规定了用户使用我们网站和服务的条件和规则。',
    type: 'legal',
    translations: {
      en: {
        title: 'Terms of Service',
        content: 'These terms of service set out the conditions and rules for users to use our website and services.'
      },
      zh: {
        title: '服务条款',
        content: '本服务条款规定了用户使用我们网站和服务的条件和规则。'
      }
    }
  },
  {
    title: '隐私政策',
    slug: 'privacy-policy',
    content: '我们重视您的隐私，本政策说明了我们如何收集、使用和保护您的个人信息。',
    type: 'legal',
    translations: {
      en: {
        title: 'Privacy Policy',
        content: 'We value your privacy. This policy explains how we collect, use and protect your personal information.'
      },
      zh: {
        title: '隐私政策',
        content: '我们重视您的隐私，本政策说明了我们如何收集、使用和保护您的个人信息。'
      }
    }
  }
];

async function createTestContent() {
  const client = await pool.connect();
  
  try {
    console.log('开始创建测试内容...');
    
    for (const content of testContent) {
      try {
        // 检查内容是否已存在
        const existing = await client.query('SELECT id FROM contents WHERE slug = $1', [content.slug]);
        
        if (existing.rows.length > 0) {
          console.log(`内容 "${content.title}" 已存在，跳过创建`);
          continue;
        }
        
        // 插入内容
        const result = await client.query(
          `INSERT INTO contents (title, slug, content, type, created_at, updated_at)
           VALUES ($1, $2, $3, $4, NOW(), NOW())
           RETURNING id`,
          [content.title, content.slug, content.content, content.type]
        );
        
        const contentId = result.rows[0].id;
        console.log(`✅ 创建内容: ${content.title} (ID: ${contentId})`);
        
        // 插入翻译
        if (content.translations) {
          for (const [locale, translation] of Object.entries(content.translations)) {
            await client.query(
              `INSERT INTO content_translations (content_id, locale, title, content)
               VALUES ($1, $2, $3, $4)`,
              [contentId, locale, translation.title, translation.content]
            );
          }
          console.log(`  - 添加了 ${Object.keys(content.translations).length} 个翻译`);
        }
        
      } catch (error) {
        console.error(`❌ 创建内容 "${content.title}" 失败:`, error.message);
      }
    }
    
    // 检查创建结果
    const count = await client.query('SELECT COUNT(*) as count FROM contents');
    console.log(`\n数据库中现有内容总数: ${count.rows[0].count}`);
    
  } catch (error) {
    console.error('❌ 创建测试内容时出错:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// 执行脚本
if (require.main === module) {
  createTestContent()
    .then(() => {
      console.log('\n🎉 测试内容创建完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { createTestContent };
