/**
 * 从产品文件夹上传产品到数据库
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

// 产品数据映射
const productMapping = {
  '3d电子沙盘': {
    name: '3D电子沙盘',
    slug: '3d-electronic-sandbox',
    description: '先进的3D电子沙盘系统，提供沉浸式的地形展示和交互体验，适用于城市规划、教育展示等多种场景。',
    type: 'interactive_equipment',
    style: '科技,教育',
    features: ['3D地形显示', '实时交互', '多点触控', '数据可视化', '教育展示']
  },
  'AR体感蹦床': {
    name: 'AR体感蹦床',
    slug: 'ar-motion-trampoline',
    description: 'AR增强现实体感蹦床，结合虚拟现实技术与体感运动，为用户提供全新的娱乐健身体验。',
    type: 'interactive_equipment',
    style: '运动,娱乐',
    features: ['AR增强现实', '体感识别', '运动健身', '多人互动', '安全防护']
  },
  'AR教育': {
    name: 'AR教育系统',
    slug: 'ar-education-system',
    description: 'AR增强现实教育系统，通过虚拟与现实的结合，为学生提供生动有趣的学习体验。',
    type: 'interactive_equipment',
    style: '教育,科技',
    features: ['AR技术', '教育内容', '互动学习', '多媒体展示', '个性化教学']
  },
  'ktv': {
    name: 'KTV互动系统',
    slug: 'ktv-interactive-system',
    description: '智能KTV互动娱乐系统，集成点歌、游戏、社交等多种功能，提升KTV娱乐体验。',
    type: 'interactive_equipment',
    style: '娱乐,社交',
    features: ['智能点歌', '互动游戏', '社交功能', '音效优化', '氛围灯光']
  },
  '一体机': {
    name: '互动一体机',
    slug: 'interactive-all-in-one',
    description: '多功能互动一体机，集成触控、显示、计算等功能，适用于教育、商业展示等多种场景。',
    type: 'interactive_equipment',
    style: '商业,教育',
    features: ['多点触控', '高清显示', '一体化设计', '多媒体支持', '易于安装']
  },
  '互动砸球': {
    name: '互动砸球游戏',
    slug: 'interactive-ball-smash',
    description: '互动砸球游戏系统，通过投影和体感技术，让玩家体验刺激有趣的砸球游戏。',
    type: 'interactive_equipment',
    style: '游戏,娱乐',
    features: ['体感识别', '投影互动', '多人游戏', '计分系统', '趣味挑战']
  },
  '互动足球': {
    name: '互动足球系统',
    slug: 'interactive-football-system',
    description: '互动足球娱乐系统，结合投影技术和体感识别，提供真实的足球游戏体验。',
    type: 'interactive_equipment',
    style: '运动,娱乐',
    features: ['足球模拟', '体感控制', '竞技模式', '技能训练', '多人对战']
  },
  '体感攀岩': {
    name: '体感攀岩系统',
    slug: 'motion-sensing-climbing',
    description: '体感攀岩互动系统，通过投影和体感技术，提供安全刺激的攀岩体验。',
    type: 'interactive_equipment',
    style: '运动,冒险',
    features: ['体感识别', '投影互动', '安全保护', '难度调节', '成就系统']
  }
};

// 复制图片到public目录
function copyImages(productFolder, productSlug) {
  const sourceDir = path.join('产品/1', productFolder);
  const targetDir = path.join('public/images/products', productSlug);
  
  // 创建目标目录
  if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
  }
  
  const images = [];
  
  try {
    const files = fs.readdirSync(sourceDir);
    let imageIndex = 1;
    
    for (const file of files) {
      const filePath = path.join(sourceDir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isFile() && /\.(jpg|jpeg|png|gif|webp)$/i.test(file)) {
        const ext = path.extname(file);
        const newFileName = `${productSlug}-${imageIndex}${ext}`;
        const targetPath = path.join(targetDir, newFileName);
        
        // 复制文件
        fs.copyFileSync(filePath, targetPath);
        images.push(`/images/products/${productSlug}/${newFileName}`);
        imageIndex++;
        
        console.log(`  ✅ 复制图片: ${file} -> ${newFileName}`);
      }
    }
  } catch (error) {
    console.log(`  ⚠️  读取图片失败: ${error.message}`);
  }
  
  return images;
}

async function uploadProducts() {
  console.log('🚀 开始上传产品到数据库...');
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    let successCount = 0;
    const productFolders = Object.keys(productMapping);
    
    for (const folder of productFolders) {
      try {
        console.log(`\n📝 处理产品: ${folder}`);
        
        const productData = productMapping[folder];
        
        // 检查产品是否已存在
        const existingCheck = await client.query('SELECT id FROM products WHERE slug = $1', [productData.slug]);
        
        if (existingCheck.rows.length > 0) {
          console.log(`⚠️  产品已存在，跳过: ${productData.slug}`);
          continue;
        }
        
        // 复制图片
        console.log(`  📷 处理图片...`);
        const images = copyImages(folder, productData.slug);
        
        if (images.length === 0) {
          console.log(`  ⚠️  未找到图片文件`);
        }
        
        // 插入产品到数据库
        const result = await client.query(
          `INSERT INTO products 
           (name, slug, description, type, style, features, images, in_stock, is_featured, price) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
           RETURNING id`,
          [
            productData.name,
            productData.slug,
            productData.description,
            productData.type,
            productData.style,
            JSON.stringify(productData.features),
            JSON.stringify(images),
            true,
            false,
            0
          ]
        );
        
        console.log(`  ✅ 产品创建成功 (ID: ${result.rows[0].id})`);
        console.log(`  📷 图片数量: ${images.length} 张`);
        successCount++;
        
      } catch (error) {
        console.log(`  ❌ 产品处理失败: ${error.message}`);
      }
    }
    
    console.log('\n📊 上传结果统计:');
    console.log(`✅ 成功上传: ${successCount} 个产品`);
    console.log(`📁 总计处理: ${productFolders.length} 个产品文件夹`);
    
    // 验证上传结果
    const verifyResult = await client.query('SELECT name, slug, images FROM products ORDER BY id DESC LIMIT 10');
    console.log('\n📋 最新产品列表:');
    verifyResult.rows.forEach(row => {
      console.log(`  - ${row.name} (${row.slug})`);
      const imageCount = row.images ? JSON.parse(row.images).length : 0;
      console.log(`    图片数量: ${imageCount} 张`);
    });
    
    client.release();
    
    console.log('\n🎉 产品上传完成!');
    console.log('\n🌐 现在您可以访问:');
    console.log('- 管理后台: http://localhost:3000/admin/products');
    console.log('- 前端产品页: http://localhost:3000/zh/products');
    
  } catch (error) {
    console.error('❌ 上传过程中发生错误:', error.message);
  } finally {
    await pool.end();
  }
}

uploadProducts();
