/**
 * 测试API产品上传
 */

async function testUpload() {
  console.log('🧪 测试API产品上传...');

  try {
    // 检查fetch是否可用
    if (typeof fetch === 'undefined') {
      console.log('❌ fetch不可用');
      return;
    }

    console.log('✅ fetch可用');

    // 使用内置fetch
    const testProduct = {
      title: '测试产品_' + Date.now(),
      slug: 'test-product-' + Date.now(),
      description: '这是一个API测试产品',
      type: 'interactive_equipment',
      size: '100-500 SQM',
      style: '现代,互动',
      features: ['API测试', '自动上传'],
      images: ['/images/products/placeholder.jpg'],
      isPublished: false
    };

    console.log('📝 测试产品数据:', testProduct);

    const response = await fetch('http://localhost:3001/api/products', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testProduct)
    });

    console.log('📡 API响应状态:', response.status);

    const result = await response.json();
    console.log('📋 API响应结果:', result);

    if (result.success) {
      console.log('✅ 产品创建成功!');
    } else {
      console.log('❌ 产品创建失败:', result.message);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testUpload();
