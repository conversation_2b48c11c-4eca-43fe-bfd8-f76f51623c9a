// 脚本用于添加解决方案导航菜单项
const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function addSolutionMenuItems() {
  // 连接数据库
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL || process.env.POSTGRES_URI,
  });

  try {
    // 确定是否已存在解决方案菜单项
    const existingCheck = await pool.query(
      "SELECT * FROM categories WHERE is_featured = true AND featured_type = 'solution'"
    );

    const existingCount = existingCheck.rows.length;
    console.log(`当前有 ${existingCount} 个解决方案导航菜单项`);

    // 如果已经有足够的菜单项，则不添加
    if (existingCount >= 2) {
      console.log('已有足够的解决方案导航菜单项，无需添加');
      return;
    }

    // 准备要添加的解决方案菜单项
    const solutionItems = [
      {
        name: '100-500平方米',
        name_en: '100-500 sqm',
        slug: '100-500-sqm',
        description: '小型游乐场解决方案',
        type: 'size_range',
        is_active: true,
        order_num: 1,
        is_featured: true,
        featured_order: 1,
        featured_type: 'solution',
        translations: {
          zh: { name: '100-500平方米' },
          en: { name: '100-500 sqm' },
        },
      },
      {
        name: '500-1000平方米',
        name_en: '500-1000 sqm',
        slug: '500-1000-sqm',
        description: '中型游乐场解决方案',
        type: 'size_range',
        is_active: true,
        order_num: 2,
        is_featured: true,
        featured_order: 2,
        featured_type: 'solution',
        translations: {
          zh: { name: '500-1000平方米' },
          en: { name: '500-1000 sqm' },
        },
      },
      {
        name: '1000+平方米',
        name_en: '1000+ sqm',
        slug: '1000-plus-sqm',
        description: '大型游乐场解决方案',
        type: 'size_range',
        is_active: true,
        order_num: 3,
        is_featured: true,
        featured_order: 3,
        featured_type: 'solution',
        translations: {
          zh: { name: '1000+平方米' },
          en: { name: '1000+ sqm' },
        },
      },
    ];

    // 选择需要添加的菜单项数量
    const itemsToAdd = solutionItems.slice(0, Math.min(3, 3 - existingCount));

    // 添加菜单项
    for (const item of itemsToAdd) {
      // 检查是否已存在相同slug的分类
      const slugCheck = await pool.query('SELECT id FROM categories WHERE slug = $1', [item.slug]);

      if (slugCheck.rows.length > 0) {
        console.log(`分类 ${item.slug} 已存在，跳过`);
        continue;
      }

      // 插入新菜单项
      await pool.query(
        `INSERT INTO categories 
        (name, name_en, slug, description, type, is_active, order_num, is_featured, featured_order, featured_type, translations) 
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
        [
          item.name,
          item.name_en,
          item.slug,
          item.description,
          item.type,
          item.is_active,
          item.order_num,
          item.is_featured,
          item.featured_order,
          item.featured_type,
          JSON.stringify(item.translations),
        ]
      );

      console.log(`成功添加菜单项: ${item.name}`);
    }

    console.log('解决方案导航菜单项添加完成');
  } catch (error) {
    console.error('添加解决方案导航菜单项时出错:', error);
  } finally {
    await pool.end();
  }
}

// 运行函数
addSolutionMenuItems().catch(console.error);
