/**
 * 快速单产品上传工具
 * 通过命令行参数快速上传单个产品
 */

const { Pool } = require('pg');
require('dotenv').config();

// 数据库连接配置
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 
    'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

/**
 * 生成slug
 */
function generateSlug(name) {
  return name
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/--+/g, '-')
    .trim();
}

/**
 * 检查产品是否已存在
 */
async function checkProductExists(slug) {
  try {
    const result = await pool.query('SELECT id FROM products WHERE slug = $1', [slug]);
    return result.rows.length > 0;
  } catch (error) {
    console.error('检查产品时出错:', error.message);
    return false;
  }
}

/**
 * 创建产品
 */
async function createProduct(productData) {
  const client = await pool.connect();
  try {
    const result = await client.query(
      `INSERT INTO products
       (name, slug, description, size, style, type, features, images, in_stock, is_featured, price)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
       RETURNING id`,
      [
        productData.name,
        productData.slug,
        productData.description,
        productData.size || '100-500 SQM',
        productData.style || '现代,互动',
        productData.type || 'interactive_equipment',
        JSON.stringify(productData.features || []),
        JSON.stringify(productData.images || []),
        productData.isPublished || false,
        productData.isFeatured || false,
        productData.price || 0
      ]
    );

    return result.rows[0].id;
  } finally {
    client.release();
  }
}

/**
 * 显示使用说明
 */
function showUsage() {
  console.log('🚀 快速产品上传工具');
  console.log('==================');
  console.log('');
  console.log('用法:');
  console.log('  node single-upload-product.js --name "产品名称" --description "产品描述" [选项]');
  console.log('');
  console.log('必需参数:');
  console.log('  --name         产品名称');
  console.log('  --description  产品描述');
  console.log('');
  console.log('可选参数:');
  console.log('  --slug         产品slug (默认从名称生成)');
  console.log('  --type         产品类型 (默认: interactive_equipment)');
  console.log('  --size         产品尺寸 (默认: 100-500 SQM)');
  console.log('  --style        产品风格 (默认: 现代,互动)');
  console.log('  --features     产品特性 (用逗号分隔)');
  console.log('  --images       图片URL (用逗号分隔)');
  console.log('  --price        产品价格 (默认: 0)');
  console.log('  --published    是否发布 (true/false, 默认: false)');
  console.log('');
  console.log('示例:');
  console.log('  node single-upload-product.js \\');
  console.log('    --name "全息投影展示台" \\');
  console.log('    --description "先进的全息投影技术，创造令人惊叹的3D视觉效果" \\');
  console.log('    --type "holographic_solutions" \\');
  console.log('    --features "3D全息显示,高清投影,交互控制" \\');
  console.log('    --published true');
}

/**
 * 解析命令行参数
 */
function parseArgs() {
  const args = process.argv.slice(2);
  const params = {};
  
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i];
    const value = args[i + 1];
    
    if (!key || !key.startsWith('--') || !value) {
      continue;
    }
    
    const paramName = key.substring(2);
    params[paramName] = value;
  }
  
  return params;
}

/**
 * 主函数
 */
async function main() {
  try {
    const params = parseArgs();
    
    // 检查必需参数
    if (!params.name || !params.description) {
      showUsage();
      process.exit(1);
    }

    // 测试数据库连接
    await pool.query('SELECT 1');
    console.log('✅ 数据库连接成功');

    // 准备产品数据
    const slug = params.slug || generateSlug(params.name);
    
    // 检查slug是否已存在
    if (await checkProductExists(slug)) {
      console.log(`❌ 产品slug "${slug}" 已存在`);
      process.exit(1);
    }

    const productData = {
      name: params.name,
      slug: slug,
      description: params.description,
      type: params.type || 'interactive_equipment',
      size: params.size || '100-500 SQM',
      style: params.style || '现代,互动',
      features: params.features ? params.features.split(',').map(f => f.trim()) : [],
      images: params.images ? params.images.split(',').map(img => img.trim()) : [],
      price: parseFloat(params.price) || 0,
      isPublished: params.published === 'true'
    };

    // 显示产品信息
    console.log('\n📋 产品信息:');
    console.log(`名称: ${productData.name}`);
    console.log(`Slug: ${productData.slug}`);
    console.log(`描述: ${productData.description}`);
    console.log(`类型: ${productData.type}`);
    console.log(`尺寸: ${productData.size}`);
    console.log(`风格: ${productData.style}`);
    console.log(`特性: ${productData.features.join(', ')}`);
    console.log(`图片: ${productData.images.length} 张`);
    console.log(`价格: ${productData.price}`);
    console.log(`发布状态: ${productData.isPublished ? '已发布' : '草稿'}`);

    // 创建产品
    const productId = await createProduct(productData);
    console.log(`\n✅ 产品创建成功! ID: ${productId}`);

  } catch (error) {
    console.error('❌ 错误:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// 运行主函数
main();
