/**
 * 专门修复导入路径问题
 */
const fs = require('fs');
const path = require('path');

// 日志函数
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m', // 青色
    success: '\x1b[32m', // 绿色
    warning: '\x1b[33m', // 黄色
    error: '\x1b[31m', // 红色
  };

  console.log(`${colors[type]}[${type.toUpperCase()}]\x1b[0m ${message}`);
}

// 手动修复特定文件
function fixFile(filePath, replacements) {
  if (!fs.existsSync(filePath)) {
    log(`文件不存在: ${filePath}`, 'error');
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;

  for (const { from, to } of replacements) {
    if (content.includes(from)) {
      content = content.replace(from, to);
      changed = true;
    }
  }

  if (changed) {
    fs.writeFileSync(filePath, content, 'utf8');
    log(`修复了文件: ${filePath}`, 'success');
    return true;
  }

  log(`无需修改: ${filePath}`, 'info');
  return false;
}

// 主函数
async function main() {
  log('开始修复特定导入路径问题...', 'info');
  
  const rootDir = process.cwd();
  const fixedFiles = [];

  // 修复categories/featured/route.ts
  const featuredFile = path.join(rootDir, 'app/api/categories/featured/route.ts');
  const featuredResult = fixFile(featuredFile, [
    { 
      from: "import { query } from '../../../../../lib/db';", 
      from: "import { query } from '../../../../../lib/db';", 
      to: "import { query } from '../../../../../lib/db.js';" 
    }
  ]);
  if (featuredResult) fixedFiles.push('app/api/categories/featured/route.ts');

  // 修复categories/[id]/route.ts
  const idFile = path.join(rootDir, 'app/api/categories/[id]/route.ts');
  const idResult = fixFile(idFile, [
    { 
      from: "import { query } from '../../../../../lib/db';", 
      to: "import { query } from '../../../../../lib/db.js';" 
    },
    {
      from: "import { authOptions } from '../../../auth/[...nextauth]/route';",
      to: "import { authOptions } from '../../../auth/[...nextauth]/route.js';"
    }
  ]);
  if (idResult) fixedFiles.push('app/api/categories/[id]/route.ts');

  // 修复upload/route.ts
  const uploadFile = path.join(rootDir, 'app/api/upload/route.ts');
  const uploadResult = fixFile(uploadFile, [
    {
      from: "import { authOptions } from '../../auth/[...nextauth]/route';",
      to: "import { authOptions } from '../../auth/[...nextauth]/route.js';"
    }
  ]);
  if (uploadResult) fixedFiles.push('app/api/upload/route.ts');

  log(`修复完成。修复了 ${fixedFiles.length} 个文件: ${fixedFiles.join(', ')}`, 'success');
  log(`请重新启动开发服务器以应用更改。`, 'info');
}

// 执行主函数
main().catch(error => {
  log(`发生错误: ${error.message}`, 'error');
  process.exit(1);
}); 