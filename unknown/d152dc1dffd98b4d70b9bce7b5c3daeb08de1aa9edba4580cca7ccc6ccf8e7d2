// 修复字符串引号问题的简单脚本
const fs = require('fs');
const path = require('path');

// 要搜索的目录
const adminDir = './app/[lang]/admin';

// 递归处理目录
function processDirectory(dirPath) {
  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const item of items) {
      const itemPath = path.join(dirPath, item.name);

      if (item.isDirectory()) {
        processDirectory(itemPath);
      } else if (item.isFile() && (item.name.endsWith('.tsx') || item.name.endsWith('.jsx'))) {
        fixFile(itemPath);
      }
    }
  } catch (error) {
    console.error(`处理目录时出错: ${dirPath}`, error);
  }
}

// 修复单个文件
function fixFile(filePath) {
  try {
    console.log(`检查文件: ${filePath}`);
    let content = fs.readFileSync(filePath, 'utf8');
    let original = content;
    
    // 修复路由引号问题
    // 1. 修复单引号字符串缺少结尾引号的情况
    content = content.replace(/router\.push\('\/\[lang\]\/admin\/[^']*(?=\);)/g, match => {
      console.log(`  发现缺少单引号结束的路径: ${match}`);
      return `${match}'`;
    });
    
    // 2. 修复双引号字符串缺少结尾引号的情况
    content = content.replace(/router\.push\("\/\[lang\]\/admin\/[^"]*(?=\);)/g, match => {
      console.log(`  发现缺少双引号结束的路径: ${match}`);
      return `${match}"`;
    });
    
    // 3. 修复多余的引号
    content = content.replace(/router\.push\(['"](\/\[lang\]\/admin\/[^'"]*)['"]'\)/g, (_, path) => {
      console.log(`  发现多余的引号: ${_}`);
      return `router.push('${path}')`;
    });
    
    // 4. 修复onClick中缺少引号的情况
    content = content.replace(/onClick\s*=\s*{\s*\(\s*\)\s*=>\s*router\.push\(['"](\/\[lang\]\/admin\/[^'"]*)\)}/g, (_, path) => {
      console.log(`  发现onClick中缺少引号的路径: ${_}`);
      return `onClick={() => router.push('${path}')}`;
    });
    
    // 判断是否有修改
    if (content !== original) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
    } else {
      console.log(`  无需修改: ${filePath}`);
    }
  } catch (error) {
    console.error(`处理文件时出错: ${filePath}`, error);
  }
}

// 执行主函数
console.log('开始修复引号问题...');
processDirectory(adminDir);
console.log('完成！'); 