/* 购买指南页面样式 */
.purchase-steps {
  padding: 60px 0;
  background-color: #f8f9fd;
}

.purchase-steps .section-title {
  text-align: center;
  margin-bottom: 40px;
  color: #0a59f7;
  font-size: 32px;
}

.steps-container {
  margin-top: 40px;
}

.step-item {
  margin-bottom: 60px;
  background-color: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.step-number {
  background-color: #0a59f7;
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  margin-right: 20px;
  flex-shrink: 0;
}

.step-header h3 {
  font-size: 24px;
  color: #333;
  margin: 0;
}

.step-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  align-items: center;
}

.step-image {
  width: 100%;
  height: 400px;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background-color: #0a59f7;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.step-text {
  padding-right: 20px;
}

.step-list {
  margin-bottom: 20px;
  padding-left: 20px;
  list-style-type: none;
}

.step-list li {
  margin-bottom: 10px;
  position: relative;
  line-height: 1.5;
}

.step-list li:before {
  content: "•";
  color: #0a59f7;
  position: absolute;
  left: -15px;
}

/* 响应式样式 */
@media (max-width: 992px) {
  .step-content {
    grid-template-columns: 1fr;
  }

  .step-image {
    order: -1;
    margin-bottom: 20px;
  }

  .step-text {
    padding-right: 0;
  }
}

@media (max-width: 768px) {
  .step-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .step-number {
    margin-bottom: 15px;
  }

  .step-image {
    height: 300px;
  }
}
