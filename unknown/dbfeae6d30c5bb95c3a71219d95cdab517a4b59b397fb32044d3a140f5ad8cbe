/**
 * 初始化管理员用户脚本
 */
const path = require('path');

// 修复路径别名导入问题
process.env.NODE_PATH = path.resolve(__dirname, '..');
require('module').Module._initPaths();

// 直接调用 API 端点来初始化
const fetch = require('node-fetch');

async function initAdmin() {
  const baseUrl = 'http://localhost:3000';

  try {
    console.log('🚀 正在初始化管理员后台...\n');

    // 1. 初始化数据库表
    console.log('📊 正在创建用户表...');
    try {
      const setupResponse = await fetch(`${baseUrl}/api/admin/setup`);
      const setupResult = await setupResponse.json();

      if (setupResult.success) {
        console.log('✅ 用户表创建成功');
      } else {
        console.log('⚠️  用户表可能已存在:', setupResult.message);
      }
    } catch (error) {
      console.log('⚠️  无法连接到服务器，请确保应用正在运行 (npm run dev)');
      console.log('   或者手动访问: http://localhost:3000/api/admin/setup');
    }

    console.log('\n📝 手动创建管理员账户:');
    console.log('1. 启动开发服务器: npm run dev');
    console.log('2. 访问: http://localhost:3000/api/admin/setup');
    console.log('3. 使用以下 API 创建管理员账户:');
    console.log('   POST http://localhost:3000/api/admin/users');
    console.log('   Body: {');
    console.log('     "username": "admin",');
    console.log('     "email": "<EMAIL>",');
    console.log('     "password": "admin123",');
    console.log('     "role": "super_admin"');
    console.log('   }');

    console.log('\n🔑 默认登录信息:');
    console.log('   用户名: admin');
    console.log('   邮箱: <EMAIL>');
    console.log('   密码: admin123');
    console.log('   登录地址: http://localhost:3000/zh/admin/login');

  } catch (error) {
    console.error('❌ 初始化过程中发生错误:', error);
  }
}

// 执行初始化
initAdmin().then(() => {
  console.log('初始化脚本执行完成');
});