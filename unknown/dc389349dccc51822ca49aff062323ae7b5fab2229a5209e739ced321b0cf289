require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

// 获取数据库连接参数
const connectionString =
  process.env.DATABASE_URL ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 创建数据库连接池
const pool = new Pool({ connectionString });

// 要添加的分类列表
const categories = [
  {
    name: '室内游乐场',
    slug: 'indoor-playground',
    description: '专为室内场所设计的综合游乐设施',
    type: 'product_type',
    is_active: true,
    is_featured: true,
    featured_type: 'product',
    order_num: 1,
  },
  {
    name: '蹦床公园',
    slug: 'trampoline-park',
    description: '专业蹦床娱乐设施',
    type: 'product_type',
    is_active: true,
    is_featured: true,
    featured_type: 'product',
    order_num: 2,
  },
  {
    name: '100-500平方米',
    slug: '100-500-sqm',
    description: '适合小型场所的游乐设施',
    type: 'size_range',
    is_active: true,
    is_featured: false,
    featured_type: 'product',
    order_num: 3,
  },
  {
    name: '500-1000平方米',
    slug: '500-1000-sqm',
    description: '适合中型场所的游乐设施',
    type: 'size_range',
    is_active: true,
    is_featured: false,
    featured_type: 'product',
    order_num: 4,
  },
  {
    name: '1000平方米以上',
    slug: '1000-plus-sqm',
    description: '适合大型场所的游乐设施',
    type: 'size_range',
    is_active: true,
    is_featured: false,
    featured_type: 'product',
    order_num: 5,
  },
];

async function addCategories() {
  try {
    console.log('开始添加分类...');

    for (const category of categories) {
      // 检查分类是否已存在
      const checkResult = await pool.query('SELECT * FROM categories WHERE slug = $1', [
        category.slug,
      ]);

      if (checkResult.rows.length > 0) {
        console.log(`分类 "${category.name}" (${category.slug}) 已存在，跳过`);
        continue;
      }

      // 插入分类
      const result = await pool.query(
        `
        INSERT INTO categories 
        (name, slug, description, type, is_active, is_featured, featured_type, order_num, created_at, updated_at)
        VALUES 
        ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id
      `,
        [
          category.name,
          category.slug,
          category.description,
          category.type,
          category.is_active,
          category.is_featured,
          category.featured_type,
          category.order_num,
        ]
      );

      console.log(`✅ 成功添加分类 "${category.name}"，ID: ${result.rows[0].id}`);
    }

    console.log('\n所有分类添加完成！');
  } catch (error) {
    console.error('添加分类失败:', error);
  } finally {
    await pool.end();
  }
}

addCategories();
