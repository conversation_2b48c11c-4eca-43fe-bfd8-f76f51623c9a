/**
 * 快速开始产品上传测试
 * 自动创建一个测试产品来验证上传功能
 */

const readline = require('readline');

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * 提示用户输入
 */
function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

/**
 * 通过API创建产品
 */
async function createProductViaAPI(productData) {
  try {
    // 使用node-fetch或者内置fetch
    let fetch;
    try {
      fetch = (await import('node-fetch')).default;
    } catch {
      // 如果node-fetch不可用，尝试使用Node.js 18+的内置fetch
      fetch = globalThis.fetch;
    }

    const response = await fetch('http://localhost:3000/api/admin/products', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(productData)
    });

    const result = await response.json();
    return result;
  } catch (error) {
    throw new Error(`API请求失败: ${error.message}`);
  }
}

/**
 * 检查Next.js应用是否运行
 */
async function checkNextJSApp() {
  try {
    let fetch;
    try {
      fetch = (await import('node-fetch')).default;
    } catch {
      fetch = globalThis.fetch;
    }

    const response = await fetch('http://localhost:3000/api/admin/products', {
      method: 'GET',
    });
    
    return response.status !== 404;
  } catch (error) {
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 产品上传快速开始测试');
  console.log('========================');
  console.log('这个工具将帮助您快速测试产品上传功能\n');

  try {
    // 检查Next.js应用是否运行
    console.log('🔍 检查Next.js应用状态...');
    const isAppRunning = await checkNextJSApp();
    
    if (!isAppRunning) {
      console.log('❌ Next.js应用未运行或API不可访问');
      console.log('请先运行以下命令启动应用:');
      console.log('  npm run dev');
      console.log('\n然后重新运行此脚本');
      process.exit(1);
    }
    
    console.log('✅ Next.js应用运行正常\n');

    // 询问用户是否继续
    const continueChoice = await question('是否继续创建测试产品? (y/N): ');
    if (continueChoice.toLowerCase() !== 'y') {
      console.log('操作已取消');
      process.exit(0);
    }

    // 创建测试产品数据
    const timestamp = Date.now();
    const testProduct = {
      name: `测试产品_${timestamp}`,
      slug: `test-product-${timestamp}`,
      description: '这是一个通过快速开始工具创建的测试产品，用于验证上传功能是否正常工作。',
      type: 'interactive_equipment',
      size: '100-500 SQM',
      style: '现代,互动',
      features: [
        '快速测试功能',
        '自动化上传',
        'API集成验证',
        '数据库连接测试'
      ],
      images: [
        '/images/products/test-placeholder.jpg'
      ],
      isPublished: false,
      isFeatured: false,
      price: 0
    };

    console.log('📝 测试产品信息:');
    console.log(`  名称: ${testProduct.name}`);
    console.log(`  Slug: ${testProduct.slug}`);
    console.log(`  描述: ${testProduct.description}`);
    console.log(`  类型: ${testProduct.type}`);
    console.log(`  特性: ${testProduct.features.join(', ')}`);
    console.log(`  发布状态: ${testProduct.isPublished ? '已发布' : '草稿'}\n`);

    // 创建产品
    console.log('🚀 正在创建测试产品...');
    const result = await createProductViaAPI(testProduct);
    
    if (result.success) {
      console.log(`✅ 测试产品创建成功!`);
      console.log(`   产品ID: ${result.productId}`);
      console.log(`   产品Slug: ${testProduct.slug}`);
      console.log('\n🎉 产品上传功能测试通过!');
      console.log('\n📋 接下来您可以:');
      console.log('1. 使用 api-upload-product.js 进行交互式单产品上传');
      console.log('2. 使用 api-batch-upload.js 进行批量产品上传');
      console.log('3. 查看 product-upload-guide.md 了解详细使用说明');
    } else {
      console.log(`❌ 测试产品创建失败: ${result.message}`);
      console.log('\n🔧 可能的解决方案:');
      console.log('1. 检查数据库连接是否正常');
      console.log('2. 检查API端点是否正确配置');
      console.log('3. 查看服务器日志获取更多错误信息');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.log('\n🔧 可能的解决方案:');
    console.log('1. 确保Next.js应用正在运行 (npm run dev)');
    console.log('2. 检查网络连接');
    console.log('3. 检查API端点配置');
  } finally {
    rl.close();
  }
}

// 运行主函数
main();
