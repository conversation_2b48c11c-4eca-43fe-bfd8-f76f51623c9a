'use client';

import React, { ReactNode, useEffect, useState } from 'react';

interface HydrationErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

// 这个组件确保客户端挂载后才渲染内容，彻底避免水合错误
export default function HydrationErrorBoundary({
  children,
  fallback = <div style={{ minHeight: '50px' }}></div>,
}: HydrationErrorBoundaryProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // 在服务器端和客户端初始挂载前渲染fallback
  if (!mounted) {
    return <>{fallback}</>;
  }

  // 客户端挂载后渲染实际内容
  return <>{children}</>;
}
