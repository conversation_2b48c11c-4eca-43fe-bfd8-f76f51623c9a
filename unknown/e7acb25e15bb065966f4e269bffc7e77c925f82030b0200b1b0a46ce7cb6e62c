import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { query } from '../../../lib/db';
import { memoryCache } from '../../../lib/cache';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  const { id } = req.query;

  if (!id || Array.isArray(id)) {
    return res.status(400).json({ success: false, message: 'Invalid product ID' });
  }

  // 处理不同的HTTP方法
  switch (req.method) {
    case 'GET':
      try {
        // 尝试从缓存获取产品
        const cacheKey = `product-id-${id}`;
        const cachedProduct = memoryCache.get(cacheKey);

        if (cachedProduct) {
          console.log(`[API] Serving cached product for ID: ${id}`);
          return res.status(200).json(cachedProduct);
        }

        console.log(`[API] Fetching product from database for ID: ${id}`);
        // 查询产品详情
        const result = await query('SELECT * FROM products WHERE id = $1', [id]);

        if (result.rows.length === 0) {
          return res.status(404).json({ success: false, message: 'Product not found' });
        }

        const product = result.rows[0];

        // 处理可能为字符串格式的JSON字段
        let productCategories = [];
        if (product.categories) {
          try {
            if (typeof product.categories === 'string') {
              productCategories = JSON.parse(product.categories);
            } else {
              productCategories = product.categories;
            }
          } catch (e: unknown) {
            console.error('Error parsing categories:', e);
            // 如果解析失败，保持空数组
          }
        }

        let productFeatures = [];
        if (product.features) {
          try {
            if (typeof product.features === 'string') {
              productFeatures = JSON.parse(product.features);
            } else {
              productFeatures = product.features;
            }
          } catch (e: unknown) {
            console.error('Error parsing features:', e);
            // 如果解析失败，保持空数组
          }
        }

        // 转换为前端格式，注意处理可能不存在的字段
        const formattedProduct = {
          _id: product.id.toString(),
          title: product.name,
          slug: product.slug,
          description: product.description,
          // 使用显式的null检查，并提供默认空字符串
          type: product.type || '',
          size: product.size || '',
          style: product.style || '',
          images: product.image_url ? [product.image_url] : [],
          // 使用解析后的features
          features: productFeatures,
          // 确保category字段正确映射到categories数组
          categories: productCategories,
          isPublished: product.in_stock || false,
          createdAt: product.created_at,
          updatedAt: product.updated_at,
        };

        const responseData = {
          success: true,
          data: formattedProduct,
        };

        // 将结果存入缓存
        memoryCache.set(cacheKey, responseData);

        return res.status(200).json(responseData);
      } catch (error: unknown) {
        console.error('Error fetching product:', error);
        const message = error instanceof Error ? error.message : 'Server error';
        return res.status(500).json({ success: false, message: message });
      }

    case 'PUT':
      try {
        // 检查用户是否有权限更新产品
        if (!session || (session.user.role !== 'admin' && session.user.role !== 'editor')) {
          return res.status(403).json({ success: false, message: 'Forbidden' });
        }

        const {
          title,
          slug,
          description,
          // 确保可能不存在的字段采用空字符串作为默认值
          type = '',
          size = '',
          style = '',
          features = [],
          categories = [],
          images = [],
          isPublished,
        } = req.body;

        console.log('接收到的分类数据:', categories);

        // 检查产品是否存在
        const checkResult = await query('SELECT id FROM products WHERE id = $1', [id]);

        if (checkResult.rows.length === 0) {
          return res.status(404).json({ success: false, message: 'Product not found' });
        }

        // 更新产品 - 只更新表中实际存在的字段
        const result = await query(
          `UPDATE products SET 
           name = $1, 
           slug = $2, 
           description = $3, 
           categories = $4, 
           image_url = $5, 
           in_stock = $6, 
           type = $7,
           size = $8,
           style = $9,
           features = $10,
           updated_at = CURRENT_TIMESTAMP
           WHERE id = $11 
           RETURNING *`,
          [
            title,
            slug,
            description,
            JSON.stringify(categories), // 保存完整的分类数组
            images.length > 0 ? images[0] : null, // 使用第一张图片作为主图
            isPublished,
            type,
            size,
            style,
            JSON.stringify(features),
            id,
          ]
        );

        const updatedProduct = result.rows[0];

        // 转换为前端格式
        const formattedProduct = {
          _id: updatedProduct.id.toString(),
          title: updatedProduct.name,
          slug: updatedProduct.slug,
          description: updatedProduct.description,
          type: updatedProduct.type || '',
          size: updatedProduct.size || '',
          style: updatedProduct.style || '',
          images: updatedProduct.image_url ? [updatedProduct.image_url] : [],
          features: updatedProduct.features || [],
          categories: updatedProduct.categories || [],
          isPublished: updatedProduct.in_stock || false,
          createdAt: updatedProduct.created_at,
          updatedAt: updatedProduct.updated_at,
        };

        // 清除该产品的相关缓存
        memoryCache.delete(`product-id-${id}`);
        memoryCache.delete(`product-slug-${slug}`);
        // 清除产品列表缓存，因为产品数据已更改
        const productListCacheKeys = memoryCache.keys().filter(key => key.startsWith('products-'));
        productListCacheKeys.forEach(key => memoryCache.delete(key));

        return res.status(200).json({
          success: true,
          data: formattedProduct,
        });
      } catch (error: unknown) {
        console.error('Error updating product:', error);
        const message = error instanceof Error ? error.message : 'Server error';
        return res.status(500).json({ success: false, message: message });
      }

    case 'DELETE':
      try {
        // 检查用户是否有权限删除产品
        if (!session || session.user.role !== 'admin') {
          return res.status(403).json({ success: false, message: 'Forbidden' });
        }

        // 首先获取产品信息，用于后续清除缓存
        const productInfo = await query('SELECT slug FROM products WHERE id = $1', [id]);
        const productSlug = productInfo.rows.length > 0 ? productInfo.rows[0].slug : null;

        // 删除产品
        const result = await query('DELETE FROM products WHERE id = $1 RETURNING id', [id]);

        if (result.rows.length === 0) {
          return res.status(404).json({ success: false, message: 'Product not found' });
        }

        // 清除该产品的相关缓存
        memoryCache.delete(`product-id-${id}`);
        if (productSlug) {
          memoryCache.delete(`product-slug-${productSlug}`);
        }
        // 清除产品列表缓存，因为产品列表已更改
        const productListCacheKeys = memoryCache.keys().filter(key => key.startsWith('products-'));
        productListCacheKeys.forEach(key => memoryCache.delete(key));

        return res.status(200).json({
          success: true,
          message: 'Product deleted successfully',
        });
      } catch (error: unknown) {
        console.error('Error deleting product:', error);
        const message = error instanceof Error ? error.message : 'Server error';
        return res.status(500).json({ success: false, message: message });
      }

    default:
      res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
      return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }
}
