require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

// 获取数据库连接参数
const connectionString =
  process.env.DATABASE_URL ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 创建数据库连接池
const pool = new Pool({ connectionString });

async function checkProductSchema() {
  try {
    console.log('==== 检查产品表结构 ====');

    // 查询产品表的列定义
    const result = await pool.query(`
      SELECT column_name, data_type, character_maximum_length, 
             column_default, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'products'
      ORDER BY ordinal_position
    `);

    if (result.rows.length === 0) {
      console.log('⚠️ 产品表不存在');
    } else {
      console.log('✅ 产品表存在，列定义如下:');
      console.log('-----------------------------------------');
      console.log('列名 | 数据类型 | 最大长度 | 默认值 | 可空');
      console.log('-----------------------------------------');
      result.rows.forEach(column => {
        console.log(
          `${column.column_name} | ${column.data_type} | ${column.character_maximum_length || 'N/A'} | ${column.column_default || 'N/A'} | ${column.is_nullable}`
        );
      });
    }
  } catch (error) {
    console.error('查询表结构失败:', error);
  } finally {
    await pool.end();
  }
}

checkProductSchema();
