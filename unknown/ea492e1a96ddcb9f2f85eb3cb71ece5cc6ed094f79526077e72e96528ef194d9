/**
 * 直接数据库产品上传
 */

const { Pool } = require('pg');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

// 示例产品数据
const products = [
  {
    name: '全息餐厅投影系统',
    slug: 'holographic-restaurant-system',
    description: '为餐厅打造独特的全息投影用餐环境，提升顾客用餐体验，创造难忘的视觉盛宴。',
    type: 'holographic_solutions',
    size: '500-1000 SQM',
    style: '奢华,科技',
    features: ['360度全息投影', '主题场景切换', '音效同步', '氛围营造', '定制化内容'],
    images: ['/images/products/hologram-dining-1.jpg', '/images/products/hologram-dining-2.jpg'],
    in_stock: true,
    is_featured: false,
    price: 0
  },
  {
    name: '互动地面投影系统',
    slug: 'interactive-floor-projection',
    description: '地面互动投影系统，通过脚步触发各种视觉效果，为用户带来沉浸式的互动体验。',
    type: 'interactive_equipment',
    size: '500-1000 SQM',
    style: '现代,互动',
    features: ['脚步感应技术', '实时互动反馈', '多种视觉效果', '自定义内容', '多人同时互动'],
    images: ['/images/products/3d-1.jpg', '/images/products/3d-2.jpg'],
    in_stock: true,
    is_featured: false,
    price: 0
  },
  {
    name: '体感游戏互动系统',
    slug: 'motion-sensing-game-system',
    description: '无需手柄的体感游戏设备，通过全身动作控制游戏，提供健康有趣的娱乐体验。',
    type: 'interactive_equipment',
    size: '100-500 SQM',
    style: '现代,互动',
    features: ['体感识别技术', '无线控制', '多人游戏支持', '丰富游戏内容', '健身娱乐结合'],
    images: ['/images/products/ar-1.jpg', '/images/products/ar-2.jpg'],
    in_stock: true,
    is_featured: false,
    price: 0
  },
  {
    name: 'AR增强现实教育系统',
    slug: 'ar-education-system',
    description: '结合增强现实技术的教育系统，让学习变得更加生动有趣，提高学习效果。',
    type: 'indoor_playground',
    size: '100-500 SQM',
    style: '教育,科技',
    features: ['AR增强现实', '互动学习内容', '多学科支持', '实时反馈', '个性化学习'],
    images: ['/images/products/ar-education-1.jpg', '/images/products/ar-education-2.jpg'],
    in_stock: true,
    is_featured: false,
    price: 0
  },
  {
    name: '全息舞台投影系统',
    slug: 'holographic-stage-system',
    description: '为舞台表演打造震撼的全息投影效果，提升演出的视觉冲击力和观众体验。',
    type: 'holographic_solutions',
    size: '500-1000 SQM',
    style: '奢华,科技',
    features: ['360度全息投影', '主题场景切换', '音效同步', '氛围营造', '定制化内容'],
    images: ['/images/products/hologram-stage-1.jpg', '/images/products/hologram-stage-2.jpg'],
    in_stock: true,
    is_featured: false,
    price: 0
  }
];

async function uploadProducts() {
  console.log('🚀 开始直接数据库产品上传...');
  
  try {
    const client = await pool.connect();
    console.log('✅ 数据库连接成功');
    
    let successCount = 0;
    let failCount = 0;
    
    for (const product of products) {
      try {
        console.log(`📝 上传产品: ${product.name}`);
        
        // 检查产品是否已存在
        const existingCheck = await client.query('SELECT id FROM products WHERE slug = $1', [product.slug]);
        
        if (existingCheck.rows.length > 0) {
          console.log(`⚠️  产品已存在，跳过: ${product.slug}`);
          continue;
        }
        
        // 插入产品
        const result = await client.query(
          `INSERT INTO products 
           (name, slug, description, size, style, type, features, images, in_stock, is_featured, price) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
           RETURNING id`,
          [
            product.name,
            product.slug,
            product.description,
            product.size,
            product.style,
            product.type,
            JSON.stringify(product.features),
            JSON.stringify(product.images),
            product.in_stock,
            product.is_featured,
            product.price
          ]
        );
        
        console.log(`✅ 产品创建成功 (ID: ${result.rows[0].id})`);
        successCount++;
        
      } catch (error) {
        console.log(`❌ 产品创建失败: ${error.message}`);
        failCount++;
      }
    }
    
    console.log('\n📊 上传结果统计:');
    console.log(`✅ 成功: ${successCount} 个`);
    console.log(`❌ 失败: ${failCount} 个`);
    
    client.release();
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
  } finally {
    await pool.end();
  }
}

uploadProducts();
