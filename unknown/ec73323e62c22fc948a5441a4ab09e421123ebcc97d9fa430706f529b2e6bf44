// 脚本用于删除不需要的解决方案导航菜单项
const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function deleteSolutionMenuItems() {
  // 连接数据库
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL || process.env.POSTGRES_URI,
  });

  try {
    console.log('删除不需要的解决方案菜单项...');

    // 要删除的菜单项ID列表
    const menuIdsToRemove = [23, 24, 25];

    // 查看当前菜单项
    const before = await pool.query(`
      SELECT id, name, slug, is_featured, featured_type, featured_order, translations
      FROM categories 
      WHERE featured_type = 'solution' AND is_featured = true
      ORDER BY featured_order
    `);

    console.log('删除前的解决方案菜单项:');
    console.table(
      before.rows.map(row => ({
        id: row.id,
        name: row.name,
        slug: row.slug,
        featured_order: row.featured_order,
        translations: row.translations,
      }))
    );

    // 将这些菜单项的is_featured设置为false
    const result = await pool.query(`
      UPDATE categories
      SET is_featured = false
      WHERE id IN (${menuIdsToRemove.join(',')})
    `);

    console.log(`已将${result.rowCount}个菜单项从导航中移除`);

    // 查看更新后的菜单项
    const after = await pool.query(`
      SELECT id, name, slug, is_featured, featured_type, featured_order, translations
      FROM categories 
      WHERE featured_type = 'solution' AND is_featured = true
      ORDER BY featured_order
    `);

    console.log('删除后的解决方案菜单项:');
    console.table(
      after.rows.map(row => ({
        id: row.id,
        name: row.name,
        slug: row.slug,
        featured_order: row.featured_order,
        translations: row.translations,
      }))
    );
  } catch (error) {
    console.error('删除菜单项时出错:', error);
  } finally {
    await pool.end();
  }
}

// 运行函数
deleteSolutionMenuItems().catch(console.error);
