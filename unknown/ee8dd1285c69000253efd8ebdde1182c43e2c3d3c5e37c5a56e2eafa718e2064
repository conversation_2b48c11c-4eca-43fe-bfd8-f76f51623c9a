'use client';

import { ReactNode, useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

interface AuthGuardProps {
  children: ReactNode;
  adminOnly?: boolean;
}

/**
 * AuthGuard component
 *
 * This component protects admin routes by:
 * 1. Checking if the user is authenticated
 * 2. Optionally checking if the user has admin role
 * 3. Redirecting to login if not authenticated
 * 4. Only rendering children when authentication is confirmed
 *
 * This prevents hydration errors by ensuring useSession is only used client-side
 */
export default function AuthGuard({ children, adminOnly = false }: AuthGuardProps) {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [isClient, setIsClient] = useState(false);
  const [localSession, setLocalSession] = useState<any>(null);

  // 从当前路径获取语言参数
  const currentLang = typeof window !== 'undefined' ? window.location.pathname.split('/')[1] || 'zh' : 'zh';

  // Set isClient to true when component mounts (client-side only)
  useEffect(() => {
    setIsClient(true);

    // 检查本地会话
    if (typeof window !== 'undefined') {
      const adminSession = localStorage.getItem('admin_session');
      if (adminSession) {
        try {
          const sessionData = JSON.parse(adminSession);
          // 检查会话是否过期
          if (new Date(sessionData.expires) > new Date()) {
            setLocalSession(sessionData);
          } else {
            localStorage.removeItem('admin_session');
          }
        } catch (error) {
          localStorage.removeItem('admin_session');
        }
      }
    }
  }, []);

  // Handle authentication and redirection
  useEffect(() => {
    // Only run this effect on the client side
    if (!isClient) return;

    // 统一获取用户信息和角色
    const getUserInfo = () => {
      if (localSession?.user) {
        return {
          user: localSession.user,
          role: localSession.user.role,
          isAuthenticated: true,
          source: 'local'
        };
      }
      
      if (status === 'authenticated' && session?.user) {
        return {
          user: session.user,
          role: session.user.role,
          isAuthenticated: true,
          source: 'nextauth'
        };
      }
      
      return {
        user: null,
        role: null,
        isAuthenticated: false,
        source: null
      };
    };

    const userInfo = getUserInfo();
    console.log('AuthGuard 用户信息:', userInfo);

    // 如果已认证，检查管理员权限
    if (userInfo.isAuthenticated) {
      if (adminOnly) {
        // 统一的管理员权限检查
        const hasAdminPermission = 
          userInfo.role === 'admin' || 
          userInfo.role === 'super_admin' || 
          (typeof userInfo.role === 'string' && userInfo.role.includes('admin'));
        
        if (!hasAdminPermission) {
          console.log('权限不足，角色:', userInfo.role);
          router.push(`/${currentLang}/admin/login`);
          return;
        }
      }
      
      console.log(`${userInfo.source} 会话验证通过:`, userInfo.user);
      return;
    }

    // 只有在确定未认证时才重定向到登录页
    if (status === 'unauthenticated' && !localSession) {
      console.log('用户未认证，重定向到登录页');
      router.push(`/${currentLang}/admin/login`);
      return;
    }
  }, [status, session, router, adminOnly, isClient, localSession, currentLang]);

  // Don't render anything on the server side to prevent hydration errors
  if (!isClient) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-100">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Show loading state while checking authentication
  if (status === 'loading' && !localSession) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-100">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // 检查是否已认证（NextAuth会话或本地会话）
  const isAuthenticated = localSession || (status === 'authenticated' && session);

  // 统一获取用户角色
  const getUserRole = () => {
    if (localSession?.user?.role) {
      return localSession.user.role;
    }
    if (session?.user?.role) {
      return session.user.role;
    }
    return null;
  };
  
  const userRole = getUserRole();

  // If not authenticated and status is confirmed unauthenticated, don't render children
  if (!isAuthenticated && status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-100">
        <div className="text-xl text-gray-600">重定向到登录页面...</div>
      </div>
    );
  }

  // If adminOnly is true and the user is not an admin, don't render children
  if (adminOnly) {
    const hasAdminPermission = 
      userRole === 'admin' || 
      userRole === 'super_admin' || 
      (typeof userRole === 'string' && userRole.includes('admin'));
    
    if (!hasAdminPermission) {
      console.log('渲染阶段权限检查失败，用户角色:', userRole);
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-100">
          <div className="text-xl text-gray-600">权限不足，需要管理员权限</div>
      </div>
    );
    }
  }

  // If authenticated and has the right role, render children
  console.log('AuthGuard: 认证通过，用户角色:', userRole, '渲染子组件');
  return <>{children}</>;
}
