/* 定制解决方案页面增强样式 */

/* 强制确保所有内容可见 */
* {
  visibility: visible !important;
  opacity: 1 !important;
}

/* 页面顶部横幅增强 */
.page-banner {
  background-image: url('/images/holographic-tech-bg.jpg') !important;
  background-size: cover !important;
  background-position: center !important;
  padding: 100px 0 80px !important;
  position: relative !important;
  overflow: hidden !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
  z-index: 10 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.page-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(10, 36, 99, 0.85) 0%, rgba(30, 80, 162, 0.75) 100%);
  z-index: 1;
}

.page-banner::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 35%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  z-index: 2;
}

.page-banner .container {
  position: relative;
  z-index: 3;
}

.page-banner .page-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  letter-spacing: 1px;
}

.page-banner .breadcrumb {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
}

.page-banner .breadcrumb a {
  color: rgba(255, 255, 255, 0.9);
  transition: color 0.3s ease;
}

.page-banner .breadcrumb a:hover {
  color: #ffffff;
  text-decoration: none;
}

.page-banner .separator {
  margin: 0 10px;
  opacity: 0.7;
}

/* 内容区域样式增强 */
.solutions-intro {
  padding: 80px 0 !important;
  background-color: #fff !important;
  position: relative !important;
  z-index: 10 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.solutions-intro .content-grid {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
}

@media (max-width: 768px) {
  .solutions-intro .content-grid {
    grid-template-columns: 1fr;
  }
}

.solutions-intro .content-image {
  position: relative;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  overflow: hidden;
  transform: perspective(1000px) rotateY(-5deg);
  transition: transform 0.5s ease;
  width: fit-content;
  max-width: 600px;
  margin: 0 auto;
}

.solutions-intro .content-image img {
  width: auto;
  height: auto;
  max-width: 600px;
  max-height: 400px;
  object-fit: contain;
  display: block;
}

.solutions-intro .content-image:hover {
  transform: perspective(1000px) rotateY(0deg);
}

.solutions-intro .content-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(30, 80, 162, 0.1) 0%, transparent 60%);
  z-index: 1;
}

.solutions-intro .content-text {
  position: relative;
  padding: 30px;
}

.solutions-intro .content-text h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a2e;
  margin-bottom: 30px;
  position: relative;
  padding-bottom: 15px;
}

.solutions-intro .content-text h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #0a2463 0%, #1e50a2 100%);
}

.solutions-intro .content-text p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
  margin-bottom: 20px;
}

/* 服务卡片增强 */
.solutions-list {
  padding: 60px 0 !important;
  background-color: #f8fafd !important;
  position: relative !important;
  z-index: 10 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.solutions-list::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 30%, rgba(30, 80, 162, 0.03) 0%, transparent 70%);
  opacity: 0.8;
}

.solutions-list .section-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #1a1a2e;
  text-align: center;
  margin-bottom: 50px;
  position: relative;
  padding-bottom: 20px;
}

.solutions-list .section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #0a2463 0%, #1e50a2 100%);
}

.solutions-list .services-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
  gap: 30px !important;
  margin-top: 40px !important;
  position: relative !important;
  z-index: 10 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

@media (max-width: 768px) {
  .solutions-list .services-grid {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }
}

.solutions-list .service-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: transform 0.4s ease, box-shadow 0.4s ease;
  background-color: #fff;
  border-top: 5px solid transparent;
  border-image: linear-gradient(90deg, #0a2463, #1e50a2);
  border-image-slice: 1;
}

.solutions-list .service-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.solutions-list .service-image {
  position: relative;
  overflow: hidden;
}

.solutions-list .service-image img {
  transition: transform 0.6s ease;
}

/* 移除服务卡片图片悬停放大效果 */

.solutions-list .service-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.1) 0%, transparent 50%);
}

.solutions-list .service-content {
  padding: 30px;
}

.solutions-list .service-content h3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1a1a2e;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 15px;
}

.solutions-list .service-content h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #0a2463 0%, #1e50a2 100%);
}

.solutions-list .service-content p {
  font-size: 1.05rem;
  line-height: 1.7;
  color: #555;
  margin-bottom: 20px;
}

.solutions-list .service-features {
  margin: 20px 0 25px;
  padding-left: 5px;
}

.solutions-list .service-features li {
  margin-bottom: 12px;
  position: relative;
  padding-left: 28px;
  font-size: 1.05rem;
  color: #555;
}

.solutions-list .service-features li:before {
  content: "\f00c";
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  color: #1e50a2;
  position: absolute;
  left: 0;
  top: 2px;
}

.solutions-list .btn-secondary {
  padding: 12px 28px;
  font-size: 1rem;
  font-weight: 600;
  border-width: 2px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.solutions-list .btn-secondary:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(30, 80, 162, 0.2);
}

/* 流程时间线增强 */
.design-process {
  padding: 80px 0 !important;
  background-color: #fff !important;
  position: relative !important;
  overflow: hidden !important;
  z-index: 10 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.design-process::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle at center, rgba(30, 80, 162, 0.05) 0%, transparent 70%);
  border-radius: 50%;
}

.design-process::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle at center, rgba(30, 80, 162, 0.05) 0%, transparent 70%);
  border-radius: 50%;
}

.design-process .section-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #1a1a2e;
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  padding-bottom: 20px;
}

.design-process .section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #0a2463 0%, #1e50a2 100%);
}

.design-process .process-timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.design-process .process-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 24px;
  width: 2px;
  background: linear-gradient(to bottom, #0a2463 0%, #1e50a2 100%);
  z-index: 1;
}

.design-process .timeline-item {
  position: relative;
  padding-left: 70px;
  margin-bottom: 40px;
  z-index: 2;
}

.design-process .timeline-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #0a2463 0%, #1e50a2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: #fff;
  position: absolute;
  left: 0;
  top: 0;
  box-shadow: 0 5px 15px rgba(30, 80, 162, 0.2);
}

.design-process .timeline-content {
  background-color: #fff;
  padding: 25px 30px;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  border-left: 3px solid #1e50a2;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.design-process .timeline-item:hover .timeline-content {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
}

.design-process .timeline-content h3 {
  font-size: 1.6rem;
  font-weight: 700;
  color: #1a1a2e;
  margin-bottom: 10px;
}

.design-process .timeline-content p {
  font-size: 1.05rem;
  line-height: 1.7;
  color: #555;
  margin-bottom: 0;
}

/* CTA 部分增强 */
.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #0a2463 0%, #1e50a2 100%);
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 75%, transparent 75%);
  background-size: 20px 20px;
  opacity: 0.05;
}

.cta-section::after {
  content: '';
  position: absolute;
  bottom: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

.cta-section .cta-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 1;
}

.cta-section .cta-content h2 {
  font-size: 2.8rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 20px;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.cta-section .cta-content p {
  font-size: 1.2rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 40px;
}

.cta-section .btn-primary {
  padding: 15px 40px;
  font-size: 1.1rem;
  font-weight: 600;
  background-color: #fff;
  color: #1e50a2;
  border-radius: 6px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
}

.cta-section .btn-primary:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* 响应式调整 */
@media (max-width: 992px) {
  .page-banner .page-title {
    font-size: 2.8rem;
  }

  .solutions-intro .content-text h2 {
    font-size: 2.2rem;
  }

  .solutions-list .section-title,
  .design-process .section-title,
  .cta-section .cta-content h2 {
    font-size: 2.4rem;
  }
}

@media (max-width: 768px) {
  .page-banner {
    padding: 80px 0 60px;
  }

  .page-banner .page-title {
    font-size: 2.5rem;
  }

  .solutions-intro {
    padding: 60px 0;
  }

  .solutions-intro .content-text h2 {
    font-size: 2rem;
  }

  .solutions-list .section-title,
  .design-process .section-title,
  .cta-section .cta-content h2 {
    font-size: 2.2rem;
  }

  .design-process .timeline-content h3 {
    font-size: 1.4rem;
  }

  .cta-section .cta-content p {
    font-size: 1.1rem;
  }
}

@media (max-width: 576px) {
  .page-banner {
    padding: 60px 0 40px;
  }

  .page-banner .page-title {
    font-size: 2.2rem;
  }

  .solutions-intro .content-text h2 {
    font-size: 1.8rem;
  }

  .solutions-list .section-title,
  .design-process .section-title,
  .cta-section .cta-content h2 {
    font-size: 2rem;
  }

  .solutions-list .service-content h3 {
    font-size: 1.6rem;
  }

  .design-process .timeline-number {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .design-process .timeline-item {
    padding-left: 60px;
  }

  .cta-section {
    padding: 60px 0;
  }

  .cta-section .btn-primary {
    padding: 12px 30px;
    font-size: 1rem;
  }

  /* 小屏幕设备图片进一步优化 */
  .solutions-intro .content-image {
    max-width: 100%;
    width: 100%;
    transform: none;
  }

  .solutions-intro .content-image img {
    max-width: 100%;
    max-height: 300px;
  }
}

/* 设计流程步骤图片优化 - 统一600x400尺寸 */
.step-image {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  height: 400px;
  margin: 0 auto;
  display: block;
}

.step-image img,
.step-image .step-img {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.step-image:hover img,
.step-image:hover .step-img {
  transform: none;
}

.step-content-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.step-image:hover .step-content-overlay {
  transform: translateY(0);
}

.step-number {
  font-size: 2rem;
  font-weight: bold;
  color: #1e50a2;
  margin-bottom: 10px;
}

.step-content-overlay h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: white;
}

.step-content-overlay p {
  font-size: 0.9rem;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.9);
}

/* 设计流程步骤项目布局 */
.step-item {
  text-align: center;
  margin-bottom: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.design-process .process-steps {
  display: flex;
  flex-direction: column;
  gap: 30px;
  align-items: center;
}

.design-process .steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

/* 响应式调整 - 步骤图片 */
@media (max-width: 768px) {
  .step-image {
    max-width: 100%;
    width: 100%;
  }

  .step-image img {
    max-width: 100%;
    max-height: 250px;
  }

  .design-process .steps-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 576px) {
  .step-image img {
    max-height: 200px;
  }

  .step-content-overlay {
    position: static;
    transform: none;
    background: rgba(0, 0, 0, 0.8);
    margin-top: -50px;
    border-radius: 0 0 8px 8px;
  }
}

/* 强制覆盖页面中的图片样式 */
.premium-card .step-img {
  width: auto !important;
  height: auto !important;
  max-width: 100% !important;
  max-height: none !important;
  object-fit: contain !important;
}

.premium-card {
  width: fit-content !important;
  max-width: 100% !important;
  margin: 0 auto !important;
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

.premium-card:hover {
  box-shadow: none !important;
  background: transparent !important;
}

/* 强制移除section背景色 */
.design-process.bg-light {
  background-color: transparent !important;
  background: transparent !important;
}

.design-process {
  background-color: transparent !important;
  background: transparent !important;
}

/* 更强的覆盖规则 */
section.design-process.section-padding.bg-light {
  background-color: transparent !important;
  background: transparent !important;
}

.bg-light {
  background-color: transparent !important;
  background: transparent !important;
}

/* 最高优先级覆盖 - 针对home-page.css中的.bg-light */
section.bg-light,
.section.bg-light,
div.bg-light {
  background-color: transparent !important;
  background: transparent !important;
  position: relative !important;
  overflow: hidden !important;
}

/* 特别针对设计流程页面 */
body section.design-process.section-padding.bg-light {
  background-color: transparent !important;
  background: transparent !important;
}