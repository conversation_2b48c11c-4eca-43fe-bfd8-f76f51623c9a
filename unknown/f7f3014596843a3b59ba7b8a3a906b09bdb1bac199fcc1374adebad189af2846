import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route.js';
import { query } from '@/lib/db.js';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Invalid content ID' },
        { status: 400 }
      );
    }

    // Get content with translations
    const result = await query(
      `SELECT 
        c.id,
        c.title,
        c.slug,
        c.content,
        c.type,
        c.created_at,
        c.updated_at,
        COALESCE(
          json_object_agg(
            ct.locale, 
            json_build_object(
              'title', ct.title,
              'content', ct.content
            )
          ) FILTER (WHERE ct.locale IS NOT NULL),
          '{}'::json
        ) as translations
      FROM contents c
      LEFT JOIN content_translations ct ON c.id = ct.content_id
      WHERE c.id = $1
      GROUP BY c.id, c.title, c.slug, c.content, c.type, c.created_at, c.updated_at`,
      [id]
    );

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Content not found' },
        { status: 404 }
      );
    }

    const content = result.rows[0];

    return NextResponse.json({
      success: true,
      data: {
        id: content.id.toString(),
        key: content.slug,
        title: content.title,
        slug: content.slug,
        content: content.content,
        page: content.type,
        section: content.type,
        type: content.type,
        translations: content.translations || {},
        createdAt: content.created_at,
        updatedAt: content.updated_at,
      },
    });
  } catch (error: unknown) {
    console.error('Error fetching content:', error);
    const message = error instanceof Error ? error.message : 'Server error';
    return NextResponse.json(
      { success: false, message: message },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Invalid content ID' },
        { status: 400 }
      );
    }

    // Check if user is authenticated and has permission
    if (!session || (session.user.role !== 'admin' && session.user.role !== 'editor')) {
      return NextResponse.json(
        { success: false, message: 'Forbidden' },
        { status: 403 }
      );
    }

    const data = await request.json();
    const { key, title, content, page, section, type, translations = {} } = data;

    // Validate required fields
    if (!title || !content) {
      return NextResponse.json(
        { success: false, message: 'Title and content are required' },
        { status: 400 }
      );
    }

    // Check if content exists
    const existingContent = await query('SELECT id FROM contents WHERE id = $1', [id]);

    if (existingContent.rows.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Content not found' },
        { status: 404 }
      );
    }

    // Update content
    await query(
      `UPDATE contents 
       SET title = $1, slug = $2, content = $3, type = $4, updated_at = NOW()
       WHERE id = $5`,
      [title, key || `content-${id}`, content, type || page || 'general', id]
    );

    // Delete existing translations
    await query('DELETE FROM content_translations WHERE content_id = $1', [id]);

    // Insert new translations if provided
    if (translations && Object.keys(translations).length > 0) {
      for (const [locale, translation] of Object.entries(translations)) {
        if (translation && typeof translation === 'object') {
          const trans = translation as { title?: string; content?: string };
          if (trans.title || trans.content) {
            await query(
              `INSERT INTO content_translations (content_id, locale, title, content)
               VALUES ($1, $2, $3, $4)`,
              [id, locale, trans.title || title, trans.content || content]
            );
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Content updated successfully',
    });
  } catch (error: unknown) {
    console.error('Error updating content:', error);
    const message = error instanceof Error ? error.message : 'Server error';
    return NextResponse.json(
      { success: false, message: message },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: 'Invalid content ID' },
        { status: 400 }
      );
    }

    // Check if user is authenticated and has permission
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, message: 'Forbidden' },
        { status: 403 }
      );
    }

    // Check if content exists
    const existingContent = await query('SELECT id FROM contents WHERE id = $1', [id]);

    if (existingContent.rows.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Content not found' },
        { status: 404 }
      );
    }

    // Delete content (translations will be deleted automatically due to CASCADE)
    await query('DELETE FROM contents WHERE id = $1', [id]);

    return NextResponse.json({
      success: true,
      message: 'Content deleted successfully',
    });
  } catch (error: unknown) {
    console.error('Error deleting content:', error);
    const message = error instanceof Error ? error.message : 'Server error';
    return NextResponse.json(
      { success: false, message: message },
      { status: 500 }
    );
  }
}
