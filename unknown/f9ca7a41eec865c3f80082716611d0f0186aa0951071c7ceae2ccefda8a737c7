// 修复所有路由字符串引号问题
const fs = require('fs');
const path = require('path');

// 主要需要修复的文件夹
const adminDir = './app/[lang]/admin';

// 需要修复的模式
const patterns = [
  // 修复缺少结束引号的路径
  {
    pattern: /router\.push\(['"]\/\[lang\]\/admin\/[^'"]*\);/g,
    replacement: (match) => {
      return match.replace(/\);/, "');");
    },
    description: '修复缺少结束引号的路径'
  },
  // 修复多余的撇号
  {
    pattern: /router\.push\(['"]\/\[lang\]\/admin\/[^'"]*['"]'\);/g,
    replacement: (match) => {
      return match.replace(/['"]'\);/, "');");
    },
    description: '修复多余的撇号'
  },
  // 修复onClick中的字符串格式
  {
    pattern: /onClick\s*=\s*{\s*\(\s*\)\s*=>\s*router\.push\(['"]\/\[lang\]\/admin\/[^'"]*\)}/g,
    replacement: (match) => {
      return match.replace(/\)}/g, "')}");
    },
    description: '修复onClick中的字符串格式'
  }
];

// 递归处理目录
function processDirectory(dirPath) {
  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const item of items) {
      const itemPath = path.join(dirPath, item.name);

      if (item.isDirectory()) {
        processDirectory(itemPath);
      } else if (item.isFile() && (item.name.endsWith('.tsx') || item.name.endsWith('.jsx'))) {
        fixFile(itemPath);
      }
    }
  } catch (error) {
    console.error(`处理目录时出错: ${dirPath}`, error);
  }
}

// 修复单个文件
function fixFile(filePath) {
  try {
    console.log(`检查文件: ${filePath}`);
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 应用所有模式替换
    for (const { pattern, replacement, description } of patterns) {
      if (pattern.test(content)) {
        console.log(`  ${description}: ${filePath}`);
        content = content.replace(pattern, replacement);
        modified = true;
      }
    }

    // 更全面地检查常见错误
    // 1. router.push('/[lang]/admin/xxxx);  - 缺少右引号
    content = content.replace(
      /(router\.push\(['"]\/\[lang\]\/admin\/[^'"]*)([\);])/g,
      (_, prefix, suffix) => {
        if (!prefix.endsWith("'") && !prefix.endsWith('"')) {
          modified = true;
          console.log(`  修复缺少右引号: ${filePath}`);
          return `${prefix}'${suffix}`;
        }
        return `${prefix}${suffix}`;
      }
    );

    // 2. router.push('/[lang]/admin/xxxx''); - 多余的右引号
    content = content.replace(
      /(router\.push\(['"]\/\[lang\]\/admin\/[^'"]*['"])(['"]\))/g,
      (_, prefix, suffix) => {
        if (suffix !== "')") {
          modified = true;
          console.log(`  修复多余的右引号: ${filePath}`);
          return `${prefix})`;
        }
        return `${prefix}${suffix.slice(1)}`;
      }
    );

    // 3. onClick={() => router.push('/[lang]/admin/xxxx)} - 缺少右引号
    content = content.replace(
      /(onClick\s*=\s*{\s*\(\s*\)\s*=>\s*router\.push\(['"]\/\[lang\]\/admin\/[^'"]*)([\)}])/g,
      (_, prefix, suffix) => {
        if (!prefix.endsWith("'") && !prefix.endsWith('"')) {
          modified = true;
          console.log(`  修复onClick中缺少右引号: ${filePath}`);
          return `${prefix}'${suffix}`;
        }
        return `${prefix}${suffix}`;
      }
    );

    // 4. onClick={() => router.push('/[lang]/admin/xxxx'')} - 多余的右引号
    content = content.replace(
      /(onClick\s*=\s*{\s*\(\s*\)\s*=>\s*router\.push\(['"]\/\[lang\]\/admin\/[^'"]*['"])(['"]\)}/g,
      (_, prefix, suffix) => {
        if (suffix !== "')}") {
          modified = true;
          console.log(`  修复onClick中多余的右引号: ${filePath}`);
          return `${prefix})}`;
        }
        return `${prefix}${suffix.slice(1)}`;
      }
    );

    // 保存修改后的文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
    } else {
      console.log(`  无需修改: ${filePath}`);
    }
  } catch (error) {
    console.error(`处理文件时出错: ${filePath}`, error);
  }
}

// 执行主函数
console.log('开始修复路由字符串引号问题...');
processDirectory(adminDir);
console.log('完成！'); 