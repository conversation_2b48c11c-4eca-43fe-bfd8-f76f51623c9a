/* 联系我们页面样式 */
.contact-page {
  padding: 0; /* 移除默认内边距 */
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
  margin-top: 0; /* 确保没有额外的顶部边距 */
}

/* 确保联系我们页面标题没有阴影 */
.contact-page .page-title {
  text-shadow: none !important;
}

/* 骨架屏样式 */
.loading-placeholder {
  padding: 2rem 0;
}

.form-skeleton {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.skeleton-line {
  height: 50px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-button {
  height: 50px;
  width: 200px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-top: 1rem;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 全息投影主题背景头部区域 */
.page-header {
  background: linear-gradient(135deg,
    #0a0a23 0%,     /* 深蓝紫色 - 夜空感 */
    #1a1a2e 25%,    /* 深紫色 - 神秘感 */
    #16213e 50%,    /* 深蓝色 - 科技感 */
    #0f3460 75%,    /* 蓝色 - 全息感 */
    #0e4b99 100%    /* 亮蓝色 - 投影光感 */
  );
  color: white;
  text-align: center;
  padding: 8rem 0 6rem 0;
  position: relative;
  overflow: hidden;
  margin-bottom: 0;
}

/* 全息投影网格背景装饰效果 */
.page-header::before {
  content: '';
  position: absolute;
  top: -30%;
  left: 0;
  right: 0;
  bottom: 0;
  height: 200%;
  background-image:
    linear-gradient(rgba(0, 255, 255, 0.2) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.2) 1px, transparent 1px);
  background-size: 40px 40px;
  transform: skewY(12deg);
  mask-image: radial-gradient(400px circle at center, white, transparent);
  -webkit-mask-image: radial-gradient(400px circle at center, white, transparent);
  z-index: 0;
  pointer-events: none;
}

/* 全息投影特定网格方块高亮效果 */
.page-header::after {
  content: '';
  position: absolute;
  top: -30%;
  left: 0;
  right: 0;
  bottom: 0;
  height: 200%;
  background-image:
    /* 青色高亮方块 - 模拟 [4,4] */
    radial-gradient(circle at calc(4 * 40px + 20px) calc(4 * 40px + 20px), rgba(0, 255, 255, 0.6) 0%, transparent 20px),
    /* 紫色高亮方块 - 模拟 [5,1] */
    radial-gradient(circle at calc(5 * 40px + 20px) calc(1 * 40px + 20px), rgba(147, 51, 234, 0.6) 0%, transparent 20px),
    /* 蓝色高亮方块 - 模拟 [8,2] */
    radial-gradient(circle at calc(8 * 40px + 20px) calc(2 * 40px + 20px), rgba(59, 130, 246, 0.6) 0%, transparent 20px),
    /* 青色高亮方块 - 模拟 [6,6] */
    radial-gradient(circle at calc(6 * 40px + 20px) calc(6 * 40px + 20px), rgba(34, 211, 238, 0.6) 0%, transparent 20px),
    /* 紫色高亮方块 - 模拟 [10,5] */
    radial-gradient(circle at calc(10 * 40px + 20px) calc(5 * 40px + 20px), rgba(168, 85, 247, 0.6) 0%, transparent 20px),
    /* 蓝色高亮方块 - 模拟 [13,3] */
    radial-gradient(circle at calc(13 * 40px + 20px) calc(3 * 40px + 20px), rgba(96, 165, 250, 0.6) 0%, transparent 20px);
  transform: skewY(12deg);
  mask-image: radial-gradient(400px circle at center, white, transparent);
  -webkit-mask-image: radial-gradient(400px circle at center, white, transparent);
  z-index: 1;
  pointer-events: none;
}

/* 移除圆形装饰 */

/* 确保内容在装饰效果之上 */
.page-header .page-title,
.page-header .page-description {
  position: relative;
  z-index: 2;
}

.page-header .container {
  position: relative;
  z-index: 2;
}

.contact-page .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* 内容区域容器 */
.contact-content {
  padding: 4rem 0;
  background-color: #f8f9fa;
}

/* 移除原有的装饰线样式 */

.page-title {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
  text-shadow: none !important; /* 移除阴影效果 */
}

/* 移除原有的装饰线，因为在蓝色背景上不需要 */
.page-title::before,
.page-title::after {
  display: none;
}

.page-description {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

.contact-container {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.contact-info {
  flex: 1;
  min-width: 300px;
  background-color: #2c3e50;
  color: white;
  padding: 2.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  position: relative;
  overflow: hidden;
}

.contact-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 10% 90%, rgba(255,255,255,0.1) 0%, transparent 40%),
    radial-gradient(circle at 90% 10%, rgba(255,255,255,0.1) 0%, transparent 40%);
  z-index: 0;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  position: relative;
  z-index: 1;
  transition: transform 0.3s ease;
  padding: 1rem;
  border-left: 3px solid transparent;
}

.info-item:hover {
  transform: translateX(5px);
  border-left-color: #3498db;
  background-color: rgba(255, 255, 255, 0.05);
}

.info-item i {
  font-size: 1.5rem;
  color: #3498db;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(52, 152, 219, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.info-item:hover i {
  background-color: #3498db;
  color: white;
  transform: scale(1.1);
}

.info-content h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #ecf0f1;
}

.info-content p {
  color: #bdc3c7;
  font-size: 0.95rem;
  line-height: 1.5;
}

.contact-form-container {
  flex: 2;
  min-width: 400px;
  padding: 2.5rem;
  background-color: white;
}

.contact-form-container h2 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  position: relative;
  display: inline-block;
  padding-bottom: 0.5rem;
}

.contact-form-container h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: #3498db;
}

.contact-form-container p {
  color: #7f8c8d;
  margin-bottom: 2rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  flex: 1;
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #34495e;
  font-size: 0.95rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.8rem 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: #f9f9f9;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
  outline: none;
  background-color: white;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  margin-top: 1rem;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2980b9, #3498db);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover::before {
  left: 100%;
  opacity: 1;
}

.btn-primary:disabled {
  background: #95a5a6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.success-message {
  text-align: center;
  background-color: #f1f9f7;
  padding: 2rem;
  border-radius: 10px;
  border-left: 4px solid #2ecc71;
}

.success-message i {
  font-size: 3rem;
  color: #2ecc71;
  margin-bottom: 1rem;
  display: block;
}

.success-message h3 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.success-message p {
  color: #7f8c8d;
}

.error-message {
  background-color: #fdf3f2;
  color: #e74c3c;
  padding: 0.8rem 1rem;
  border-radius: 5px;
  margin-bottom: 1rem;
  border-left: 3px solid #e74c3c;
}

.social-links {
  display: flex;
  gap: 0.8rem;
  margin-top: 0.5rem;
}

.social-links a {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ecf0f1;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.social-links a:hover {
  transform: translateY(-3px);
}

.social-links a:nth-child(1):hover {
  background-color: #3b5998;
  color: white;
}

.social-links a:nth-child(2):hover {
  background-color: #1da1f2;
  color: white;
}

.social-links a:nth-child(3):hover {
  background-color: #0077b5;
  color: white;
}

.social-links a:nth-child(4):hover {
  background-color: #e1306c;
  color: white;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .contact-container {
    flex-direction: column;
  }

  .contact-info {
    min-width: 100%;
  }

  .contact-form-container {
    min-width: 100%;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 6rem 0 4rem 0; /* 移动端调整内边距 */
  }

  .page-title {
    font-size: 2.2rem; /* 移动端减小标题字体 */
  }

  .page-description {
    font-size: 1.1rem; /* 移动端调整描述字体 */
  }

  .contact-content {
    padding: 3rem 0; /* 移动端减少内容区域内边距 */
  }
}

.map-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%233498db' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.3;
  z-index: 0;
}

.world-map-section {
  margin-top: 4rem;
  text-align: center;
  position: relative;
  z-index: 1;
  padding: 2rem 0;
}

.world-map-section h2 {
  font-size: 2rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
}

.world-map-section h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #0e4b99, #16213e);
}

.world-map-section p {
  color: #7f8c8d;
  max-width: 600px;
  margin: 0 auto 3rem;
  font-size: 1.1rem;
  line-height: 1.6;
}

/* Globe容器样式 */
.globe-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  margin: 2rem 0;
  position: relative;
}

.interactive-globe {
  filter: drop-shadow(0 0 20px rgba(0, 255, 255, 0.3));
  transition: filter 0.3s ease;
}

.interactive-globe:hover {
  filter: drop-shadow(0 0 30px rgba(0, 255, 255, 0.5));
}

.map-point {
  width: 12px;
  height: 12px;
  background-color: #3498db;
  border-radius: 50%;
  position: absolute;
  transform: translate(-50%, -50%);
  cursor: pointer;
  z-index: 2;
  box-shadow: 0 0 0 rgba(52, 152, 219, 0.4);
  animation: pulse 2s infinite;
}

.map-point::after {
  content: attr(title);
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #2c3e50;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  white-space: nowrap;
  pointer-events: none;
}

.map-point:hover::after {
  opacity: 1;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
  }
}

/* 响应式设计 */
@media (max-width: 992px) {
  .world-map {
    height: 200px;
  }
}

@media (max-width: 576px) {
  .world-map {
    height: 150px;
  }

  .map-point {
    width: 8px;
    height: 8px;
  }
}