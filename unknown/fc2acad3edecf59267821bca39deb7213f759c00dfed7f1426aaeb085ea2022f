/* Shared Services Pages Styles */

/* Services Grid Layout */
.services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 40px;
}

/* Service Card */
.service-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.service-image {
  height: 200px;
  overflow: hidden;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.service-card:hover .service-image img {
  transform: scale(1.05);
}

.service-content {
  padding: 25px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-content h3 {
  font-size: 22px;
  margin-bottom: 15px;
  color: #0a59f7;
}

.service-content p {
  margin-bottom: 15px;
}

.service-features {
  margin: 15px 0;
  padding-left: 20px;
}

.service-features li {
  margin-bottom: 8px;
  position: relative;
}

.service-features li:before {
  content: "•";
  color: #0a59f7;
  position: absolute;
  left: -15px;
}

/* Benefits Grid */
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 40px;
}

.benefit-item {
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
  height: 100%;
}

.benefit-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
  font-size: 36px;
  color: #0a59f7;
  margin-bottom: 20px;
}

.benefit-item h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
}

/* Process Timeline */
.process-timeline {
  margin-top: 40px;
}

.timeline-item {
  display: flex;
  margin-bottom: 30px;
  position: relative;
}

.timeline-number {
  background-color: #0a59f7;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
  margin-right: 20px;
  flex-shrink: 0;
}

.timeline-content h3 {
  font-size: 22px;
  margin-bottom: 10px;
  color: #333;
}

/* Process Steps */
.process-steps {
  margin-top: 40px;
}

.step-item {
  margin-bottom: 60px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.step-number {
  background-color: #0a59f7;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
  margin-right: 20px;
}

.step-header h3 {
  font-size: 24px;
  color: #333;
}

.step-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 30px;
  align-items: center;
}

/* Team Section */
.team-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-top: 40px;
  align-items: center;
}

.team-info h3 {
  font-size: 28px;
  margin-bottom: 20px;
  color: #333;
}

.team-info p {
  margin-bottom: 15px;
  line-height: 1.7;
}

.team-features {
  margin-top: 20px;
  padding-left: 20px;
}

.team-features li {
  margin-bottom: 10px;
  position: relative;
}

.team-features li:before {
  content: "•";
  color: #0a59f7;
  position: absolute;
  left: -15px;
}

/* Case Studies */
.case-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 40px;
}

.case-item {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.case-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.case-content {
  padding: 20px;
}

.case-content h3 {
  font-size: 20px;
  margin-bottom: 10px;
  color: #0a59f7;
}

.case-content p {
  margin-bottom: 10px;
}

.case-results {
  font-size: 14px;
  color: #666;
}

/* Maintenance Plans */
.plans-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-top: 40px;
}

.plan-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  height: 100%;
}

.plan-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.plan-item.featured {
  background-color: #f5f8fd;
  border: 2px solid #0a59f7;
}

.plan-header {
  margin-bottom: 20px;
  text-align: center;
}

.plan-header h3 {
  font-size: 24px;
  margin-bottom: 10px;
  color: #0a59f7;
}

.plan-subtitle {
  font-size: 14px;
  color: #666;
}

.plan-features ul {
  list-style: none;
  padding: 0;
}

.plan-features ul li {
  margin-bottom: 10px;
  padding-left: 25px;
  position: relative;
}

.plan-features i {
  color: #0a59f7;
  position: absolute;
  left: 0;
}

/* CTA Section Unified Style */
.cta-section .btn-primary {
  display: inline-block;
  padding: 12px 30px;
  background-color: #fff;
  color: #0a59f7;
  font-weight: 600;
  border-radius: 5px;
  font-size: 16px;
  transition: all 0.3s ease;
  border: 2px solid #fff;
}

.cta-section .btn-primary:hover {
  background-color: transparent;
  color: #fff;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.2);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .services-grid, .benefits-grid, .case-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .plans-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .team-content {
    grid-template-columns: 1fr;
  }
  
  .step-content {
    grid-template-columns: 1fr;
  }
  
  .step-image {
    margin-top: 20px;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .services-grid, .benefits-grid, .case-grid, .plans-container {
    grid-template-columns: 1fr;
  }
  
  .process-timeline {
    padding-left: 20px;
  }
  
  .timeline-item {
    flex-direction: column;
  }
  
  .timeline-number {
    margin-bottom: 15px;
  }
  
  .team-info {
    text-align: center;
  }
  
  .team-features {
    text-align: left;
    max-width: 80%;
    margin: 0 auto;
  }
} 