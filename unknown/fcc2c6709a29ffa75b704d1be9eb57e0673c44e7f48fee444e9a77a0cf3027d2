// 测试优化后的PostgreSQL连接
require('dotenv').config();

const { Pool } = require('pg');
const { setTimeout } = require('timers/promises');

// 从环境变量获取连接信息
const connectionString =
  process.env.DATABASE_URL ||
  process.env.POSTGRES_URI ||
  'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 测试函数
async function testConnection() {
  console.log('测试数据库连接...');
  console.log('使用连接字符串:', connectionString.replace(/:[^:]+@/, ':****@'));

  // 创建带有增强配置的连接池
  const pool = new Pool({
    connectionString,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    max: 5, // 连接池中的最大连接数
    idleTimeoutMillis: 60000, // 空闲连接超时时间（1分钟）
    connectionTimeoutMillis: 30000, // 连接超时时间（30秒）
    keepAlive: true, // 启用TCP keepalive
    keepAliveInitialDelayMillis: 30000, // TCP keepalive初始延迟（30秒）
  });

  // 添加池错误处理
  pool.on('error', err => {
    console.error('连接池错误:', err);
  });

  // 测试重试机制
  let connected = false;
  let retryCount = 0;
  const maxRetries = 3;

  while (!connected && retryCount <= maxRetries) {
    try {
      console.log(`连接尝试 ${retryCount + 1}/${maxRetries + 1}...`);

      // 获取客户端并测试连接
      const client = await pool.connect();
      console.log('已获取数据库客户端');

      // 运行测试查询
      const result = await client.query('SELECT NOW() as current_time');
      console.log('数据库查询成功:', result.rows[0]);

      // 查询表列表
      const tables = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema='public' 
        ORDER BY table_name
      `);

      console.log('\n数据库中的表:');
      if (tables.rows.length > 0) {
        tables.rows.forEach(table => {
          console.log(`- ${table.table_name}`);
        });
      } else {
        console.log('没有找到表');
      }

      // 释放客户端
      client.release();
      connected = true;
    } catch (error) {
      console.error(`连接尝试 ${retryCount + 1} 失败:`, error.message);

      if (retryCount < maxRetries) {
        const delayTime = 1000 * (retryCount + 1); // 递增延迟
        console.log(`将在 ${delayTime}ms 后重试...`);
        await setTimeout(delayTime);
        retryCount++;
      } else {
        console.error('达到最大重试次数，连接失败');
        break;
      }
    }
  }

  // 结束连接池
  try {
    await pool.end();
    console.log('数据库连接池已关闭');
  } catch (error) {
    console.error('关闭连接池时出错:', error.message);
  }

  return connected;
}

// 运行测试
testConnection()
  .then(success => {
    console.log('\n测试结果:', success ? '成功' : '失败');
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('测试过程中发生错误:', error);
    process.exit(1);
  });
