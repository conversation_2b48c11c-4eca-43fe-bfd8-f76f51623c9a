{"version": 2, "name": "interactive-equipment-ecommerce", "builds": [{"src": "package.json", "use": "@vercel/next"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/$1"}], "env": {"NODE_ENV": "production"}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "regions": ["hkg1", "sin1", "sfo1"], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "s-maxage=60, stale-while-revalidate"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}