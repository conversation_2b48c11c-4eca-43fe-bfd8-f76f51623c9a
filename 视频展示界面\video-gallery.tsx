"use client"

import { useState, useRef, useEffect } from "react"
import { Play, Pause, Volume2, VolumeX, Maximize, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Card } from "@/components/ui/card"
import { Dialog, DialogContent } from "@/components/ui/dialog"

interface Video {
  id: string
  title: string
  thumbnail: string
  duration: string
  category: string
  aspectRatio: "landscape" | "portrait" // 新增：视频方向
  resolution: string
}

const videos: Video[] = [
  {
    id: "1",
    title: "AR增强现实体验系统",
    thumbnail: "/placeholder.svg?height=200&width=350&text=AR体验",
    duration: "3:24",
    category: "AR技术",
    aspectRatio: "landscape",
    resolution: "1920x1080",
  },
  {
    id: "2",
    title: "互动投影地面系统",
    thumbnail: "/placeholder.svg?height=350&width=200&text=互动投影",
    duration: "2:45",
    category: "投影技术",
    aspectRatio: "portrait",
    resolution: "1080x1920",
  },
  {
    id: "3",
    title: "VR虚拟现实解决方案",
    thumbnail: "/placeholder.svg?height=200&width=350&text=VR体验",
    duration: "4:12",
    category: "VR技术",
    aspectRatio: "landscape",
    resolution: "1920x1080",
  },
  {
    id: "4",
    title: "全息投影展示系统",
    thumbnail: "/placeholder.svg?height=350&width=200&text=全息投影",
    duration: "3:56",
    category: "全息技术",
    aspectRatio: "portrait",
    resolution: "1080x1920",
  },
  {
    id: "5",
    title: "互动触摸屏解决方案",
    thumbnail: "/placeholder.svg?height=200&width=350&text=触摸屏",
    duration: "2:18",
    category: "触控技术",
    aspectRatio: "landscape",
    resolution: "1920x1080",
  },
  {
    id: "6",
    title: "沉浸式环境投影",
    thumbnail: "/placeholder.svg?height=350&width=200&text=环境投影",
    duration: "5:03",
    category: "投影技术",
    aspectRatio: "portrait",
    resolution: "1080x1920",
  },
  {
    id: "7",
    title: "智能展示互动系统",
    thumbnail: "/placeholder.svg?height=200&width=350&text=智能展示",
    duration: "3:37",
    category: "智能技术",
    aspectRatio: "landscape",
    resolution: "1920x1080",
  },
  {
    id: "8",
    title: "多媒体融合展示",
    thumbnail: "/placeholder.svg?height=350&width=200&text=多媒体",
    duration: "4:28",
    category: "融合技术",
    aspectRatio: "portrait",
    resolution: "1080x1920",
  },
  {
    id: "9",
    title: "数字沙盘演示系统",
    thumbnail: "/placeholder.svg?height=200&width=350&text=数字沙盘",
    duration: "6:15",
    category: "数字技术",
    aspectRatio: "landscape",
    resolution: "1920x1080",
  },
]

export default function Component() {
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [showControls, setShowControls] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<string>("全部")
  const [selectedFormat, setSelectedFormat] = useState<string>("全部")

  const videoRef = useRef<HTMLVideoElement>(null)
  const controlsTimeoutRef = useRef<NodeJS.Timeout>()

  const categories = ["全部", ...Array.from(new Set(videos.map((v) => v.category)))]
  const formats = ["全部", "横屏视频", "竖屏视频"]

  const filteredVideos = videos.filter((video) => {
    const categoryMatch = selectedCategory === "全部" || video.category === selectedCategory
    const formatMatch =
      selectedFormat === "全部" ||
      (selectedFormat === "横屏视频" && video.aspectRatio === "landscape") ||
      (selectedFormat === "竖屏视频" && video.aspectRatio === "portrait")
    return categoryMatch && formatMatch
  })

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const updateTime = () => setCurrentTime(video.currentTime)
    const updateDuration = () => setDuration(video.duration)

    video.addEventListener("timeupdate", updateTime)
    video.addEventListener("loadedmetadata", updateDuration)
    video.addEventListener("ended", () => setIsPlaying(false))

    return () => {
      video.removeEventListener("timeupdate", updateTime)
      video.removeEventListener("loadedmetadata", updateDuration)
      video.removeEventListener("ended", () => setIsPlaying(false))
    }
  }, [selectedVideo])

  const openVideo = (video: Video) => {
    setSelectedVideo(video)
    setIsPlaying(false)
    setCurrentTime(0)
  }

  const closeVideo = () => {
    setSelectedVideo(null)
    setIsPlaying(false)
    if (videoRef.current) {
      videoRef.current.pause()
    }
  }

  const togglePlay = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
    setIsPlaying(!isPlaying)
  }

  const handleProgressChange = (value: number[]) => {
    const video = videoRef.current
    if (!video) return

    const newTime = (value[0] / 100) * duration
    video.currentTime = newTime
    setCurrentTime(newTime)
  }

  const handleVolumeChange = (value: number[]) => {
    const video = videoRef.current
    if (!video) return

    const newVolume = value[0] / 100
    video.volume = newVolume
    setVolume(newVolume)
    setIsMuted(newVolume === 0)
  }

  const toggleMute = () => {
    const video = videoRef.current
    if (!video) return

    if (isMuted) {
      video.volume = volume
      setIsMuted(false)
    } else {
      video.volume = 0
      setIsMuted(true)
    }
  }

  const toggleFullscreen = () => {
    const video = videoRef.current
    if (!video) return

    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      video.requestFullscreen()
    }
  }

  const handleMouseMove = () => {
    setShowControls(true)
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current)
    }
    controlsTimeoutRef.current = setTimeout(() => {
      if (isPlaying) {
        setShowControls(false)
      }
    }, 3000)
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, "0")}`
  }

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0

  return (
    <div className="w-full max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">产品演示视频</h1>
        <p className="text-xl text-gray-600">探索我们完整的交互式技术解决方案</p>
      </div>

      {/* Filters */}
      <div className="mb-8 space-y-4">
        {/* Category Filter */}
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">技术分类</h3>
          <div className="flex flex-wrap gap-3">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                onClick={() => setSelectedCategory(category)}
                className="rounded-full"
              >
                {category}
              </Button>
            ))}
          </div>
        </div>

        {/* Format Filter */}
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">视频格式</h3>
          <div className="flex flex-wrap gap-3">
            {formats.map((format) => (
              <Button
                key={format}
                variant={selectedFormat === format ? "default" : "outline"}
                onClick={() => setSelectedFormat(format)}
                className="rounded-full"
              >
                {format}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Video Grid - 使用CSS Grid自动适应不同尺寸 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 auto-rows-max">
        {filteredVideos.map((video) => (
          <Card
            key={video.id}
            className={`group cursor-pointer overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 ${
              video.aspectRatio === "portrait" ? "row-span-2" : ""
            }`}
            onClick={() => openVideo(video)}
          >
            <div
              className={`relative bg-gray-100 ${video.aspectRatio === "landscape" ? "aspect-video" : "aspect-[9/16]"}`}
            >
              <img
                src={video.thumbnail || "/placeholder.svg"}
                alt={video.title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />

              {/* Play Button Overlay */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                <div className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 shadow-lg">
                  <Play className="w-6 h-6 text-gray-900 ml-1" fill="currentColor" />
                </div>
              </div>

              {/* Duration Badge */}
              <div className="absolute bottom-3 right-3 bg-black/80 text-white text-sm px-2 py-1 rounded">
                {video.duration}
              </div>

              {/* Category Badge */}
              <div className="absolute top-3 left-3 bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                {video.category}
              </div>

              {/* Resolution Badge */}
              <div className="absolute top-3 right-3 bg-green-600 text-white text-xs px-2 py-1 rounded-full">
                {video.resolution}
              </div>

              {/* Format Indicator */}
              <div
                className={`absolute bottom-3 left-3 text-white text-xs px-2 py-1 rounded-full ${
                  video.aspectRatio === "landscape" ? "bg-orange-600" : "bg-purple-600"
                }`}
              >
                {video.aspectRatio === "landscape" ? "横屏" : "竖屏"}
              </div>
            </div>

            <div className="p-4">
              <h3 className="font-semibold text-lg text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-2">
                {video.title}
              </h3>
            </div>
          </Card>
        ))}
      </div>

      {/* Video Modal */}
      <Dialog open={!!selectedVideo} onOpenChange={() => closeVideo()}>
        <DialogContent
          className={`w-full p-0 bg-black ${selectedVideo?.aspectRatio === "portrait" ? "max-w-2xl" : "max-w-5xl"}`}
        >
          {selectedVideo && (
            <div className="relative">
              {/* Close Button */}
              <Button
                variant="ghost"
                size="sm"
                className="absolute top-4 right-4 z-50 text-white hover:bg-white/20 rounded-full w-10 h-10"
                onClick={closeVideo}
              >
                <X className="w-5 h-5" />
              </Button>

              {/* Video Player */}
              <div
                className={`relative group ${
                  selectedVideo.aspectRatio === "landscape" ? "aspect-video" : "aspect-[9/16]"
                }`}
                onMouseMove={handleMouseMove}
                onMouseLeave={() => isPlaying && setShowControls(false)}
              >
                <video
                  ref={videoRef}
                  className="w-full h-full object-cover"
                  onClick={togglePlay}
                  poster={selectedVideo.thumbnail}
                >
                  <source
                    src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
                    type="video/mp4"
                  />
                  您的浏览器不支持视频播放。
                </video>

                {/* Play Button Overlay */}
                {!isPlaying && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                    <Button
                      size="lg"
                      className="w-20 h-20 rounded-full bg-white/90 hover:bg-white text-black shadow-lg"
                      onClick={togglePlay}
                    >
                      <Play className="w-8 h-8 ml-1" fill="currentColor" />
                    </Button>
                  </div>
                )}

                {/* Video Controls */}
                <div
                  className={`absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent transition-opacity duration-300 ${showControls ? "opacity-100" : "opacity-0"}`}
                >
                  {/* Progress Bar */}
                  <div className="px-6 pb-2">
                    <Slider
                      value={[progress]}
                      onValueChange={handleProgressChange}
                      className="w-full [&>span:first-child]:h-1 [&>span:first-child]:bg-white/30 [&_[role=slider]]:bg-blue-500 [&_[role=slider]]:w-4 [&_[role=slider]]:h-4 [&_[role=slider]]:border-0 [&>span:first-child_span]:bg-blue-500"
                      max={100}
                      step={0.1}
                    />
                  </div>

                  {/* Control Buttons */}
                  <div className="flex items-center justify-between px-6 pb-4">
                    <div className="flex items-center gap-4">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-white hover:bg-white/20 w-10 h-10"
                        onClick={togglePlay}
                      >
                        {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
                      </Button>

                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="text-white hover:bg-white/20 w-10 h-10"
                          onClick={toggleMute}
                        >
                          {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                        </Button>
                        <div className="w-20">
                          <Slider
                            value={[isMuted ? 0 : volume * 100]}
                            onValueChange={handleVolumeChange}
                            className="[&>span:first-child]:h-1 [&>span:first-child]:bg-white/30 [&_[role=slider]]:bg-white [&_[role=slider]]:w-3 [&_[role=slider]]:h-3 [&_[role=slider]]:border-0 [&>span:first-child_span]:bg-white"
                            max={100}
                          />
                        </div>
                      </div>

                      <div className="text-white text-sm font-medium">
                        {formatTime(currentTime)} / {formatTime(duration)}
                      </div>

                      <div className="hidden md:block text-white text-lg font-medium max-w-md truncate">
                        {selectedVideo.title}
                      </div>

                      <div className="text-white text-sm bg-white/20 px-2 py-1 rounded">{selectedVideo.resolution}</div>
                    </div>

                    <Button
                      size="sm"
                      variant="ghost"
                      className="text-white hover:bg-white/20 w-10 h-10"
                      onClick={toggleFullscreen}
                    >
                      <Maximize className="w-5 h-5" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
